"""
Launch Grid Trading ML Training System
Updated system with correct grid trading implementation
"""

import os
import sys
import logging
import asyncio
from datetime import datetime

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import updated grid trading system
from advanced_ml_training_system import AdvancedMLTradingSystem

def setup_logging():
    """Setup logging for grid training"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/grid_training.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """Main training function"""
    print("🚀 GRID TRADING ML TRAINING SYSTEM")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger('GridTrainingLauncher')
    
    try:
        # Initialize configuration
        config = TradingConfig()
        
        # Validate grid trading configuration
        logger.info("📋 Grid Trading Configuration:")
        logger.info(f"   Starting Capital: ${config.INITIAL_CAPITAL}")
        logger.info(f"   Risk per Trade: ${config.FIXED_RISK_AMOUNT}")
        logger.info(f"   Profit per Trade: ${config.TARGET_PROFIT}")
        logger.info(f"   Grid Spacing: {config.GRID_SPACING * 100:.2f}%")
        logger.info(f"   Trading Pairs: {config.TRADING_PAIRS}")
        logger.info(f"   Training Days: {config.TRAINING_DAYS}")
        logger.info(f"   Testing Days: {config.TESTING_DAYS}")
        logger.info(f"   Composite Threshold: {config.COMPOSITE_SCORE_THRESHOLD * 100:.0f}%")
        
        # Initialize training system
        logger.info("🤖 Initializing Grid Trading ML System...")
        training_system = AdvancedMLTradingSystem(config)
        
        # Start training
        logger.info("🎯 Starting Grid Trading Model Training...")
        logger.info("   Features: VWAP, RSI-5, ETH/BTC ratio, Bollinger Bands-20")
        logger.info("   Models: TCN, CNN, PPO ensemble")
        logger.info("   Objective: Maximize composite reward score")
        
        # Train all symbols (BTC only for grid trading)
        results = training_system.train_all_symbols()
        
        # Print results
        print("\n" + "=" * 60)
        print("🎉 GRID TRADING TRAINING COMPLETED")
        print("=" * 60)
        
        summary = results.get('summary', {})
        
        print(f"📊 TRAINING SUMMARY:")
        print(f"   Total Symbols: {summary.get('total_symbols', 0)}")
        print(f"   Successful Models: {summary.get('successful_models', 0)}")
        print(f"   Threshold Met: {summary.get('threshold_met', 0)}")
        print(f"   Success Rate: {summary.get('success_rate', 0):.1f}%")
        
        print(f"\n🏆 BEST MODELS:")
        print(f"   Best Composite Score: {summary.get('best_composite_score', 0):.4f}")
        print(f"   Best Net Profit: ${summary.get('best_net_profit', 0):.2f}")
        print(f"   Deployment Ready: {'✅ YES' if summary.get('deployment_ready', False) else '❌ NO'}")
        
        # Show deployment model info
        deployment_model = summary.get('deployment_model', {})
        if deployment_model.get('model') is not None:
            print(f"\n🚀 DEPLOYMENT MODEL:")
            print(f"   Symbol: {deployment_model.get('symbol', 'Unknown')}")
            print(f"   Composite Score: {deployment_model.get('score', 0):.4f}")
            print(f"   Net Profit: ${deployment_model.get('net_profit', 0):.2f}")
            print(f"   Timestamp: {deployment_model.get('timestamp', 'Unknown')}")
            
            if deployment_model.get('score', 0) >= config.COMPOSITE_SCORE_THRESHOLD:
                print("   Status: ✅ MEETS THRESHOLD - Ready for live trading!")
            else:
                print("   Status: ⚠️ BELOW THRESHOLD - Best available model")
        else:
            print("\n❌ NO DEPLOYMENT MODEL AVAILABLE")
            print("   Training failed or no models met minimum criteria")
        
        # Show individual symbol results
        print(f"\n📈 INDIVIDUAL RESULTS:")
        for symbol, result in results.items():
            if symbol == 'summary':
                continue
                
            if 'error' in result:
                print(f"   {symbol}: ❌ ERROR - {result['error']}")
            else:
                score = result.get('composite_score', 0)
                profit = result.get('estimated_profit', 0)
                threshold = result.get('meets_threshold', False)
                status = "✅ PASS" if threshold else "❌ FAIL"
                print(f"   {symbol}: Score {score:.4f}, Profit ${profit:.2f} {status}")
        
        # Next steps
        print(f"\n🔄 NEXT STEPS:")
        if summary.get('deployment_ready', False):
            print("   1. ✅ Model ready for deployment")
            print("   2. 🔧 Create monitoring dashboard")
            print("   3. 📊 Test with paper trading")
            print("   4. 💰 Deploy for real money trading")
        else:
            print("   1. 🔍 Review training parameters")
            print("   2. 📊 Analyze model performance")
            print("   3. 🔧 Adjust hyperparameters")
            print("   4. 🔄 Retrain with optimizations")
        
        print(f"\n📁 Reports saved to: {config.REPORTS_DIR}")
        print(f"📁 Models saved to: {config.MODELS_DIR}")
        print(f"📁 Logs saved to: {config.LOGS_DIR}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        print(f"\n❌ TRAINING FAILED: {e}")
        return None

if __name__ == "__main__":
    # Ensure directories exist
    os.makedirs('logs', exist_ok=True)
    os.makedirs('models', exist_ok=True)
    os.makedirs('reports', exist_ok=True)
    
    # Run training
    results = main()
    
    if results:
        print("\n🎯 Grid Trading ML Training completed successfully!")
        print("Check the logs and reports for detailed results.")
    else:
        print("\n💥 Grid Trading ML Training failed!")
        print("Check the logs for error details.")
    
    input("\nPress Enter to exit...")
