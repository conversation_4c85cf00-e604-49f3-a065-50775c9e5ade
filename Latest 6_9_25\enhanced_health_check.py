#!/usr/bin/env python3
"""
ENHANCED TCN-CNN-PPO HEALTH CHECK
=================================
Standalone health check for the refactored Enhanced TCN-CNN-PPO system
"""

import json
import os
import requests
from datetime import datetime

def run_enhanced_health_check():
    """Run comprehensive health check for Enhanced TCN-CNN-PPO system"""
    print("🔍 ENHANCED TCN-CNN-PPO HEALTH CHECK")
    print("=" * 45)
    
    health_report = {
        'timestamp': datetime.now().isoformat(),
        'overall_status': 'HEALTHY',
        'checks': {},
        'issues': [],
        'warnings': []
    }
    
    base_url = "http://localhost:5000"
    
    try:
        # 1. Test webapp connectivity
        print("🌐 Testing webapp connectivity...")
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print("✅ Webapp accessible")
            health_report['checks']['webapp'] = 'ACCESSIBLE'
        else:
            print(f"❌ Webapp returned status {response.status_code}")
            health_report['checks']['webapp'] = f'HTTP_{response.status_code}'
            health_report['issues'].append(f'Webapp returned status {response.status_code}')
        
        # 2. Test API endpoints
        print("\n🔌 Testing API endpoints...")
        endpoints = [
            '/api/trading_status',
            '/api/health_check',
            '/api/recent_trades'
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint}")
                    health_report['checks'][f'api_{endpoint.replace("/", "_")}'] = 'OK'
                else:
                    print(f"❌ {endpoint} - Status: {response.status_code}")
                    health_report['checks'][f'api_{endpoint.replace("/", "_")}'] = f'HTTP_{response.status_code}'
                    health_report['issues'].append(f'{endpoint} returned {response.status_code}')
            except Exception as e:
                print(f"❌ {endpoint} - Error: {e}")
                health_report['checks'][f'api_{endpoint.replace("/", "_")}'] = 'FAILED'
                health_report['issues'].append(f'{endpoint} failed: {str(e)}')
        
        # 3. Validate Enhanced TCN-CNN-PPO model data
        print("\n🧠 Validating Enhanced TCN-CNN-PPO model...")
        try:
            response = requests.get(f"{base_url}/api/trading_status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                
                # Check model name
                model_name = data.get('model_name', '')
                if 'Enhanced TCN-CNN-PPO' in model_name:
                    print(f"✅ Model: {model_name}")
                    health_report['checks']['model_name'] = 'CORRECT'
                else:
                    print(f"⚠️ Unexpected model: {model_name}")
                    health_report['checks']['model_name'] = 'UNEXPECTED'
                    health_report['warnings'].append(f'Unexpected model name: {model_name}')
                
                # Check performance metrics
                win_rate = data.get('win_rate', 0) * 100
                composite_score = data.get('composite_score', 0) * 100
                trades_per_day = data.get('trades_per_day', 0)
                
                print(f"✅ Win Rate: {win_rate:.1f}% (Target: >85%)")
                print(f"{'✅' if composite_score >= 90 else '❌'} Composite Score: {composite_score:.1f}% (Target: >90%)")
                print(f"✅ Trades/Day: {trades_per_day} (Target: 5.0)")
                
                health_report['checks']['win_rate'] = f'{win_rate:.1f}%'
                health_report['checks']['composite_score'] = f'{composite_score:.1f}%'
                health_report['checks']['trades_per_day'] = str(trades_per_day)
                
                # Check ensemble weights
                ensemble_weights = data.get('ensemble_weights', {})
                if ensemble_weights:
                    tcn = ensemble_weights.get('tcn', 0)
                    cnn = ensemble_weights.get('cnn', 0)
                    ppo = ensemble_weights.get('ppo', 0)
                    
                    if tcn == 40 and cnn == 40 and ppo == 20:
                        print(f"✅ Ensemble Weights: TCN {tcn}%, CNN {cnn}%, PPO {ppo}%")
                        health_report['checks']['ensemble_weights'] = 'CORRECT'
                    else:
                        print(f"⚠️ Ensemble Weights: TCN {tcn}%, CNN {cnn}%, PPO {ppo}%")
                        health_report['checks']['ensemble_weights'] = 'INCORRECT'
                        health_report['warnings'].append('Ensemble weights not at expected values')
                else:
                    print("⚠️ Ensemble weights not available")
                    health_report['warnings'].append('Ensemble weights not reported')
                
        except Exception as e:
            print(f"❌ Model validation failed: {e}")
            health_report['issues'].append(f'Model validation failed: {str(e)}')
        
        # 4. Check database file
        print("\n🗄️ Checking database...")
        db_file = "enhanced_tcn_cnn_ppo_trades.db"
        if os.path.exists(db_file):
            print(f"✅ Database file exists: {db_file}")
            health_report['checks']['database_file'] = 'EXISTS'
        else:
            print(f"⚠️ Database file not found: {db_file}")
            health_report['checks']['database_file'] = 'MISSING'
            health_report['warnings'].append('Database file not found')
        
        # 5. Check essential files
        print("\n📁 Checking essential files...")
        essential_files = [
            'bitcoin_freedom_enhanced_tcn_cnn_ppo.py',
            'templates/enhanced_tcn_cnn_ppo_dashboard.html',
            'requirements.txt'
        ]
        
        for file_path in essential_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path}")
                health_report['checks'][f'file_{file_path.replace("/", "_").replace(".", "_")}'] = 'EXISTS'
            else:
                print(f"❌ {file_path}")
                health_report['checks'][f'file_{file_path.replace("/", "_").replace(".", "_")}'] = 'MISSING'
                health_report['issues'].append(f'Essential file missing: {file_path}')
        
        # Determine overall status
        if health_report['issues']:
            health_report['overall_status'] = 'CRITICAL'
        elif health_report['warnings']:
            health_report['overall_status'] = 'WARNING'
        else:
            health_report['overall_status'] = 'HEALTHY'
        
        # Summary
        print(f"\n📊 HEALTH CHECK SUMMARY")
        print("-" * 30)
        print(f"Overall Status: {health_report['overall_status']}")
        print(f"Issues: {len(health_report['issues'])}")
        print(f"Warnings: {len(health_report['warnings'])}")
        
        if health_report['issues']:
            print("\n❌ Issues Found:")
            for issue in health_report['issues']:
                print(f"  - {issue}")
        
        if health_report['warnings']:
            print("\n⚠️ Warnings:")
            for warning in health_report['warnings']:
                print(f"  - {warning}")
        
        # Save report
        report_file = f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(health_report, f, indent=2)
        
        print(f"\n📄 Health report saved: {report_file}")
        
        return health_report
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to webapp - ensure it's running on http://localhost:5000")
        health_report['overall_status'] = 'CRITICAL'
        health_report['issues'].append('Cannot connect to webapp')
        return health_report
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        health_report['overall_status'] = 'ERROR'
        health_report['issues'].append(f'Health check failed: {str(e)}')
        return health_report

def main():
    """Main health check function"""
    print("🚀 ENHANCED TCN-CNN-PPO SYSTEM HEALTH CHECK")
    print("=" * 50)
    
    report = run_enhanced_health_check()
    
    print("\n🎯 HEALTH CHECK COMPLETE")
    if report['overall_status'] == 'HEALTHY':
        print("✅ Enhanced TCN-CNN-PPO system is healthy and ready")
    elif report['overall_status'] == 'WARNING':
        print("⚠️ Enhanced TCN-CNN-PPO system has warnings but is operational")
    else:
        print("❌ Enhanced TCN-CNN-PPO system has critical issues")
    
    return report

if __name__ == "__main__":
    main()
