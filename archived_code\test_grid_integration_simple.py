"""
Grid Trading System Integration Test - Simplified Version
Tests all components working together with simplified ML system
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import json
from datetime import datetime
from typing import Dict, List

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import grid trading components
from grid_trading_core import GridLevelCalculator, GridTradeManager, GridAction, GridLevel
from grid_feature_engineering import GridFeatureEngineering
from grid_composite_metrics import GridCompositeMetrics
from launch_grid_training_simple import SimpleMLTrainingSystem

class SimpleGridIntegrationTester:
    """Integration testing for simplified grid trading system"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('SimpleGridIntegrationTester')
        
        # Test results
        self.test_results = {}
        self.errors = []
        
    def run_all_tests(self) -> Dict[str, bool]:
        """Run comprehensive integration tests"""
        
        print("🧪 SIMPLIFIED GRID TRADING INTEGRATION TESTS")
        print("=" * 50)
        
        tests = [
            ("Configuration Validation", self.test_configuration),
            ("Grid Level Calculator", self.test_grid_calculator),
            ("Feature Engineering", self.test_feature_engineering),
            ("Composite Metrics", self.test_composite_metrics),
            ("Trade Manager", self.test_trade_manager),
            ("Simple ML System", self.test_simple_ml_system),
            ("Model Integration", self.test_model_integration),
            ("Performance Calculation", self.test_performance_calculation),
            ("End-to-End Pipeline", self.test_end_to_end_pipeline)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Testing: {test_name}")
            try:
                result = test_func()
                self.test_results[test_name] = result
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"   Result: {status}")
            except Exception as e:
                self.test_results[test_name] = False
                self.errors.append(f"{test_name}: {str(e)}")
                print(f"   Result: ❌ ERROR - {e}")
        
        # Summary
        self._print_test_summary()
        
        return self.test_results
    
    def test_configuration(self) -> bool:
        """Test configuration matches specification"""
        
        checks = [
            (self.config.INITIAL_CAPITAL == 300, "Starting capital should be $300"),
            (self.config.FIXED_RISK_AMOUNT == 10, "Risk per trade should be $10"),
            (self.config.TARGET_PROFIT == 20, "Profit target should be $20"),
            (self.config.GRID_SPACING == 0.0025, "Grid spacing should be 0.25%"),
            (self.config.TRADING_PAIRS == ['BTCUSDT'], "Should trade BTC only"),
            (self.config.COMPOSITE_SCORE_THRESHOLD == 0.85, "Threshold should be 85%"),
            (self.config.MAX_CONCURRENT_TRADES == 15, "Max trades should be 15")
        ]
        
        for check, message in checks:
            if not check:
                print(f"   ❌ {message}")
                return False
            else:
                print(f"   ✅ {message}")
        
        return True
    
    def test_grid_calculator(self) -> bool:
        """Test grid level calculation"""
        
        calculator = GridLevelCalculator(self.config)
        
        test_price = 50000.0
        grid_levels = calculator.calculate_grid_levels(test_price)
        
        if len(grid_levels) == 0:
            print("   ❌ No grid levels generated")
            return False
        
        print(f"   ✅ Generated {len(grid_levels)} grid levels")
        
        # Test grid touch detection
        touched = calculator.check_grid_touch(50125.0, 50000.0, grid_levels)
        if len(touched) == 0:
            print("   ❌ Grid touch detection failed")
            return False
        
        print(f"   ✅ Grid touch detection working")
        return True
    
    def test_feature_engineering(self) -> bool:
        """Test feature engineering with 4 indicators"""
        
        feature_engineer = GridFeatureEngineering(self.config)
        
        # Create sample data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
        
        btc_data = pd.DataFrame({
            'open': np.random.uniform(49000, 51000, 100),
            'high': np.random.uniform(50000, 52000, 100),
            'low': np.random.uniform(48000, 50000, 100),
            'close': np.random.uniform(49500, 50500, 100),
            'volume': np.random.uniform(100, 1000, 100)
        }, index=dates)
        
        eth_data = pd.DataFrame({
            'open': np.random.uniform(3200, 3400, 100),
            'high': np.random.uniform(3300, 3500, 100),
            'low': np.random.uniform(3100, 3300, 100),
            'close': np.random.uniform(3200, 3400, 100),
            'volume': np.random.uniform(100, 1000, 100)
        }, index=dates)
        
        features_df = feature_engineer.create_grid_features(btc_data, eth_data)
        
        if not feature_engineer.validate_features(features_df):
            print("   ❌ Feature validation failed")
            return False
        
        feature_columns = [col for col in features_df.columns 
                          if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        if len(feature_columns) != 9:
            print(f"   ❌ Expected 9 features, got {len(feature_columns)}")
            return False
        
        print(f"   ✅ Created {len(feature_columns)} features from 4 indicators")
        return True
    
    def test_composite_metrics(self) -> bool:
        """Test composite metrics calculation"""
        
        metrics_calculator = GridCompositeMetrics(self.config)
        
        sample_trades = [
            {'profit_loss': 20, 'timestamp': '2024-01-01T10:00:00'},
            {'profit_loss': -10, 'timestamp': '2024-01-01T11:00:00'},
            {'profit_loss': 20, 'timestamp': '2024-01-01T12:00:00'}
        ]
        
        metrics = metrics_calculator.calculate_composite_score(sample_trades, 300)
        
        if 'composite_score' not in metrics:
            print("   ❌ Composite score not calculated")
            return False
        
        required_metrics = [
            'win_rate', 'equity', 'sortino_ratio', 'calmar_ratio',
            'profit_factor', 'max_drawdown', 'risk_of_ruin', 'trade_frequency'
        ]
        
        for metric in required_metrics:
            if metric not in metrics:
                print(f"   ❌ Missing metric: {metric}")
                return False
        
        print(f"   ✅ Composite score: {metrics['composite_score']:.4f}")
        print(f"   ✅ All 8 metrics calculated")
        return True
    
    def test_trade_manager(self) -> bool:
        """Test trade management functionality"""
        
        trade_manager = GridTradeManager(self.config)
        
        test_price = 50000.0
        position_size = trade_manager.calculate_position_size(test_price)
        
        if position_size <= 0:
            print("   ❌ Invalid position size")
            return False
        
        print(f"   ✅ Position size calculation: {position_size:.6f} BTC")
        
        exit_price, stop_loss = trade_manager.calculate_exit_levels(test_price, GridAction.BUY)
        
        expected_exit = test_price * (1 + self.config.GRID_SPACING)
        if abs(exit_price - expected_exit) > 1.0:
            print(f"   ❌ Incorrect exit price: {exit_price} vs {expected_exit}")
            return False
        
        print(f"   ✅ Exit levels calculated correctly")
        return True
    
    def test_simple_ml_system(self) -> bool:
        """Test simple ML training system"""
        
        try:
            ml_system = SimpleMLTrainingSystem(self.config)
            
            # Test model creation
            model_info = ml_system.create_simple_model()
            
            if 'model' not in model_info:
                print("   ❌ Model creation failed")
                return False
            
            print(f"   ✅ Simple model created: {model_info['model'].name}")
            
            # Test model prediction
            sample_features = np.random.random((5, 9))  # 5 samples, 9 features
            predictions = model_info['model'].predict(sample_features)
            
            if len(predictions) != 5:
                print(f"   ❌ Wrong prediction count: {len(predictions)}")
                return False
            
            print(f"   ✅ Model predictions working")
            
            # Check if saved model exists
            if os.path.exists('models/simple_grid_model.json'):
                with open('models/simple_grid_model.json', 'r') as f:
                    saved_model = json.load(f)
                
                if 'model_type' not in saved_model:
                    print("   ❌ Saved model format invalid")
                    return False
                
                print(f"   ✅ Model persistence working")
            else:
                print("   ⚠️ No saved model found (run training first)")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Simple ML system error: {e}")
            return False
    
    def test_model_integration(self) -> bool:
        """Test model integration with grid components"""
        
        try:
            # Test feature compatibility
            feature_engineer = GridFeatureEngineering(self.config)
            
            # Create sample data
            dates = pd.date_range(start='2024-01-01', periods=50, freq='1min')
            btc_data = pd.DataFrame({
                'open': np.random.uniform(49000, 51000, 50),
                'high': np.random.uniform(50000, 52000, 50),
                'low': np.random.uniform(48000, 50000, 50),
                'close': np.random.uniform(49500, 50500, 50),
                'volume': np.random.uniform(100, 1000, 50)
            }, index=dates)
            
            eth_data = pd.DataFrame({
                'open': np.random.uniform(3200, 3400, 50),
                'high': np.random.uniform(3300, 3500, 50),
                'low': np.random.uniform(3100, 3300, 50),
                'close': np.random.uniform(3200, 3400, 50),
                'volume': np.random.uniform(100, 1000, 50)
            }, index=dates)
            
            # Create features
            current_features = feature_engineer.get_current_features(btc_data, eth_data)
            
            if current_features.shape != (1, 9):
                print(f"   ❌ Wrong feature shape: {current_features.shape}")
                return False
            
            print(f"   ✅ Feature shape compatibility")
            
            # Test with simple model
            ml_system = SimpleMLTrainingSystem(self.config)
            model_info = ml_system.create_simple_model()
            
            prediction = model_info['model'].predict(current_features)
            
            if len(prediction) != 1:
                print(f"   ❌ Wrong prediction shape: {len(prediction)}")
                return False
            
            # Test grid action mapping
            prob = prediction[0]
            if prob > 0.6:
                action = GridAction.BUY
            elif prob < 0.4:
                action = GridAction.SELL
            else:
                action = GridAction.HOLD
            
            print(f"   ✅ Grid action mapping: {prob:.3f} -> {action.name}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Model integration error: {e}")
            return False
    
    def test_performance_calculation(self) -> bool:
        """Test performance calculation accuracy"""
        
        metrics_calculator = GridCompositeMetrics(self.config)
        
        # Test with known values
        test_trades = [
            {'profit_loss': 20, 'timestamp': '2024-01-01T10:00:00'},  # Win
            {'profit_loss': 20, 'timestamp': '2024-01-01T11:00:00'},  # Win
            {'profit_loss': -10, 'timestamp': '2024-01-01T12:00:00'}, # Loss
            {'profit_loss': 20, 'timestamp': '2024-01-01T13:00:00'},  # Win
        ]
        
        expected_win_rate = 0.75  # 3 wins, 1 loss
        
        metrics = metrics_calculator.calculate_composite_score(test_trades, 300)
        actual_win_rate = metrics.get('win_rate', 0)
        
        if abs(actual_win_rate - expected_win_rate) > 0.01:
            print(f"   ❌ Win rate calculation: {actual_win_rate} vs {expected_win_rate}")
            return False
        
        print(f"   ✅ Win rate calculation: {actual_win_rate:.2%}")
        
        expected_equity = (20 + 20 - 10 + 20) / 300  # Total P&L / Initial capital
        actual_equity = metrics.get('equity', 0)
        
        if abs(actual_equity - expected_equity) > 0.01:
            print(f"   ❌ Equity calculation: {actual_equity} vs {expected_equity}")
            return False
        
        print(f"   ✅ Equity calculation: {actual_equity:.2%}")
        
        return True
    
    def test_end_to_end_pipeline(self) -> bool:
        """Test complete end-to-end pipeline"""
        
        try:
            print("   🔄 Running end-to-end pipeline test...")
            
            # Initialize all components
            grid_calculator = GridLevelCalculator(self.config)
            trade_manager = GridTradeManager(self.config)
            feature_engineer = GridFeatureEngineering(self.config)
            metrics_calculator = GridCompositeMetrics(self.config)
            ml_system = SimpleMLTrainingSystem(self.config)
            
            # Create sample data
            dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
            btc_data = pd.DataFrame({
                'open': np.random.uniform(49000, 51000, 100),
                'high': np.random.uniform(50000, 52000, 100),
                'low': np.random.uniform(48000, 50000, 100),
                'close': np.random.uniform(49500, 50500, 100),
                'volume': np.random.uniform(100, 1000, 100)
            }, index=dates)
            
            eth_data = pd.DataFrame({
                'open': np.random.uniform(3200, 3400, 100),
                'high': np.random.uniform(3300, 3500, 100),
                'low': np.random.uniform(3100, 3300, 100),
                'close': np.random.uniform(3200, 3400, 100),
                'volume': np.random.uniform(100, 1000, 100)
            }, index=dates)
            
            # Step 1: Feature engineering
            features_df = feature_engineer.create_grid_features(btc_data, eth_data)
            print(f"   ✅ Features created: {len(features_df)} rows")
            
            # Step 2: Grid level calculation
            current_price = btc_data['close'].iloc[-1]
            grid_levels = grid_calculator.calculate_grid_levels(current_price)
            print(f"   ✅ Grid levels: {len(grid_levels)} levels")
            
            # Step 3: ML prediction
            model_info = ml_system.create_simple_model()
            current_features = feature_engineer.get_current_features(btc_data, eth_data)
            prediction = model_info['model'].predict(current_features)[0]
            
            # Map to grid action
            if prediction > 0.6:
                action = GridAction.BUY
            elif prediction < 0.4:
                action = GridAction.SELL
            else:
                action = GridAction.HOLD
            
            print(f"   ✅ ML prediction: {prediction:.3f} -> {action.name}")
            
            # Step 4: Trade execution simulation
            if action != GridAction.HOLD:
                position_size = trade_manager.calculate_position_size(current_price)
                level = GridLevel(1, current_price, self.config.GRID_SPACING)
                trade_record = trade_manager.create_trade_record(level, action, position_size, current_price)
                
                # Simulate trade outcome
                trade_record['profit_loss'] = 20 if np.random.random() > 0.3 else -10
                trade_record['status'] = 'CLOSED'
                
                print(f"   ✅ Trade executed: {action.name} {position_size:.6f} BTC")
                
                # Step 5: Performance calculation
                metrics = metrics_calculator.calculate_composite_score([trade_record], 300)
                print(f"   ✅ Performance calculated: {metrics['composite_score']:.4f}")
            else:
                print(f"   ✅ HOLD decision - no trade executed")
            
            print("   ✅ End-to-end pipeline completed successfully")
            
            return True
            
        except Exception as e:
            print(f"   ❌ End-to-end pipeline failed: {e}")
            return False
    
    def _print_test_summary(self):
        """Print comprehensive test summary"""
        
        print("\n" + "=" * 60)
        print("🧪 SIMPLIFIED INTEGRATION TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests
        
        print(f"📊 RESULTS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests} ✅")
        print(f"   Failed: {failed_tests} ❌")
        print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for test_name, result in self.test_results.items():
                if not result:
                    print(f"   - {test_name}")
        
        if self.errors:
            print(f"\n🚨 ERRORS:")
            for error in self.errors:
                print(f"   - {error}")
        
        print(f"\n🎯 DEPLOYMENT READINESS:")
        if passed_tests == total_tests:
            print("   ✅ READY FOR LIVE DEPLOYMENT")
            print("   All integration tests passed successfully!")
        elif passed_tests >= total_tests * 0.8:
            print("   ⚠️ MOSTLY READY - Minor issues detected")
            print("   Review failed tests before deployment")
        else:
            print("   ❌ NOT READY FOR DEPLOYMENT")
            print("   Critical issues must be resolved first")

def main():
    """Run integration tests"""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Initialize config
    config = TradingConfig()
    
    # Run tests
    tester = SimpleGridIntegrationTester(config)
    results = tester.run_all_tests()
    
    return results

if __name__ == "__main__":
    results = main()
    
    # Exit with appropriate code
    all_passed = all(results.values())
    exit(0 if all_passed else 1)
