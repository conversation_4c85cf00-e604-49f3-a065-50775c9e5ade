#!/usr/bin/env python3
"""
Test Enhanced Retraining System - VPS 4
=======================================

Quick test script to verify the enhanced retraining system works correctly.

Usage:
    python test_enhanced_retraining.py

Author: Trading System VPS 4
Date: 2025-01-27
"""

import os
import sys
import torch
import numpy as np
from datetime import datetime
import logging

# Configure logging for testing
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        from enhanced_retraining_system import (
            RetrainingConfig, 
            EnhancedTradingModel, 
            EnhancedModelTrainer,
            EnhancedModelTester,
            EnhancedRetrainingSystem,
            ModelPerformance
        )
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_config():
    """Test configuration creation."""
    print("🧪 Testing configuration...")
    
    try:
        from enhanced_retraining_system import RetrainingConfig
        
        config = RetrainingConfig(
            training_days=60,
            testing_days=30,
            target_composite_score=0.95
        )
        
        assert config.training_days == 60
        assert config.testing_days == 30
        assert config.target_composite_score == 0.95
        assert len(config.model_variants) == 4
        
        print("✅ Configuration test passed")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_model_creation():
    """Test enhanced model creation."""
    print("🧪 Testing model creation...")
    
    try:
        from enhanced_retraining_system import EnhancedTradingModel
        
        model = EnhancedTradingModel(
            input_size=216,
            hidden_size=512,  # Smaller for testing
            target_frequency='balanced'
        )
        
        # Test forward pass
        test_input = torch.randn(10, 216)
        signals, frequency, risk_level, confidence = model(test_input)
        
        assert signals.shape == (10, 3)  # Buy, Hold, Sell
        assert frequency.shape == (10, 1)
        assert risk_level.shape == (10, 1)
        assert confidence.shape == (10, 1)
        
        print("✅ Model creation test passed")
        return True
    except Exception as e:
        print(f"❌ Model creation test failed: {e}")
        return False

def test_trainer():
    """Test model trainer."""
    print("🧪 Testing model trainer...")
    
    try:
        from enhanced_retraining_system import RetrainingConfig, EnhancedModelTrainer
        
        config = RetrainingConfig()
        trainer = EnhancedModelTrainer(config)
        
        # Test single model training (reduced epochs for testing)
        model = trainer.train_model_variant(
            target_frequency='balanced',
            model_id='test_model',
            num_epochs=5  # Reduced for testing
        )
        
        assert model is not None
        assert hasattr(model, 'target_frequency')
        assert model.target_frequency == 'balanced'
        
        print("✅ Trainer test passed")
        return True
    except Exception as e:
        print(f"❌ Trainer test failed: {e}")
        return False

def test_tester():
    """Test model tester."""
    print("🧪 Testing model tester...")
    
    try:
        from enhanced_retraining_system import (
            RetrainingConfig, 
            EnhancedTradingModel, 
            EnhancedModelTester
        )
        
        config = RetrainingConfig()
        tester = EnhancedModelTester(config)
        
        # Create a test model
        model = EnhancedTradingModel(target_frequency='balanced')
        
        # Test model evaluation
        performance = tester.test_model(model, 'test_model', test_days=5)  # Reduced for testing
        
        assert performance.model_id == 'test_model'
        assert performance.target_frequency == 'balanced'
        assert 0 <= performance.composite_score <= 1
        assert performance.net_profit is not None
        assert performance.trades_per_day > 0
        assert 0 <= performance.win_rate <= 1
        
        print("✅ Tester test passed")
        return True
    except Exception as e:
        print(f"❌ Tester test failed: {e}")
        return False

def test_composite_scoring():
    """Test enhanced composite scoring."""
    print("🧪 Testing composite scoring...")
    
    try:
        from enhanced_retraining_system import RetrainingConfig, EnhancedModelTester
        
        config = RetrainingConfig()
        tester = EnhancedModelTester(config)
        
        # Test with perfect metrics
        perfect_score = tester.calculate_enhanced_composite_score(
            trades_per_day=8.0,
            win_rate=0.95,
            profit_factor=4.0,
            max_drawdown=0.05,
            net_profit=5000,
            sharpe_ratio=3.0,
            confidence_score=0.95
        )
        
        # Should be close to 1.0 for perfect metrics
        assert 0.9 <= perfect_score <= 1.0
        
        # Test with poor metrics
        poor_score = tester.calculate_enhanced_composite_score(
            trades_per_day=1.0,
            win_rate=0.50,
            profit_factor=1.0,
            max_drawdown=0.30,
            net_profit=100,
            sharpe_ratio=0.5,
            confidence_score=0.50
        )
        
        # Should be much lower
        assert poor_score < 0.5
        
        print("✅ Composite scoring test passed")
        return True
    except Exception as e:
        print(f"❌ Composite scoring test failed: {e}")
        return False

def test_full_system():
    """Test the complete retraining system (minimal version)."""
    print("🧪 Testing full system (minimal)...")
    
    try:
        from enhanced_retraining_system import RetrainingConfig, EnhancedRetrainingSystem
        
        # Create minimal config for testing
        config = RetrainingConfig(
            training_days=5,  # Reduced for testing
            testing_days=3,   # Reduced for testing
            target_composite_score=0.80,  # Lower target for testing
            model_variants=['balanced'],  # Single variant for testing
            epochs_per_variant=3  # Minimal epochs for testing
        )
        
        system = EnhancedRetrainingSystem(config)
        
        # This would normally run a full cycle, but we'll just test initialization
        assert system.config.training_days == 5
        assert system.config.testing_days == 3
        assert system.trainer is not None
        assert system.tester is not None
        
        print("✅ Full system test passed")
        return True
    except Exception as e:
        print(f"❌ Full system test failed: {e}")
        return False

def run_all_tests():
    """Run all tests."""
    print("🚀 Enhanced Retraining System - Test Suite")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_config,
        test_model_creation,
        test_trainer,
        test_tester,
        test_composite_scoring,
        test_full_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced retraining system is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = run_all_tests()
    sys.exit(exit_code)
