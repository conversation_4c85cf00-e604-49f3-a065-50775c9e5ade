#!/usr/bin/env python3
"""
Verification script to test if webapp updates are working correctly.
This script will start the server and test the actual responses.
"""

import requests
import time
import json
import subprocess
import sys
from threading import Thread
import signal
import os

def start_server():
    """Start the Flask server in a subprocess."""
    try:
        print("🚀 Starting Flask server...")
        process = subprocess.Popen([
            sys.executable, "-c", 
            "from live_trading_web_app import app; app.run(host='127.0.0.1', port=5001, debug=False)"
        ], cwd=".", stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        time.sleep(5)
        return process
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def test_api_endpoints():
    """Test API endpoints to verify Conservative Elite data."""
    base_url = "http://localhost:5001"
    
    tests = [
        ("/api/trading_status", "Trading Status"),
        ("/api/model_status", "Model Status"),
        ("/api/risk_info", "Risk Info")
    ]
    
    results = {}
    
    for endpoint, name in tests:
        try:
            print(f"🧪 Testing {name} ({endpoint})...")
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                results[endpoint] = {
                    "status": "✅ SUCCESS",
                    "data": data
                }
                
                # Check for Conservative Elite indicators
                if "model_info" in data:
                    model_type = data["model_info"].get("model_type", "")
                    composite_score = data["model_info"].get("composite_score", 0)
                    
                    if "Conservative Elite" in model_type:
                        print(f"   ✅ Conservative Elite found in model_type")
                    else:
                        print(f"   ❌ Conservative Elite NOT found in model_type: {model_type}")
                    
                    if composite_score == 93.2:
                        print(f"   ✅ Composite score is 93.2%")
                    else:
                        print(f"   ❌ Composite score is {composite_score}%, expected 93.2%")
                
            else:
                results[endpoint] = {
                    "status": f"❌ FAILED ({response.status_code})",
                    "error": response.text
                }
                print(f"   ❌ Failed with status {response.status_code}")
                
        except Exception as e:
            results[endpoint] = {
                "status": "❌ ERROR",
                "error": str(e)
            }
            print(f"   ❌ Error: {e}")
    
    return results

def test_html_content():
    """Test HTML content for Conservative Elite branding."""
    try:
        print("🧪 Testing HTML content...")
        response = requests.get("http://localhost:5001/conservative_elite", timeout=10)
        
        if response.status_code == 200:
            html = response.text
            
            checks = [
                ("Conservative Elite Trading System", "Page title"),
                ("Conservative Elite System", "Header subtitle"),
                ("93.2%", "Performance score"),
                ("🔒", "Lock indicator"),
                ("Conservative Elite Model", "Model badge")
            ]
            
            results = {}
            for text, description in checks:
                if text in html:
                    print(f"   ✅ {description} found")
                    results[description] = "✅ FOUND"
                else:
                    print(f"   ❌ {description} NOT found")
                    results[description] = "❌ NOT FOUND"
            
            return {"status": "✅ SUCCESS", "checks": results}
        else:
            return {"status": f"❌ FAILED ({response.status_code})", "error": response.text}
            
    except Exception as e:
        return {"status": "❌ ERROR", "error": str(e)}

def main():
    """Main verification function."""
    print("🔍 CONSERVATIVE ELITE WEBAPP VERIFICATION")
    print("=" * 50)
    
    # Start server
    server_process = start_server()
    if not server_process:
        print("❌ Could not start server")
        return False
    
    try:
        # Wait for server to be ready
        print("⏳ Waiting for server to be ready...")
        time.sleep(3)
        
        # Test server is responding
        try:
            response = requests.get("http://localhost:5001/api/trading_status", timeout=5)
            print("✅ Server is responding")
        except:
            print("❌ Server is not responding")
            return False
        
        # Run tests
        print("\n📊 TESTING API ENDPOINTS")
        print("-" * 30)
        api_results = test_api_endpoints()
        
        print("\n🌐 TESTING HTML CONTENT")
        print("-" * 30)
        html_results = test_html_content()
        
        # Summary
        print("\n📋 VERIFICATION SUMMARY")
        print("=" * 30)
        
        api_success = all("SUCCESS" in result["status"] for result in api_results.values())
        html_success = "SUCCESS" in html_results["status"]
        
        if api_success and html_success:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Conservative Elite system is properly integrated")
            print("✅ 93.2% composite score is displayed")
            print("✅ Webapp updates are working correctly")
            return True
        else:
            print("❌ SOME TESTS FAILED")
            if not api_success:
                print("❌ API endpoints have issues")
            if not html_success:
                print("❌ HTML content has issues")
            return False
            
    finally:
        # Clean up
        if server_process:
            print("\n🛑 Stopping server...")
            server_process.terminate()
            server_process.wait()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
