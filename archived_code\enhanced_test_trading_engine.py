"""
Enhanced Test Trading Engine
Provides test trading functionality that matches live trading logic exactly
"""

import os
import sys
import logging
import asyncio
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
import time
from dataclasses import dataclass, asdict
from enum import Enum

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import core components
from grid_trading_core import GridLevelCalculator, GridAction, GridLevel
from grid_feature_engineering import GridFeatureEngineering
from grid_composite_metrics import GridCompositeMetrics
from binance_data_collector import BinanceDataCollector


class TradingMode(Enum):
    """Trading mode enumeration"""
    LIVE = "LIVE"
    TEST = "TEST"
    SIMULATION = "SIMULATION"


@dataclass
class TestTrade:
    """Test trade record that matches live trade structure exactly"""
    trade_id: str
    symbol: str
    side: str  # BUY/SELL
    action: str  # GridAction name
    entry_price: float
    exit_price: float
    position_size: float
    quantity: float
    risk_amount: float
    target_profit: float
    entry_time: str
    exit_time: Optional[str] = None
    status: str = "OPEN"  # OPEN, CLOSED, CANCELLED
    profit_loss: float = 0.0
    exit_reason: str = ""
    grid_level_id: str = ""
    grid_price: float = 0.0
    stop_loss: float = 0.0
    commission_entry: float = 0.0
    commission_exit: float = 0.0
    commission_total: float = 0.0
    balance_before: float = 0.0
    balance_after: float = 0.0
    mode: str = "TEST"


class TestTradeExecutor:
    """Test trade executor that simulates live trading exactly"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('TestTradeExecutor')
        self.commission_rate = 0.001  # 0.1% commission rate
        
    def calculate_commission(self, trade_value: float) -> Dict[str, float]:
        """Calculate commission exactly as live trading"""
        entry_commission = trade_value * self.commission_rate
        exit_commission = trade_value * self.commission_rate
        total_commission = entry_commission + exit_commission
        
        return {
            'entry_commission': entry_commission,
            'exit_commission': exit_commission,
            'total_commission': total_commission
        }
    
    def calculate_position_size(self, risk_amount: float, entry_price: float, 
                              stop_loss_price: float) -> Tuple[float, float]:
        """Calculate position size exactly as live trading"""
        price_diff = abs(entry_price - stop_loss_price)
        position_size = risk_amount / price_diff if price_diff > 0 else 0
        quantity = position_size / entry_price if entry_price > 0 else 0
        
        return position_size, quantity
    
    async def execute_test_trade(self, action: GridAction, grid_level: GridLevel, 
                               current_price: float, current_balance: float) -> TestTrade:
        """Execute a test trade with identical logic to live trading"""
        
        # Calculate exit levels exactly as live trading
        if action == GridAction.BUY:
            entry_price = current_price
            stop_loss = entry_price * (1 - self.config.GRID_SPACING)
            exit_price = entry_price * (1 + self.config.GRID_SPACING)
            side = "BUY"
        elif action == GridAction.SELL:
            entry_price = current_price
            stop_loss = entry_price * (1 + self.config.GRID_SPACING)
            exit_price = entry_price * (1 - self.config.GRID_SPACING)
            side = "SELL"
        else:
            raise ValueError(f"Invalid action for trade execution: {action}")
        
        # Calculate position sizing
        position_size, quantity = self.calculate_position_size(
            self.config.FIXED_RISK_AMOUNT, entry_price, stop_loss
        )
        
        # Calculate commissions
        trade_value = position_size
        commissions = self.calculate_commission(trade_value)
        
        # Create test trade record
        trade_id = f"TEST_{grid_level.level_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        test_trade = TestTrade(
            trade_id=trade_id,
            symbol="BTCUSDT",
            side=side,
            action=action.name,
            entry_price=entry_price,
            exit_price=exit_price,
            position_size=position_size,
            quantity=quantity,
            risk_amount=self.config.FIXED_RISK_AMOUNT,
            target_profit=self.config.TARGET_PROFIT,
            entry_time=datetime.now().isoformat(),
            status="OPEN",
            grid_level_id=grid_level.level_id,
            grid_price=grid_level.price,
            stop_loss=stop_loss,
            commission_entry=commissions['entry_commission'],
            commission_exit=commissions['exit_commission'],
            commission_total=commissions['total_commission'],
            balance_before=current_balance,
            balance_after=current_balance,  # Will be updated on exit
            mode="TEST"
        )
        
        self.logger.info(f"🧪 TEST TRADE EXECUTED: {action.name} @ ${entry_price:.2f}")
        return test_trade


class TestTradingEngine:
    """Enhanced test trading engine that matches live trading exactly"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('TestTradingEngine')
        
        # Initialize components (same as live trading)
        self.data_collector = BinanceDataCollector(config)
        self.grid_calculator = GridLevelCalculator(config)
        self.feature_engineer = GridFeatureEngineering(config)
        self.metrics_calculator = GridCompositeMetrics(config)
        self.test_executor = TestTradeExecutor(config)
        
        # Test trading state
        self.mode = TradingMode.TEST
        self.is_running = False
        self.start_time = None
        self.current_balance = config.INITIAL_CAPITAL
        self.open_trades: Dict[str, TestTrade] = {}
        self.closed_trades: List[TestTrade] = []
        self.grid_levels: Dict[str, GridLevel] = {}
        
        # Performance tracking (identical to live)
        self.session_data = {
            'trades': [],
            'equity_history': [config.INITIAL_CAPITAL],
            'drawdown_history': [0.0],
            'price_history': [],
            'grid_levels': {},
            'performance_metrics': {},
            'timestamps': [datetime.now()],
            'btc_prices': [],
            'daily_pnl': [],
            'mode': 'TEST'
        }
        
    async def start_test_trading(self) -> bool:
        """Start test trading with identical initialization to live trading"""
        if self.is_running:
            self.logger.warning("Test trading already running")
            return False
            
        try:
            self.is_running = True
            self.start_time = datetime.now()
            
            # Reset session data (identical to live)
            self.session_data = {
                'trades': [],
                'equity_history': [self.config.INITIAL_CAPITAL],
                'drawdown_history': [0.0],
                'price_history': [],
                'grid_levels': {},
                'performance_metrics': {},
                'timestamps': [datetime.now()],
                'btc_prices': [],
                'daily_pnl': [],
                'mode': 'TEST'
            }
            
            # Test data connection (same validation as live)
            connection_ok = await self.data_collector.test_connection()
            if not connection_ok:
                self.logger.warning("⚠️ Data connection failed - using simulated data")
            
            # Initialize grid levels
            current_price = await self.data_collector.get_real_time_price('BTC/USDT')
            if current_price:
                self.grid_levels = self.grid_calculator.calculate_grid_levels(current_price)
                self.logger.info(f"🧪 Test trading initialized with {len(self.grid_levels)} grid levels")
            
            self.logger.info("🧪 TEST TRADING STARTED - All logic matches live trading")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start test trading: {e}")
            self.is_running = False
            return False
    
    async def stop_test_trading(self) -> Dict:
        """Stop test trading and return session summary"""
        if not self.is_running:
            return {'success': False, 'error': 'Test trading not running'}
        
        self.is_running = False
        
        # Close all open trades (identical to live trading)
        for trade_id, trade in self.open_trades.items():
            await self._close_test_trade(trade, "MANUAL_STOP")
        
        # Calculate final metrics
        final_metrics = self.metrics_calculator.calculate_composite_metrics(
            self.closed_trades, self.current_balance, self.config.INITIAL_CAPITAL
        )
        
        session_summary = {
            'success': True,
            'mode': 'TEST',
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': datetime.now().isoformat(),
            'duration_minutes': (datetime.now() - self.start_time).total_seconds() / 60 if self.start_time else 0,
            'total_trades': len(self.closed_trades),
            'final_balance': self.current_balance,
            'total_pnl': self.current_balance - self.config.INITIAL_CAPITAL,
            'final_metrics': final_metrics,
            'session_data': self.session_data
        }
        
        self.logger.info(f"🧪 TEST TRADING STOPPED - Final Balance: ${self.current_balance:.2f}")
        return session_summary

    async def _close_test_trade(self, trade: TestTrade, exit_reason: str) -> None:
        """Close a test trade with identical logic to live trading"""
        try:
            # Calculate profit/loss exactly as live trading
            if trade.side == "BUY":
                profit_loss = (trade.exit_price - trade.entry_price) * trade.quantity
            else:  # SELL
                profit_loss = (trade.entry_price - trade.exit_price) * trade.quantity

            # Subtract commissions
            profit_loss -= trade.commission_total

            # Update trade record
            trade.exit_time = datetime.now().isoformat()
            trade.status = "CLOSED"
            trade.profit_loss = profit_loss
            trade.exit_reason = exit_reason
            trade.balance_after = self.current_balance + profit_loss

            # Update balance
            self.current_balance += profit_loss

            # Move to closed trades
            if trade.trade_id in self.open_trades:
                del self.open_trades[trade.trade_id]
            self.closed_trades.append(trade)

            # Update session data
            self.session_data['trades'].append(asdict(trade))
            self.session_data['equity_history'].append(self.current_balance)

            self.logger.info(f"🧪 TEST TRADE CLOSED: {trade.trade_id} P&L: ${profit_loss:.2f}")

        except Exception as e:
            self.logger.error(f"Error closing test trade {trade.trade_id}: {e}")

    async def process_test_trading_cycle(self) -> Dict:
        """Process one test trading cycle - identical to live trading logic"""
        if not self.is_running:
            return {'success': False, 'error': 'Test trading not running'}

        try:
            # Get current market data (same as live)
            current_price = await self.data_collector.get_real_time_price('BTC/USDT')
            if not current_price:
                return {'success': False, 'error': 'Could not get current price'}

            # Update price history
            self.session_data['price_history'].append(current_price)
            self.session_data['btc_prices'].append(current_price)
            self.session_data['timestamps'].append(datetime.now())

            # Check for grid level touches (identical logic)
            touched_levels = self._check_grid_touches(current_price)
            executed_trades = []

            for level in touched_levels:
                # Get trading decision (same ML logic as live)
                action = await self._get_test_trading_decision(level, current_price)

                if action != GridAction.HOLD and len(self.open_trades) < 15:  # Max concurrent trades
                    # Execute test trade
                    test_trade = await self.test_executor.execute_test_trade(
                        action, level, current_price, self.current_balance
                    )

                    self.open_trades[test_trade.trade_id] = test_trade
                    executed_trades.append(test_trade)

            # Check for trade exits (identical logic)
            await self._check_test_trade_exits(current_price)

            # Update performance metrics
            if self.closed_trades:
                current_metrics = self.metrics_calculator.calculate_composite_metrics(
                    self.closed_trades, self.current_balance, self.config.INITIAL_CAPITAL
                )
                self.session_data['performance_metrics'] = current_metrics

            return {
                'success': True,
                'current_price': current_price,
                'executed_trades': len(executed_trades),
                'open_trades': len(self.open_trades),
                'closed_trades': len(self.closed_trades),
                'current_balance': self.current_balance,
                'mode': 'TEST'
            }

        except Exception as e:
            self.logger.error(f"Error in test trading cycle: {e}")
            return {'success': False, 'error': str(e)}

    def _check_grid_touches(self, current_price: float) -> List[GridLevel]:
        """Check for grid level touches - identical to live trading"""
        touched_levels = []

        for level_id, level in self.grid_levels.items():
            # Check if price touched this grid level (same tolerance as live)
            price_diff = abs(current_price - level.price) / level.price
            if price_diff < 0.001:  # 0.1% tolerance
                touched_levels.append(level)

        return touched_levels

    async def _get_test_trading_decision(self, level: GridLevel, current_price: float) -> GridAction:
        """Get trading decision using same ML logic as live trading"""
        try:
            # Use same feature engineering as live trading
            market_data = {
                'price': current_price,
                'timestamp': datetime.now(),
                'grid_level': level.price
            }

            features = self.feature_engineer.prepare_features(market_data)

            # Simplified decision logic (same as live trading fallback)
            if current_price < level.price:
                return GridAction.BUY
            elif current_price > level.price:
                return GridAction.SELL
            else:
                return GridAction.HOLD

        except Exception as e:
            self.logger.error(f"Error getting test trading decision: {e}")
            return GridAction.HOLD

    async def _check_test_trade_exits(self, current_price: float) -> None:
        """Check for trade exits - identical logic to live trading"""
        trades_to_close = []

        for trade_id, trade in self.open_trades.items():
            should_close = False
            exit_reason = ""

            if trade.side == "BUY":
                # Check take profit
                if current_price >= trade.exit_price:
                    should_close = True
                    exit_reason = "TAKE_PROFIT"
                # Check stop loss
                elif current_price <= trade.stop_loss:
                    should_close = True
                    exit_reason = "STOP_LOSS"

            elif trade.side == "SELL":
                # Check take profit
                if current_price <= trade.exit_price:
                    should_close = True
                    exit_reason = "TAKE_PROFIT"
                # Check stop loss
                elif current_price >= trade.stop_loss:
                    should_close = True
                    exit_reason = "STOP_LOSS"

            if should_close:
                trades_to_close.append((trade, exit_reason))

        # Close trades
        for trade, exit_reason in trades_to_close:
            await self._close_test_trade(trade, exit_reason)

    def get_test_status(self) -> Dict:
        """Get current test trading status - identical format to live trading"""
        return {
            'mode': 'TEST',
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'current_balance': self.current_balance,
            'initial_capital': self.config.INITIAL_CAPITAL,
            'total_pnl': self.current_balance - self.config.INITIAL_CAPITAL,
            'open_trades': len(self.open_trades),
            'closed_trades': len(self.closed_trades),
            'grid_levels': len(self.grid_levels),
            'session_data': self.session_data
        }

    def export_test_results(self) -> Dict:
        """Export test results in same format as live trading"""
        return {
            'mode': 'TEST',
            'export_time': datetime.now().isoformat(),
            'session_summary': self.get_test_status(),
            'all_trades': [asdict(trade) for trade in self.closed_trades],
            'open_trades': [asdict(trade) for trade in self.open_trades.values()],
            'performance_metrics': self.session_data.get('performance_metrics', {}),
            'equity_curve': {
                'timestamps': self.session_data.get('timestamps', []),
                'equity_values': self.session_data.get('equity_history', []),
                'price_values': self.session_data.get('btc_prices', [])
            }
        }
