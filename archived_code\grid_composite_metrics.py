"""
Grid Trading Composite Metrics
Implements the exact 8-metric composite score formula from specification
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import math

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

class GridCompositeMetrics:
    """Calculate composite score using exact specification formula"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('GridCompositeMetrics')
        
        # Exact weights from specification
        self.weights = {
            'win_rate': 0.22,           # 22% - Primary success metric
            'equity': 0.20,             # 20% - Overall profitability  
            'sortino': 0.18,            # 18% - Risk-adjusted returns
            'calmar': 0.15,             # 15% - Return vs max drawdown
            'profit_factor': 0.10,      # 10% - Gross profit vs gross loss
            'max_drawdown': 0.08,       # 8% - Risk management (inverted)
            'risk_of_ruin': 0.05,       # 5% - Survival probability (inverted)
            'trade_freq': 0.02          # 2% - Activity level
        }
        
    def calculate_composite_score(self, trade_history: List[Dict], 
                                initial_capital: float = None) -> Dict[str, float]:
        """Calculate complete composite score from trade history"""
        
        if not trade_history:
            return {'composite_score': 0.0, 'error': 'No trade history'}
        
        initial_capital = initial_capital or self.config.INITIAL_CAPITAL
        
        try:
            # Calculate all 8 metrics
            metrics = {}
            
            # 1. Win Rate (22%)
            metrics['win_rate'] = self._calculate_win_rate(trade_history)
            
            # 2. Equity (20%) 
            metrics['equity'] = self._calculate_equity_return(trade_history, initial_capital)
            
            # 3. Sortino Ratio (18%)
            metrics['sortino'] = self._calculate_sortino_ratio(trade_history)
            
            # 4. Calmar Ratio (15%)
            metrics['calmar'] = self._calculate_calmar_ratio(trade_history, initial_capital)
            
            # 5. Profit Factor (10%)
            metrics['profit_factor'] = self._calculate_profit_factor(trade_history)
            
            # 6. Max Drawdown (8%) - inverted
            metrics['max_drawdown'] = self._calculate_max_drawdown(trade_history, initial_capital)
            
            # 7. Risk of Ruin (5%) - inverted
            metrics['risk_of_ruin'] = self._calculate_risk_of_ruin(trade_history, initial_capital)
            
            # 8. Trade Frequency (2%)
            metrics['trade_freq'] = self._calculate_trade_frequency(trade_history)
            
            # Calculate composite score
            composite_score = self._calculate_weighted_composite(metrics)
            
            # Add composite score to metrics
            metrics['composite_score'] = composite_score
            
            self.logger.info(f"Composite Score: {composite_score:.4f}")
            self.logger.info(f"Metrics: {metrics}")
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to calculate composite score: {e}")
            return {'composite_score': 0.0, 'error': str(e)}
    
    def _calculate_win_rate(self, trade_history: List[Dict]) -> float:
        """Calculate win rate (% of profitable trades)"""
        if not trade_history:
            return 0.0
        
        winning_trades = sum(1 for trade in trade_history 
                           if trade.get('profit_loss', 0) > 0)
        
        win_rate = winning_trades / len(trade_history)
        self.logger.debug(f"Win Rate: {win_rate:.3f} ({winning_trades}/{len(trade_history)})")
        return win_rate
    
    def _calculate_equity_return(self, trade_history: List[Dict], initial_capital: float) -> float:
        """Calculate total equity return"""
        if not trade_history:
            return 0.0
        
        total_pnl = sum(trade.get('profit_loss', 0) for trade in trade_history)
        equity_return = total_pnl / initial_capital
        
        self.logger.debug(f"Equity Return: {equity_return:.3f} (${total_pnl:.2f})")
        return equity_return
    
    def _calculate_sortino_ratio(self, trade_history: List[Dict]) -> float:
        """Calculate Sortino ratio (return per unit of downside risk)"""
        if not trade_history:
            return 0.0
        
        returns = [trade.get('profit_loss', 0) for trade in trade_history]
        
        if not returns:
            return 0.0
        
        # Calculate average return
        avg_return = np.mean(returns)
        
        # Calculate downside deviation (only negative returns)
        negative_returns = [r for r in returns if r < 0]
        
        if not negative_returns:
            return 2.0  # High Sortino if no negative returns
        
        downside_deviation = np.std(negative_returns)
        
        if downside_deviation == 0:
            return 2.0
        
        sortino_ratio = avg_return / downside_deviation
        
        # Normalize to 0-1 scale (assume good Sortino > 1.5)
        normalized_sortino = min(abs(sortino_ratio) / 1.5, 1.0)
        
        self.logger.debug(f"Sortino Ratio: {normalized_sortino:.3f} (raw: {sortino_ratio:.3f})")
        return normalized_sortino
    
    def _calculate_calmar_ratio(self, trade_history: List[Dict], initial_capital: float) -> float:
        """Calculate Calmar ratio (annual return / max drawdown)"""
        if not trade_history:
            return 0.0
        
        # Calculate annual return
        total_pnl = sum(trade.get('profit_loss', 0) for trade in trade_history)
        annual_return = total_pnl / initial_capital
        
        # Calculate max drawdown
        max_dd = self._calculate_max_drawdown_raw(trade_history, initial_capital)
        
        if max_dd == 0:
            return 2.0  # High Calmar if no drawdown
        
        calmar_ratio = annual_return / max_dd
        
        # Normalize to 0-1 scale (assume good Calmar > 2.0)
        normalized_calmar = min(abs(calmar_ratio) / 2.0, 1.0)
        
        self.logger.debug(f"Calmar Ratio: {normalized_calmar:.3f} (raw: {calmar_ratio:.3f})")
        return normalized_calmar
    
    def _calculate_profit_factor(self, trade_history: List[Dict]) -> float:
        """Calculate profit factor (gross profit / gross loss)"""
        if not trade_history:
            return 0.0
        
        gross_profit = sum(trade.get('profit_loss', 0) for trade in trade_history 
                          if trade.get('profit_loss', 0) > 0)
        gross_loss = abs(sum(trade.get('profit_loss', 0) for trade in trade_history 
                            if trade.get('profit_loss', 0) < 0))
        
        if gross_loss == 0:
            return 1.0 if gross_profit > 0 else 0.0
        
        profit_factor = gross_profit / gross_loss
        
        # Normalize to 0-1 scale (assume good profit factor > 2.0)
        normalized_pf = min(profit_factor / 2.0, 1.0)
        
        self.logger.debug(f"Profit Factor: {normalized_pf:.3f} (raw: {profit_factor:.3f})")
        return normalized_pf
    
    def _calculate_max_drawdown(self, trade_history: List[Dict], initial_capital: float) -> float:
        """Calculate max drawdown (inverted for composite score)"""
        max_dd = self._calculate_max_drawdown_raw(trade_history, initial_capital)
        
        # Invert for composite score (lower drawdown = higher score)
        inverted_dd = max(0, 1 - (max_dd / 0.15))  # Assume 15% max acceptable drawdown
        
        self.logger.debug(f"Max Drawdown: {inverted_dd:.3f} (raw: {max_dd:.3f})")
        return inverted_dd
    
    def _calculate_max_drawdown_raw(self, trade_history: List[Dict], initial_capital: float) -> float:
        """Calculate raw maximum drawdown"""
        if not trade_history:
            return 0.0
        
        # Calculate running equity curve
        equity_curve = [initial_capital]
        running_balance = initial_capital
        
        for trade in trade_history:
            running_balance += trade.get('profit_loss', 0)
            equity_curve.append(running_balance)
        
        # Find maximum drawdown
        peak = equity_curve[0]
        max_drawdown = 0.0
        
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            
            drawdown = (peak - equity) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        return max_drawdown
    
    def _calculate_risk_of_ruin(self, trade_history: List[Dict], initial_capital: float) -> float:
        """Calculate risk of ruin (inverted for composite score)"""
        if not trade_history:
            return 0.0
        
        # Simple risk of ruin calculation based on win rate and average win/loss
        win_rate = self._calculate_win_rate(trade_history)
        
        if win_rate >= 0.6:
            risk_of_ruin = 0.02  # 2% risk if good win rate
        elif win_rate >= 0.5:
            risk_of_ruin = 0.05  # 5% risk if moderate win rate
        else:
            risk_of_ruin = 0.15  # 15% risk if poor win rate
        
        # Invert for composite score (lower risk = higher score)
        inverted_ror = max(0, 1 - (risk_of_ruin / 0.15))
        
        self.logger.debug(f"Risk of Ruin: {inverted_ror:.3f} (raw: {risk_of_ruin:.3f})")
        return inverted_ror
    
    def _calculate_trade_frequency(self, trade_history: List[Dict]) -> float:
        """Calculate trade frequency score"""
        if not trade_history:
            return 0.0
        
        # Calculate trades per day (assuming 1 day = 24 hours of data)
        if len(trade_history) < 2:
            return 0.5  # Neutral score for insufficient data
        
        # Estimate trading period from first and last trade
        first_trade = min(trade_history, key=lambda x: x.get('timestamp', ''))
        last_trade = max(trade_history, key=lambda x: x.get('timestamp', ''))
        
        try:
            first_time = datetime.fromisoformat(first_trade['timestamp'].replace('Z', '+00:00'))
            last_time = datetime.fromisoformat(last_trade['timestamp'].replace('Z', '+00:00'))
            
            trading_days = max(1, (last_time - first_time).days)
            trades_per_day = len(trade_history) / trading_days
            
            # Normalize to 0-1 scale (assume optimal 1-3 trades per day)
            if 1 <= trades_per_day <= 3:
                frequency_score = 1.0
            elif trades_per_day < 1:
                frequency_score = trades_per_day
            else:
                frequency_score = max(0, 1 - ((trades_per_day - 3) / 10))
            
        except Exception:
            frequency_score = 0.5  # Neutral score if timestamp parsing fails
        
        self.logger.debug(f"Trade Frequency: {frequency_score:.3f}")
        return frequency_score
    
    def _calculate_weighted_composite(self, metrics: Dict[str, float]) -> float:
        """Calculate final weighted composite score"""
        
        composite_score = 0.0
        
        for metric, weight in self.weights.items():
            if metric in metrics:
                contribution = metrics[metric] * weight
                composite_score += contribution
                self.logger.debug(f"{metric}: {metrics[metric]:.3f} × {weight:.2f} = {contribution:.4f}")
        
        # Ensure score is between 0 and 1
        composite_score = max(0.0, min(1.0, composite_score))
        
        return composite_score
    
    def meets_threshold(self, composite_score: float) -> bool:
        """Check if composite score meets deployment threshold"""
        return composite_score >= self.config.COMPOSITE_SCORE_THRESHOLD
    
    def get_performance_summary(self, metrics: Dict[str, float]) -> str:
        """Get human-readable performance summary"""
        
        if 'error' in metrics:
            return f"Error calculating metrics: {metrics['error']}"
        
        summary = f"""
Grid Trading Performance Summary:
================================
Composite Score: {metrics.get('composite_score', 0):.1%}
Threshold Met: {'✅ YES' if self.meets_threshold(metrics.get('composite_score', 0)) else '❌ NO'}

Individual Metrics:
- Win Rate: {metrics.get('win_rate', 0):.1%} (22% weight)
- Equity Return: {metrics.get('equity', 0):.1%} (20% weight)  
- Sortino Ratio: {metrics.get('sortino', 0):.3f} (18% weight)
- Calmar Ratio: {metrics.get('calmar', 0):.3f} (15% weight)
- Profit Factor: {metrics.get('profit_factor', 0):.3f} (10% weight)
- Max Drawdown: {metrics.get('max_drawdown', 0):.3f} (8% weight)
- Risk of Ruin: {metrics.get('risk_of_ruin', 0):.3f} (5% weight)
- Trade Frequency: {metrics.get('trade_freq', 0):.3f} (2% weight)
"""
        
        return summary
