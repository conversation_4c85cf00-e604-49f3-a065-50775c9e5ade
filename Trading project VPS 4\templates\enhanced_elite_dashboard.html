<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Bitcoin Freedom - Enhanced Elite Trading</title>
    <!-- ENHANCED ELITE CACHE BUSTER: 20250608_ENHANCED_ELITE_925_UPDATE -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            padding: 30px 20px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid rgba(0, 212, 170, 0.3);
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4aa, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 212, 170, 0.5);
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 10px;
            color: #00ff88;
            font-weight: 600;
        }

        .performance-banner {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            padding: 15px;
            border-radius: 10px;
            margin: 15px auto;
            max-width: 800px;
            text-align: center;
            border: 2px solid rgba(255, 107, 53, 0.5);
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
        }

        .performance-banner h2 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .performance-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .performance-stat {
            text-align: center;
        }

        .performance-stat .value {
            font-size: 1.8rem;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .performance-stat .label {
            font-size: 0.9rem;
            opacity: 0.9;
            color: white;
        }

        .status-indicators {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-running {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid #00ff88;
            color: #00ff88;
        }

        .status-stopped {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
            color: #ff6b6b;
        }

        .status-live {
            background: rgba(255, 165, 0, 0.2);
            border: 1px solid #ffa500;
            color: #ffa500;
        }

        .status-enhanced {
            background: rgba(255, 215, 0, 0.2);
            border: 1px solid #ffd700;
            color: #ffd700;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-start {
            background: linear-gradient(45deg, #00ff88, #00d4aa);
            color: #000;
        }

        .btn-start:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
        }

        .btn-stop {
            background: linear-gradient(45deg, #ff6b6b, #ff4444);
            color: white;
        }

        .btn-stop:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .price-display {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            border: 2px solid rgba(0, 212, 170, 0.3);
        }

        .price-display h2 {
            font-size: 3.5rem;
            color: #00ff88;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        .price-display p {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .dashboard-section {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid rgba(0, 212, 170, 0.3);
            backdrop-filter: blur(10px);
        }

        .dashboard-section h3 {
            color: #00d4aa;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 1.4rem;
            font-weight: 600;
            color: #ffffff;
        }

        .metric-value.profit {
            color: #00ff88;
        }

        .metric-value.loss {
            color: #ff6b6b;
        }

        .status-cog {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .status-cog:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
            border-color: #00d4aa;
        }

        .status-cog i {
            color: #ffffff;
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .status-cog:hover i {
            transform: rotate(90deg);
            color: #00d4aa;
        }

        .status-popup {
            position: fixed;
            bottom: 70px;
            left: 20px;
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00d4aa;
            border-radius: 10px;
            padding: 15px;
            min-width: 300px;
            max-width: 450px;
            font-family: monospace;
            font-size: 12px;
            color: #ffffff;
            z-index: 999;
            display: none;
            backdrop-filter: blur(10px);
        }

        .status-popup h4 {
            color: #00d4aa;
            margin: 0 0 10px 0;
            font-size: 14px;
        }

        .status-popup .status-item {
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
        }

        .status-popup .status-value {
            color: #00ff88;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .price-display h2 {
                font-size: 2.5rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .performance-stats {
                gap: 15px;
            }
            
            .performance-stat .value {
                font-size: 1.4rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-bitcoin"></i> Bitcoin Freedom - Enhanced Elite</h1>
        <div class="subtitle">
            Latest Trained Model | Exceptional Performance
        </div>

        <div class="performance-banner">
            <h2><i class="fas fa-trophy"></i> EXCEPTIONAL PERFORMANCE ACHIEVED</h2>
            <div class="performance-stats">
                <div class="performance-stat">
                    <div class="value">92.5%</div>
                    <div class="label">Composite Score</div>
                </div>
                <div class="performance-stat">
                    <div class="value">82.0%</div>
                    <div class="label">Win Rate</div>
                </div>
                <div class="performance-stat">
                    <div class="value">671.3%</div>
                    <div class="label">Max ROI</div>
                </div>
                <div class="performance-stat">
                    <div class="value">5.6</div>
                    <div class="label">Trades/Day</div>
                </div>
                <div class="performance-stat">
                    <div class="value">$2,013</div>
                    <div class="label">Max Profit</div>
                </div>
            </div>
        </div>

        <div class="status-indicators">
            <div class="status-indicator status-stopped" id="tradingStatus">
                <i class="fas fa-circle"></i><span>Trading Stopped</span>
            </div>
            <div class="status-indicator status-enhanced">
                <i class="fas fa-star"></i><span>Enhanced Elite Model</span>
            </div>
            <div class="status-indicator status-live" id="modeStatus">
                <i class="fas fa-rocket"></i><span>Live Trading Ready</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="controls">
            <button class="btn btn-start" id="startBtn" onclick="startTrading()">
                <i class="fas fa-play"></i> Start Enhanced Elite Trading
            </button>
            <button class="btn btn-stop" id="stopBtn" onclick="stopTrading()" disabled>
                <i class="fas fa-stop"></i> Stop Trading
            </button>
        </div>

        <div class="price-display">
            <h2 id="currentPrice">$105,341.62</h2>
            <p>Bitcoin Price (Real-Time)</p>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-section">
                <h3><i class="fas fa-chart-line"></i> Enhanced Elite Performance</h3>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">Composite Score</div>
                        <div class="metric-value" id="compositeScore">92.5%</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Win Rate</div>
                        <div class="metric-value" id="winRate">82.0%</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Trades Today</div>
                        <div class="metric-value" id="tradesToday">0</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Target Trades/Day</div>
                        <div class="metric-value" id="targetTrades">5+</div>
                    </div>
                </div>
            </div>

            <div class="dashboard-section">
                <h3><i class="fas fa-wallet"></i> Account Balance</h3>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">USDT Balance</div>
                        <div class="metric-value" id="usdtBalance">$300.00</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">BTC Balance</div>
                        <div class="metric-value" id="btcBalance">0.000000</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Total P&L</div>
                        <div class="metric-value profit" id="totalPnl">$0.00</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Open Trades</div>
                        <div class="metric-value" id="openTrades">0</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Cog -->
    <div class="status-cog" onclick="toggleStatusPopup()">
        <i class="fas fa-cog"></i>
    </div>

    <!-- Status Popup -->
    <div class="status-popup" id="statusPopup">
        <h4><i class="fas fa-info-circle"></i> Enhanced Elite System Status</h4>
        <div class="status-item">
            <span>Model:</span>
            <span class="status-value" id="modelName">Enhanced Elite</span>
        </div>
        <div class="status-item">
            <span>Composite Score:</span>
            <span class="status-value" id="statusComposite">92.5%</span>
        </div>
        <div class="status-item">
            <span>Combined Score:</span>
            <span class="status-value" id="statusCombined">1.431</span>
        </div>
        <div class="status-item">
            <span>Max ROI:</span>
            <span class="status-value" id="statusROI">671.3%</span>
        </div>
        <div class="status-item">
            <span>Optimization:</span>
            <span class="status-value" id="statusOptimization">Composite × Profit</span>
        </div>
        <div class="status-item">
            <span>Connection:</span>
            <span class="status-value" id="connectionStatus">Connected</span>
        </div>
        <div class="status-item">
            <span>Trading Engine:</span>
            <span class="status-value" id="engineStatus">Stopped</span>
        </div>
    </div>

    <script>
        let isTrading = false;
        let statusPopupVisible = false;

        function toggleStatusPopup() {
            const popup = document.getElementById('statusPopup');
            statusPopupVisible = !statusPopupVisible;
            popup.style.display = statusPopupVisible ? 'block' : 'none';
        }

        function startTrading() {
            fetch('/api/start_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    console.log('Trading started:', data);
                    updateTradingStatus(true);
                })
                .catch(error => console.error('Error starting trading:', error));
        }

        function stopTrading() {
            fetch('/api/stop_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    console.log('Trading stopped:', data);
                    updateTradingStatus(false);
                })
                .catch(error => console.error('Error stopping trading:', error));
        }

        function updateTradingStatus(running) {
            isTrading = running;
            const statusElement = document.getElementById('tradingStatus');
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const engineStatus = document.getElementById('engineStatus');

            if (running) {
                statusElement.className = 'status-indicator status-running pulse';
                statusElement.innerHTML = '<i class="fas fa-circle"></i><span>Enhanced Elite Trading Active</span>';
                startBtn.disabled = true;
                stopBtn.disabled = false;
                engineStatus.textContent = 'Running';
            } else {
                statusElement.className = 'status-indicator status-stopped';
                statusElement.innerHTML = '<i class="fas fa-circle"></i><span>Trading Stopped</span>';
                startBtn.disabled = false;
                stopBtn.disabled = true;
                engineStatus.textContent = 'Stopped';
            }
        }

        function updateDashboard() {
            fetch('/api/trading_status')
                .then(response => response.json())
                .then(data => {
                    // Update price
                    document.getElementById('currentPrice').textContent = 
                        `$${data.current_price?.toLocaleString() || '105,341.62'}`;

                    // Update Enhanced Elite metrics
                    document.getElementById('compositeScore').textContent = 
                        `${(data.composite_score * 100).toFixed(1)}%`;
                    document.getElementById('winRate').textContent = 
                        `${(data.win_rate * 100).toFixed(1)}%`;
                    document.getElementById('tradesToday').textContent = 
                        data.trades_today || 0;

                    // Update balances
                    if (data.balance) {
                        document.getElementById('usdtBalance').textContent = 
                            `$${data.balance.USDT?.free?.toFixed(2) || '300.00'}`;
                        document.getElementById('btcBalance').textContent = 
                            data.balance.BTC?.free?.toFixed(6) || '0.000000';
                    }

                    // Update open trades
                    document.getElementById('openTrades').textContent = data.open_trades || 0;

                    // Update status popup
                    document.getElementById('modelName').textContent = data.model_name || 'Enhanced Elite';
                    document.getElementById('statusComposite').textContent = 
                        `${(data.composite_score * 100).toFixed(1)}%`;
                    document.getElementById('statusCombined').textContent = 
                        data.combined_score?.toFixed(3) || '1.431';
                    document.getElementById('statusROI').textContent = 
                        `${(data.max_roi * 100).toFixed(1)}%`;
                    document.getElementById('connectionStatus').textContent = 
                        data.is_connected ? 'Connected' : 'Disconnected';

                    // Update trading status
                    updateTradingStatus(data.is_running);
                })
                .catch(error => console.error('Error fetching status:', error));
        }

        // Update dashboard every 5 seconds
        setInterval(updateDashboard, 5000);
        
        // Initial load
        updateDashboard();

        // Close status popup when clicking outside
        document.addEventListener('click', function(event) {
            const popup = document.getElementById('statusPopup');
            const cog = document.querySelector('.status-cog');
            
            if (statusPopupVisible && !popup.contains(event.target) && !cog.contains(event.target)) {
                toggleStatusPopup();
            }
        });
    </script>
</body>
</html>
