<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Bitcoin Freedom - Live Trading</title>
    <!-- BITCOIN FREEDOM CACHE BUSTER: 20250608_BITCOIN_FREEDOM_UPDATE -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            padding: 30px 20px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid rgba(0, 212, 170, 0.3);
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4aa, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 212, 170, 0.5);
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 10px;
            color: #00ff88;
            font-weight: 600;
        }



        .status-indicators {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-running {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid #00ff88;
            color: #00ff88;
        }

        .status-stopped {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
            color: #ff6b6b;
        }

        .status-live {
            background: rgba(255, 165, 0, 0.2);
            border: 1px solid #ffa500;
            color: #ffa500;
        }



        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-start {
            background: linear-gradient(45deg, #00ff88, #00d4aa);
            color: #000;
        }

        .btn-start:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
        }

        .btn-stop {
            background: linear-gradient(45deg, #ff6b6b, #ff4444);
            color: white;
        }

        .btn-stop:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .price-display {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            border: 2px solid rgba(0, 212, 170, 0.3);
        }

        .price-display h2 {
            font-size: 3.5rem;
            color: #00ff88;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        .price-display p {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .dashboard-section {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid rgba(0, 212, 170, 0.3);
            backdrop-filter: blur(10px);
        }

        .dashboard-section h3 {
            color: #00d4aa;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 1.4rem;
            font-weight: 600;
            color: #ffffff;
        }

        .metric-value.profit {
            color: #00ff88;
        }

        .metric-value.loss {
            color: #ff6b6b;
        }

        .status-cog {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .status-cog:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
            border-color: #00d4aa;
        }

        .status-cog i {
            color: #ffffff;
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .status-cog:hover i {
            transform: rotate(90deg);
            color: #00d4aa;
        }

        .status-popup {
            position: fixed;
            bottom: 70px;
            left: 20px;
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00d4aa;
            border-radius: 10px;
            padding: 15px;
            min-width: 300px;
            max-width: 450px;
            font-family: monospace;
            font-size: 12px;
            color: #ffffff;
            z-index: 999;
            display: none;
            backdrop-filter: blur(10px);
        }

        .status-popup h4 {
            color: #00d4aa;
            margin: 0 0 10px 0;
            font-size: 14px;
        }

        .status-popup .status-item {
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
        }

        .status-popup .status-value {
            color: #00ff88;
        }

        .trades-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .trade-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            border-left: 4px solid #00d4aa;
        }

        .trade-item.profit {
            border-left-color: #00ff88;
        }

        .trade-item.loss {
            border-left-color: #ff6b6b;
        }

        .trade-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .trade-type {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .trade-type.buy {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
        }

        .trade-type.sell {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
        }

        .trade-details {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .trade-pnl {
            font-weight: bold;
            font-size: 1rem;
        }

        .trade-pnl.profit {
            color: #00ff88;
        }

        .trade-pnl.loss {
            color: #ff6b6b;
        }

        .no-trades {
            text-align: center;
            opacity: 0.6;
            padding: 20px;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .price-display h2 {
                font-size: 2.5rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .performance-stats {
                gap: 15px;
            }
            
            .performance-stat .value {
                font-size: 1.4rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fab fa-bitcoin"></i> Bitcoin Freedom</h1>
        <div class="subtitle">
            Advanced Trading System | Live Trading
        </div>

        <div class="status-indicators">
            <div class="status-indicator status-stopped" id="tradingStatus">
                <i class="fas fa-circle"></i><span>Trading Stopped</span>
            </div>
            <div class="status-indicator status-live" id="modeStatus">
                <i class="fas fa-rocket"></i><span>Live Trading Ready</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="controls">
            <button class="btn btn-start" id="startBtn" onclick="startTrading()">
                <i class="fas fa-play"></i> Start Trading
            </button>
            <button class="btn btn-stop" id="stopBtn" onclick="stopTrading()" disabled>
                <i class="fas fa-stop"></i> Stop Trading
            </button>
        </div>

        <div class="price-display">
            <h2 id="currentPrice">$105,341.62</h2>
            <p>Bitcoin Price (Real-Time)</p>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-section">
                <h3><i class="fas fa-chart-line"></i> Trading Performance</h3>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">Win Rate</div>
                        <div class="metric-value" id="winRate">82.0%</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Trades Today</div>
                        <div class="metric-value" id="tradesToday">0</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Total P&L</div>
                        <div class="metric-value profit" id="totalPnl">$0.00</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Open Trades</div>
                        <div class="metric-value" id="openTrades">0</div>
                    </div>
                </div>
            </div>

            <div class="dashboard-section">
                <h3><i class="fas fa-wallet"></i> Account Balance</h3>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">USDT Balance</div>
                        <div class="metric-value" id="usdtBalance">$300.00</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">BTC Balance</div>
                        <div class="metric-value" id="btcBalance">0.000000</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Available to Trade</div>
                        <div class="metric-value" id="availableBalance">$300.00</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Trade Risk</div>
                        <div class="metric-value" id="tradeRisk">$20.00</div>
                    </div>
                </div>
            </div>

            <div class="dashboard-section">
                <h3><i class="fas fa-history"></i> Recent Trades</h3>
                <div id="recentTrades" class="trades-list">
                    <div class="no-trades">No recent trades</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Cog -->
    <div class="status-cog" onclick="toggleStatusPopup()">
        <i class="fas fa-cog"></i>
    </div>

    <!-- Status Popup -->
    <div class="status-popup" id="statusPopup">
        <h4><i class="fas fa-info-circle"></i> Bitcoin Freedom System Status</h4>
        <div class="status-item">
            <span>Trading Mode:</span>
            <span class="status-value" id="tradingMode">Live Trading</span>
        </div>
        <div class="status-item">
            <span>System Status:</span>
            <span class="status-value" id="systemStatus">Active</span>
        </div>
        <div class="status-item">
            <span>Trading Engine:</span>
            <span class="status-value" id="engineStatus">Stopped</span>
        </div>
        <div class="status-item">
            <span>Binance Connection:</span>
            <span class="status-value" id="connectionStatus">Connected</span>
        </div>
        <div class="status-item">
            <span>Account Type:</span>
            <span class="status-value" id="accountType">Cross Margin</span>
        </div>
        <div class="status-item">
            <span>Win Rate:</span>
            <span class="status-value" id="statusWinRate">82.0%</span>
        </div>
        <div class="status-item">
            <span>Trade Risk:</span>
            <span class="status-value" id="statusTradeRisk">$20.00</span>
        </div>
    </div>

    <script>
        let isTrading = false;
        let statusPopupVisible = false;

        function toggleStatusPopup() {
            const popup = document.getElementById('statusPopup');
            statusPopupVisible = !statusPopupVisible;
            popup.style.display = statusPopupVisible ? 'block' : 'none';
        }

        function startTrading() {
            fetch('/api/start_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    console.log('Trading started:', data);
                    updateTradingStatus(true);
                })
                .catch(error => console.error('Error starting trading:', error));
        }

        function stopTrading() {
            fetch('/api/stop_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    console.log('Trading stopped:', data);
                    updateTradingStatus(false);
                })
                .catch(error => console.error('Error stopping trading:', error));
        }

        function updateTradingStatus(running) {
            isTrading = running;
            const statusElement = document.getElementById('tradingStatus');
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const engineStatus = document.getElementById('engineStatus');

            if (running) {
                statusElement.className = 'status-indicator status-running pulse';
                statusElement.innerHTML = '<i class="fas fa-circle"></i><span>Trading Active</span>';
                startBtn.disabled = true;
                stopBtn.disabled = false;
                engineStatus.textContent = 'Running';
            } else {
                statusElement.className = 'status-indicator status-stopped';
                statusElement.innerHTML = '<i class="fas fa-circle"></i><span>Trading Stopped</span>';
                startBtn.disabled = false;
                stopBtn.disabled = true;
                engineStatus.textContent = 'Stopped';
            }
        }

        function calculateTradeRisk(balance) {
            // Money management: For every $500 in account add $10 trade risk
            // Example: $1000 = $20, $1500 = $30, $2000 = $40
            const baseRisk = 10;
            const riskIncrement = 10;
            const balanceIncrement = 500;

            const additionalRisk = Math.floor(balance / balanceIncrement) * riskIncrement;
            return baseRisk + additionalRisk;
        }

        function updateRecentTrades(trades) {
            const tradesContainer = document.getElementById('recentTrades');

            if (!trades || trades.length === 0) {
                tradesContainer.innerHTML = '<div class="no-trades">No recent trades</div>';
                return;
            }

            const tradesHtml = trades.map(trade => {
                const isProfit = trade.pnl > 0;
                const tradeClass = isProfit ? 'profit' : 'loss';
                const typeClass = trade.side.toLowerCase();

                return `
                    <div class="trade-item ${tradeClass}">
                        <div class="trade-header">
                            <span class="trade-type ${typeClass}">${trade.side}</span>
                            <span class="trade-pnl ${tradeClass}">${isProfit ? '+' : ''}$${trade.pnl.toFixed(2)}</span>
                        </div>
                        <div class="trade-details">
                            Entry: $${trade.entry_price.toFixed(2)} | Exit: $${trade.exit_price.toFixed(2)} | Size: ${trade.quantity.toFixed(6)} BTC
                        </div>
                        <div class="trade-details">
                            ${new Date(trade.timestamp).toLocaleString()}
                        </div>
                    </div>
                `;
            }).join('');

            tradesContainer.innerHTML = tradesHtml;
        }

        function updateDashboard() {
            fetch('/api/trading_status')
                .then(response => response.json())
                .then(data => {
                    // Update price
                    document.getElementById('currentPrice').textContent =
                        `$${data.current_price?.toLocaleString() || '105,341.62'}`;

                    // Update trading metrics
                    document.getElementById('winRate').textContent =
                        `${(data.win_rate * 100).toFixed(1)}%`;
                    document.getElementById('tradesToday').textContent =
                        data.trades_today || 0;

                    // Update balances and calculate available balance
                    let availableBalance = 300.00;
                    if (data.balance) {
                        const usdtBalance = data.balance.USDT?.free || 300.00;
                        const btcBalance = data.balance.BTC?.free || 0.000000;
                        const btcValue = btcBalance * (data.current_price || 105341.62);
                        availableBalance = usdtBalance + btcValue;

                        document.getElementById('usdtBalance').textContent =
                            `$${usdtBalance.toFixed(2)}`;
                        document.getElementById('btcBalance').textContent =
                            btcBalance.toFixed(6);
                        document.getElementById('availableBalance').textContent =
                            `$${availableBalance.toFixed(2)}`;
                    }

                    // Calculate and update trade risk based on money management
                    const tradeRisk = calculateTradeRisk(availableBalance);
                    document.getElementById('tradeRisk').textContent = `$${tradeRisk.toFixed(2)}`;

                    // Update P&L
                    const totalPnl = availableBalance - 300.00;
                    const pnlElement = document.getElementById('totalPnl');
                    pnlElement.textContent = `${totalPnl >= 0 ? '+' : ''}$${totalPnl.toFixed(2)}`;
                    pnlElement.className = `metric-value ${totalPnl >= 0 ? 'profit' : 'loss'}`;

                    // Update open trades
                    document.getElementById('openTrades').textContent = data.open_trades || 0;

                    // Update recent trades
                    updateRecentTrades(data.recent_trades);

                    // Update status popup
                    document.getElementById('statusWinRate').textContent =
                        `${(data.win_rate * 100).toFixed(1)}%`;
                    document.getElementById('statusTradeRisk').textContent =
                        `$${tradeRisk.toFixed(2)}`;
                    document.getElementById('connectionStatus').textContent =
                        data.is_connected ? 'Connected' : 'Disconnected';
                    document.getElementById('tradingMode').textContent =
                        data.is_live_trading ? 'Live Trading' : 'Simulated';
                    document.getElementById('systemStatus').textContent =
                        data.is_running ? 'Active' : 'Inactive';

                    // Update trading status
                    updateTradingStatus(data.is_running);
                })
                .catch(error => console.error('Error fetching status:', error));
        }

        // Update dashboard every 5 seconds
        setInterval(updateDashboard, 5000);
        
        // Initial load
        updateDashboard();

        // Close status popup when clicking outside
        document.addEventListener('click', function(event) {
            const popup = document.getElementById('statusPopup');
            const cog = document.querySelector('.status-cog');
            
            if (statusPopupVisible && !popup.contains(event.target) && !cog.contains(event.target)) {
                toggleStatusPopup();
            }
        });
    </script>
</body>
</html>
