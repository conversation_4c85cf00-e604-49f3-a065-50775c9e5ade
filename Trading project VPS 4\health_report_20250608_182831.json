{"timestamp": "2025-06-08T18:28:26.931750", "duration_seconds": 4.207786, "overall_status": "CRITICAL", "total_checks": 26, "issues_count": 4, "warnings_count": 5, "results": {"webapp_connectivity": "HEALTHY", "template_correct": "HEALTHY", "api__api_trading_status": "HEALTHY", "api__api_health_check": "HEALTHY", "api__api_preflight_check": "HEALTHY", "api__api_recent_trades": "HEALTHY", "binance_connection": "ERROR", "account_balance": "ERROR", "price_data": "HEALTHY", "database_file": "HEALTHY", "trades_table": "HEALTHY", "recent_trades_count": 0, "recent_trades_api": "HEALTHY", "trading_engine_running": "HEALTHY", "model_configuration": "HEALTHY", "model_metrics": "ERROR", "risk_management": "HEALTHY", "dashboard_ui": "ERROR", "real_time_updates": "WARNING", "health_check_interaction": "HEALTHY", "preflight_interaction": "HEALTHY", "response_time__": 88.**************, "response_time__api_trading_status": 61.**************, "response_time__api_health_check": 68.**************, "error_handling_404": "HEALTHY", "data_accuracy": "HEALTHY"}, "issues": ["Binance API connection failed", "Account balance not available", "Missing model metrics: composite_score", "Missing UI elements: Balance section"], "warnings": ["Missing risk parameter: max_open_trades", "Missing risk parameter: risk_per_trade", "Missing risk parameter: leverage", "Real-time timestamps not updating", "Missing data field: account_balance"], "recommendations": ["Check Binance API credentials and network connectivity"]}