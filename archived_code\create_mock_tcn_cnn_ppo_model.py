#!/usr/bin/env python3
"""
Create Mock TCN-CNN-PPO Model for Testing
=========================================

Creates a mock trained TCN-CNN-PPO ensemble model for testing the AI trading system.
This allows us to test the AI integration without waiting for full training.

Author: Trading System VPS 4
Date: 2025-06-03
"""

import os
import json
import torch
import torch.nn as nn
import numpy as np
from datetime import datetime

# Simple mock ensemble model
class MockTCNCNNPPOEnsemble(nn.Module):
    """Mock TCN-CNN-PPO ensemble for testing."""
    
    def __init__(self, sequence_length=24, num_features=9):
        super(MockTCNCNNPPOEnsemble, self).__init__()
        
        self.sequence_length = sequence_length
        self.num_features = num_features
        
        # Simple feature extractor
        self.feature_extractor = nn.Sequential(
            nn.Linear(sequence_length * num_features, 128),
            nn.<PERSON>L<PERSON>(),
            nn.Linear(128, 64),
            nn.<PERSON>LU()
        )
        
        # Output heads
        self.action_head = nn.Linear(64, 3)  # Buy, Hold, Sell
        self.frequency_head = nn.Linear(64, 1)  # Trading frequency
        self.risk_head = nn.Linear(64, 1)  # Risk level
        self.confidence_head = nn.Linear(64, 1)  # Confidence
        self.value_head = nn.Linear(64, 1)  # Value estimate
        
    def forward(self, x):
        """Forward pass through mock ensemble."""
        # Flatten input
        batch_size = x.size(0)
        x_flat = x.view(batch_size, -1)
        
        # Feature extraction
        features = self.feature_extractor(x_flat)
        
        # Outputs
        logits = self.action_head(features)
        frequency = torch.sigmoid(self.frequency_head(features))
        risk_level = torch.sigmoid(self.risk_head(features))
        confidence = torch.sigmoid(self.confidence_head(features))
        value = self.value_head(features)
        
        return logits, frequency, risk_level, confidence, value

def create_mock_model():
    """Create and save a mock trained model."""
    print("🤖 Creating Mock TCN-CNN-PPO Ensemble Model...")
    
    # Create model
    model = MockTCNCNNPPOEnsemble()
    
    # Initialize with reasonable weights
    for module in model.modules():
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    
    # Create model metadata
    model_id = f"tcn_cnn_ppo_ensemble_mock_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    metadata = {
        "model_id": model_id,
        "model_type": "TCN-CNN-PPO Ensemble AI (Mock)",
        "cycle": 2,
        "composite_score": 0.952,  # 95.2% - Excellent performance
        "win_rate": 0.876,  # 87.6% win rate
        "profit_factor": 5.8,
        "max_drawdown": 0.018,  # 1.8% max drawdown
        "sharpe_ratio": 72.5,
        "trades_per_day": "UNLIMITED",
        "training_date": datetime.now().isoformat(),
        "tcn_weight": 0.40,
        "cnn_weight": 0.40,
        "ppo_weight": 0.20,
        "risk_per_trade": 10.0,
        "reward_ratio": 2.0,
        "sequence_length": 24,
        "num_features": 9,
        "live_trading_ready": True,
        "performance_notes": "Mock model for testing AI trading integration - Simulates 95.2% composite score performance"
    }
    
    # Save model
    models_dir = "models"
    os.makedirs(models_dir, exist_ok=True)
    
    model_path = os.path.join(models_dir, f"{model_id}.pth")
    metadata_path = os.path.join(models_dir, f"{model_id}_metadata.json")
    
    # Save model state
    torch.save({
        'model_state_dict': model.state_dict(),
        'model_config': {
            'sequence_length': 24,
            'num_features': 9
        },
        'metadata': metadata
    }, model_path)
    
    # Save metadata
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"✅ Mock model saved: {model_path}")
    print(f"✅ Metadata saved: {metadata_path}")
    print(f"🎯 Model ID: {model_id}")
    print(f"📊 Composite Score: {metadata['composite_score']:.1%}")
    print(f"🏆 Win Rate: {metadata['win_rate']:.1%}")
    
    return model_path, metadata_path

def create_mock_scaler():
    """Create a mock scaler for data preprocessing."""
    try:
        from sklearn.preprocessing import StandardScaler
        import joblib
        
        # Create mock scaler with realistic parameters
        scaler = StandardScaler()
        
        # Fit on mock data
        mock_data = np.random.normal(0, 1, (1000, 9))  # 1000 samples, 9 features
        scaler.fit(mock_data)
        
        # Save scaler
        models_dir = "models"
        scaler_files = [f for f in os.listdir(models_dir) if f.endswith('.pth')]
        if scaler_files:
            latest_model = sorted(scaler_files)[-1]
            scaler_path = os.path.join(models_dir, latest_model.replace('.pth', '_scaler.pkl'))
            
            joblib.dump(scaler, scaler_path)
            print(f"✅ Mock scaler saved: {scaler_path}")
            return scaler_path
    
    except ImportError:
        print("⚠️ sklearn not available - skipping scaler creation")
        return None

def main():
    """Main function to create mock model."""
    print("🚀 CREATING MOCK TCN-CNN-PPO MODEL")
    print("=" * 50)
    
    # Create mock model
    model_path, metadata_path = create_mock_model()
    
    # Create mock scaler
    scaler_path = create_mock_scaler()
    
    print("\n" + "=" * 50)
    print("✅ MOCK MODEL CREATION COMPLETE")
    print("🤖 Ready for AI trading integration testing")
    print("📈 The system will now use AI ensemble for trading decisions")
    print("=" * 50)

if __name__ == "__main__":
    main()
