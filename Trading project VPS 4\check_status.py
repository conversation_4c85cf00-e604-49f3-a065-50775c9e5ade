#!/usr/bin/env python3
"""
Check Bitcoin Freedom webapp status
"""

import requests
import json

def check_status():
    """Check current system status"""
    base_url = "http://localhost:5000"
    
    print("🔍 Bitcoin Freedom System Status Check")
    print("=" * 50)
    
    try:
        # Get trading status
        response = requests.get(f"{base_url}/api/trading_status", timeout=10)
        if response.status_code == 200:
            status = response.json()
            print("📊 Trading Status:")
            for key, value in status.items():
                print(f"   {key}: {value}")
        
        print("\n" + "=" * 50)
        
        # Get recent trades
        response = requests.get(f"{base_url}/api/recent_trades", timeout=10)
        if response.status_code == 200:
            trades = response.json()
            print(f"📈 Recent Trades ({len(trades)} trades):")
            for trade in trades[:5]:  # Show first 5 trades
                print(f"   {trade}")
        
        print("\n" + "=" * 50)
        
        # Get health check
        response = requests.get(f"{base_url}/api/health_check", timeout=10)
        if response.status_code == 200:
            health = response.json()
            print("🏥 Health Check:")
            for key, value in health.items():
                print(f"   {key}: {value}")
                
    except Exception as e:
        print(f"❌ Error checking status: {str(e)}")

if __name__ == "__main__":
    check_status()
