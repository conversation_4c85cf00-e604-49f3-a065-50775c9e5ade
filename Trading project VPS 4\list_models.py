#!/usr/bin/env python3
"""
List all available trading models with performance metrics
"""

import json
import os
from pathlib import Path

def main():
    print('📊 AVAILABLE TRADING MODELS WITH PERFORMANCE METRICS')
    print('=' * 80)

    # Check webapp model metadata
    try:
        with open('models/webapp_model_metadata.json', 'r') as f:
            webapp_data = json.load(f)
        
        print('\n🎯 MAIN WEBAPP MODELS:')
        print('-' * 50)
        
        for i, model in enumerate(webapp_data.get('all_models', []), 1):
            name = model["name"]
            print(f'{i}. {name}')
            print(f'   Composite Score: {model["composite_score"]:.1%}')
            print(f'   Net Profit: ${model["net_profit"]:.2f}')
            print(f'   Win Rate: {model["win_rate"]:.1%}')
            print(f'   Profit Factor: {model["profit_factor"]:.2f}')
            print(f'   Max Drawdown: {model["max_drawdown"]:.1%}')
            print(f'   Trades/Day: {model["trades_per_day"]:.1f}')
            print(f'   Total Trades: {model["total_trades"]}')
            print(f'   Risk-Reward: {model["risk_reward_ratio"]}:1')
            tcn_pct = model["config"]["tcn"]*100
            cnn_pct = model["config"]["cnn"]*100
            ppo_pct = model["config"]["ppo"]*100
            print(f'   Config: TCN={tcn_pct:.0f}%, CNN={cnn_pct:.0f}%, PPO={ppo_pct:.0f}%')
            print()
            
    except Exception as e:
        print(f'Error reading webapp models: {e}')

    # Check individual model metadata files
    print('\n🤖 INDIVIDUAL TCN-CNN-PPO MODELS:')
    print('-' * 50)

    model_files = []
    for file in os.listdir('models'):
        if file.endswith('_metadata.json') and 'tcn_cnn_ppo' in file:
            model_files.append(file)

    model_files.sort()

    for i, file in enumerate(model_files, 1):
        try:
            with open(f'models/{file}', 'r') as f:
                model_data = json.load(f)
            
            model_id = model_data.get("model_id", file.replace("_metadata.json", ""))
            print(f'{i}. {model_id}')
            print(f'   Type: {model_data.get("model_type", "Unknown")}')
            print(f'   Composite Score: {model_data.get("composite_score", 0):.1%}')
            print(f'   Win Rate: {model_data.get("win_rate", 0):.1%}')
            print(f'   Trades/Day: {model_data.get("trades_per_day", "Unknown")}')
            
            if 'ensemble_weights' in model_data:
                weights = model_data['ensemble_weights']
                tcn_w = weights.get("tcn_weight", 0)*100
                cnn_w = weights.get("cnn_weight", 0)*100
                ppo_w = weights.get("ppo_weight", 0)*100
                print(f'   Ensemble: TCN={tcn_w:.0f}%, CNN={cnn_w:.0f}%, PPO={ppo_w:.0f}%')
            
            if 'robust_metrics' in model_data:
                robust = model_data['robust_metrics']
                print(f'   Sortino Ratio: {robust.get("sortino_ratio", 0):.1f}')
                print(f'   Equity Curve R²: {robust.get("equity_curve_r2", 0):.2f}')
            
            print(f'   Live Ready: {model_data.get("live_trading_ready", False)}')
            print()
            
        except Exception as e:
            print(f'   Error reading {file}: {e}')
            print()

    print('\n📈 PERFORMANCE RANKING (by Composite Score):')
    print('-' * 50)

    all_models = []

    # Add webapp models
    try:
        for model in webapp_data.get('all_models', []):
            all_models.append({
                'name': model['name'],
                'composite_score': model['composite_score'],
                'net_profit': model['net_profit'],
                'win_rate': model['win_rate'],
                'source': 'webapp'
            })
    except:
        pass

    # Add individual models
    for file in model_files:
        try:
            with open(f'models/{file}', 'r') as f:
                model_data = json.load(f)
            
            all_models.append({
                'name': model_data.get('model_id', file.replace('_metadata.json', '')),
                'composite_score': model_data.get('composite_score', 0),
                'net_profit': model_data.get('net_profit', 0),
                'win_rate': model_data.get('win_rate', 0),
                'source': 'individual'
            })
        except:
            pass

    # Sort by composite score
    all_models.sort(key=lambda x: x['composite_score'], reverse=True)

    for i, model in enumerate(all_models[:10], 1):  # Top 10
        name = model["name"]
        score = model["composite_score"]
        win_rate = model["win_rate"]
        profit = model.get("net_profit", 0)
        print(f'{i:2d}. {name}')
        print(f'     Score: {score:.1%} | Win Rate: {win_rate:.1%} | Profit: ${profit:.0f}')

    print(f'\n📊 SUMMARY: Found {len(all_models)} total models')
    best_score = max(all_models, key=lambda x: x["composite_score"])["composite_score"]
    best_profit = max(all_models, key=lambda x: x.get("net_profit", 0))["net_profit"]
    best_win_rate = max(all_models, key=lambda x: x["win_rate"])["win_rate"]
    print(f'🏆 Best Composite Score: {best_score:.1%}')
    print(f'💰 Best Net Profit: ${best_profit:.0f}')
    print(f'🎯 Best Win Rate: {best_win_rate:.1%}')

if __name__ == "__main__":
    main()
