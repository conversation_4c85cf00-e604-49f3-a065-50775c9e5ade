"""
Quick ML Training Launcher
Launches ML model training for web app integration
"""

import os
import sys
import asyncio
from datetime import datetime

def main():
    """Main launcher function"""
    print("\n" + "="*80)
    print("🤖 ML MODEL TRAINING LAUNCHER")
    print("="*80)
    print("🎯 Target: 85% Composite Score OR Best Available")
    print("💰 Strategy: Save Highest Composite + Highest Net Profit")
    print("🔗 Integration: Direct Web App Integration")
    print("📊 Models: TCN + CNN + PPO Ensemble")
    print("="*80)
    print(f"📅 Launch Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # Check if training script exists
    training_script = 'train_ml_models_for_webapp.py'
    if not os.path.exists(training_script):
        print(f"❌ Training script not found: {training_script}")
        return 1
    
    print(f"\n🚀 Starting ML training...")
    print(f"📄 Script: {training_script}")
    print(f"⏱️ Estimated time: 15-30 minutes")
    print(f"🔄 Progress will be shown in real-time")
    
    # Confirm start
    response = input(f"\n🤔 Start ML training now? (y/N): ")
    if response.lower() != 'y':
        print("❌ Training cancelled by user")
        return 0
    
    print(f"\n🎬 Launching training...")
    print("-" * 50)
    
    try:
        # Import and run training
        from train_ml_models_for_webapp import MLModelTrainerForWebApp
        
        # Create trainer
        trainer = MLModelTrainerForWebApp()
        
        # Run training
        results = asyncio.run(trainer.train_models_for_webapp())
        
        if results['success']:
            print(f"\n🎉 SUCCESS: ML models trained and ready for web app!")
            print(f"🌐 Restart your web app to use the new models")
            return 0
        else:
            print(f"\n❌ FAILED: {results.get('error', 'Unknown error')}")
            return 1
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print(f"💡 Make sure all dependencies are installed")
        return 1
    except Exception as e:
        print(f"❌ Training error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    print(f"\n" + "="*80)
    if exit_code == 0:
        print("✅ ML TRAINING COMPLETED SUCCESSFULLY!")
        print("🌐 Next steps:")
        print("   1. Restart your web app")
        print("   2. Look for the ML model status in the header")
        print("   3. Test trading will now use ML models")
    else:
        print("❌ ML TRAINING FAILED!")
        print("🔧 Troubleshooting:")
        print("   1. Check error messages above")
        print("   2. Ensure all dependencies are installed")
        print("   3. Try running: pip install -r requirements.txt")
    print("="*80)
    
    input("\nPress Enter to exit...")
    sys.exit(exit_code)
