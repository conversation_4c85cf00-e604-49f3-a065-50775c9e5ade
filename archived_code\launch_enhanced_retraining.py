#!/usr/bin/env python3
"""
Enhanced Retraining Launcher - VPS 4
====================================

Simple launcher for the enhanced retraining system with predefined configurations.

Usage Examples:
    python launch_enhanced_retraining.py                    # Single cycle, 95% target
    python launch_enhanced_retraining.py --quick            # Quick test (90% target)
    python launch_enhanced_retraining.py --aggressive       # Aggressive (98% target)
    python launch_enhanced_retraining.py --continuous       # Continuous retraining

Author: Trading System VPS 4
Date: 2025-01-27
"""

import os
import sys
import argparse
import subprocess
from datetime import datetime

def print_banner():
    """Print system banner."""
    print("🚀 Enhanced Retraining System Launcher - VPS 4")
    print("=" * 70)
    print("🎯 Target: 95% Composite Reward")
    print("📅 Training: 60 days | Testing: 30 days (out-of-sample)")
    print("💾 Saves: Best Composite + Best Profit Models")
    print("🔄 Variants: High Frequency, Balanced, Conservative, Aggressive")
    print("=" * 70)

def launch_retraining(args):
    """Launch the enhanced retraining system with specified arguments."""
    
    # Build command
    cmd = [sys.executable, "enhanced_retraining_system.py"]
    
    # Add arguments based on preset or custom options
    if args.quick:
        cmd.extend(["--target-score", "0.90", "--training-days", "45", "--testing-days", "20"])
        print("🏃 Quick Mode: 90% target, 45/20 day cycle")
    elif args.aggressive:
        cmd.extend(["--target-score", "0.98", "--training-days", "75", "--testing-days", "35"])
        print("🔥 Aggressive Mode: 98% target, 75/35 day cycle")
    elif args.conservative:
        cmd.extend(["--target-score", "0.85", "--training-days", "50", "--testing-days", "25"])
        print("🛡️ Conservative Mode: 85% target, 50/25 day cycle")
    else:
        # Default: 95% target
        cmd.extend(["--target-score", "0.95", "--training-days", "60", "--testing-days", "30"])
        print("🎯 Standard Mode: 95% target, 60/30 day cycle")
    
    # Add continuous mode if requested
    if args.continuous:
        cmd.extend(["--continuous", "--max-cycles", str(args.max_cycles), 
                   "--interval-hours", str(args.interval_hours)])
        print(f"🔄 Continuous Mode: {args.max_cycles} cycles, {args.interval_hours}h intervals")
    
    print(f"⚡ Launching: {' '.join(cmd)}")
    print("=" * 70)
    
    try:
        # Launch the retraining system
        result = subprocess.run(cmd, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"❌ Retraining failed with exit code: {e.returncode}")
        return e.returncode
    except FileNotFoundError:
        print("❌ Enhanced retraining system not found!")
        print("💡 Make sure 'enhanced_retraining_system.py' is in the current directory")
        return 1

def main():
    """Main launcher function."""
    
    parser = argparse.ArgumentParser(description='Enhanced Retraining System Launcher')
    
    # Preset modes
    preset_group = parser.add_mutually_exclusive_group()
    preset_group.add_argument('--quick', action='store_true',
                             help='Quick mode: 90% target, 45/20 day cycle')
    preset_group.add_argument('--aggressive', action='store_true',
                             help='Aggressive mode: 98% target, 75/35 day cycle')
    preset_group.add_argument('--conservative', action='store_true',
                             help='Conservative mode: 85% target, 50/25 day cycle')
    
    # Continuous mode options
    parser.add_argument('--continuous', action='store_true',
                       help='Run continuous retraining cycles')
    parser.add_argument('--max-cycles', type=int, default=5,
                       help='Maximum cycles for continuous mode (default: 5)')
    parser.add_argument('--interval-hours', type=int, default=12,
                       help='Hours between cycles (default: 12)')
    
    args = parser.parse_args()
    
    # Print banner
    print_banner()
    
    # Check if enhanced retraining system exists
    if not os.path.exists("enhanced_retraining_system.py"):
        print("❌ Enhanced retraining system not found!")
        print("💡 Make sure you're in the correct directory")
        return 1
    
    # Launch retraining
    return launch_retraining(args)

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
