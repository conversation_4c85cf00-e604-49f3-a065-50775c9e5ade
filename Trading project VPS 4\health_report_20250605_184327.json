{"timestamp": "2025-06-05T18:43:31.682584", "overall_status": "WARNING", "summary": {"total_checks": 9, "healthy": 3, "warnings": 5, "errors": 0, "critical": 0}, "detailed_results": {"server": {"status": "HEALTHY", "response_time": 0.104068, "status_code": 200}, "api_endpoints": {"/api/trading_status": {"status": "HEALTHY", "response_time": 0.004885, "data_keys": ["current_price", "is_live_mode", "is_running", "last_update", "model_info", "performance", "price_source"]}, "/api/open_positions": {"status": "HEALTHY", "response_time": 0.006148, "data_keys": "non-dict"}, "/api/recent_trades": {"status": "HEALTHY", "response_time": 0.005737, "data_keys": "non-dict"}, "/api/cross_margin_analysis": {"status": "HEALTHY", "response_time": 0.004531, "data_keys": ["analysis", "grid_size", "margin_available", "risk_reward_ratio", "status"]}, "/api/trading_test_status": {"status": "HEALTHY", "response_time": 0.007914, "data_keys": ["last_test", "status", "test_functions_available"]}}, "real_time_data": {"status": "HEALTHY", "current_btc_price": 105170.62, "price_source": "real_api", "last_update": "2025-06-05T18:43:28.308722"}, "model_integration": {"status": "WARNING", "model_id": "conservative_elite", "composite_score": 79.1, "cycle": null, "checks": {"model_id_correct": false, "composite_score_correct": false, "cycle_correct": false, "tcn_weight_present": false, "cnn_weight_present": false, "ppo_weight_present": false}, "component_weights": {"tcn": null, "cnn": null, "ppo": null}}, "cross_margin": {"status": "WARNING", "grid_size_percent": null, "current_btc_price": null, "validation_checks": {"grid_size_correct": false, "risk_reward_ratio_buy": false, "risk_reward_ratio_sell": false, "account_size_correct": false, "margin_type_correct": false, "position_sizes_calculated": false}, "buy_risk_reward": null, "sell_risk_reward": null}, "trading_engine": {"status": "HEALTHY", "is_running": false, "current_equity": 300.0, "total_trades": 0, "win_rate": 79.1, "open_positions": 0, "checks": {"trading_status_available": true, "performance_metrics_available": true, "positions_endpoint_working": true, "trades_endpoint_working": true, "balance_tracking": true, "account_size_correct": true}}, "test_functions": {"status": "WARNING", "test_capabilities": {}, "checks": {"force_trade_available": false, "close_all_available": false, "test_cycle_available": false, "reset_stats_available": false}}, "web_interface": {"status": "WARNING", "response_time": 0.009639, "content_length": 10600, "ui_checks": {"dashboard_loads": false, "start_trading_button": false, "stop_trading_button": false, "test_functions_section": false, "performance_section": true, "btc_price_display": true}}, "data_integrity": {"status": "WARNING", "checks": {"btc_price_consistent": false, "account_size_consistent": false, "model_data_complete": false, "performance_data_complete": true}, "btc_price_status": 105649.62, "btc_price_margin": null}}, "warnings": ["Model integration issues: ['model_id_correct', 'composite_score_correct', 'cycle_correct', 'tcn_weight_present', 'cnn_weight_present', 'ppo_weight_present']", "Cross margin calculation issues: ['grid_size_correct', 'risk_reward_ratio_buy', 'risk_reward_ratio_sell', 'account_size_correct', 'margin_type_correct', 'position_sizes_calculated']", "Missing test functions: ['force_trade_available', 'close_all_available', 'test_cycle_available', 'reset_stats_available']", "Missing UI components: ['dashboard_loads', 'start_trading_button', 'stop_trading_button', 'test_functions_section']", "Data integrity issues: ['btc_price_consistent', 'account_size_consistent', 'model_data_complete']"], "errors": [], "recommendations": ["🤖 Verify Cycle 1 model is properly loaded and configured", "📊 Verify cross margin calculations and grid trading parameters", "⚠️ Review and address all warnings before live trading"]}