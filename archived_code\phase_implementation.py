#!/usr/bin/env python3
"""
Phase Implementation Runner
Executes trading system implementation in phases with testing and validation
"""

import os
import sys
import time
import logging
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# Import our modules
from core_infrastructure import HealthMonitor, AutoFixSystem
from test_framework import run_all_tests
from code_audit import ComprehensiveAuditor
from secure_api_manager import SecureAPIManager

@dataclass
class PhaseResult:
    """Phase execution result"""
    phase_name: str
    success: bool
    duration: float
    tests_passed: int
    tests_failed: int
    critical_issues: int
    warnings: int
    message: str

class PhaseImplementation:
    """Phase-by-phase implementation with testing and validation"""
    
    def __init__(self):
        self.logger = logging.getLogger('PhaseImplementation')
        self.setup_logging()
        
        self.health_monitor = None
        self.api_manager = SecureAPIManager()
        self.phase_results = []
        
        # Phase definitions
        self.phases = [
            {
                'name': 'Phase 1: Core Infrastructure',
                'description': 'Setup core infrastructure, health monitoring, and auto-fix',
                'function': self.phase_1_infrastructure,
                'critical': True
            },
            {
                'name': 'Phase 2: API & Security Setup',
                'description': 'Configure secure API management and validation',
                'function': self.phase_2_api_security,
                'critical': True
            },
            {
                'name': 'Phase 3: Database & Data Pipeline',
                'description': 'Initialize database and data collection systems',
                'function': self.phase_3_database,
                'critical': True
            },
            {
                'name': 'Phase 4: Trading Engine',
                'description': 'Implement core trading engine with commission tracking',
                'function': self.phase_4_trading_engine,
                'critical': True
            },
            {
                'name': 'Phase 5: Web Interface',
                'description': 'Deploy web interface with manual controls',
                'function': self.phase_5_web_interface,
                'critical': False
            },
            {
                'name': 'Phase 6: Final Validation',
                'description': 'Comprehensive testing and deployment readiness',
                'function': self.phase_6_final_validation,
                'critical': True
            }
        ]
    
    def setup_logging(self):
        """Setup comprehensive logging"""
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # Create log file with timestamp
        log_file = os.path.join(log_dir, f"implementation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger.info(f"Phase implementation started - Log file: {log_file}")
    
    def run_all_phases(self) -> bool:
        """Execute all phases with testing and validation"""
        self.logger.info("🚀 Starting Phase Implementation")
        self.logger.info("=" * 60)
        
        overall_success = True
        
        for i, phase in enumerate(self.phases, 1):
            self.logger.info(f"\n📋 {phase['name']}")
            self.logger.info(f"Description: {phase['description']}")
            self.logger.info("-" * 40)
            
            start_time = time.time()
            
            try:
                # Execute phase
                phase_success = phase['function']()
                
                # Run tests after each phase
                test_results = self.run_phase_tests(phase['name'])
                
                # Run code audit
                audit_results = self.run_code_audit()
                
                # Calculate duration
                duration = time.time() - start_time
                
                # Create phase result
                result = PhaseResult(
                    phase_name=phase['name'],
                    success=phase_success and test_results['success'],
                    duration=duration,
                    tests_passed=test_results['passed'],
                    tests_failed=test_results['failed'],
                    critical_issues=audit_results['critical'],
                    warnings=audit_results['warnings'],
                    message=f"Phase completed in {duration:.1f}s"
                )
                
                self.phase_results.append(result)
                
                # Log results
                if result.success:
                    self.logger.info(f"✅ {phase['name']} - SUCCESS")
                    self.logger.info(f"   Tests: {result.tests_passed} passed, {result.tests_failed} failed")
                    self.logger.info(f"   Audit: {result.critical_issues} critical, {result.warnings} warnings")
                else:
                    self.logger.error(f"❌ {phase['name']} - FAILED")
                    self.logger.error(f"   Tests: {result.tests_passed} passed, {result.tests_failed} failed")
                    self.logger.error(f"   Audit: {result.critical_issues} critical, {result.warnings} warnings")
                    
                    if phase['critical']:
                        self.logger.error("🚨 Critical phase failed - stopping implementation")
                        overall_success = False
                        break
                    else:
                        self.logger.warning("⚠️ Non-critical phase failed - continuing")
                        overall_success = False
                
            except Exception as e:
                self.logger.error(f"💥 {phase['name']} - EXCEPTION: {e}")
                if phase['critical']:
                    overall_success = False
                    break
        
        # Generate final report
        self.generate_final_report(overall_success)
        
        return overall_success
    
    def phase_1_infrastructure(self) -> bool:
        """Phase 1: Core Infrastructure Setup"""
        try:
            self.logger.info("🔧 Setting up core infrastructure...")
            
            # Initialize health monitoring
            from trading_config import TradingConfig
            config = TradingConfig()
            
            self.health_monitor = HealthMonitor(config)
            
            # Test health monitoring
            health_results = self.health_monitor.run_health_checks()
            
            # Start monitoring
            self.health_monitor.start_monitoring()
            
            # Test auto-fix system
            auto_fix = AutoFixSystem(config)
            
            self.logger.info("✅ Core infrastructure setup complete")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Infrastructure setup failed: {e}")
            return False
    
    def phase_2_api_security(self) -> bool:
        """Phase 2: API & Security Setup"""
        try:
            self.logger.info("🔐 Setting up API security...")
            
            # Check if API keys are configured
            if not self.api_manager.encrypted_file.exists():
                self.logger.warning("⚠️ No API keys configured")
                self.logger.info("Run 'python secure_api_manager.py --setup' to configure API keys")
                return True  # Not critical for testing
            
            # Load and validate API keys
            if self.api_manager.load_api_keys():
                keys = self.api_manager.get_api_keys()
                if keys['api_key'] and keys['secret_key']:
                    self.logger.info("✅ API keys loaded and validated")
                else:
                    self.logger.warning("⚠️ API keys not properly configured")
            
            self.logger.info("✅ API security setup complete")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ API security setup failed: {e}")
            return False
    
    def phase_3_database(self) -> bool:
        """Phase 3: Database & Data Pipeline"""
        try:
            self.logger.info("🗄️ Setting up database and data pipeline...")
            
            # Initialize database
            from trade_manager_webapp import TradeDatabase
            from trading_config import TradingConfig
            
            config = TradingConfig()
            db_path = os.path.join(config.DATA_DIR, 'trades.db')
            
            db = TradeDatabase(db_path)
            db.init_database()
            
            self.logger.info("✅ Database setup complete")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Database setup failed: {e}")
            return False
    
    def phase_4_trading_engine(self) -> bool:
        """Phase 4: Trading Engine Implementation"""
        try:
            self.logger.info("⚙️ Setting up trading engine...")
            
            # Test commission calculator
            from trade_manager_webapp import CommissionCalculator
            calc = CommissionCalculator()
            
            # Test calculation
            result = calc.calculate_trade_commission(10.0)
            expected_total = 0.02  # 0.1% entry + 0.1% exit
            
            if abs(result['total_commission'] - expected_total) < 0.001:
                self.logger.info("✅ Commission calculator working correctly")
            else:
                self.logger.error("❌ Commission calculator test failed")
                return False
            
            # Test manual trade controller
            from trade_manager_webapp import ManualTradeController
            controller = ManualTradeController(1000.0)
            
            # Test validation
            validation = controller.validate_trade_size(25.0)
            if validation['valid']:
                self.logger.info("✅ Manual trade controller working correctly")
            else:
                self.logger.error("❌ Manual trade controller test failed")
                return False
            
            self.logger.info("✅ Trading engine setup complete")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Trading engine setup failed: {e}")
            return False
    
    def phase_5_web_interface(self) -> bool:
        """Phase 5: Web Interface Deployment"""
        try:
            self.logger.info("🌐 Setting up web interface...")
            
            # Test web application import
            from trade_manager_webapp import TradingWebApp
            
            # Create app instance (don't start server)
            app = TradingWebApp()
            
            self.logger.info("✅ Web interface setup complete")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Web interface setup failed: {e}")
            return False
    
    def phase_6_final_validation(self) -> bool:
        """Phase 6: Final Validation and Deployment Readiness"""
        try:
            self.logger.info("🔍 Running final validation...")
            
            # Run comprehensive health check
            if self.health_monitor:
                health_results = self.health_monitor.run_health_checks()
                health_summary = self.health_monitor.get_health_summary()
                
                if health_summary['overall_status'] in ['HEALTHY', 'WARNING']:
                    self.logger.info("✅ System health check passed")
                else:
                    self.logger.error("❌ System health check failed")
                    return False
            
            # Validate API configuration
            if self.api_manager.is_configured():
                self.logger.info("✅ API configuration validated")
            else:
                self.logger.warning("⚠️ API not configured - required for live trading")
            
            # Check all required files exist
            required_files = [
                'trade_manager_webapp.py',
                'core_infrastructure.py',
                'secure_api_manager.py',
                'config/trading_config.py'
            ]
            
            for file_path in required_files:
                if os.path.exists(file_path):
                    self.logger.info(f"✅ {file_path} exists")
                else:
                    self.logger.error(f"❌ {file_path} missing")
                    return False
            
            self.logger.info("✅ Final validation complete")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Final validation failed: {e}")
            return False
    
    def run_phase_tests(self, phase_name: str) -> Dict[str, Any]:
        """Run tests for current phase"""
        try:
            self.logger.info(f"🧪 Running tests for {phase_name}...")
            
            # Run test framework
            result = run_all_tests()
            
            passed = result.testsRun - len(result.failures) - len(result.errors)
            failed = len(result.failures) + len(result.errors)
            
            return {
                'success': result.wasSuccessful(),
                'passed': passed,
                'failed': failed
            }
            
        except Exception as e:
            self.logger.error(f"❌ Test execution failed: {e}")
            return {'success': False, 'passed': 0, 'failed': 1}
    
    def run_code_audit(self) -> Dict[str, int]:
        """Run code audit"""
        try:
            self.logger.info("🔍 Running code audit...")
            
            auditor = ComprehensiveAuditor()
            results = auditor.audit_project(".")
            
            summary = results['summary']
            
            return {
                'critical': summary['severity_breakdown']['CRITICAL'],
                'warnings': summary['severity_breakdown']['HIGH'] + summary['severity_breakdown']['MEDIUM']
            }
            
        except Exception as e:
            self.logger.error(f"❌ Code audit failed: {e}")
            return {'critical': 0, 'warnings': 0}
    
    def generate_final_report(self, overall_success: bool):
        """Generate final implementation report"""
        self.logger.info("\n" + "=" * 60)
        self.logger.info("📊 FINAL IMPLEMENTATION REPORT")
        self.logger.info("=" * 60)
        
        total_phases = len(self.phase_results)
        successful_phases = sum(1 for r in self.phase_results if r.success)
        total_tests_passed = sum(r.tests_passed for r in self.phase_results)
        total_tests_failed = sum(r.tests_failed for r in self.phase_results)
        total_critical_issues = sum(r.critical_issues for r in self.phase_results)
        
        self.logger.info(f"Phases Completed: {successful_phases}/{total_phases}")
        self.logger.info(f"Tests Passed: {total_tests_passed}")
        self.logger.info(f"Tests Failed: {total_tests_failed}")
        self.logger.info(f"Critical Issues: {total_critical_issues}")
        
        self.logger.info("\nPhase Details:")
        for result in self.phase_results:
            status = "✅ PASS" if result.success else "❌ FAIL"
            self.logger.info(f"  {status} {result.phase_name} ({result.duration:.1f}s)")
        
        if overall_success and total_critical_issues == 0:
            self.logger.info("\n🎉 IMPLEMENTATION SUCCESSFUL!")
            self.logger.info("System is ready for deployment and testing.")
        else:
            self.logger.error("\n⚠️ IMPLEMENTATION ISSUES DETECTED")
            self.logger.error("Review and fix issues before deployment.")
        
        # Stop health monitoring
        if self.health_monitor:
            self.health_monitor.stop_monitoring()

def main():
    """Main implementation runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Phase Implementation Runner')
    parser.add_argument('--phase', type=int, help='Run specific phase (1-6)')
    parser.add_argument('--test-only', action='store_true', help='Run tests only')
    parser.add_argument('--audit-only', action='store_true', help='Run code audit only')
    
    args = parser.parse_args()
    
    implementation = PhaseImplementation()
    
    if args.test_only:
        print("🧪 Running tests only...")
        result = run_all_tests()
        return result.wasSuccessful()
    
    elif args.audit_only:
        print("🔍 Running code audit only...")
        auditor = ComprehensiveAuditor()
        results = auditor.audit_project(".")
        auditor.generate_report(results, "audit_report.md")
        return results['summary']['severity_breakdown']['CRITICAL'] == 0
    
    elif args.phase:
        if 1 <= args.phase <= 6:
            phase = implementation.phases[args.phase - 1]
            print(f"🚀 Running {phase['name']}...")
            return phase['function']()
        else:
            print("❌ Invalid phase number. Use 1-6.")
            return False
    
    else:
        # Run all phases
        return implementation.run_all_phases()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
