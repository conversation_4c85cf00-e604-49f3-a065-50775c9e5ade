#!/usr/bin/env python3
"""
Fix the missing trading loop by starting it properly
"""

import urllib.request
import json
import threading
import time

def check_current_threads():
    """Check what threads are currently running"""
    print("🧵 CHECKING CURRENT THREADS")
    print("=" * 50)
    
    active_threads = threading.enumerate()
    print(f"Total active threads: {len(active_threads)}")
    
    for thread in active_threads:
        print(f"   Thread: {thread.name} (Alive: {thread.is_alive()})")
        
    # Look for trading-related threads
    trading_threads = [t for t in active_threads if 'trading' in t.name.lower()]
    
    if len(trading_threads) == 0:
        print("❌ NO TRADING THREADS FOUND - This is the problem!")
        return False
    else:
        print(f"✅ Found {len(trading_threads)} trading threads")
        return True

def start_trading_via_api():
    """Try to start trading via the webapp API"""
    print("\n🚀 STARTING TRADING VIA API")
    print("=" * 50)
    
    try:
        # Create POST request to start trading
        req = urllib.request.Request('http://localhost:5000/api/start_trading', 
                                   data=b'{}', 
                                   headers={'Content-Type': 'application/json'})
        req.get_method = lambda: 'POST'
        
        with urllib.request.urlopen(req, timeout=10) as response:
            if response.getcode() == 200:
                data = json.loads(response.read().decode('utf-8'))
                print(f"✅ Start Trading Response: {data}")
                
                if data.get('status') == 'success':
                    print("✅ Trading started successfully!")
                    return True
                else:
                    print(f"❌ Trading start failed: {data.get('message', 'Unknown error')}")
                    return False
            else:
                print(f"❌ HTTP Error: {response.getcode()}")
                return False
                
    except Exception as e:
        print(f"❌ API call failed: {e}")
        return False

def verify_trading_started():
    """Verify that trading actually started"""
    print("\n🔍 VERIFYING TRADING STARTED")
    print("=" * 50)
    
    # Wait a moment for thread to start
    time.sleep(2)
    
    # Check threads again
    has_trading_threads = check_current_threads()
    
    # Check webapp status
    try:
        with urllib.request.urlopen('http://localhost:5000/api/trading_status', timeout=10) as response:
            if response.getcode() == 200:
                data = json.loads(response.read().decode('utf-8'))
                
                is_running = data.get('is_running', False)
                total_trades = data.get('performance', {}).get('total_trades', 0)
                
                print(f"\n📊 Trading Status After Start:")
                print(f"   Running: {is_running}")
                print(f"   Total Trades: {total_trades}")
                
                if is_running and has_trading_threads:
                    print("✅ SUCCESS: Trading is now properly running!")
                    return True
                else:
                    print("❌ FAILED: Trading still not working properly")
                    return False
                    
    except Exception as e:
        print(f"❌ Status check failed: {e}")
        return False

def force_start_trading_loop():
    """Force start the trading loop by calling it directly"""
    print("\n🔧 FORCE STARTING TRADING LOOP")
    print("=" * 50)
    
    try:
        # Import the webapp components
        import sys
        sys.path.append('.')
        
        from live_trading_web_app import trading_engine, live_trading_loop
        import threading
        
        print(f"📊 Current trading engine status:")
        print(f"   Is Running: {trading_engine.is_running}")
        print(f"   Model Loaded: {hasattr(trading_engine, 'model')}")
        
        # Force start the trading engine
        if not trading_engine.is_running:
            print("🚀 Starting trading engine...")
            trading_engine.is_running = True
        
        # Start the trading loop in a new thread
        print("🧵 Starting trading loop thread...")
        trading_thread = threading.Thread(
            target=live_trading_loop, 
            daemon=True, 
            name="ForcedTradingLoop"
        )
        trading_thread.start()
        
        # Wait and verify
        time.sleep(2)
        
        if trading_thread.is_alive():
            print("✅ SUCCESS: Trading loop thread started!")
            print(f"   Thread name: {trading_thread.name}")
            print(f"   Thread alive: {trading_thread.is_alive()}")
            return True
        else:
            print("❌ FAILED: Trading loop thread died immediately")
            return False
            
    except Exception as e:
        print(f"❌ Force start failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def monitor_trading_activity():
    """Monitor for actual trading activity"""
    print("\n👀 MONITORING TRADING ACTIVITY")
    print("=" * 50)
    print("Watching for trades for 60 seconds...")
    
    initial_trades = 0
    
    # Get initial trade count
    try:
        with urllib.request.urlopen('http://localhost:5000/api/trading_status', timeout=10) as response:
            if response.getcode() == 200:
                data = json.loads(response.read().decode('utf-8'))
                initial_trades = data.get('performance', {}).get('total_trades', 0)
    except:
        pass
    
    print(f"📊 Initial trade count: {initial_trades}")
    
    # Monitor for 60 seconds
    for i in range(12):  # 12 checks over 60 seconds
        time.sleep(5)
        
        try:
            with urllib.request.urlopen('http://localhost:5000/api/trading_status', timeout=5) as response:
                if response.getcode() == 200:
                    data = json.loads(response.read().decode('utf-8'))
                    current_trades = data.get('performance', {}).get('total_trades', 0)
                    current_price = data.get('current_price', 0)
                    
                    if current_trades > initial_trades:
                        print(f"🎉 NEW TRADE DETECTED! Count: {current_trades}")
                        return True
                    else:
                        print(f"⏳ Check {i+1}/12: Price ${current_price:,.2f}, Trades: {current_trades}")
        except:
            print(f"❌ Check {i+1}/12: Status check failed")
    
    print("⏰ 60-second monitoring complete - no new trades detected")
    return False

def main():
    """Main fix function"""
    print("🔧 FIXING TRADING LOOP ISSUE")
    print("=" * 80)
    print("Diagnosing and fixing the missing trading loop...")
    print("=" * 80)
    
    # Step 1: Check current state
    print("STEP 1: Check current thread state")
    has_threads = check_current_threads()
    
    # Step 2: Try API start
    print("\nSTEP 2: Try starting via API")
    api_success = start_trading_via_api()
    
    # Step 3: Verify it worked
    print("\nSTEP 3: Verify trading started")
    verify_success = verify_trading_started()
    
    # Step 4: Force start if needed
    if not verify_success:
        print("\nSTEP 4: Force start trading loop")
        force_success = force_start_trading_loop()
        
        if force_success:
            print("\nSTEP 5: Final verification")
            verify_trading_started()
    
    # Step 5: Monitor for activity
    print("\nSTEP 6: Monitor for trading activity")
    activity_detected = monitor_trading_activity()
    
    # Final summary
    print("\n" + "=" * 80)
    print("🎯 TRADING LOOP FIX SUMMARY")
    print("=" * 80)
    
    if activity_detected:
        print("🎉 SUCCESS: Trading loop is now working!")
        print("   ✅ Threads running")
        print("   ✅ Trades being executed")
        print("   ✅ System fully operational")
    else:
        print("⚠️ PARTIAL SUCCESS: Trading loop started but no trades yet")
        print("   ✅ Threads running")
        print("   ⏳ Waiting for trading signals")
        print("   💡 This may be normal for Conservative Elite model")

if __name__ == "__main__":
    main()
