"""
Real Binance Data Collector
Collects live market data from Binance API for grid trading system
"""

import os
import sys
import logging
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import ccxt
import time

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

class BinanceDataCollector:
    """Real-time Binance data collector for grid trading"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('BinanceDataCollector')
        
        # Initialize Binance exchange
        self.exchange = None
        self._initialize_exchange()
        
        # Data cache
        self.data_cache = {}
        self.last_update = {}
        
    def _initialize_exchange(self):
        """Initialize Binance exchange connection"""
        try:
            # Get API credentials from config or environment
            api_key = getattr(self.config, 'BINANCE_API_KEY', os.getenv('BINANCE_API_KEY', ''))
            secret_key = getattr(self.config, 'BINANCE_SECRET_KEY', os.getenv('BINANCE_SECRET_KEY', ''))
            testnet = getattr(self.config, 'BINANCE_TESTNET', os.getenv('BINANCE_TESTNET', 'True').lower() == 'true')
            
            # Initialize exchange
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': testnet,  # Use testnet by default for safety
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'margin'  # Use margin for cross margin readiness
                }
            })
            
            if testnet:
                self.exchange.set_sandbox_mode(True)
            
            # Test connection
            if api_key and secret_key:
                try:
                    balance = self.exchange.fetch_balance()
                    self.logger.info(f"✅ Binance API connected successfully")
                    self.logger.info(f"   Mode: {'TESTNET' if testnet else 'LIVE'}")
                    self.logger.info(f"   Account: {balance.get('info', {}).get('accountType', 'Unknown')}")
                except Exception as e:
                    self.logger.warning(f"⚠️ API credentials provided but connection failed: {e}")
                    self.logger.info("   Falling back to public data only")
            else:
                self.logger.info("ℹ️ No API credentials provided - using public data only")
                
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Binance exchange: {e}")
            # Fallback to public-only access
            self.exchange = ccxt.binance({
                'enableRateLimit': True,
                'options': {'defaultType': 'spot'}
            })
    
    async def get_real_time_price(self, symbol: str = 'BTC/USDT') -> float:
        """Get real-time price for a symbol"""
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            price = ticker['last']
            
            self.logger.debug(f"Real-time price {symbol}: ${price:,.2f}")
            return price
            
        except Exception as e:
            self.logger.error(f"Failed to get real-time price for {symbol}: {e}")
            return 50000.0  # Fallback price
    
    def fetch_ohlcv(self, symbol, timeframe, since, limit=1000):
        for _ in range(5):
            try:
                return self.exchange.fetch_ohlcv(symbol, timeframe, since=since, limit=limit)
            except Exception as e:
                self.logger.warning(f"Retrying fetch_ohlcv for {symbol}: {e}")
                time.sleep(2)
        raise RuntimeError(f"Failed to fetch OHLCV for {symbol}")

    def get_90d_1m_ohlcv(self, symbol):
        """Get 90 days of 1-minute OHLCV data for a symbol"""
        # 90 days * 24h * 60m = 129600 candles
        timeframe = '1m'
        days = 90
        total_candles = days * 24 * 60
        limit = 1000
        now = self.exchange.milliseconds()
        since = now - days * 24 * 60 * 60 * 1000
        all_ohlcv = []
        while len(all_ohlcv) < total_candles:
            ohlcv = self.fetch_ohlcv(symbol, timeframe, since, limit)
            if not ohlcv:
                break
            all_ohlcv.extend(ohlcv)
            since = ohlcv[-1][0] + 60 * 1000  # next minute
            if len(ohlcv) < limit:
                break
            time.sleep(0.2)
        df = pd.DataFrame(all_ohlcv, columns=['timestamp','open','high','low','close','volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        return df

    async def get_historical_data(self, symbol: str, timeframe: str = '1m', 
                                 days: int = 1) -> pd.DataFrame:
        """Get historical OHLCV data from Binance, handling pagination for large requests."""
        try:
            # Calculate the number of candles needed
            timeframe_minutes = {
                '1m': 1, '5m': 5, '15m': 15, '30m': 30,
                '1h': 60, '4h': 240, '1d': 1440
            }
            minutes_per_candle = timeframe_minutes.get(timeframe, 1)
            total_candles_needed = (days * 24 * 60) // minutes_per_candle
            
            # Binance API limit per request (typically 1000 or 1500, using 1000 for safety)
            limit_per_request = 1000 
            
            all_ohlcv = []
            since = self.exchange.milliseconds() - days * 24 * 60 * 60 * 1000 # Start from 'days' ago
            
            self.logger.info(f"Fetching {days} days ({total_candles_needed} candles) of {timeframe} data for {symbol} in chunks...")

            while len(all_ohlcv) < total_candles_needed:
                remaining_candles = total_candles_needed - len(all_ohlcv)
                current_limit = min(limit_per_request, remaining_candles)
                if current_limit <= 0: # Should not happen if logic is correct, but as a safeguard
                    break

                self.logger.debug(f"Fetching chunk for {symbol}: since={self.exchange.iso8601(since)}, limit={current_limit}")
                
                # Fetch OHLCV data
                ohlcv_chunk = self.exchange.fetch_ohlcv(symbol, timeframe, since=since, limit=current_limit)
                
                if not ohlcv_chunk:
                    self.logger.info(f"No more data received for {symbol} after fetching {len(all_ohlcv)} candles.")
                    break 
                
                all_ohlcv.extend(ohlcv_chunk)
                
                if len(ohlcv_chunk) < current_limit: # Reached the earliest available data
                    self.logger.info(f"Fetched all available data for {symbol} (less than requested). Total: {len(all_ohlcv)} candles.")
                    break

                # Update 'since' for the next request to the timestamp of the last candle + 1ms
                since = ohlcv_chunk[-1][0] + (minutes_per_candle * 60 * 1000) # Add timeframe duration to avoid overlap
                                                                            # or use ohlcv_chunk[-1][0] + 1 if API handles exact 'since'

                # Small delay to respect rate limits, if not handled by ccxt's enableRateLimit
                # await asyncio.sleep(self.exchange.rateLimit / 1000) 

            if not all_ohlcv:
                raise ValueError(f"No data received for {symbol} after attempting all chunks.")
            
            # Remove duplicates just in case, based on timestamp
            df = pd.DataFrame(all_ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df.drop_duplicates(subset=['timestamp'], keep='first', inplace=True)
            
            # Ensure we don't have more than requested due to chunking logic / API returning more
            if len(df) > total_candles_needed:
                df = df.iloc[-total_candles_needed:]

            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Convert to float
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = df[col].astype(float)
            
            self.logger.info(f"✅ Successfully fetched {len(df)} {timeframe} candles for {symbol}")
            if not df.empty:
                self.logger.info(f"   Period: {df.index[0]} to {df.index[-1]}")
                self.logger.info(f"   Price range: ${df['low'].min():,.2f} - ${df['high'].max():,.2f}")
            
            # Cache the data
            cache_key = f"{symbol}_{timeframe}_{days}d" # Make cache key more specific
            self.data_cache[cache_key] = df
            self.last_update[cache_key] = datetime.now()
            
            return df
            
        except Exception as e:
            self.logger.error(f"Failed to fetch historical data for {symbol}: {e}")
            
            # Return sample data as fallback
            return self._generate_fallback_data(symbol, days)
    
    def _generate_fallback_data(self, symbol: str, days: int) -> pd.DataFrame:
        """Generate fallback data when API fails"""
        self.logger.warning(f"Generating fallback data for {symbol}")
        
        # Generate realistic price data
        periods = days * 24 * 60  # 1-minute data
        dates = pd.date_range(end=datetime.now(), periods=periods, freq='1min')
        
        # Base prices
        base_prices = {'BTC/USDT': 50000, 'ETH/USDT': 3200}
        base_price = base_prices.get(symbol, 50000)
        
        # Generate price series with realistic volatility
        np.random.seed(42)
        returns = np.random.normal(0, 0.001, periods)  # 0.1% volatility per minute
        prices = [base_price]
        
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(new_price)
        
        # Create OHLCV data
        df = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.0005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.0005))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(100, 1000, periods)
        }, index=dates)
        
        return df
    
    async def get_current_market_data(self) -> Dict[str, pd.DataFrame]:
        """Get current market data for BTC and ETH"""
        try:
            self.logger.info("📊 Fetching current market data...")
            
            # Fetch BTC data
            btc_data = await self.get_historical_data('BTC/USDT', '1m', 1)
            
            # Fetch ETH data
            eth_data = await self.get_historical_data('ETH/USDT', '1m', 1)
            
            # Get real-time prices
            btc_price = await self.get_real_time_price('BTC/USDT')
            eth_price = await self.get_real_time_price('ETH/USDT')
            
            self.logger.info(f"✅ Current prices: BTC ${btc_price:,.2f}, ETH ${eth_price:,.2f}")
            
            return {
                'BTC': btc_data,
                'ETH': eth_data,
                'current_prices': {
                    'BTC': btc_price,
                    'ETH': eth_price
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get current market data: {e}")
            return {}
    
    async def get_training_data(self, days: int = 90) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Get training data for ML models (90 days of 1-minute data as per spec)"""
        try:
            self.logger.info(f"📈 Fetching {days} days of 1-minute training data...")
            
            # Fetch 1-minute data for training
            btc_data = await self.get_historical_data('BTC/USDT', '1m', days)
            eth_data = await self.get_historical_data('ETH/USDT', '1m', days)
            
            self.logger.info(f"✅ Training data ready:")
            if not btc_data.empty:
                self.logger.info(f"   BTC: {len(btc_data)} 1-minute candles from {btc_data.index[0]} to {btc_data.index[-1]}")
            else:
                self.logger.warning("   BTC: No data fetched.")
            
            if not eth_data.empty:
                self.logger.info(f"   ETH: {len(eth_data)} 1-minute candles from {eth_data.index[0]} to {eth_data.index[-1]}")
            else:
                self.logger.warning("   ETH: No data fetched.")
                
            return btc_data, eth_data
            
        except Exception as e:
            self.logger.error(f"Failed to get training data: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def get_cached_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """Get cached data if available and recent"""
        cache_key = f"{symbol}_{timeframe}"
        
        if cache_key in self.data_cache:
            last_update = self.last_update.get(cache_key)
            if last_update and (datetime.now() - last_update).seconds < 60:  # 1 minute cache
                return self.data_cache[cache_key]
        
        return None
    
    async def test_connection(self) -> bool:
        """Test Binance connection"""
        try:
            # Test public endpoint
            ticker = self.exchange.fetch_ticker('BTC/USDT')
            self.logger.info(f"✅ Public API test successful: BTC ${ticker['last']:,.2f}")
            
            # Test private endpoint if credentials available
            api_key = getattr(self.config, 'BINANCE_API_KEY', os.getenv('BINANCE_API_KEY', ''))
            if api_key:
                try:
                    balance = self.exchange.fetch_balance()
                    self.logger.info(f"✅ Private API test successful")
                    return True
                except Exception as e:
                    self.logger.warning(f"⚠️ Private API test failed: {e}")
                    return False
            else:
                self.logger.info("ℹ️ No API key provided - public access only")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Connection test failed: {e}")
            return False
    
    def get_account_info(self) -> Dict:
        """Get account information"""
        try:
            api_key = getattr(self.config, 'BINANCE_API_KEY', os.getenv('BINANCE_API_KEY', ''))
            if not api_key:
                return {'error': 'No API key provided'}
            
            balance = self.exchange.fetch_balance()
            
            # Extract relevant information
            account_info = {
                'account_type': balance.get('info', {}).get('accountType', 'Unknown'),
                'can_trade': balance.get('info', {}).get('canTrade', False),
                'can_withdraw': balance.get('info', {}).get('canWithdraw', False),
                'can_deposit': balance.get('info', {}).get('canDeposit', False),
                'balances': {}
            }
            
            # Get non-zero balances
            for currency, balance_info in balance.items():
                if currency not in ['info', 'free', 'used', 'total'] and isinstance(balance_info, dict):
                    total = balance_info.get('total', 0)
                    if total > 0:
                        account_info['balances'][currency] = {
                            'free': balance_info.get('free', 0),
                            'used': balance_info.get('used', 0),
                            'total': total
                        }
            
            return account_info
            
        except Exception as e:
            self.logger.error(f"Failed to get account info: {e}")
            return {'error': str(e)}

async def test_binance_data_collector():
    """Test the Binance data collector"""
    
    print("🧪 TESTING BINANCE DATA COLLECTOR")
    print("=" * 50)
    
    # Initialize
    config = TradingConfig()
    collector = BinanceDataCollector(config)
    
    # Test connection
    print("\n🔗 Testing connection...")
    connection_ok = await collector.test_connection()
    
    if connection_ok:
        print("✅ Connection successful")
        
        # Test real-time price
        print("\n💰 Testing real-time prices...")
        btc_price = await collector.get_real_time_price('BTC/USDT')
        eth_price = await collector.get_real_time_price('ETH/USDT')
        print(f"   BTC: ${btc_price:,.2f}")
        print(f"   ETH: ${eth_price:,.2f}")
        
        # Test historical data
        print("\n📊 Testing historical data...")
        btc_data = await collector.get_historical_data('BTC/USDT', '1h', 1)
        print(f"   BTC data: {len(btc_data)} candles")
        print(f"   Latest close: ${btc_data['close'].iloc[-1]:,.2f}")
        
        # Test account info
        print("\n👤 Testing account info...")
        account_info = collector.get_account_info()
        if 'error' not in account_info:
            print(f"   Account type: {account_info.get('account_type', 'Unknown')}")
            print(f"   Can trade: {account_info.get('can_trade', False)}")
            print(f"   Balances: {len(account_info.get('balances', {}))}")
        else:
            print(f"   Account info: {account_info['error']}")
        
        print("\n✅ All tests completed successfully!")
        
    else:
        print("❌ Connection failed")

if __name__ == "__main__":
    asyncio.run(test_binance_data_collector())
