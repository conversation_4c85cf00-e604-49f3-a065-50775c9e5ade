# Bitcoin Freedom Production Launcher
# PowerShell script to launch the Bitcoin Freedom trading webapp

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "BITCOIN FREEDOM PRODUCTION LAUNCHER" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Real Money Trading System" -ForegroundColor Green
Write-Host "Conservative Elite Model (93.2% Win Rate)" -ForegroundColor Green
Write-Host "Cross Margin Trading at 3x Leverage" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan

# Change to the script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptPath

Write-Host "`nActivating virtual environment..." -ForegroundColor Yellow

# Try to activate virtual environment
$venvPath = Join-Path (Split-Path -Parent $scriptPath) "venv\Scripts\Activate.ps1"
if (Test-Path $venvPath) {
    & $venvPath
    Write-Host "✅ Virtual environment activated" -ForegroundColor Green
} else {
    Write-Host "⚠️ Virtual environment not found, using system Python" -ForegroundColor Yellow
}

Write-Host "`nLaunching Bitcoin Freedom Production Webapp..." -ForegroundColor Yellow

# Try to launch the webapp
try {
    $pythonPath = Join-Path (Split-Path -Parent $scriptPath) "venv\Scripts\python.exe"
    if (Test-Path $pythonPath) {
        & $pythonPath "start_bitcoin_freedom.py"
    } else {
        python "start_bitcoin_freedom.py"
    }
} catch {
    Write-Host "❌ Error launching webapp: $_" -ForegroundColor Red
    Write-Host "`nPress any key to exit..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
