
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Retraining Report - 20250608_231418</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; font-size: 1.2em; opacity: 0.9; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .section h2 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea; }
        .metric-value { font-size: 1.8em; font-weight: bold; color: #667eea; }
        .metric-label { color: #666; font-size: 0.9em; margin-top: 5px; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background: #f8f9fa; font-weight: bold; }
        .highlight { background: #fff3cd; }
        .badge { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .badge-success { background: #d4edda; color: #155724; }
        .badge-warning { background: #fff3cd; color: #856404; }
        .badge-danger { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Advanced Retraining Report</h1>
            <p>Optimization Metric: Composite Score × Net Profit | Targets: >87% Win Rate, >80% Composite Score, 5+ Trades/Day</p>
            <p>Generated: 2025-06-08 23:14:18 | Duration: 1.1s</p>
        </div>

        <div class="section">
            <h2>🎯 Performance Targets & Achievement</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value danger">
                        0.0%
                    </div>
                    <div class="metric-label">Win Rate (Target: >87%)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value danger">
                        0.0%
                    </div>
                    <div class="metric-label">Composite Score (Target: >80%)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value danger">
                        0.0
                    </div>
                    <div class="metric-label">Trades/Day (Target: ≥5)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value success">
                        0.00
                    </div>
                    <div class="metric-label">Combined Score (Composite × Profit)</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🏆 Best Models Summary</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Model Type</th>
                        <th>Composite Score</th>
                        <th>Net Profit</th>
                        <th>Win Rate</th>
                        <th>Trades/Day</th>
                        <th>Combined Score</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="">
                        <td><strong>Best Composite</strong></td>
                        <td>0.0%</td>
                        <td>$0.00</td>
                        <td>0.0%</td>
                        <td>0.0</td>
                        <td>0.00</td>
                        <td><span class="badge badge-success">Saved</span></td>
                    </tr>
                    <tr class="">
                        <td><strong>Best Profit</strong></td>
                        <td>0.0%</td>
                        <td>$0.00</td>
                        <td>0.0%</td>
                        <td>0.0</td>
                        <td>0.00</td>
                        <td><span class="badge badge-success">Saved</span></td>
                    </tr>
                    <tr class="">
                        <td><strong>Best Combined</strong></td>
                        <td>0.0%</td>
                        <td>$0.00</td>
                        <td>0.0%</td>
                        <td>0.0</td>
                        <td>0.00</td>
                        <td><span class="badge badge-success">Saved</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🔄 Backtester Performance</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">0</div>
                    <div class="metric-label">Total Trades Validated</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">300.00</div>
                    <div class="metric-label">Current Balance</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">21</div>
                    <div class="metric-label">RL Feedback Records</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">20</div>
                    <div class="metric-label">Optimization Trials</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Hyperparameter Optimization History</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Trial</th>
                        <th>TCN Layers</th>
                        <th>TCN Filters</th>
                        <th>Dropout Rate</th>
                        <th>Learning Rate</th>
                        <th>Combined Score</th>
                        <th>Win Rate</th>
                        <th>Composite Score</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>11</td>
                        <td>3</td>
                        <td>32</td>
                        <td>0.3</td>
                        <td>1e-04</td>
                        <td>0.00</td>
                        <td>0.0%</td>
                        <td>0.0%</td>
                    </tr>
                    <tr>
                        <td>12</td>
                        <td>4</td>
                        <td>128</td>
                        <td>0.2</td>
                        <td>1e-04</td>
                        <td>0.00</td>
                        <td>0.0%</td>
                        <td>0.0%</td>
                    </tr>
                    <tr>
                        <td>13</td>
                        <td>4</td>
                        <td>32</td>
                        <td>0.2</td>
                        <td>3e-04</td>
                        <td>0.00</td>
                        <td>0.0%</td>
                        <td>0.0%</td>
                    </tr>
                    <tr>
                        <td>14</td>
                        <td>2</td>
                        <td>256</td>
                        <td>0.25</td>
                        <td>1e-03</td>
                        <td>0.00</td>
                        <td>0.0%</td>
                        <td>0.0%</td>
                    </tr>
                    <tr>
                        <td>15</td>
                        <td>5</td>
                        <td>128</td>
                        <td>0.15</td>
                        <td>1e-04</td>
                        <td>0.00</td>
                        <td>0.0%</td>
                        <td>0.0%</td>
                    </tr>
                    <tr>
                        <td>16</td>
                        <td>3</td>
                        <td>32</td>
                        <td>0.2</td>
                        <td>1e-05</td>
                        <td>0.00</td>
                        <td>0.0%</td>
                        <td>0.0%</td>
                    </tr>
                    <tr>
                        <td>17</td>
                        <td>3</td>
                        <td>256</td>
                        <td>0.15</td>
                        <td>1e-05</td>
                        <td>0.00</td>
                        <td>0.0%</td>
                        <td>0.0%</td>
                    </tr>
                    <tr>
                        <td>18</td>
                        <td>3</td>
                        <td>32</td>
                        <td>0.25</td>
                        <td>1e-05</td>
                        <td>0.00</td>
                        <td>0.0%</td>
                        <td>0.0%</td>
                    </tr>
                    <tr>
                        <td>19</td>
                        <td>3</td>
                        <td>256</td>
                        <td>0.15</td>
                        <td>3e-05</td>
                        <td>0.00</td>
                        <td>0.0%</td>
                        <td>0.0%</td>
                    </tr>
                    <tr>
                        <td>20</td>
                        <td>4</td>
                        <td>128</td>
                        <td>0.2</td>
                        <td>3e-04</td>
                        <td>0.00</td>
                        <td>0.0%</td>
                        <td>0.0%</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🔒 Locked Parameters</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">0.25%</div>
                    <div class="metric-label">Grid Spacing (LOCKED)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">2.5:1</div>
                    <div class="metric-label">Risk-Reward Ratio (LOCKED)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">1</div>
                    <div class="metric-label">Max Open Trades (LOCKED)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$300</div>
                    <div class="metric-label">Starting Balance (LOCKED)</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>✅ Retraining Summary</h2>
            <p><strong>Optimization Metric:</strong> Composite Score × Net Profit</p>
            <p><strong>All Results Validated Through:</strong> Integrated Backtester with RL Feedback</p>
            <p><strong>Models Saved:</strong> Best Composite, Best Profit, Best Combined</p>
            <p><strong>Next Steps:</strong> Deploy best model to live trading system</p>
        </div>
    </div>
</body>
</html>