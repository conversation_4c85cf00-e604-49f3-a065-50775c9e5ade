#!/usr/bin/env python3
"""
INTELLIGENT MARGIN SYSTEM TEST
=============================
Test the intelligent margin management system with your actual Binance account.
"""

import time
import requests
from datetime import datetime

def test_margin_system():
    """Test the intelligent margin management system."""
    print("🤖 TESTING INTELLIGENT MARGIN MANAGEMENT SYSTEM")
    print("=" * 60)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    base_url = "http://localhost:5000"
    
    # Test 1: Check if webapp is running
    print("📡 STEP 1: Testing webapp connectivity")
    print("-" * 40)
    try:
        response = requests.get(f"{base_url}/api/trading_status", timeout=5)
        if response.status_code == 200:
            print("✅ Webapp is running")
            data = response.json()
            print(f"   Trading Running: {data.get('is_running', False)}")
            print(f"   Live Mode: {data.get('is_live_mode', False)}")
        else:
            print(f"❌ Webapp error: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to webapp: {e}")
        print("   Make sure the webapp is running at http://localhost:5000")
        return False
    
    # Test 2: Check Binance connection
    print("\n🔗 STEP 2: Testing Binance connection")
    print("-" * 40)
    try:
        response = requests.get(f"{base_url}/api/binance_status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ CCXT Available: {data.get('ccxt_available', False)}")
            print(f"✅ Binance Connected: {data.get('connected', False)}")
            
            if data.get('account_balance'):
                balance = data['account_balance']
                print(f"   USDT Balance: ${balance.get('USDT', 0):.2f}")
                print(f"   BTC Balance: {balance.get('BTC', 0):.8f}")
        else:
            print(f"❌ Binance status error: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Binance status check failed: {e}")
    
    # Test 3: Test margin manager (if available)
    print("\n🤖 STEP 3: Testing Intelligent Margin Manager")
    print("-" * 40)
    try:
        response = requests.get(f"{base_url}/api/margin_status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            
            if data.get('margin_manager_active'):
                print("✅ Margin Manager: ACTIVE")
                print(f"   Margin Level: {data.get('margin_level', 0):.2f}")
                print(f"   Net Worth: ${data.get('total_net_usd', 0):.2f}")
                print(f"   BTC Price: ${data.get('btc_price', 0):.2f}")
                print(f"   Trading Status: {data.get('trading_status', 'UNKNOWN')}")
                print(f"   Risk Assessment: {data.get('risk_assessment', 'UNKNOWN')}")
                print(f"   Rebalancing Needed: {data.get('rebalancing_needed', False)}")
                
                # Show detailed status report
                if data.get('status_report'):
                    print("\n📋 DETAILED STATUS REPORT:")
                    print(data['status_report'])
            else:
                print("⚠️ Margin Manager: NOT ACTIVE")
                print("   Enable cross margin mode to activate intelligent management")
                print("   Message:", data.get('message', 'Unknown'))
        else:
            print(f"❌ Margin status error: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Margin status check failed: {e}")
    
    # Test 4: Demonstrate live mode activation
    print("\n🎮 STEP 4: Live Mode Activation Guide")
    print("-" * 40)
    print("To activate intelligent margin management:")
    print("1. Go to http://localhost:5000")
    print("2. Click 'Switch to Live Mode'")
    print("3. Choose option 3 (LIVE CROSS MARGIN)")
    print("4. Confirm the warnings")
    print("5. The intelligent margin manager will activate automatically")
    
    print("\n🔄 STEP 5: Automatic Features")
    print("-" * 40)
    print("When cross margin mode is active, the system will:")
    print("✅ Monitor margin level in real-time")
    print("✅ Automatically adjust position sizes based on risk")
    print("✅ Trigger rebalancing when needed")
    print("✅ Optimize leverage dynamically")
    print("✅ Provide risk assessments")
    print("✅ Execute emergency procedures if needed")
    
    print("\n📊 STEP 6: Risk Management Features")
    print("-" * 40)
    print("Intelligent risk adjustments:")
    print(f"• Margin Level ≥ 5.0: Risk multiplier 1.2x (aggressive)")
    print(f"• Margin Level ≥ 3.5: Risk multiplier 1.0x (normal)")
    print(f"• Margin Level ≥ 2.5: Risk multiplier 0.8x (cautious)")
    print(f"• Margin Level ≥ 2.0: Risk multiplier 0.5x (defensive)")
    print(f"• Margin Level < 2.0: Risk multiplier 0.2x (emergency)")
    
    print("\n⚠️ STEP 7: Current Recommendations")
    print("-" * 40)
    print("Based on your margin level of 1.44:")
    print("🚨 IMMEDIATE: Use SPOT trading only")
    print("🔧 REDUCE: Current margin positions to improve level")
    print("📊 TARGET: Get margin level above 3.0 before cross margin")
    print("🧪 TEST: Start with small amounts when ready")
    
    return True

def demonstrate_api_calls():
    """Demonstrate API calls for margin management."""
    print("\n🔧 API DEMONSTRATION")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    # Show how to check margin status programmatically
    print("📡 Checking margin status via API:")
    print("GET /api/margin_status")
    
    try:
        response = requests.get(f"{base_url}/api/margin_status")
        if response.status_code == 200:
            data = response.json()
            print("✅ Response received:")
            for key, value in data.items():
                if key != 'status_report':  # Skip long report for demo
                    print(f"   {key}: {value}")
        else:
            print(f"❌ API Error: {response.status_code}")
    except Exception as e:
        print(f"❌ API Call failed: {e}")
    
    print("\n📊 Other useful API endpoints:")
    print("• GET /api/trading_status - Trading engine status")
    print("• GET /api/binance_status - Binance connection status")
    print("• POST /api/toggle_live_mode - Switch trading modes")
    print("• POST /api/start_trading - Start trading engine")
    print("• POST /api/stop_trading - Stop trading engine")

def main():
    """Main test function."""
    print("🚀 BITCOIN FREEDOM INTELLIGENT MARGIN SYSTEM TEST")
    print("=" * 70)
    
    # Run comprehensive test
    success = test_margin_system()
    
    if success:
        print("\n✅ TEST COMPLETED SUCCESSFULLY")
        print("=" * 70)
        print("🎯 NEXT STEPS:")
        print("1. Review your current margin level (1.44 - HIGH RISK)")
        print("2. Consider reducing margin positions to improve safety")
        print("3. Test with SPOT trading first")
        print("4. Activate cross margin when margin level > 3.0")
        print("5. Monitor the intelligent margin manager in action")
        
        # Demonstrate API usage
        demonstrate_api_calls()
        
        print("\n🤖 INTELLIGENT FEATURES READY:")
        print("• Automatic position sizing based on margin level")
        print("• Real-time rebalancing when needed")
        print("• Dynamic risk adjustment")
        print("• Emergency protection protocols")
        print("• Comprehensive status monitoring")
        
    else:
        print("\n❌ TEST FAILED")
        print("Please ensure:")
        print("1. Webapp is running at http://localhost:5000")
        print("2. All dependencies are installed")
        print("3. API keys are properly configured")
    
    print("\n" + "=" * 70)
    print("🚀 Bitcoin Freedom Intelligent Margin System Ready!")
    print("=" * 70)

if __name__ == "__main__":
    main()
