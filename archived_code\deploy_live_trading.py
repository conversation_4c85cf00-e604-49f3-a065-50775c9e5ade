"""
Deploy Grid Trading System for Live Trading
Final deployment script for real money trading with $300 capital
"""

import os
import sys
import logging
import asyncio
import json
from datetime import datetime
from typing import Dict, Optional

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import grid trading components
from grid_trade_manager import GridBinanceTradeManager
from advanced_ml_training_system import AdvancedMLTradingSystem
from grid_composite_metrics import GridCompositeMetrics

class LiveTradingDeployment:
    """Manages live trading deployment with safety checks"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('LiveDeployment')
        
        # Initialize components
        self.ml_system = AdvancedMLTradingSystem(config)
        self.trade_manager = GridBinanceTradeManager(config)
        self.metrics_calculator = GridCompositeMetrics(config)
        
        # Deployment state
        self.deployment_active = False
        self.safety_checks_passed = False
        self.model_loaded = False
        
    async def deploy_live_trading(self) -> bool:
        """Deploy system for live trading with comprehensive safety checks"""
        
        print("🚀 GRID TRADING LIVE DEPLOYMENT")
        print("=" * 50)
        
        try:
            # Phase 1: Pre-deployment safety checks
            print("\n🔍 Phase 1: Safety Checks")
            if not await self._run_safety_checks():
                print("❌ Safety checks failed - deployment aborted")
                return False
            
            # Phase 2: Model validation
            print("\n🤖 Phase 2: Model Validation")
            if not await self._validate_model():
                print("❌ Model validation failed - deployment aborted")
                return False
            
            # Phase 3: Exchange connection
            print("\n🔗 Phase 3: Exchange Connection")
            if not await self._setup_exchange_connection():
                print("❌ Exchange connection failed - deployment aborted")
                return False
            
            # Phase 4: Final confirmation
            print("\n⚠️ Phase 4: Final Confirmation")
            if not self._get_final_confirmation():
                print("❌ Deployment cancelled by user")
                return False
            
            # Phase 5: Start live trading
            print("\n🎯 Phase 5: Starting Live Trading")
            if not await self._start_live_trading():
                print("❌ Failed to start live trading")
                return False
            
            print("\n✅ LIVE TRADING DEPLOYMENT SUCCESSFUL!")
            print("🎉 Grid trading system is now active with real money!")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Deployment failed: {e}")
            print(f"❌ DEPLOYMENT FAILED: {e}")
            return False
    
    async def _run_safety_checks(self) -> bool:
        """Run comprehensive safety checks"""
        
        checks = [
            ("Configuration Validation", self._check_configuration),
            ("API Keys Present", self._check_api_keys),
            ("Capital Verification", self._check_capital),
            ("Risk Parameters", self._check_risk_parameters),
            ("Model Availability", self._check_model_availability),
            ("System Dependencies", self._check_dependencies)
        ]
        
        all_passed = True
        
        for check_name, check_func in checks:
            print(f"   🔍 {check_name}...", end=" ")
            try:
                result = await check_func() if asyncio.iscoroutinefunction(check_func) else check_func()
                if result:
                    print("✅")
                else:
                    print("❌")
                    all_passed = False
            except Exception as e:
                print(f"❌ Error: {e}")
                all_passed = False
        
        self.safety_checks_passed = all_passed
        return all_passed
    
    def _check_configuration(self) -> bool:
        """Verify configuration matches specification"""
        
        required_values = {
            'INITIAL_CAPITAL': 300,
            'FIXED_RISK_AMOUNT': 10,
            'TARGET_PROFIT': 20,
            'GRID_SPACING': 0.0025,
            'MAX_CONCURRENT_TRADES': 15,
            'COMPOSITE_SCORE_THRESHOLD': 0.85
        }
        
        for attr, expected in required_values.items():
            actual = getattr(self.config, attr, None)
            if actual != expected:
                self.logger.error(f"Config mismatch: {attr} = {actual}, expected {expected}")
                return False
        
        return True
    
    def _check_api_keys(self) -> bool:
        """Check if Binance API keys are configured"""
        
        api_key = getattr(self.config, 'BINANCE_API_KEY', None)
        secret_key = getattr(self.config, 'BINANCE_SECRET_KEY', None)
        
        if not api_key or not secret_key:
            self.logger.error("Binance API keys not configured")
            return False
        
        if len(api_key) < 20 or len(secret_key) < 20:
            self.logger.error("API keys appear to be invalid")
            return False
        
        return True
    
    def _check_capital(self) -> bool:
        """Verify starting capital is correct"""
        
        if self.config.INITIAL_CAPITAL != 300:
            self.logger.error(f"Capital should be $300, got ${self.config.INITIAL_CAPITAL}")
            return False
        
        # Check risk management
        max_risk = self.config.MAX_CONCURRENT_TRADES * self.config.FIXED_RISK_AMOUNT
        if max_risk > self.config.INITIAL_CAPITAL * 0.6:  # Max 60% at risk
            self.logger.error(f"Risk too high: ${max_risk} vs ${self.config.INITIAL_CAPITAL}")
            return False
        
        return True
    
    def _check_risk_parameters(self) -> bool:
        """Verify risk parameters are safe"""
        
        # Check risk/reward ratio
        if self.config.TARGET_PROFIT / self.config.FIXED_RISK_AMOUNT != 2.0:
            self.logger.error("Risk/reward ratio should be 2:1")
            return False
        
        # Check grid spacing
        if self.config.GRID_SPACING != 0.0025:
            self.logger.error("Grid spacing should be exactly 0.25%")
            return False
        
        return True
    
    def _check_model_availability(self) -> bool:
        """Check if trained model is available"""
        
        deployment_model = self.ml_system.get_deployment_model()
        
        if deployment_model.get('model') is None:
            self.logger.error("No trained model available for deployment")
            return False
        
        # Log model info
        score = deployment_model.get('score', 0)
        profit = deployment_model.get('net_profit', 0)
        
        self.logger.info(f"Model ready: Score {score:.4f}, Profit ${profit:.2f}")
        
        return True
    
    def _check_dependencies(self) -> bool:
        """Check system dependencies"""
        
        try:
            import ccxt
            import pandas as pd
            import numpy as np
            return True
        except ImportError as e:
            self.logger.error(f"Missing dependency: {e}")
            return False
    
    async def _validate_model(self) -> bool:
        """Validate model performance and readiness"""
        
        deployment_model = self.ml_system.get_deployment_model()
        
        if deployment_model.get('model') is None:
            return False
        
        score = deployment_model.get('score', 0)
        profit = deployment_model.get('net_profit', 0)
        
        print(f"   📊 Model Performance:")
        print(f"      Composite Score: {score:.4f}")
        print(f"      Net Profit: ${profit:.2f}")
        print(f"      Symbol: {deployment_model.get('symbol', 'Unknown')}")
        
        # Check if model meets threshold
        if score >= self.config.COMPOSITE_SCORE_THRESHOLD:
            print(f"   ✅ Model meets 85% threshold")
        else:
            print(f"   ⚠️ Model below 85% threshold - using best available")
            
            # Additional confirmation for sub-threshold models
            confirm = input("   Continue with sub-threshold model? (yes/no): ").lower()
            if confirm != 'yes':
                return False
        
        self.model_loaded = True
        return True
    
    async def _setup_exchange_connection(self) -> bool:
        """Setup and test exchange connection"""
        
        try:
            # Test connection
            if not await self.trade_manager._test_connection():
                return False
            
            # Get account info
            balance = await self.trade_manager._get_current_price('BTC/USDT')
            if balance <= 0:
                self.logger.error("Could not get BTC price")
                return False
            
            print(f"   ✅ Exchange connected - BTC Price: ${balance:,.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Exchange connection failed: {e}")
            return False
    
    def _get_final_confirmation(self) -> bool:
        """Get final user confirmation for live trading"""
        
        print("\n⚠️ FINAL CONFIRMATION REQUIRED")
        print("=" * 40)
        print("You are about to start LIVE TRADING with REAL MONEY!")
        print(f"Starting Capital: ${self.config.INITIAL_CAPITAL}")
        print(f"Risk per Trade: ${self.config.FIXED_RISK_AMOUNT}")
        print(f"Maximum Risk: ${self.config.MAX_CONCURRENT_TRADES * self.config.FIXED_RISK_AMOUNT}")
        print("Grid Trading: 0.25% spacing with ML decisions")
        print("\nThis system will:")
        print("- Execute real trades on Binance")
        print("- Risk real money on each trade")
        print("- Operate autonomously based on ML predictions")
        print("- Continue trading until manually stopped")
        
        print("\n🚨 WARNING: Trading involves risk of loss!")
        print("Only trade with money you can afford to lose.")
        
        # Triple confirmation
        confirm1 = input("\nType 'CONFIRM' to proceed with live trading: ").strip()
        if confirm1 != 'CONFIRM':
            return False
        
        confirm2 = input("Type 'LIVE' to confirm real money trading: ").strip()
        if confirm2 != 'LIVE':
            return False
        
        confirm3 = input("Type 'START' to begin grid trading: ").strip()
        if confirm3 != 'START':
            return False
        
        return True
    
    async def _start_live_trading(self) -> bool:
        """Start the live trading system"""
        
        try:
            # Load the best model
            deployment_model = self.ml_system.get_deployment_model()
            ml_model = deployment_model['model']
            
            # Start grid trading
            success = await self.trade_manager.start_grid_trading(ml_model)
            
            if success:
                self.deployment_active = True
                
                # Log deployment
                deployment_log = {
                    'timestamp': datetime.now().isoformat(),
                    'model_score': deployment_model.get('score', 0),
                    'model_profit': deployment_model.get('net_profit', 0),
                    'starting_capital': self.config.INITIAL_CAPITAL,
                    'risk_per_trade': self.config.FIXED_RISK_AMOUNT,
                    'grid_spacing': self.config.GRID_SPACING,
                    'max_trades': self.config.MAX_CONCURRENT_TRADES
                }
                
                with open('logs/live_deployment.json', 'w') as f:
                    json.dump(deployment_log, f, indent=2)
                
                print(f"   ✅ Live trading started successfully!")
                print(f"   📊 Monitoring dashboard: python grid_dashboard.py")
                print(f"   📁 Logs: logs/live_deployment.json")
                
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to start live trading: {e}")
            return False
    
    def get_deployment_status(self) -> Dict:
        """Get current deployment status"""
        
        return {
            'deployment_active': self.deployment_active,
            'safety_checks_passed': self.safety_checks_passed,
            'model_loaded': self.model_loaded,
            'trading_status': self.trade_manager.get_trading_status() if self.deployment_active else None
        }

async def main():
    """Main deployment function"""
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/live_deployment.log'),
            logging.StreamHandler()
        ]
    )
    
    # Ensure directories exist
    os.makedirs('logs', exist_ok=True)
    
    # Initialize config
    config = TradingConfig()
    
    # Create deployment manager
    deployment = LiveTradingDeployment(config)
    
    # Run deployment
    success = await deployment.deploy_live_trading()
    
    if success:
        print("\n🎉 DEPLOYMENT SUCCESSFUL!")
        print("Grid trading system is now live!")
        print("\nNext steps:")
        print("1. Monitor performance via dashboard")
        print("2. Check logs regularly")
        print("3. Be prepared to stop if needed")
        print("\nTo stop trading: Use dashboard or emergency stop")
        
        # Keep running to monitor
        try:
            while deployment.deployment_active:
                await asyncio.sleep(60)  # Check every minute
                status = deployment.get_deployment_status()
                print(f"Status check: {datetime.now().strftime('%H:%M:%S')} - Active: {status['deployment_active']}")
        except KeyboardInterrupt:
            print("\n🛑 Stopping live trading...")
            deployment.trade_manager.stop_grid_trading()
            
    else:
        print("\n❌ DEPLOYMENT FAILED!")
        print("Review the errors and try again.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
