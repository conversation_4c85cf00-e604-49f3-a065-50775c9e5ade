#!/usr/bin/env python3
"""
Simple check of the actual risk-reward ratio
"""

import sys
sys.path.append('.')

def main():
    print('🎯 CHECKING ACTUAL RISK-REWARD RATIO')
    print('=' * 60)

    try:
        from simple_trading_executor import SimpleTradingExecutor
        
        # Create executor
        executor = SimpleTradingExecutor()
        
        # Test signal
        test_signal = {
            'action': 'BUY',
            'price': 105000.0,
            'confidence': 0.85,
            'grid_level': 0
        }
        
        print(f'💹 Testing with: {test_signal["action"]} @ ${test_signal["price"]:,.2f}')
        
        # Execute test trade
        trade = executor.execute_trade(test_signal, risk_mode='fixed')
        
        if trade:
            risk_amount = trade.risk_amount
            profit_target = trade.profit_target
            ratio = profit_target / risk_amount
            
            print(f'\n📊 ACTUAL EXECUTION RESULTS:')
            print(f'   Risk Amount: ${risk_amount:.2f}')
            print(f'   Profit Target: ${profit_target:.2f}')
            print(f'   Risk:Reward Ratio: 1:{ratio:.1f}')
            
            if ratio == 2.5:
                print(f'   ✅ CONFIRMED: System uses 2.5:1 ratio')
            elif ratio == 1.0:
                print(f'   ⚠️  System uses 1:1 ratio')
            else:
                print(f'   🔍 System uses {ratio:.1f}:1 ratio')
                
            # Compare to grid spacing
            current_price = 105000.0
            grid_spacing = 0.0025
            grid_profit = current_price * grid_spacing
            
            print(f'\n🎯 COMPARISON TO GRID:')
            print(f'   Grid-based profit (0.25%): ${grid_profit:.2f}')
            print(f'   Fixed profit target: ${profit_target:.2f}')
            
            if profit_target > grid_profit:
                print(f'   ✅ Fixed target is HIGHER than grid spacing')
                print(f'   💰 {profit_target/grid_profit:.1f}x more profit than grid')
            else:
                print(f'   ⚠️  Fixed target matches or is lower than grid')
                
            print(f'\n💡 SUMMARY:')
            if ratio == 2.5:
                print(f'   • System uses 2.5:1 risk-reward (${risk_amount} risk, ${profit_target} profit)')
                print(f'   • This is BETTER than pure grid trading')
                print(f'   • Break-even win rate: 28.6%')
                print(f'   • Conservative Elite: 93.2% win rate')
                print(f'   • Massive profit edge!')
            elif ratio == 1.0:
                print(f'   • System uses 1:1 risk-reward (${risk_amount} risk, ${profit_target} profit)')
                print(f'   • This matches grid spacing approach')
                print(f'   • Break-even win rate: 50%')
                print(f'   • Conservative Elite: 93.2% win rate')
                print(f'   • Still very profitable!')
                
        else:
            print('❌ Failed to execute test trade')
            
    except Exception as e:
        print(f'❌ Test failed: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
