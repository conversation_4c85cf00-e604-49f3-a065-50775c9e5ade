"""
Grid Trading Feature Engineering
Implements ONLY the 4 required indicators: VWAP, RSI-5, ETH/BTC ratio, Bollinger Bands-20
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

class GridFeatureEngineering:
    """Feature engineering for grid trading system (strict 4-indicator compliance)"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('GridFeatureEngineering')
        
    def create_grid_features(self, btc_data: pd.DataFrame, eth_data: pd.DataFrame) -> pd.DataFrame:
        """Create features using only the 4 specified indicators"""
        
        if len(btc_data) < 50 or len(eth_data) < 50:
            raise ValueError("Insufficient data for feature engineering")
        
        # Ensure data is aligned by timestamp
        btc_data = btc_data.copy()
        eth_data = eth_data.copy()
        
        # Create feature dataframe
        features_df = btc_data[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # 1. VWAP (Volume Weighted Average Price)
        features_df = self._add_vwap_features(features_df)
        
        # 2. RSI 5-Period
        features_df = self._add_rsi_5_features(features_df)
        
        # 3. ETH/BTC Ratio
        features_df = self._add_eth_btc_ratio_features(features_df, btc_data, eth_data)
        
        # 4. Bollinger Bands 20-Period
        features_df = self._add_bollinger_bands_features(features_df)
        
        # Remove NaN values
        features_df = features_df.dropna()
        
        # Validate we have exactly the right features
        expected_features = [
            # VWAP (2 features)
            'vwap_position', 'vwap_distance',
            # RSI 5-Period (1 feature - spec implies only the RSI value itself, not normalized)
            'rsi_5',
            # ETH/BTC Ratio (1 feature - spec implies only the current ratio)
            'eth_btc_ratio',
            # Bollinger Bands 20-Period (3 features)
            'bb_position', 'bb_width', 'bb_squeeze'
        ]
        # Total of 2 + 1 + 1 + 3 = 7 features, not 9.
        # The spec says "ONLY 4 INPUT INDICATORS" and then lists sub-features for each.
        # VWAP: "Price position relative to VWAP", "Distance from VWAP" (2 features)
        # RSI 5-Period: "5-period Relative Strength Index" (1 feature)
        # ETH/BTC Ratio: "Current ETH/BTC price ratio" (1 feature)
        # Bollinger Bands 20-Period: "Price position within bands", "Band width", "Band squeeze detection" (3 features)

        feature_columns = [col for col in features_df.columns 
                          if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        self.logger.info(f"Created {len(feature_columns)} features: {feature_columns}")
        
        # Select only the 7 specified features
        final_features_df = features_df[expected_features].copy()
        
        self.logger.info(f"Selected {len(final_features_df.columns)} features as per spec: {final_features_df.columns.tolist()}")

        return final_features_df
    
    def _add_vwap_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add VWAP (Volume Weighted Average Price) features"""
        
        # Calculate VWAP
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        # Ensure cumsum starts fresh for each call if data is chunked, though here it's on the whole df
        vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum() 
        
        # Feature 1: Price position relative to VWAP (as a ratio/percentage)
        df['vwap_position'] = (df['close'] - vwap) / vwap 
        
        # Feature 2: Distance from VWAP (absolute value of the ratio)
        df['vwap_distance'] = abs(df['vwap_position'])
        
        self.logger.debug("Added VWAP features: vwap_position, vwap_distance")
        return df
    
    def _add_rsi_5_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add RSI 5-period features"""
        
        # Calculate 5-period RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=5).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=5).mean()
        
        rs = gain / loss
        rsi_5 = 100 - (100 / (1 + rs))
        
        # Feature 3: Raw RSI value (0-100) - as per spec
        df['rsi_5'] = rsi_5
        
        # Removed 'rsi_5_normalized' as spec implies only one RSI feature
        # df['rsi_5_normalized'] = rsi_5 / 100 
        
        self.logger.debug("Added RSI-5 feature: rsi_5")
        return df
    
    def _add_eth_btc_ratio_features(self, df: pd.DataFrame, 
                                   btc_data: pd.DataFrame, 
                                   eth_data: pd.DataFrame) -> pd.DataFrame:
        """Add ETH/BTC ratio features"""
        
        try:
            # Align timestamps - df is based on btc_data, so its index is the primary one
            # Use reindex with method='ffill' to ensure all timestamps in df have a corresponding price
            btc_close_aligned = btc_data['close'].reindex(df.index, method='ffill')
            eth_close_aligned = eth_data['close'].reindex(df.index, method='ffill')

            # Calculate ETH/BTC ratio
            eth_btc_ratio = eth_close_aligned / btc_close_aligned
            
            # Feature 4: Current ETH/BTC ratio - as per spec
            df['eth_btc_ratio'] = eth_btc_ratio
            
            # Removed 'eth_btc_ratio_change' as spec implies only one ETH/BTC ratio feature
            # df['eth_btc_ratio_change'] = eth_btc_ratio.pct_change(5) 
            
            self.logger.debug("Added ETH/BTC ratio feature: eth_btc_ratio")
            
        except Exception as e:
            self.logger.warning(f"Failed to calculate ETH/BTC ratio: {e}")
            # Fallback: use dummy values
            df['eth_btc_ratio'] = 0.065  # Typical ETH/BTC ratio
            # df['eth_btc_ratio_change'] = 0.0 # Removed
        
        return df
    
    def _add_bollinger_bands_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add Bollinger Bands 20-period features"""
        
        # Calculate 20-period Bollinger Bands
        sma_20 = df['close'].rolling(window=20).mean()
        std_20 = df['close'].rolling(window=20).std()
        
        bb_upper = sma_20 + (2 * std_20)
        bb_lower = sma_20 - (2 * std_20)
        
        # Feature 7: Position within bands (0 = lower band, 1 = upper band)
        df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
        
        # Feature 8: Band width (volatility measure)
        df['bb_width'] = (bb_upper - bb_lower) / sma_20
        
        # Feature 9: Squeeze detection (bands contracting)
        bb_width_avg = df['bb_width'].rolling(window=20).mean()
        df['bb_squeeze'] = (df['bb_width'] < bb_width_avg).astype(int)
        
        self.logger.debug("Added Bollinger Bands features: bb_position, bb_width, bb_squeeze")
        return df
    
    def prepare_grid_training_data(self, features_df: pd.DataFrame, 
                                  grid_levels: Dict, # This seems unused based on current logic
                                  price_history: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare training data for grid trading decisions"""
        
        # The features_df passed here should already be the one with exactly 7 features
        # Create grid-specific labels based on future price movement relative to grid spacing
        # The 'grid_levels' argument might be intended for a more complex labeling,
        # but current _create_grid_labels uses a simpler approach.
        labeled_df = self._create_grid_labels(features_df.copy(), self.config.GRID_SPACING, price_history) # Pass grid_spacing
        
        # Get feature columns (exactly the 7 specified)
        feature_columns = [
            'vwap_position', 'vwap_distance',
            'rsi_5',
            'eth_btc_ratio',
            'bb_position', 'bb_width', 'bb_squeeze'
        ]
        
        # Validate all features exist in the dataframe passed to this function
        missing_features = [col for col in feature_columns if col not in labeled_df.columns]
        if missing_features:
            raise ValueError(f"Missing features in labeled_df: {missing_features}. Available: {labeled_df.columns.tolist()}")
        
        X = labeled_df[feature_columns].values
        y = labeled_df['grid_action'].values # Ensure 'grid_action' is created by _create_grid_labels
        
        # Remove rows with NaN in features or labels
        # This should ideally be handled by ensuring features_df passed in is already cleaned,
        # or that _create_grid_labels doesn't introduce NaNs in 'grid_action'.
        mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
        X = X[mask]
        y = y[mask]
        
        self.logger.info(f"Prepared grid training data: {X.shape[0]} samples, {X.shape[1]} features")
        self.logger.info(f"Feature columns used for X: {feature_columns}")
        
        return X, y
    
    def _create_grid_labels(self, features_df: pd.DataFrame, 
                           grid_spacing: float, # Changed from grid_levels dict
                           price_history: pd.DataFrame) -> pd.DataFrame:
        """Create labels for grid trading actions (BUY/SELL/HOLD)
           based on future price movement relative to a fixed grid_spacing.
        """
        
        # Initialize with HOLD action (0)
        features_df['grid_action'] = 0  # 0 = HOLD, 1 = BUY, -1 = SELL
        
        # Ensure price_history is aligned with features_df index for lookahead
        # Assuming features_df is derived from btc_data, its index is the reference.
        # price_history['close'] should be available for these timestamps.
        aligned_close_prices = price_history['close'].reindex(features_df.index, method='ffill')

        # For each price point, determine optimal action based on future price movement
        # Look ahead by one period (e.g., 1 minute if data is 1-minute frequency)
        for i in range(len(features_df) - 1): # Avoid index out of bounds for the last element
            current_price = aligned_close_prices.iloc[i]
            future_price = aligned_close_prices.iloc[i + 1]
            
            if pd.isna(current_price) or pd.isna(future_price):
                features_df['grid_action'].iloc[i] = 0 # Default to HOLD if data is missing
                continue

            price_change_percentage = (future_price - current_price) / current_price
            
            # Determine optimal action based on grid spacing (0.25% from spec)
            # If price moves more than half the grid spacing, consider it a signal.
            # This is a simplified labeling strategy. True optimal action for grid trading
            # would depend on maximizing the composite reward over a longer horizon.
            
            if price_change_percentage > (grid_spacing / 2):  # Price likely to move to next upper grid
                features_df['grid_action'].iloc[i] = 1  # BUY
            elif price_change_percentage < -(grid_spacing / 2):  # Price likely to move to next lower grid
                features_df['grid_action'].iloc[i] = -1  # SELL
            else:
                features_df['grid_action'].iloc[i] = 0  # HOLD
        
        # Last element will remain HOLD due to loop range, which is acceptable.
        return features_df
    
    def get_current_features(self, btc_data: pd.DataFrame, eth_data: pd.DataFrame) -> np.ndarray:
        """Get current feature vector for real-time prediction"""
        
        # Create features for current data (will select the 7 specified ones)
        current_features_df = self.create_grid_features(btc_data, eth_data)
        
        if current_features_df.empty:
            self.logger.warning("No features could be created from current data. Returning zeros.")
            # Return a zero vector of the expected shape (1, 7)
            return np.zeros((1, 7))
            
        # Get latest feature values (already selected and ordered by create_grid_features)
        latest_features_values = current_features_df.iloc[-1].values
        
        # Check for NaN values
        if np.isnan(latest_features_values).any():
            self.logger.warning("NaN values in current features, using np.nan_to_num to replace with 0.0")
            latest_features_values = np.nan_to_num(latest_features_values, nan=0.0)
        
        self.logger.debug(f"Current features: {dict(zip(current_features_df.columns, latest_features_values))}")
        
        return latest_features_values.reshape(1, -1)  # Reshape for model input (1 sample, 7 features)
    
    def validate_features(self, features_df: pd.DataFrame) -> bool:
        """Validate that features_df contains exactly the 7 specified features."""
        
        # These are the 7 features that should be in the DataFrame after create_grid_features
        required_features = [
            'vwap_position', 'vwap_distance', # VWAP (2)
            'rsi_5',                         # RSI (1)
            'eth_btc_ratio',                 # ETH/BTC Ratio (1)
            'bb_position', 'bb_width', 'bb_squeeze' # Bollinger Bands (3)
        ]
        
        # The features_df passed here should *only* contain these columns.
        # It should not contain ohlcv, grid_action, or other intermediate columns.
        
        current_feature_columns = features_df.columns.tolist()
        
        if set(current_feature_columns) == set(required_features) and len(current_feature_columns) == len(required_features):
            # Additionally, check order if it matters for the model, though usually models are robust to column order if named.
            # For now, just checking presence and count.
            self.logger.info(f"✅ Features validated: DataFrame contains exactly the {len(required_features)} specified features: {required_features}")
            return True
        else:
            self.logger.error(f"Feature mismatch during validation!")
            self.logger.error(f"Required ({len(required_features)}): {sorted(required_features)}")
            self.logger.error(f"Found ({len(current_feature_columns)}): {sorted(current_feature_columns)}")
            # Detailed diff
            missing_in_df = set(required_features) - set(current_feature_columns)
            extra_in_df = set(current_feature_columns) - set(required_features)
            if missing_in_df:
                self.logger.error(f"Missing from DataFrame: {missing_in_df}")
            if extra_in_df:
                self.logger.error(f"Extra in DataFrame: {extra_in_df}")
            return False

# Example Usage (for testing)
# Ensure trading_config.py exists in a 'config' subdirectory or adjust path
async def test_feature_engineering():
    from binance_data_collector import BinanceDataCollector # Assuming this is in the same directory or accessible

    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("FeatureTest")

    logger.info("🧪 TESTING FEATURE ENGINEERING")
    logger.info("=" * 50)

    # 0. Setup Config and Data Collector
    try:
        config = TradingConfig() # Assumes trading_config.py is accessible
        config.GRID_SPACING = 0.0025 # Explicitly set for the test
    except ImportError:
        logger.error("Failed to import TradingConfig. Make sure config/trading_config.py exists.")
        # Create a dummy config for testing if not found
        class DummyConfig:
            GRID_SPACING = 0.0025
            # Add any other attributes GridFeatureEngineering might access from config
        config = DummyConfig()
        logger.info("Using dummy config for testing.")

    collector = BinanceDataCollector(config) # Requires TradingConfig instance

    # 1. Fetch sample data (e.g., 3 days of 1-minute data for testing features)
    logger.info("\\n📥 Fetching sample market data...")
    # Use a small amount of data for faster testing, e.g., 3 days
    # The feature engineering itself needs about 50 periods for some rolling calculations.
    # Bollinger Bands need 20 periods, RSI 5 needs 5. VWAP is cumulative.
    # Let's fetch 3 days to be safe, which is 3 * 24 * 60 = 4320 candles.
    # The get_historical_data in collector is now paginated.
    try:
        btc_hist_data = await collector.get_historical_data('BTC/USDT', '1m', days=3)
        eth_hist_data = await collector.get_historical_data('ETH/USDT', '1m', days=3)
    except Exception as e:
        logger.error(f"Error fetching data: {e}")
        logger.error("Cannot proceed with feature engineering test without data.")
        return

    if btc_hist_data.empty or eth_hist_data.empty:
        logger.error("Fetched data is empty. Cannot proceed.")
        return
    
    logger.info(f"   BTC data: {len(btc_hist_data)} candles, ETH data: {len(eth_hist_data)} candles")
    logger.info(f"   BTC data period: {btc_hist_data.index.min()} to {btc_hist_data.index.max()}")
    logger.info(f"   ETH data period: {eth_hist_data.index.min()} to {eth_hist_data.index.max()}")

    # 2. Initialize Feature Engineering
    feature_engineer = GridFeatureEngineering(config)

    # 3. Create Features
    logger.info("\\n⚙️ Creating features...")
    try:
        # Ensure enough data for all lookback periods (e.g., min 20 for BB, 5 for RSI)
        # The create_grid_features method itself checks for len < 50.
        if len(btc_hist_data) < 50 or len(eth_hist_data) < 50:
             logger.error("Not enough historical data to create features (min 50 periods required).")
             return

        features = feature_engineer.create_grid_features(btc_hist_data, eth_hist_data)
        logger.info(f"   Successfully created features DataFrame with shape: {features.shape}")
        logger.info(f"   Feature columns: {features.columns.tolist()}")
        logger.info(f"   First 5 rows of features:\\n{features.head()}")
        logger.info(f"   Last 5 rows of features:\\n{features.tail()}")

        # 4. Validate Features
        logger.info("\\n✔️ Validating features...")
        is_valid = feature_engineer.validate_features(features)
        if is_valid:
            logger.info("   ✅ Feature set is valid as per specification (7 features).")
        else:
            logger.error("   ❌ Feature set is NOT valid.")
            return # Stop if features are not valid

        # 5. Prepare Training Data (Example)
        # This requires 'grid_levels' and 'price_history'.
        # For this test, 'price_history' can be btc_hist_data.
        # 'grid_levels' is not directly used by the revised _create_grid_labels,
        # but the method signature still has it. We pass an empty dict.
        logger.info("\\n🧠 Preparing training data (example)...")
        # Ensure features df is not empty and has enough data after NaNs from feature creation
        if features.empty or len(features) < 2: # Need at least 2 for label creation lookahead
            logger.error("Features DataFrame is empty or too short after creation/NaN drop. Cannot prepare training data.")
            return

        X, y = feature_engineer.prepare_grid_training_data(features, {}, btc_hist_data)
        logger.info(f"   Prepared training data shapes: X={X.shape}, y={y.shape}")
        if X.shape[0] > 0:
            logger.info(f"   Example X[0]: {X[0]}")
            logger.info(f"   Example y[0]: {y[0]}")
            logger.info(f"   Unique y values: {np.unique(y, return_counts=True)}")

        # 6. Get Current Features (Example - using last part of historical as 'current')
        logger.info("\\n⚡ Getting current features (example)...")
        # Simulate current data by taking the last N rows needed for feature calculation
        # e.g., last 50 rows of btc_hist_data and eth_hist_data
        if len(btc_hist_data) >= 50 and len(eth_hist_data) >= 50:
            current_btc_data = btc_hist_data.iloc[-50:]
            current_eth_data = eth_hist_data.iloc[-50:]
            current_f = feature_engineer.get_current_features(current_btc_data, current_eth_data)
            logger.info(f"   Current feature vector (shape {current_f.shape}):\\n{current_f}")
        else:
            logger.warning("   Not enough data to simulate get_current_features properly.")

        logger.info("\\n✅ Feature engineering tests completed.")

    except ValueError as ve:
        logger.error(f"ValueError during feature engineering: {ve}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}", exc_info=True)

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_feature_engineering())
