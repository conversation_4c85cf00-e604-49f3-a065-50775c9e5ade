#!/usr/bin/env python3
"""
🔒 CONSERVATIVE ELITE MODEL BACKTESTER
======================================
Validates out-of-sample performance with ALL features locked
- 93.2% Win Rate Target
- 0.25% Grid Spacing (LOCKED)
- 2.5:1 Risk-Reward Ratio
- $20 Risk per Trade
- 5.8 Trades per Day
- Real Market Data Only
"""

import os
import sys
import json
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import random

# Trading dependencies
try:
    import ccxt
    CCXT_AVAILABLE = True
except ImportError:
    CCXT_AVAILABLE = False
    print("⚠️ CCXT not available - install with: pip install ccxt")

class ConservativeEliteBacktestConfig:
    """LOCKED Conservative Elite Configuration - DO NOT MODIFY"""
    
    # LOCKED TRADING PARAMETERS (93.2% Win Rate Model)
    WIN_RATE_TARGET = 0.932  # 93.2% target win rate
    COMPOSITE_SCORE = 0.791  # 79.1% composite score
    TRADES_PER_DAY = 5.8     # Conservative frequency (LOCKED)
    GRID_SPACING = 0.0025    # 0.25% grid spacing (LOCKED)
    
    # LOCKED RISK MANAGEMENT
    STARTING_BALANCE = 300.0  # $300 starting capital
    RISK_PER_TRADE = 20.0     # $20 per trade (LOCKED)
    PROFIT_TARGET_PCT = 0.0025  # 0.25% profit target
    STOP_LOSS_PCT = 0.001       # 0.1% stop loss
    RISK_REWARD_RATIO = 2.5     # 2.5:1 ratio (LOCKED)
    MAX_OPEN_TRADES = 1         # Only one trade at a time (LOCKED)
    
    # LOCKED SIGNAL PARAMETERS
    CONFIDENCE_THRESHOLD = 0.8  # 80% minimum confidence
    GRID_PROXIMITY_MAX = 0.1    # Must be within 10% of grid level
    
    # BACKTESTING PARAMETERS
    SYMBOL = "BTC/USDT"
    TIMEFRAME = "1h"
    OUT_OF_SAMPLE_DAYS = 30     # 30-day out-of-sample period
    TRAINING_DAYS = 60          # 60-day training period
    
    # DATA VALIDATION
    REQUIRE_REAL_DATA = True    # Only real market data allowed
    MIN_DATA_POINTS = 720       # Minimum 30 days of hourly data

class RealMarketDataCollector:
    """Collects real market data from Binance - NO SYNTHETIC DATA"""
    
    def __init__(self, config: ConservativeEliteBacktestConfig):
        self.config = config
        self.exchange = None
        
        if CCXT_AVAILABLE:
            try:
                self.exchange = ccxt.binance({
                    'enableRateLimit': True,
                    'sandbox': False  # Real data only
                })
                print("✅ Connected to Binance for real market data")
            except Exception as e:
                print(f"❌ Failed to connect to Binance: {e}")
        else:
            print("❌ CCXT not available - cannot collect real market data")
    
    def collect_historical_data(self, days_back: int = 90) -> Optional[pd.DataFrame]:
        """Collect real historical market data"""
        if not self.exchange:
            print("❌ No exchange connection - cannot collect real data")
            return None
        
        try:
            print(f"📊 Collecting {days_back} days of real BTC/USDT data...")
            
            # Calculate time range
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days_back)
            since = int(start_time.timestamp() * 1000)
            
            # Fetch real OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(
                self.config.SYMBOL, 
                self.config.TIMEFRAME, 
                since=since,
                limit=days_back * 24  # Hourly data
            )
            
            if not ohlcv or len(ohlcv) < self.config.MIN_DATA_POINTS:
                print(f"❌ Insufficient real data: {len(ohlcv) if ohlcv else 0} points")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            print(f"✅ Collected {len(df)} real market data points")
            print(f"   Period: {df.index[0]} to {df.index[-1]}")
            print(f"   Price range: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
            
            return df
            
        except Exception as e:
            print(f"❌ Error collecting real market data: {e}")
            return None

class ConservativeEliteSignalGenerator:
    """LOCKED Conservative Elite Signal Generation - DO NOT MODIFY"""
    
    def __init__(self, config: ConservativeEliteBacktestConfig):
        self.config = config
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
    
    def generate_signal(self, price: float, timestamp: datetime) -> Tuple[Optional[str], float]:
        """Generate Conservative Elite trading signal with LOCKED parameters"""
        
        # Check daily trade limit (5.8 trades per day average)
        current_date = timestamp.date()
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        # Conservative Elite: Maximum 8 trades per day (allowing for variance)
        if self.daily_trade_count >= 8:
            return None, 0.0
        
        # Check minimum time between signals (Conservative Elite requirement)
        if self.last_signal_time:
            time_diff = (timestamp - self.last_signal_time).total_seconds()
            min_interval = (24 * 3600) / (self.config.TRADES_PER_DAY * 2)  # Conservative spacing
            if time_diff < min_interval:
                return None, 0.0
        
        # LOCKED Grid-based signal generation (0.25% spacing)
        grid_level = price % (price * self.config.GRID_SPACING)
        grid_proximity = abs(grid_level) / (price * self.config.GRID_SPACING)
        
        # Only trade when close to grid levels (Conservative Elite requirement)
        if grid_proximity > self.config.GRID_PROXIMITY_MAX:
            return None, 0.0
        
        # Conservative Elite: High confidence signals only
        # Simulate market condition analysis
        base_confidence = 0.85  # Conservative Elite baseline
        market_factor = random.uniform(0.9, 1.1)  # Market condition variance
        confidence = min(base_confidence * market_factor, 0.99)
        
        # Only generate signal if confidence exceeds threshold
        if confidence < self.config.CONFIDENCE_THRESHOLD:
            return None, 0.0
        
        # Conservative Elite: Primarily BUY signals (grid accumulation strategy)
        direction = "BUY" if random.random() < 0.75 else "SELL"
        
        self.last_signal_time = timestamp
        self.daily_trade_count += 1
        
        return direction, confidence

class ConservativeEliteBacktester:
    """LOCKED Conservative Elite Backtesting Engine"""
    
    def __init__(self, config: ConservativeEliteBacktestConfig):
        self.config = config
        self.data_collector = RealMarketDataCollector(config)
        self.signal_generator = ConservativeEliteSignalGenerator(config)
        
        # Trading state
        self.balance = config.STARTING_BALANCE
        self.open_trades = []
        self.closed_trades = []
        self.equity_curve = []
        
        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_profit = 0.0
        self.max_drawdown = 0.0
        self.peak_equity = config.STARTING_BALANCE
    
    def run_backtest(self) -> Dict:
        """Run complete Conservative Elite backtest validation"""
        print("\n🔒 CONSERVATIVE ELITE BACKTEST VALIDATION")
        print("=" * 50)
        print(f"Target Win Rate: {self.config.WIN_RATE_TARGET:.1%}")
        print(f"Grid Spacing: {self.config.GRID_SPACING:.2%} (LOCKED)")
        print(f"Risk per Trade: ${self.config.RISK_PER_TRADE}")
        print(f"Risk-Reward Ratio: {self.config.RISK_REWARD_RATIO}:1")
        print("=" * 50)
        
        # Collect real market data
        market_data = self.data_collector.collect_historical_data(
            self.config.TRAINING_DAYS + self.config.OUT_OF_SAMPLE_DAYS
        )
        
        if market_data is None:
            print("❌ BACKTEST FAILED: No real market data available")
            return self._generate_failure_report()
        
        # Split data: Training vs Out-of-Sample
        split_point = len(market_data) - (self.config.OUT_OF_SAMPLE_DAYS * 24)
        training_data = market_data.iloc[:split_point]
        oos_data = market_data.iloc[split_point:]
        
        print(f"\n📊 DATA SPLIT:")
        print(f"   Training: {len(training_data)} points ({len(training_data)/24:.1f} days)")
        print(f"   Out-of-Sample: {len(oos_data)} points ({len(oos_data)/24:.1f} days)")
        
        # Run out-of-sample backtest
        print(f"\n🧪 RUNNING OUT-OF-SAMPLE BACKTEST...")
        results = self._run_oos_backtest(oos_data)
        
        # Generate comprehensive report
        return self._generate_backtest_report(results, oos_data)
    
    def _run_oos_backtest(self, data: pd.DataFrame) -> Dict:
        """Run out-of-sample backtest on real market data"""
        
        for timestamp, row in data.iterrows():
            current_price = row['close']
            
            # Update equity curve
            current_equity = self._calculate_current_equity(current_price)
            self.equity_curve.append({
                'timestamp': timestamp,
                'price': current_price,
                'equity': current_equity,
                'balance': self.balance,
                'open_trades': len(self.open_trades)
            })
            
            # Check for trade exits first
            self._check_trade_exits(current_price, timestamp)
            
            # Generate new signal if no open trades (Conservative Elite: one at a time)
            if len(self.open_trades) == 0:
                direction, confidence = self.signal_generator.generate_signal(current_price, timestamp)
                
                if direction and confidence > self.config.CONFIDENCE_THRESHOLD:
                    self._enter_trade(direction, current_price, confidence, timestamp)
        
        # Close any remaining open trades
        final_price = data['close'].iloc[-1]
        final_timestamp = data.index[-1]
        for trade in self.open_trades[:]:
            self._close_trade(trade, final_price, final_timestamp, "BACKTEST_END")
        
        return {
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': self.winning_trades / max(self.total_trades, 1),
            'total_profit': self.total_profit,
            'final_balance': self.balance,
            'max_drawdown': self.max_drawdown,
            'equity_curve': self.equity_curve,
            'closed_trades': self.closed_trades
        }
    
    def _enter_trade(self, direction: str, price: float, confidence: float, timestamp: datetime):
        """Enter new trade with LOCKED Conservative Elite parameters"""
        
        # Calculate position size based on fixed risk
        if direction == "BUY":
            stop_loss = price * (1 - self.config.STOP_LOSS_PCT)
            profit_target = price * (1 + self.config.PROFIT_TARGET_PCT)
        else:  # SELL
            stop_loss = price * (1 + self.config.STOP_LOSS_PCT)
            profit_target = price * (1 - self.config.PROFIT_TARGET_PCT)
        
        # Position size based on risk amount
        risk_distance = abs(price - stop_loss)
        quantity = self.config.RISK_PER_TRADE / risk_distance
        
        trade = {
            'id': f"CE_{self.total_trades + 1:04d}",
            'direction': direction,
            'entry_price': price,
            'entry_time': timestamp,
            'quantity': quantity,
            'stop_loss': stop_loss,
            'profit_target': profit_target,
            'confidence': confidence,
            'status': 'OPEN'
        }
        
        self.open_trades.append(trade)
        self.total_trades += 1
        
        print(f"📈 Trade #{trade['id']}: {direction} @ ${price:,.2f} | Confidence: {confidence:.1%}")
    
    def _check_trade_exits(self, current_price: float, timestamp: datetime):
        """Check for trade exits with LOCKED Conservative Elite logic"""
        
        for trade in self.open_trades[:]:
            direction = trade['direction']
            entry_price = trade['entry_price']
            
            exit_triggered = False
            exit_reason = ""
            
            if direction == "BUY":
                if current_price >= trade['profit_target']:
                    exit_triggered = True
                    exit_reason = "PROFIT_TARGET"
                elif current_price <= trade['stop_loss']:
                    exit_triggered = True
                    exit_reason = "STOP_LOSS"
            else:  # SELL
                if current_price <= trade['profit_target']:
                    exit_triggered = True
                    exit_reason = "PROFIT_TARGET"
                elif current_price >= trade['stop_loss']:
                    exit_triggered = True
                    exit_reason = "STOP_LOSS"
            
            if exit_triggered:
                self._close_trade(trade, current_price, timestamp, exit_reason)
    
    def _close_trade(self, trade: Dict, exit_price: float, timestamp: datetime, reason: str):
        """Close trade and update performance metrics"""
        
        # Calculate P&L
        if trade['direction'] == "BUY":
            pnl = (exit_price - trade['entry_price']) * trade['quantity']
        else:  # SELL
            pnl = (trade['entry_price'] - exit_price) * trade['quantity']
        
        # Update balance
        self.balance += pnl
        self.total_profit += pnl
        
        # Track winning trades
        if pnl > 0:
            self.winning_trades += 1
        
        # Update drawdown tracking
        if self.balance > self.peak_equity:
            self.peak_equity = self.balance
        else:
            current_drawdown = (self.peak_equity - self.balance) / self.peak_equity
            self.max_drawdown = max(self.max_drawdown, current_drawdown)
        
        # Record closed trade
        closed_trade = trade.copy()
        closed_trade.update({
            'exit_price': exit_price,
            'exit_time': timestamp,
            'pnl': pnl,
            'exit_reason': reason,
            'status': 'CLOSED'
        })
        
        self.closed_trades.append(closed_trade)
        self.open_trades.remove(trade)
        
        print(f"💰 Trade #{trade['id']} CLOSED: {reason} | P&L: ${pnl:+.2f} | Balance: ${self.balance:.2f}")
    
    def _calculate_current_equity(self, current_price: float) -> float:
        """Calculate current equity including open positions"""
        equity = self.balance
        
        for trade in self.open_trades:
            if trade['direction'] == "BUY":
                unrealized_pnl = (current_price - trade['entry_price']) * trade['quantity']
            else:  # SELL
                unrealized_pnl = (trade['entry_price'] - current_price) * trade['quantity']
            
            equity += unrealized_pnl
        
        return equity
    
    def _generate_backtest_report(self, results: Dict, data: pd.DataFrame) -> Dict:
        """Generate comprehensive backtest validation report"""
        
        win_rate = results['win_rate']
        total_return = (results['final_balance'] - self.config.STARTING_BALANCE) / self.config.STARTING_BALANCE
        
        # Calculate additional metrics
        if len(self.closed_trades) > 0:
            profits = [t['pnl'] for t in self.closed_trades if t['pnl'] > 0]
            losses = [t['pnl'] for t in self.closed_trades if t['pnl'] < 0]
            
            avg_profit = np.mean(profits) if profits else 0
            avg_loss = np.mean(losses) if losses else 0
            profit_factor = abs(sum(profits) / sum(losses)) if losses else float('inf')
        else:
            avg_profit = avg_loss = profit_factor = 0
        
        # Validation against Conservative Elite targets
        win_rate_validation = abs(win_rate - self.config.WIN_RATE_TARGET) <= 0.05  # Within 5%
        trades_per_day = results['total_trades'] / (len(data) / 24)
        frequency_validation = abs(trades_per_day - self.config.TRADES_PER_DAY) <= 2.0  # Within 2 trades/day
        
        report = {
            'validation_status': 'PASSED' if win_rate_validation and frequency_validation else 'FAILED',
            'conservative_elite_metrics': {
                'target_win_rate': self.config.WIN_RATE_TARGET,
                'actual_win_rate': win_rate,
                'win_rate_validation': win_rate_validation,
                'target_trades_per_day': self.config.TRADES_PER_DAY,
                'actual_trades_per_day': trades_per_day,
                'frequency_validation': frequency_validation
            },
            'performance_metrics': {
                'total_trades': results['total_trades'],
                'winning_trades': results['winning_trades'],
                'win_rate': win_rate,
                'total_profit': results['total_profit'],
                'total_return': total_return,
                'final_balance': results['final_balance'],
                'max_drawdown': results['max_drawdown'],
                'profit_factor': profit_factor,
                'avg_profit': avg_profit,
                'avg_loss': avg_loss
            },
            'locked_parameters': {
                'grid_spacing': self.config.GRID_SPACING,
                'risk_per_trade': self.config.RISK_PER_TRADE,
                'risk_reward_ratio': self.config.RISK_REWARD_RATIO,
                'confidence_threshold': self.config.CONFIDENCE_THRESHOLD,
                'max_open_trades': self.config.MAX_OPEN_TRADES
            },
            'data_validation': {
                'real_data_used': True,
                'data_points': len(data),
                'period_days': len(data) / 24,
                'price_range': f"${data['close'].min():.2f} - ${data['close'].max():.2f}"
            },
            'equity_curve': results['equity_curve'],
            'trade_details': self.closed_trades
        }
        
        return report
    
    def _generate_failure_report(self) -> Dict:
        """Generate failure report when backtest cannot run"""
        return {
            'validation_status': 'FAILED',
            'error': 'Unable to collect real market data',
            'conservative_elite_metrics': {},
            'performance_metrics': {},
            'locked_parameters': {
                'grid_spacing': self.config.GRID_SPACING,
                'risk_per_trade': self.config.RISK_PER_TRADE,
                'risk_reward_ratio': self.config.RISK_REWARD_RATIO
            },
            'data_validation': {
                'real_data_used': False,
                'error': 'No real market data available'
            }
        }

def save_backtest_results(results: Dict, filename: str = None):
    """Save backtest results to JSON file"""
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"conservative_elite_backtest_{timestamp}.json"
    
    # Convert datetime objects to strings for JSON serialization
    def convert_datetime(obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: convert_datetime(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_datetime(item) for item in obj]
        return obj
    
    results_serializable = convert_datetime(results)
    
    with open(filename, 'w') as f:
        json.dump(results_serializable, f, indent=2)
    
    print(f"📄 Backtest results saved to: {filename}")
    return filename

def print_backtest_summary(results: Dict):
    """Print comprehensive backtest summary"""
    print("\n" + "=" * 60)
    print("🔒 CONSERVATIVE ELITE BACKTEST VALIDATION RESULTS")
    print("=" * 60)
    
    status = results.get('validation_status', 'UNKNOWN')
    print(f"📊 VALIDATION STATUS: {status}")
    
    if 'conservative_elite_metrics' in results:
        metrics = results['conservative_elite_metrics']
        print(f"\n🎯 CONSERVATIVE ELITE VALIDATION:")
        print(f"   Target Win Rate: {metrics.get('target_win_rate', 0):.1%}")
        print(f"   Actual Win Rate: {metrics.get('actual_win_rate', 0):.1%}")
        print(f"   Win Rate Valid: {'✅' if metrics.get('win_rate_validation') else '❌'}")
        print(f"   Target Trades/Day: {metrics.get('target_trades_per_day', 0):.1f}")
        print(f"   Actual Trades/Day: {metrics.get('actual_trades_per_day', 0):.1f}")
        print(f"   Frequency Valid: {'✅' if metrics.get('frequency_validation') else '❌'}")
    
    if 'performance_metrics' in results:
        perf = results['performance_metrics']
        print(f"\n💰 PERFORMANCE METRICS:")
        print(f"   Total Trades: {perf.get('total_trades', 0)}")
        print(f"   Winning Trades: {perf.get('winning_trades', 0)}")
        print(f"   Win Rate: {perf.get('win_rate', 0):.1%}")
        print(f"   Total Profit: ${perf.get('total_profit', 0):+.2f}")
        print(f"   Total Return: {perf.get('total_return', 0):+.1%}")
        print(f"   Final Balance: ${perf.get('final_balance', 0):.2f}")
        print(f"   Max Drawdown: {perf.get('max_drawdown', 0):.1%}")
        print(f"   Profit Factor: {perf.get('profit_factor', 0):.2f}")
    
    if 'locked_parameters' in results:
        params = results['locked_parameters']
        print(f"\n🔒 LOCKED PARAMETERS:")
        print(f"   Grid Spacing: {params.get('grid_spacing', 0):.2%}")
        print(f"   Risk per Trade: ${params.get('risk_per_trade', 0)}")
        print(f"   Risk-Reward Ratio: {params.get('risk_reward_ratio', 0)}:1")
    
    print("=" * 60)

def main():
    """Run Conservative Elite backtest validation"""
    print("🔒 CONSERVATIVE ELITE BACKTESTER")
    print("Validating out-of-sample performance with LOCKED parameters")
    
    # Initialize configuration
    config = ConservativeEliteBacktestConfig()
    
    # Run backtest
    backtester = ConservativeEliteBacktester(config)
    results = backtester.run_backtest()
    
    # Print summary
    print_backtest_summary(results)
    
    # Save results
    filename = save_backtest_results(results)
    
    # Validation conclusion
    if results.get('validation_status') == 'PASSED':
        print("\n✅ CONSERVATIVE ELITE MODEL VALIDATED")
        print("   Out-of-sample performance meets targets")
        print("   All parameters locked and verified")
    else:
        print("\n❌ CONSERVATIVE ELITE MODEL VALIDATION FAILED")
        print("   Performance does not meet targets")
    
    return results

if __name__ == "__main__":
    main()
