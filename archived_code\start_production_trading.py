#!/usr/bin/env python3
"""
PRODUCTION TRADING STARTUP SCRIPT
=================================
Automatically installs dependencies and starts the production trading server.
"""

import os
import sys
import subprocess
import time

def install_dependencies():
    """Install required dependencies."""
    dependencies = [
        'waitress',
        'flask',
        'requests'
    ]
    
    print("📦 CHECKING DEPENDENCIES...")
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} - already installed")
        except ImportError:
            print(f"📥 Installing {dep}...")
            try:
                result = subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ {dep} - installed successfully")
                else:
                    print(f"❌ Failed to install {dep}: {result.stderr}")
                    return False
            except Exception as e:
                print(f"❌ Error installing {dep}: {e}")
                return False
    
    return True

def check_system_requirements():
    """Check system requirements."""
    print("🔍 CHECKING SYSTEM REQUIREMENTS...")
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ required")
        return False
    else:
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # Check if we're in the right directory
    if not os.path.exists('live_trading_web_app.py'):
        print("❌ live_trading_web_app.py not found - run from correct directory")
        return False
    else:
        print("✅ Trading app files found")
    
    return True

def start_production_server():
    """Start the production trading server."""
    print("\n🚀 STARTING PRODUCTION TRADING SERVER...")
    print("=" * 60)
    
    try:
        # Import and run the production server
        from production_live_trading_app import ProductionTradingServer
        
        server = ProductionTradingServer()
        server.start_server()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("🔧 Falling back to development server...")
        
        # Fallback to development server
        try:
            from live_trading_web_app import app, trading_engine, live_trading_loop
            import threading
            
            # Start trading thread manually
            if not trading_engine.is_running:
                trading_engine.is_running = True
                thread = threading.Thread(target=live_trading_loop, daemon=True)
                thread.start()
                time.sleep(1)
                
                if thread.is_alive():
                    print("✅ Auto trading started")
                else:
                    print("❌ Auto trading failed to start")
            
            # Start Flask app
            app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
            
        except Exception as e:
            print(f"❌ Fallback failed: {e}")
            return False
    
    except Exception as e:
        print(f"❌ Server error: {e}")
        return False
    
    return True

def main():
    """Main startup function."""
    print("🚀 PRODUCTION TRADING SYSTEM STARTUP")
    print("=" * 60)
    print("💰 Live BTC Trading with Auto Signal Generation")
    print("🤖 TCN-CNN-PPO Ensemble Model")
    print("📊 91.4% Composite Score")
    print("⚡ Production-Ready Threading")
    print("=" * 60)
    
    # Check system requirements
    if not check_system_requirements():
        print("❌ System requirements not met")
        return False
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        return False
    
    print("\n✅ ALL CHECKS PASSED - STARTING SERVER...")
    time.sleep(2)
    
    # Start the server
    return start_production_server()

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            print("\n❌ STARTUP FAILED")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Startup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ STARTUP ERROR: {e}")
        sys.exit(1)
