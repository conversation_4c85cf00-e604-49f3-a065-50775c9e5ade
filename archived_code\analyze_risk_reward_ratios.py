#!/usr/bin/env python3
"""
Comprehensive Risk-to-Reward Ratio Analysis for ALL Trading Systems
"""

import json
import glob
import sys

def analyze_risk_reward_ratios():
    print('⚖️ COMPREHENSIVE RISK-TO-REWARD RATIO ANALYSIS')
    print('=' * 80)
    print('🎯 ALL TRADING SYSTEMS RISK MANAGEMENT')
    
    all_systems = []
    
    # 1. Analyze Webapp Models (Main Trading Systems)
    print(f'\n1️⃣ WEBAPP TRADING SYSTEMS:')
    print('=' * 60)
    
    try:
        with open('models/webapp_model_metadata.json', 'r') as f:
            webapp_data = json.load(f)
        
        print(f'📊 WEBAPP MODELS RISK-REWARD ANALYSIS:')
        for i, model in enumerate(webapp_data.get('all_models', []), 1):
            name = model['name']
            risk_reward_ratio = model.get('risk_reward_ratio', 0)
            avg_win = model.get('avg_win', 0)
            avg_loss = model.get('avg_loss', 0)
            profit_factor = model.get('profit_factor', 0)
            win_rate = model.get('win_rate', 0) * 100
            
            print(f'\n   {i}. {name}:')
            print(f'      ⚖️ Risk-Reward Ratio: 1:{risk_reward_ratio}')
            print(f'      💰 Average Win: ${avg_win:.2f}')
            print(f'      📉 Average Loss: ${avg_loss:.2f}')
            print(f'      📊 Profit Factor: {profit_factor:.2f}')
            print(f'      🎲 Win Rate: {win_rate:.1f}%')
            
            # Calculate actual risk-reward from avg win/loss
            if avg_loss > 0:
                actual_rr = avg_win / avg_loss
                print(f'      🔍 Actual R:R (Win/Loss): 1:{actual_rr:.2f}')
            
            all_systems.append({
                'name': name,
                'type': 'Webapp Ensemble',
                'risk_reward_ratio': risk_reward_ratio,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'win_rate': win_rate,
                'actual_rr': avg_win / avg_loss if avg_loss > 0 else 0
            })
            
    except Exception as e:
        print(f'   ❌ Error loading webapp models: {e}')
    
    # 2. Check Live Trading System Configuration
    print(f'\n2️⃣ LIVE TRADING SYSTEM CONFIGURATION:')
    print('=' * 60)
    
    try:
        sys.path.append('.')
        from live_trading_web_app import BestCompositeModel
        
        model = BestCompositeModel()
        
        print(f'📊 LIVE SYSTEM RISK MANAGEMENT:')
        print(f'   Model ID: {model.model_id}')
        print(f'   Risk per Trade: ${model.risk_per_trade:.2f}')
        print(f'   Profit Target: ${model.profit_target:.2f}')
        print(f'   Account Size: ${model.account_size:.2f}')
        
        if hasattr(model, 'reward_ratio'):
            print(f'   ⚖️ Reward Ratio: 1:{model.reward_ratio}')
            live_rr = model.reward_ratio
        else:
            live_rr = model.profit_target / model.risk_per_trade
            print(f'   ⚖️ Calculated R:R: 1:{live_rr:.1f}')
        
        print(f'   📊 Risk Level: {(model.risk_per_trade/model.account_size)*100:.1f}% of account')
        
        all_systems.append({
            'name': 'Live_Trading_System',
            'type': 'Live System',
            'risk_reward_ratio': live_rr,
            'risk_per_trade': model.risk_per_trade,
            'profit_target': model.profit_target,
            'account_size': model.account_size,
            'risk_percentage': (model.risk_per_trade/model.account_size)*100
        })
        
    except Exception as e:
        print(f'   ❌ Live system check failed: {e}')
    
    # 3. Analyze Individual Model Metadata
    print(f'\n3️⃣ INDIVIDUAL MODELS RISK MANAGEMENT:')
    print('=' * 60)
    
    metadata_files = glob.glob('models/*metadata*.json')
    for metadata_file in metadata_files:
        if 'webapp_model_metadata.json' in metadata_file:
            continue
            
        try:
            with open(metadata_file, 'r') as f:
                model_data = json.load(f)
            
            model_name = model_data.get('model_id', 'Unknown')
            risk_per_trade = model_data.get('risk_per_trade', 0)
            profit_target = model_data.get('profit_target', 0)
            reward_ratio = model_data.get('reward_ratio', 0)
            
            print(f'\n📄 {model_name}:')
            print(f'   Risk per Trade: ${risk_per_trade:.2f}')
            print(f'   Profit Target: ${profit_target:.2f}')
            
            if reward_ratio > 0:
                print(f'   ⚖️ Risk-Reward Ratio: 1:{reward_ratio}')
                rr_ratio = reward_ratio
            elif profit_target > 0 and risk_per_trade > 0:
                rr_ratio = profit_target / risk_per_trade
                print(f'   ⚖️ Calculated R:R: 1:{rr_ratio:.1f}')
            else:
                rr_ratio = 0
                print(f'   ⚠️ No risk-reward data available')
            
            all_systems.append({
                'name': model_name,
                'type': 'Individual Model',
                'risk_reward_ratio': rr_ratio,
                'risk_per_trade': risk_per_trade,
                'profit_target': profit_target
            })
                
        except Exception as e:
            print(f'   ⚠️ Error reading {metadata_file}: {e}')
    
    # 4. Check Configuration Files
    print(f'\n4️⃣ SYSTEM CONFIGURATION FILES:')
    print('=' * 60)
    
    config_files = [
        'config/trading_config.py',
        'integrated_trading_system.py'
    ]
    
    for config_file in config_files:
        try:
            with open(config_file, 'r') as f:
                content = f.read()
            
            print(f'\n📄 {config_file}:')
            
            # Extract risk-reward settings
            if 'REWARD_RATIO' in content:
                import re
                reward_matches = re.findall(r'REWARD_RATIO.*?=.*?(\d+\.?\d*)', content)
                if reward_matches:
                    ratio = float(reward_matches[0])
                    print(f'   ⚖️ Configured Reward Ratio: 1:{ratio}')
            
            if 'risk_per_trade' in content:
                risk_matches = re.findall(r'risk_per_trade.*?=.*?(\d+\.?\d*)', content)
                if risk_matches:
                    risk = float(risk_matches[0])
                    print(f'   💰 Risk per Trade: ${risk:.2f}')
            
            if 'reward_ratio' in content:
                ratio_matches = re.findall(r'reward_ratio.*?=.*?(\d+\.?\d*)', content)
                if ratio_matches:
                    ratio = float(ratio_matches[0])
                    print(f'   ⚖️ Reward Ratio: 1:{ratio}')
                    
        except Exception as e:
            print(f'   ⚠️ Could not read {config_file}: {e}')
    
    # 5. Risk-Reward Summary and Ranking
    print(f'\n5️⃣ RISK-REWARD RATIO SUMMARY:')
    print('=' * 80)
    
    if all_systems:
        # Filter systems with valid risk-reward ratios
        valid_systems = [s for s in all_systems if s.get('risk_reward_ratio', 0) > 0]
        
        if valid_systems:
            # Sort by risk-reward ratio
            valid_systems.sort(key=lambda x: x['risk_reward_ratio'], reverse=True)
            
            print(f'🏆 SYSTEMS RANKED BY RISK-REWARD RATIO:')
            print(f'{"Rank":<4} {"System":<30} {"Type":<20} {"R:R Ratio":<10} {"Risk":<8} {"Target":<8}')
            print('-' * 85)
            
            for i, system in enumerate(valid_systems, 1):
                name = system['name'][:29]
                sys_type = system['type'][:19]
                rr_ratio = system['risk_reward_ratio']
                risk = system.get('risk_per_trade', 0)
                target = system.get('profit_target', 0)
                
                print(f'{i:<4} {name:<30} {sys_type:<20} 1:{rr_ratio:<9.1f} ${risk:<7.0f} ${target:<7.0f}')
            
            # Statistics
            ratios = [s['risk_reward_ratio'] for s in valid_systems]
            print(f'\n📊 RISK-REWARD STATISTICS:')
            print(f'   Total Systems with R:R Data: {len(valid_systems)}')
            print(f'   Average Risk-Reward Ratio: 1:{sum(ratios)/len(ratios):.1f}')
            print(f'   Best Risk-Reward Ratio: 1:{max(ratios):.1f}')
            print(f'   Lowest Risk-Reward Ratio: 1:{min(ratios):.1f}')
            
            # Risk management assessment
            excellent_rr = [s for s in valid_systems if s['risk_reward_ratio'] >= 2.5]
            good_rr = [s for s in valid_systems if 2.0 <= s['risk_reward_ratio'] < 2.5]
            acceptable_rr = [s for s in valid_systems if 1.5 <= s['risk_reward_ratio'] < 2.0]
            
            print(f'\n🎯 RISK MANAGEMENT QUALITY:')
            print(f'   ✅ EXCELLENT (≥2.5:1): {len(excellent_rr)} systems')
            print(f'   🟢 GOOD (2.0-2.4:1): {len(good_rr)} systems')
            print(f'   🟡 ACCEPTABLE (1.5-1.9:1): {len(acceptable_rr)} systems')
            
        else:
            print(f'   ❌ No systems found with valid risk-reward data')
    
    # 6. Risk Management Recommendations
    print(f'\n6️⃣ RISK MANAGEMENT ASSESSMENT:')
    print('=' * 60)
    
    print(f'📈 OPTIMAL RISK-REWARD RATIOS:')
    print(f'   🏆 EXCELLENT: 2.5:1 or higher (maximizes long-term growth)')
    print(f'   ✅ GOOD: 2.0:1 to 2.4:1 (solid risk management)')
    print(f'   ⚠️ MINIMUM: 1.5:1 (acceptable for high win rate systems)')
    print(f'   ❌ POOR: Below 1.5:1 (unsustainable long-term)')
    
    print(f'\n💰 CURRENT SYSTEM ASSESSMENT:')
    if valid_systems:
        best_system = max(valid_systems, key=lambda x: x['risk_reward_ratio'])
        print(f'   🥇 Best R:R System: {best_system["name"]} (1:{best_system["risk_reward_ratio"]:.1f})')
        
        avg_ratio = sum(s['risk_reward_ratio'] for s in valid_systems) / len(valid_systems)
        if avg_ratio >= 2.5:
            assessment = "EXCELLENT"
        elif avg_ratio >= 2.0:
            assessment = "GOOD"
        elif avg_ratio >= 1.5:
            assessment = "ACCEPTABLE"
        else:
            assessment = "NEEDS IMPROVEMENT"
        
        print(f'   📊 Overall Assessment: {assessment} (Average 1:{avg_ratio:.1f})')
    
    print(f'\n✅ RISK-REWARD ANALYSIS COMPLETE')
    print(f'   All trading systems analyzed for risk management quality')
    print(f'   Risk-reward ratios are critical for long-term profitability')

if __name__ == "__main__":
    analyze_risk_reward_ratios()
