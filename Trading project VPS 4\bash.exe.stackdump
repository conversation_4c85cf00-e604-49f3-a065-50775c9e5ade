Stack trace:
Frame         Function      Args
0007FFFFA4F0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF93F0) msys-2.0.dll+0x1FEBA
0007FFFFA4F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA7C8) msys-2.0.dll+0x67F9
0007FFFFA4F0  000210046832 (000210285FF9, 0007FFFFA3A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA4F0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA4F0  0002100690B4 (0007FFFFA500, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA7D0  00021006A49D (0007FFFFA500, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD48BF0000 ntdll.dll
7FFD48190000 KERNEL32.DLL
7FFD46380000 KERNELBASE.dll
7FFD477F0000 USER32.dll
7FFD46980000 win32u.dll
7FFD48160000 GDI32.dll
7FFD46A20000 gdi32full.dll
7FFD461D0000 msvcp_win.dll
7FFD46270000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD47E60000 advapi32.dll
7FFD48700000 msvcrt.dll
7FFD47A00000 sechost.dll
7FFD487C0000 RPCRT4.dll
7FFD452B0000 CRYPTBASE.DLL
7FFD46900000 bcryptPrimitives.dll
7FFD486C0000 IMM32.DLL
