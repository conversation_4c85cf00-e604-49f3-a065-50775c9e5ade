#!/usr/bin/env python3
"""
Check available models in the live webapp
"""

import requests
import json

def main():
    try:
        response = requests.get('http://localhost:5000/api/models', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print('🌐 LIVE WEBAPP AVAILABLE MODELS:')
            print('=' * 50)
            
            if data['status'] == 'success':
                for key, model in data['models'].items():
                    active = '🟢 ACTIVE' if model['active'] else '⚪'
                    print(f'{active} {key.upper()}:')
                    print(f'   Name: {model["name"]}')
                    print(f'   Composite Score: {model["composite_score"]:.1%}')
                    print(f'   Win Rate: {model["win_rate"]:.1%}')
                    print(f'   Net Profit: ${model["net_profit"]:.2f}')
                    print(f'   Trades/Day: {model["trades_per_day"]:.1f}')
                    print(f'   Sharpe Ratio: {model["sharpe_ratio"]:.2f}')
                    print(f'   Risk Level: {model["risk_level"]}')
                    print(f'   Live Ready: {model["live_ready"]}')
                    print()
                
                print(f'Active Model: {data["active_model"]}')
            else:
                print(f'Error: {data.get("error", "Unknown error")}')
        else:
            print(f'HTTP Error: {response.status_code}')
            
    except Exception as e:
        print(f'Connection Error: {e}')
        print('Webapp may not be running or accessible')

if __name__ == "__main__":
    main()
