#!/usr/bin/env python3
"""
COMPREHENSIVE SYSTEM PRECHECK - TCN-CNN-PPO DEPLOYMENT
=====================================================

Performs a complete system readiness check before deploying the TCN-CNN-PPO
model in simulation mode and preparing for live trading.

Author: Bitcoin Freedom Trading System
Date: 2025-06-03
"""

import os
import sys
import json
import time
import requests
from datetime import datetime

def check_model_availability():
    """Check if TCN-CNN-PPO model is available and properly configured."""
    print("🤖 CHECKING TCN-CNN-PPO MODEL AVAILABILITY")
    print("=" * 60)
    
    models_dir = "models"
    issues = []
    
    # Check models directory
    if not os.path.exists(models_dir):
        issues.append("❌ Models directory not found")
        return False, issues
    
    # Look for TCN-CNN-PPO models
    tcn_models = []
    for file in os.listdir(models_dir):
        if file.startswith("tcn_cnn_ppo_ensemble_") and file.endswith(".pth"):
            tcn_models.append(file)
    
    if not tcn_models:
        issues.append("❌ No TCN-CNN-PPO models found")
        return False, issues
    
    # Check for simplified model (preferred)
    simplified_models = [f for f in tcn_models if "simplified" in f]
    if simplified_models:
        latest_model = sorted(simplified_models)[-1]
        print(f"✅ Found simplified TCN-CNN-PPO model: {latest_model}")
    else:
        latest_model = sorted(tcn_models)[-1]
        print(f"✅ Found TCN-CNN-PPO model: {latest_model}")
    
    # Check metadata file
    metadata_file = latest_model.replace('.pth', '_metadata.json')
    metadata_path = os.path.join(models_dir, metadata_file)
    
    if os.path.exists(metadata_path):
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        
        print(f"✅ Model metadata found")
        print(f"   Model ID: {metadata.get('model_id', 'Unknown')}")
        print(f"   Composite Score: {metadata.get('composite_score', 0):.1%}")
        print(f"   Win Rate: {metadata.get('win_rate', 0):.1%}")
        print(f"   Architecture: {metadata.get('model_type', 'Unknown')}")
        
        # Check if model meets deployment criteria
        composite_score = metadata.get('composite_score', 0)
        win_rate = metadata.get('win_rate', 0)
        
        if composite_score >= 0.85:  # 85% minimum
            print(f"✅ Composite score {composite_score:.1%} meets deployment criteria (≥85%)")
        else:
            issues.append(f"⚠️ Composite score {composite_score:.1%} below 85% threshold")
        
        if win_rate >= 0.70:  # 70% minimum
            print(f"✅ Win rate {win_rate:.1%} meets deployment criteria (≥70%)")
        else:
            issues.append(f"⚠️ Win rate {win_rate:.1%} below 70% threshold")
    
    else:
        issues.append("❌ Model metadata file not found")
    
    return len(issues) == 0, issues

def check_dependencies():
    """Check if all required dependencies are available."""
    print("\n📦 CHECKING DEPENDENCIES")
    print("=" * 40)
    
    dependencies = {
        'torch': 'PyTorch for AI models',
        'numpy': 'Numerical computing',
        'requests': 'HTTP requests',
        'flask': 'Web framework'
    }
    
    issues = []
    
    for dep, description in dependencies.items():
        try:
            __import__(dep)
            print(f"✅ {dep}: {description}")
        except ImportError:
            issues.append(f"❌ Missing dependency: {dep} ({description})")
            print(f"❌ {dep}: NOT FOUND")
    
    return len(issues) == 0, issues

def check_price_feeds():
    """Check if price feeds are working."""
    print("\n💰 CHECKING PRICE FEEDS")
    print("=" * 40)
    
    issues = []
    
    # Test Binance API
    try:
        response = requests.get('https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT', timeout=10)
        if response.status_code == 200:
            data = response.json()
            price = float(data['price'])
            print(f"✅ Binance API: ${price:,.2f}")
        else:
            issues.append("❌ Binance API not responding")
    except Exception as e:
        issues.append(f"❌ Binance API error: {e}")
    
    # Test Coinbase API (backup)
    try:
        response = requests.get('https://api.coinbase.com/v2/exchange-rates?currency=BTC', timeout=10)
        if response.status_code == 200:
            data = response.json()
            price = float(data['data']['rates']['USD'])
            print(f"✅ Coinbase API: ${price:,.2f}")
        else:
            issues.append("❌ Coinbase API not responding")
    except Exception as e:
        issues.append(f"❌ Coinbase API error: {e}")
    
    return len(issues) == 0, issues

def check_database_system():
    """Check database and logging systems."""
    print("\n🗄️ CHECKING DATABASE & LOGGING")
    print("=" * 40)
    
    issues = []
    
    # Check SQLite database
    db_file = "bitcoin_freedom_trades.db"
    if os.path.exists(db_file):
        print(f"✅ SQLite database found: {db_file}")
    else:
        print(f"⚠️ SQLite database will be created: {db_file}")
    
    # Check CSV logging
    csv_file = "trade_history.csv"
    if os.path.exists(csv_file):
        print(f"✅ CSV trade log found: {csv_file}")
    else:
        print(f"⚠️ CSV trade log will be created: {csv_file}")
    
    return len(issues) == 0, issues

def check_grid_trading_config():
    """Check grid trading configuration."""
    print("\n🔒 CHECKING GRID TRADING CONFIGURATION")
    print("=" * 40)
    
    issues = []
    
    # Check if grid spacing is locked
    try:
        # Import and check intelligent margin manager
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from intelligent_margin_manager import IntelligentMarginManager

        manager = IntelligentMarginManager()
        grid_spacing = manager.grid_size_percent
        
        if grid_spacing == 0.0025:  # 0.25%
            print(f"✅ Grid spacing locked at {grid_spacing:.4f} (0.25%)")
        else:
            issues.append(f"❌ Grid spacing not locked: {grid_spacing}")
        
    except Exception as e:
        issues.append(f"❌ Grid calculator error: {e}")
    
    return len(issues) == 0, issues

def check_risk_management():
    """Check risk management settings."""
    print("\n🛡️ CHECKING RISK MANAGEMENT")
    print("=" * 40)
    
    issues = []
    
    # Check model metadata for risk settings
    models_dir = "models"
    tcn_models = [f for f in os.listdir(models_dir) if f.startswith("tcn_cnn_ppo_ensemble_") and f.endswith(".pth")]
    
    if tcn_models:
        latest_model = sorted(tcn_models)[-1]
        metadata_file = latest_model.replace('.pth', '_metadata.json')
        metadata_path = os.path.join(models_dir, metadata_file)
        
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            risk_per_trade = metadata.get('risk_per_trade', 0)
            profit_target = metadata.get('profit_target', 0)
            reward_ratio = metadata.get('reward_ratio', 0)
            
            print(f"✅ Risk per trade: ${risk_per_trade}")
            print(f"✅ Profit target: ${profit_target}")
            print(f"✅ Risk-reward ratio: {reward_ratio}:1")
            
            if risk_per_trade == 10.0 and profit_target == 20.0 and reward_ratio == 2.0:
                print("✅ Risk management settings correct (2:1 ratio)")
            else:
                issues.append("⚠️ Risk management settings may need adjustment")
    
    return len(issues) == 0, issues

def check_simulation_mode():
    """Check simulation mode configuration."""
    print("\n🧪 CHECKING SIMULATION MODE")
    print("=" * 40)
    
    issues = []
    
    # Simulation mode should be default
    print("✅ System will start in SIMULATION mode")
    print("✅ No real money will be used initially")
    print("✅ Live mode requires manual activation")
    print("✅ All trades will be paper trades until live mode enabled")
    
    return len(issues) == 0, issues

def run_comprehensive_precheck():
    """Run complete system precheck."""
    print("🚀 COMPREHENSIVE SYSTEM PRECHECK - TCN-CNN-PPO DEPLOYMENT")
    print("=" * 80)
    print("Checking system readiness for AI trading deployment")
    print("=" * 80)
    
    all_checks = [
        ("Model Availability", check_model_availability),
        ("Dependencies", check_dependencies),
        ("Price Feeds", check_price_feeds),
        ("Database & Logging", check_database_system),
        ("Grid Trading Config", check_grid_trading_config),
        ("Risk Management", check_risk_management),
        ("Simulation Mode", check_simulation_mode)
    ]
    
    passed_checks = 0
    total_issues = []
    
    for check_name, check_func in all_checks:
        try:
            passed, issues = check_func()
            if passed:
                passed_checks += 1
                print(f"\n✅ {check_name}: PASSED")
            else:
                print(f"\n❌ {check_name}: FAILED")
                total_issues.extend(issues)
                
            if issues:
                for issue in issues:
                    print(f"   {issue}")
                    
        except Exception as e:
            print(f"\n❌ {check_name}: ERROR - {e}")
            total_issues.append(f"Error in {check_name}: {e}")
    
    # Final summary
    print("\n" + "=" * 80)
    print("📋 PRECHECK SUMMARY")
    print("=" * 80)
    
    print(f"✅ Passed Checks: {passed_checks}/{len(all_checks)}")
    
    if total_issues:
        print(f"⚠️ Issues Found: {len(total_issues)}")
        for issue in total_issues:
            print(f"   {issue}")
    else:
        print("🎉 No issues found!")
    
    # Deployment recommendation
    if passed_checks == len(all_checks) and len(total_issues) == 0:
        print("\n🚀 DEPLOYMENT RECOMMENDATION: READY FOR SIMULATION")
        print("✅ All systems check passed")
        print("✅ TCN-CNN-PPO model ready for deployment")
        print("✅ Safe to start simulation mode")
        print("✅ Can proceed to live trading after simulation testing")
        return True
    elif passed_checks >= len(all_checks) - 1:
        print("\n⚠️ DEPLOYMENT RECOMMENDATION: READY WITH MINOR ISSUES")
        print("✅ Core systems operational")
        print("⚠️ Minor issues detected but not blocking")
        print("✅ Safe to proceed with simulation")
        return True
    else:
        print("\n❌ DEPLOYMENT RECOMMENDATION: NOT READY")
        print("❌ Critical issues detected")
        print("❌ Resolve issues before deployment")
        return False

def main():
    """Main precheck function."""
    ready = run_comprehensive_precheck()
    
    print("\n" + "=" * 80)
    if ready:
        print("🎉 SYSTEM READY FOR TCN-CNN-PPO DEPLOYMENT")
        print("🧪 Start with SIMULATION mode for testing")
        print("🚀 Switch to LIVE mode when confident")
    else:
        print("⚠️ SYSTEM NOT READY - RESOLVE ISSUES FIRST")
    print("=" * 80)
    
    return 0 if ready else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
