#!/usr/bin/env python3
"""
Simple Flask test to verify server functionality
"""

from flask import Flask, render_template

app = Flask(__name__)

@app.route('/')
def index():
    """Test main page."""
    return render_template('clean_trading_dashboard.html')

@app.route('/test')
def test():
    """Test endpoint."""
    return "<h1>FLASK TEST WORKING!</h1><p>Server is responding correctly.</p>"

@app.route('/simple')
def simple():
    """Simple test endpoint."""
    return "Simple Flask test - working!"

if __name__ == '__main__':
    print("🚀 Starting simple Flask test server...")
    app.run(debug=True, host='0.0.0.0', port=5000)
