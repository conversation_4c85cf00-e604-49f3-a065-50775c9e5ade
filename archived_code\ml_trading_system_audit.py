"""
ML Trading System Comprehensive Audit
Focused audit for TCN, CNN, PPO implementation requirements
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any
import importlib.util

class MLTradingSystemAuditor:
    """Comprehensive auditor for ML trading system implementation"""
    
    def __init__(self):
        self.audit_results = {
            'timestamp': datetime.now().isoformat(),
            'files_analyzed': [],
            'critical_issues': [],
            'recommendations': [],
            'ml_models': {},
            'dependencies': {},
            'configuration': {},
            'live_trading': {},
            'data_pipeline': {},
            'summary': {}
        }
        
        self.logger = logging.getLogger('MLTradingSystemAuditor')
        logging.basicConfig(level=logging.INFO)
    
    def audit_complete_system(self) -> Dict[str, Any]:
        """Perform complete ML trading system audit"""
        self.logger.info("🔍 Starting ML Trading System Audit...")
        
        # Audit core components
        self._audit_ml_models()
        self._audit_dependencies()
        self._audit_configuration()
        self._audit_live_trading()
        self._audit_data_pipeline()
        self._audit_performance_metrics()
        
        # Generate summary and recommendations
        self._generate_summary()
        self._generate_recommendations()
        
        return self.audit_results
    
    def _audit_ml_models(self):
        """Audit ML model implementation"""
        self.logger.info("🤖 Auditing ML Models...")
        
        # Check current ML system
        if os.path.exists('ml_training_system.py'):
            self.audit_results['files_analyzed'].append('ml_training_system.py')
            
            try:
                with open('ml_training_system.py', 'r') as f:
                    content = f.read()
                
                # Check for advanced ML models
                has_tcn = 'TCN' in content or 'TemporalConvolutional' in content
                has_cnn = 'CNN' in content or 'Conv1D' in content or 'Conv2D' in content
                has_ppo = 'PPO' in content or 'ProximalPolicy' in content
                has_lstm = 'LSTM' in content
                has_transformer = 'Transformer' in content
                
                # Check current basic models
                current_models = self._get_current_models(content)
                
                self.audit_results['ml_models'] = {
                    'has_tcn': has_tcn,
                    'has_cnn': has_cnn,
                    'has_ppo': has_ppo,
                    'has_lstm': has_lstm,
                    'has_transformer': has_transformer,
                    'current_models': current_models,
                    'advanced_models_implemented': has_tcn and has_cnn and has_ppo,
                    'basic_models_only': len(current_models) > 0 and not (has_tcn or has_cnn or has_ppo)
                }
                
                # Critical issues
                if not (has_tcn and has_cnn and has_ppo):
                    self.audit_results['critical_issues'].append({
                        'type': 'MISSING_ADVANCED_ML_MODELS',
                        'description': 'System lacks required TCN, CNN, and PPO models',
                        'impact': 'Cannot meet trading plan requirements for advanced ML',
                        'priority': 'CRITICAL'
                    })
                
                if len(current_models) > 0 and not (has_tcn or has_cnn or has_ppo):
                    self.audit_results['critical_issues'].append({
                        'type': 'BASIC_MODELS_ONLY',
                        'description': 'Only basic sklearn models implemented',
                        'impact': 'Insufficient for real-time trading performance requirements',
                        'priority': 'HIGH'
                    })
                    
            except Exception as e:
                self.audit_results['critical_issues'].append({
                    'type': 'ML_SYSTEM_ERROR',
                    'description': f'Failed to analyze ML system: {e}',
                    'impact': 'Cannot assess ML capabilities',
                    'priority': 'CRITICAL'
                })
        else:
            self.audit_results['critical_issues'].append({
                'type': 'MISSING_ML_SYSTEM',
                'description': 'ML training system file not found',
                'impact': 'No ML capabilities available',
                'priority': 'CRITICAL'
            })
    
    def _get_current_models(self, content: str) -> List[str]:
        """Extract current model implementations"""
        models = []
        
        model_patterns = {
            'RandomForest': 'RandomForestClassifier',
            'GradientBoosting': 'GradientBoostingClassifier',
            'LogisticRegression': 'LogisticRegression',
            'XGBoost': 'XGBClassifier',
            'SVM': 'SVC'
        }
        
        for model_name, pattern in model_patterns.items():
            if pattern in content:
                models.append(model_name)
        
        return models
    
    def _audit_dependencies(self):
        """Audit ML dependencies"""
        self.logger.info("📦 Auditing Dependencies...")
        
        if os.path.exists('requirements.txt'):
            self.audit_results['files_analyzed'].append('requirements.txt')
            
            try:
                with open('requirements.txt', 'r') as f:
                    requirements = f.read().splitlines()
                
                # Required for advanced ML models
                required_libs = {
                    'tensorflow': 'For TCN and CNN models',
                    'torch': 'Alternative deep learning framework',
                    'stable-baselines3': 'For PPO reinforcement learning',
                    'gym': 'RL environment framework',
                    'numpy': 'Numerical computing',
                    'pandas': 'Data manipulation',
                    'scikit-learn': 'Basic ML models'
                }
                
                found_libs = []
                missing_libs = []
                
                for lib, description in required_libs.items():
                    found = any(lib in req.lower() for req in requirements)
                    if found:
                        found_libs.append(lib)
                    else:
                        missing_libs.append({'lib': lib, 'description': description})
                
                self.audit_results['dependencies'] = {
                    'found_libraries': found_libs,
                    'missing_libraries': missing_libs,
                    'has_deep_learning': 'tensorflow' in found_libs or 'torch' in found_libs,
                    'has_rl_framework': 'stable-baselines3' in found_libs
                }
                
                # Critical dependency issues
                if not ('tensorflow' in found_libs or 'torch' in found_libs):
                    self.audit_results['critical_issues'].append({
                        'type': 'MISSING_DEEP_LEARNING_FRAMEWORK',
                        'description': 'No deep learning framework (TensorFlow/PyTorch) found',
                        'impact': 'Cannot implement TCN/CNN models',
                        'priority': 'CRITICAL'
                    })
                
                if 'stable-baselines3' not in found_libs:
                    self.audit_results['critical_issues'].append({
                        'type': 'MISSING_RL_FRAMEWORK',
                        'description': 'stable-baselines3 not found',
                        'impact': 'Cannot implement PPO agent',
                        'priority': 'HIGH'
                    })
                    
            except Exception as e:
                self.audit_results['critical_issues'].append({
                    'type': 'DEPENDENCY_ANALYSIS_ERROR',
                    'description': f'Failed to analyze dependencies: {e}',
                    'impact': 'Cannot verify ML library availability',
                    'priority': 'MEDIUM'
                })
    
    def _audit_configuration(self):
        """Audit trading configuration"""
        self.logger.info("⚙️ Auditing Configuration...")
        
        config_file = 'config/trading_config.py'
        if os.path.exists(config_file):
            self.audit_results['files_analyzed'].append(config_file)
            
            try:
                spec = importlib.util.spec_from_file_location("trading_config", config_file)
                config_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(config_module)
                
                config = config_module.TradingConfig()
                
                # Check critical configuration values
                composite_threshold = getattr(config, 'COMPOSITE_THRESHOLD', 0)
                training_days = getattr(config, 'TRAINING_DAYS', 0)
                testing_days = getattr(config, 'TESTING_DAYS', 0)
                fixed_risk = getattr(config, 'FIXED_RISK_AMOUNT', 0)
                
                self.audit_results['configuration'] = {
                    'composite_threshold': composite_threshold,
                    'training_days': training_days,
                    'testing_days': testing_days,
                    'fixed_risk_amount': fixed_risk,
                    'meets_85_percent_threshold': composite_threshold >= 0.85,
                    'meets_60_day_training': training_days == 60,
                    'meets_30_day_testing': testing_days in [30, 40],
                    'meets_10_dollar_risk': fixed_risk == 10.0
                }
                
                # Configuration issues
                if composite_threshold < 0.85:
                    self.audit_results['critical_issues'].append({
                        'type': 'INSUFFICIENT_COMPOSITE_THRESHOLD',
                        'description': f'Composite threshold {composite_threshold} below required 85%',
                        'impact': 'Models may not meet performance requirements',
                        'priority': 'HIGH'
                    })
                
                if training_days != 60:
                    self.audit_results['critical_issues'].append({
                        'type': 'INCORRECT_TRAINING_PERIOD',
                        'description': f'Training period {training_days} days, should be 60',
                        'impact': 'Not following trading plan specifications',
                        'priority': 'MEDIUM'
                    })
                    
            except Exception as e:
                self.audit_results['critical_issues'].append({
                    'type': 'CONFIG_LOAD_ERROR',
                    'description': f'Failed to load configuration: {e}',
                    'impact': 'Cannot verify system settings',
                    'priority': 'HIGH'
                })
    
    def _audit_live_trading(self):
        """Audit live trading capabilities"""
        self.logger.info("💰 Auditing Live Trading...")
        
        if os.path.exists('live_binance_integration.py'):
            self.audit_results['files_analyzed'].append('live_binance_integration.py')
            
            try:
                with open('live_binance_integration.py', 'r') as f:
                    content = f.read()
                
                has_binance_api = 'binance' in content.lower()
                has_order_execution = 'create_order' in content or 'place_order' in content
                has_cross_margin = 'cross' in content.lower() and 'margin' in content.lower()
                has_risk_management = 'stop_loss' in content or 'risk' in content
                
                self.audit_results['live_trading'] = {
                    'has_binance_api': has_binance_api,
                    'has_order_execution': has_order_execution,
                    'has_cross_margin': has_cross_margin,
                    'has_risk_management': has_risk_management,
                    'ready_for_live_trading': all([has_binance_api, has_order_execution, has_cross_margin])
                }
                
                if not has_cross_margin:
                    self.audit_results['critical_issues'].append({
                        'type': 'MISSING_CROSS_MARGIN',
                        'description': 'Cross margin trading not implemented',
                        'impact': 'Cannot meet trading plan requirements',
                        'priority': 'HIGH'
                    })
                    
            except Exception as e:
                self.audit_results['critical_issues'].append({
                    'type': 'LIVE_TRADING_ANALYSIS_ERROR',
                    'description': f'Failed to analyze live trading: {e}',
                    'impact': 'Cannot verify trading capabilities',
                    'priority': 'MEDIUM'
                })
    
    def _audit_data_pipeline(self):
        """Audit data collection and processing"""
        self.logger.info("📊 Auditing Data Pipeline...")
        
        # Check for data directory and files
        data_issues = []
        
        if not os.path.exists('data'):
            data_issues.append('Missing data directory')
        
        if not os.path.exists('btc_trading_system.db'):
            data_issues.append('Missing main database file')
        
        # Check models directory
        if os.path.exists('models'):
            model_files = os.listdir('models')
            model_count = len([f for f in model_files if f.endswith('.joblib')])
            
            self.audit_results['data_pipeline'] = {
                'has_data_directory': os.path.exists('data'),
                'has_database': os.path.exists('btc_trading_system.db'),
                'saved_models_count': model_count,
                'model_files': model_files
            }
        else:
            data_issues.append('Missing models directory')
        
        for issue in data_issues:
            self.audit_results['critical_issues'].append({
                'type': 'DATA_PIPELINE_ISSUE',
                'description': issue,
                'impact': 'May affect data collection and model training',
                'priority': 'MEDIUM'
            })
    
    def _audit_performance_metrics(self):
        """Audit performance tracking capabilities"""
        self.logger.info("📈 Auditing Performance Metrics...")
        
        if os.path.exists('ml_training_system.py'):
            try:
                with open('ml_training_system.py', 'r') as f:
                    content = f.read()
                
                # Check for comprehensive metrics
                metrics = {
                    'composite_score': 'composite_score' in content.lower(),
                    'sharpe_ratio': 'sharpe' in content.lower(),
                    'drawdown': 'drawdown' in content.lower(),
                    'win_rate': 'win_rate' in content.lower() or 'accuracy' in content.lower(),
                    'profit_factor': 'profit_factor' in content.lower()
                }
                
                self.audit_results['performance_metrics'] = metrics
                
                missing_metrics = [metric for metric, present in metrics.items() if not present]
                
                if missing_metrics:
                    self.audit_results['critical_issues'].append({
                        'type': 'INCOMPLETE_PERFORMANCE_METRICS',
                        'description': f'Missing metrics: {", ".join(missing_metrics)}',
                        'impact': 'Cannot properly evaluate model performance',
                        'priority': 'MEDIUM'
                    })
                    
            except Exception as e:
                self.audit_results['critical_issues'].append({
                    'type': 'METRICS_ANALYSIS_ERROR',
                    'description': f'Failed to analyze performance metrics: {e}',
                    'impact': 'Cannot verify performance tracking',
                    'priority': 'LOW'
                })
    
    def _generate_summary(self):
        """Generate audit summary"""
        total_files = len(self.audit_results['files_analyzed'])
        critical_issues = len([issue for issue in self.audit_results['critical_issues'] 
                              if issue['priority'] == 'CRITICAL'])
        high_issues = len([issue for issue in self.audit_results['critical_issues'] 
                          if issue['priority'] == 'HIGH'])
        
        # Determine overall status
        ml_ready = self.audit_results['ml_models'].get('advanced_models_implemented', False)
        config_ready = self.audit_results['configuration'].get('meets_85_percent_threshold', False)
        trading_ready = self.audit_results['live_trading'].get('ready_for_live_trading', False)
        
        overall_status = 'READY' if (ml_ready and config_ready and trading_ready) else 'NEEDS_WORK'
        
        self.audit_results['summary'] = {
            'total_files_analyzed': total_files,
            'critical_issues_count': critical_issues,
            'high_priority_issues_count': high_issues,
            'ml_models_status': 'ADVANCED' if ml_ready else 'BASIC_ONLY',
            'configuration_status': 'COMPLIANT' if config_ready else 'NON_COMPLIANT',
            'live_trading_status': 'READY' if trading_ready else 'NOT_READY',
            'overall_status': overall_status
        }
    
    def _generate_recommendations(self):
        """Generate specific recommendations"""
        recommendations = []
        
        # ML Model recommendations
        if not self.audit_results['ml_models'].get('has_tcn', False):
            recommendations.append({
                'priority': 'CRITICAL',
                'category': 'ML_MODELS',
                'action': 'Implement TCN (Temporal Convolutional Network) model',
                'description': 'Required for time series prediction according to trading plan'
            })
        
        if not self.audit_results['ml_models'].get('has_cnn', False):
            recommendations.append({
                'priority': 'CRITICAL',
                'category': 'ML_MODELS',
                'action': 'Implement CNN (Convolutional Neural Network) model',
                'description': 'Required for pattern recognition in market data'
            })
        
        if not self.audit_results['ml_models'].get('has_ppo', False):
            recommendations.append({
                'priority': 'CRITICAL',
                'category': 'ML_MODELS',
                'action': 'Implement PPO (Proximal Policy Optimization) agent',
                'description': 'Required for reinforcement learning trading decisions'
            })
        
        # Dependency recommendations
        missing_deps = self.audit_results['dependencies'].get('missing_libraries', [])
        for dep in missing_deps:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'DEPENDENCIES',
                'action': f'Install {dep["lib"]}',
                'description': dep['description']
            })
        
        # Configuration recommendations
        if not self.audit_results['configuration'].get('meets_85_percent_threshold', True):
            recommendations.append({
                'priority': 'HIGH',
                'category': 'CONFIGURATION',
                'action': 'Update COMPOSITE_THRESHOLD to 0.85',
                'description': 'Required 85% threshold per trading plan'
            })
        
        self.audit_results['recommendations'] = recommendations
    
    def print_summary(self):
        """Print comprehensive audit summary"""
        print("\n" + "="*80)
        print("🔍 ML TRADING SYSTEM AUDIT SUMMARY")
        print("="*80)
        
        summary = self.audit_results['summary']
        print(f"📁 Files Analyzed: {summary['total_files_analyzed']}")
        print(f"🚨 Critical Issues: {summary['critical_issues_count']}")
        print(f"⚠️  High Priority Issues: {summary['high_priority_issues_count']}")
        print(f"🤖 ML Models Status: {summary['ml_models_status']}")
        print(f"⚙️  Configuration Status: {summary['configuration_status']}")
        print(f"💰 Live Trading Status: {summary['live_trading_status']}")
        print(f"📊 Overall Status: {summary['overall_status']}")
        
        # Current models
        print(f"\n🎯 CURRENT ML MODELS:")
        current_models = self.audit_results['ml_models'].get('current_models', [])
        if current_models:
            for model in current_models:
                print(f"   ✅ {model}")
        else:
            print("   ❌ No models detected")
        
        # Advanced models status
        print(f"\n🚀 REQUIRED ADVANCED MODELS:")
        print(f"   TCN: {'✅' if self.audit_results['ml_models'].get('has_tcn') else '❌'}")
        print(f"   CNN: {'✅' if self.audit_results['ml_models'].get('has_cnn') else '❌'}")
        print(f"   PPO: {'✅' if self.audit_results['ml_models'].get('has_ppo') else '❌'}")
        
        # Critical issues
        if self.audit_results['critical_issues']:
            print(f"\n🚨 CRITICAL ISSUES:")
            for i, issue in enumerate(self.audit_results['critical_issues'][:5], 1):
                print(f"   {i}. {issue['description']}")
        
        # Top recommendations
        print(f"\n🔧 TOP RECOMMENDATIONS:")
        critical_recs = [r for r in self.audit_results['recommendations'] if r['priority'] == 'CRITICAL']
        for i, rec in enumerate(critical_recs[:3], 1):
            print(f"   {i}. {rec['action']}")
        
        print("="*80)
    
    def save_audit_report(self, filename: str = None):
        """Save detailed audit report"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ml_trading_audit_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.audit_results, f, indent=2)
        
        self.logger.info(f"📄 Audit report saved to {filename}")
        return filename

def main():
    """Run ML trading system audit"""
    auditor = MLTradingSystemAuditor()
    
    # Perform comprehensive audit
    results = auditor.audit_complete_system()
    
    # Print summary
    auditor.print_summary()
    
    # Save detailed report
    report_file = auditor.save_audit_report()
    
    print(f"\n📄 Detailed audit report saved to: {report_file}")
    
    return results

if __name__ == "__main__":
    main()
