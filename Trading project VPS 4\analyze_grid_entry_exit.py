#!/usr/bin/env python3
"""
Analyze exact grid entry and exit logic
"""

def analyze_grid_trading_logic():
    """Analyze how the grid trading system works"""
    print("📊 GRID TRADING ENTRY & EXIT ANALYSIS")
    print("=" * 80)
    
    # Grid spacing configuration
    grid_spacing = 0.0025  # 0.25%
    current_price = 104925.36
    
    print(f"🎯 GRID CONFIGURATION:")
    print(f"   Grid Spacing: {grid_spacing:.4f} ({grid_spacing*100:.2f}%)")
    print(f"   Current BTC Price: ${current_price:,.2f}")
    
    # Calculate grid levels around current price
    print(f"\n📍 GRID LEVELS AROUND CURRENT PRICE:")
    grid_levels = []
    
    for i in range(-3, 4):  # Show 7 levels around current price
        level_price = current_price * (1 + (i * grid_spacing))
        grid_levels.append((i, level_price))
        
        if i == 0:
            marker = " ← CURRENT PRICE AREA"
        else:
            marker = ""
            
        print(f"   Level {i:+2d}: ${level_price:,.2f}{marker}")
    
    # Entry Logic Analysis
    print(f"\n🚪 ENTRY LOGIC:")
    print(f"   ✅ Trades enter WHEN price touches a grid level")
    print(f"   ✅ Touch detection: Price within 0.1% of grid level")
    print(f"   ✅ ML model decides: BUY, SELL, or HOLD at that level")
    print(f"   ✅ Conservative Elite requires 75%+ confidence")
    
    # Exit Logic Analysis
    print(f"\n🚪 EXIT LOGIC:")
    print(f"   From grid_trading_core.py calculate_exit_levels():")
    print(f"   ")
    print(f"   📈 BUY TRADE EXITS:")
    print(f"      • Take Profit: 1 grid level ABOVE entry (+0.25%)")
    print(f"      • Stop Loss: 1 grid level BELOW entry (-0.25%)")
    print(f"   ")
    print(f"   📉 SELL TRADE EXITS:")
    print(f"      • Take Profit: 1 grid level BELOW entry (-0.25%)")
    print(f"      • Stop Loss: 1 grid level ABOVE entry (+0.25%)")
    
    # Example Trade Scenarios
    print(f"\n💡 EXAMPLE TRADE SCENARIOS:")
    
    # Example 1: BUY trade
    entry_level = 0
    entry_price = current_price
    buy_exit_price = entry_price * (1 + grid_spacing)
    buy_stop_price = entry_price * (1 - grid_spacing)
    
    print(f"\n   📈 EXAMPLE BUY TRADE:")
    print(f"      Entry: Level 0 @ ${entry_price:,.2f}")
    print(f"      Take Profit: Level +1 @ ${buy_exit_price:,.2f} (+0.25%)")
    print(f"      Stop Loss: Level -1 @ ${buy_stop_price:,.2f} (-0.25%)")
    print(f"      Profit if successful: ${(buy_exit_price - entry_price):,.2f}")
    
    # Example 2: SELL trade
    sell_exit_price = entry_price * (1 - grid_spacing)
    sell_stop_price = entry_price * (1 + grid_spacing)
    
    print(f"\n   📉 EXAMPLE SELL TRADE:")
    print(f"      Entry: Level 0 @ ${entry_price:,.2f}")
    print(f"      Take Profit: Level -1 @ ${sell_exit_price:,.2f} (-0.25%)")
    print(f"      Stop Loss: Level +1 @ ${sell_stop_price:,.2f} (+0.25%)")
    print(f"      Profit if successful: ${(entry_price - sell_exit_price):,.2f}")
    
    # Risk-Reward Analysis
    print(f"\n⚖️ RISK-REWARD ANALYSIS:")
    profit_amount = entry_price * grid_spacing
    risk_amount = entry_price * grid_spacing
    risk_reward_ratio = profit_amount / risk_amount
    
    print(f"   Profit Potential: ${profit_amount:,.2f}")
    print(f"   Risk Amount: ${risk_amount:,.2f}")
    print(f"   Risk:Reward Ratio: 1:{risk_reward_ratio:.1f}")
    print(f"   Win Rate Needed: {(1/(1+risk_reward_ratio))*100:.1f}% (break-even)")
    print(f"   Conservative Elite: 93.2% win rate")
    
    # Grid Touch Detection
    print(f"\n🎯 GRID TOUCH DETECTION:")
    print(f"   Touch Threshold: 0.1% of grid level price")
    
    for i, (level_id, level_price) in enumerate(grid_levels):
        touch_threshold = level_price * 0.001  # 0.1%
        distance_from_current = abs(current_price - level_price)
        
        if distance_from_current <= touch_threshold:
            status = "🟢 TOUCHED"
        else:
            status = f"⚪ ${distance_from_current:,.2f} away"
            
        print(f"   Level {level_id:+2d} @ ${level_price:,.2f}: {status}")
    
    # Trading Flow Summary
    print(f"\n🔄 COMPLETE TRADING FLOW:")
    print(f"   1. Price moves and touches a grid level (within 0.1%)")
    print(f"   2. Conservative Elite ML model analyzes market conditions")
    print(f"   3. If confidence > 75%, model decides BUY/SELL/HOLD")
    print(f"   4. If BUY/SELL, trade executes AT the grid level price")
    print(f"   5. Exit targets set at adjacent grid levels:")
    print(f"      • BUY: Profit at +1 level, Stop at -1 level")
    print(f"      • SELL: Profit at -1 level, Stop at +1 level")
    print(f"   6. Trade closes when price touches exit level")
    
    # Key Insights
    print(f"\n💎 KEY INSIGHTS:")
    print(f"   ✅ Entries: AT grid levels (when touched)")
    print(f"   ✅ Exits: AT adjacent grid levels (±1 level)")
    print(f"   ✅ Perfect grid-to-grid trading system")
    print(f"   ✅ 0.25% spacing = ~${current_price * grid_spacing:,.2f} per level")
    print(f"   ✅ 1:1 risk-reward but 93.2% win rate = profitable")
    print(f"   ✅ Conservative Elite waits for high-confidence setups")

def demonstrate_live_grid_example():
    """Show a live example with current price"""
    print(f"\n" + "=" * 80)
    print(f"🎯 LIVE GRID EXAMPLE WITH CURRENT PRICE")
    print("=" * 80)
    
    current_price = 104925.36
    grid_spacing = 0.0025
    
    # Calculate the exact grid levels
    base_price = 105000  # Approximate base for calculation
    
    # Find which grid level we're closest to
    levels_around_current = []
    for i in range(-2, 3):
        level_price = base_price * (1 + (i * grid_spacing))
        distance = abs(current_price - level_price)
        distance_pct = (distance / level_price) * 100
        
        levels_around_current.append({
            'level': i,
            'price': level_price,
            'distance': distance,
            'distance_pct': distance_pct,
            'touched': distance_pct < 0.1
        })
    
    print(f"💹 Current BTC Price: ${current_price:,.2f}")
    print(f"📊 Nearby Grid Levels:")
    
    for level in levels_around_current:
        status = "🟢 TOUCHED" if level['touched'] else f"⚪ {level['distance_pct']:.3f}% away"
        print(f"   Level {level['level']:+2d}: ${level['price']:,.2f} - {status}")
    
    # Show what would happen if we get a signal
    closest_level = min(levels_around_current, key=lambda x: x['distance'])
    
    if closest_level['touched']:
        print(f"\n🎯 TRADE OPPORTUNITY DETECTED!")
        print(f"   Grid Level {closest_level['level']} touched at ${closest_level['price']:,.2f}")
        print(f"   Conservative Elite model would analyze and decide:")
        print(f"   • BUY: Exit at ${closest_level['price'] * (1 + grid_spacing):,.2f} (+0.25%)")
        print(f"   • SELL: Exit at ${closest_level['price'] * (1 - grid_spacing):,.2f} (-0.25%)")
        print(f"   • HOLD: Wait for better conditions")
    else:
        print(f"\n⏳ WAITING FOR GRID TOUCH...")
        print(f"   Closest level: {closest_level['level']} @ ${closest_level['price']:,.2f}")
        print(f"   Need price to move ${closest_level['distance']:,.2f} to touch")

if __name__ == "__main__":
    analyze_grid_trading_logic()
    demonstrate_live_grid_example()
