#!/usr/bin/env python3
"""
Simple launcher for the Real Money Trading Webapp
This script will install dependencies and launch the Streamlit app
"""
import os
import sys
import subprocess
import time

def run_command(command, description):
    """Run a command and return success status"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
            return True
        else:
            print(f"❌ {description} - FAILED")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description} - EXCEPTION: {e}")
        return False

def main():
    """Main launcher function"""
    print("🚀 REAL MONEY TRADING WEBAPP LAUNCHER")
    print("=" * 50)
    
    # Change to the correct directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"📁 Working directory: {script_dir}")
    
    # Install dependencies
    print("\n📦 STEP 1: Installing Dependencies")
    print("-" * 30)
    
    # Core packages
    core_packages = [
        "streamlit",
        "plotly", 
        "pandas",
        "numpy",
        "ccxt",
        "python-dotenv",
        "cryptography"
    ]
    
    for package in core_packages:
        run_command(f"pip install {package}", f"Installing {package}")
    
    # Check if webapp file exists
    webapp_file = "real_money_trading_webapp.py"
    if not os.path.exists(webapp_file):
        print(f"❌ ERROR: {webapp_file} not found!")
        return False
    
    print(f"\n✅ Found webapp file: {webapp_file}")
    
    # Launch the webapp
    print("\n🌐 STEP 2: Launching Webapp")
    print("-" * 30)
    print("🔄 Starting Streamlit server...")
    print("📱 The webapp will open at: http://localhost:8501")
    print("⚠️  IMPORTANT: This is configured for REAL MONEY trading!")
    print("💰 Start with small amounts (e.g., $300)")
    print("\n🛑 Press Ctrl+C in the terminal to stop the server")
    print("=" * 50)
    
    # Start streamlit
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", webapp_file], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Webapp stopped by user")
    except Exception as e:
        print(f"\n❌ Error launching webapp: {e}")
        print("💡 Try running manually: streamlit run real_money_trading_webapp.py")

if __name__ == "__main__":
    main()
