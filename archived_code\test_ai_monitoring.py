#!/usr/bin/env python3
"""
Test AI Signal Monitoring System
"""

import time
from datetime import datetime
from ai_signal_monitor import AISignalMonitor

def test_ai_monitoring():
    print("🤖 TESTING AI SIGNAL MONITORING")
    print("=" * 60)
    
    # Create monitor
    monitor = AISignalMonitor()
    
    print("📊 Starting AI monitoring test...")
    monitor.start_monitoring()
    
    try:
        # Test for 30 seconds
        for i in range(6):
            time.sleep(5)
            
            status = monitor.get_current_status()
            
            print(f"\n📊 AI STATUS UPDATE #{i+1}:")
            print(f"   Monitoring: {'✅' if status['monitoring'] else '❌'}")
            print(f"   Current Confidence: {status['current_confidence']:.1%}")
            print(f"   Threshold: {status['confidence_threshold']:.1%}")
            print(f"   Above Threshold: {'✅' if status.get('above_threshold', False) else '❌'}")
            print(f"   Signals (1h): {status['signals_last_hour']}")
            print(f"   Total Signals: {status['total_signals']}")
            print(f"   Readings Stored: {status['readings_stored']}")
            
            if status.get('current_price'):
                print(f"   Current Price: ${status['current_price']:,.2f}")
            
            # Check for signals
            if status.get('above_threshold', False):
                print(f"   🎯 SIGNAL READY - Confidence above threshold!")
                
                recent_signals = monitor.get_recent_signals(1)
                if recent_signals:
                    signal = recent_signals[-1]
                    print(f"   Latest Signal: {signal['action']} @ ${signal['price']:,.2f}")
                    print(f"   Signal Confidence: {signal['confidence']:.1%}")
            
            # Get trend
            if i >= 2:  # Need some data for trend
                trend = monitor.get_confidence_trend(10)  # 10-minute trend
                if trend['trend'] != 'insufficient_data':
                    print(f"   Trend (10min): {trend['trend']} (strength: {trend['strength']:.3f})")
    
    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
    
    finally:
        monitor.stop_monitoring()
        print("\n✅ AI monitoring test complete")

def test_confidence_calculation():
    """Test the confidence calculation logic"""
    print("\n🧪 TESTING CONFIDENCE CALCULATION")
    print("=" * 60)
    
    monitor = AISignalMonitor()
    
    # Test with different price scenarios
    test_prices = [104500, 105000, 105500, 106000]
    
    for price in test_prices:
        confidence_data = monitor._calculate_ai_confidence(price)
        
        print(f"\n💹 Price: ${price:,.2f}")
        print(f"   Confidence: {confidence_data['confidence']:.1%}")
        print(f"   Grid Level: {confidence_data['grid_level']}")
        
        probs = confidence_data['probabilities']
        print(f"   Action Probabilities:")
        print(f"     BUY: {probs['BUY']:.1%}")
        print(f"     SELL: {probs['SELL']:.1%}")
        print(f"     HOLD: {probs['HOLD']:.1%}")
        
        indicators = confidence_data['indicators']
        print(f"   Technical Indicators:")
        print(f"     VWAP: {indicators['vwap_signal']:.3f}")
        print(f"     BB Position: {indicators['bb_position']:.3f}")
        print(f"     ETH/BTC: {indicators['eth_btc_ratio']:.4f}")
        print(f"     Flow: {indicators['flow_strength']:.1f}")
        
        # Check if signal would be generated
        if confidence_data['confidence'] >= monitor.confidence_threshold:
            max_action = max(probs, key=probs.get)
            if max_action in ['BUY', 'SELL'] and probs[max_action] > 0.4:
                print(f"   🎯 SIGNAL: {max_action} (Confidence above 75%)")
            else:
                print(f"   ⏳ NO SIGNAL: Action probability too low")
        else:
            print(f"   ⏳ NO SIGNAL: Confidence below 75%")

def demonstrate_signal_generation():
    """Demonstrate how signals are generated"""
    print("\n🎯 SIGNAL GENERATION DEMONSTRATION")
    print("=" * 60)
    
    print("📋 CONSERVATIVE ELITE SIGNAL CRITERIA:")
    print("   1. Overall Confidence > 75%")
    print("   2. Action Probability > 40%")
    print("   3. Action must be BUY or SELL (not HOLD)")
    print("   4. Based on 4 technical indicators:")
    print("      • VWAP Signal (30% weight)")
    print("      • Bollinger Bands Position (25% weight)")
    print("      • ETH/BTC Ratio (25% weight)")
    print("      • Flow Strength (20% weight)")
    
    print("\n🔄 MONITORING PROCESS:")
    print("   • Check every 5 seconds")
    print("   • Store confidence history (last 1000 readings)")
    print("   • Generate signals when criteria met")
    print("   • Track signal history (last 100 signals)")
    
    print("\n📊 WEBAPP INTEGRATION:")
    print("   • Real-time confidence display")
    print("   • Threshold indicator (75%)")
    print("   • Confidence trend analysis")
    print("   • Technical indicator breakdown")
    print("   • Recent signals list")

def main():
    """Main test function"""
    print("🤖 AI SIGNAL MONITORING SYSTEM TEST")
    print("=" * 80)
    
    # Test confidence calculation
    test_confidence_calculation()
    
    # Demonstrate signal generation
    demonstrate_signal_generation()
    
    # Test live monitoring
    print(f"\n🔴 LIVE MONITORING TEST")
    print("Press Ctrl+C to stop...")
    test_ai_monitoring()

if __name__ == "__main__":
    main()
