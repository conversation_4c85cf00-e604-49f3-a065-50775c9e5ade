#!/usr/bin/env python3
"""
COMPREHENSIVE WEBAPP HEALTH CHECK & AUDIT
==========================================
Complete diagnostic tool to investigate why trades are not being taken
and ensure the trading system is functioning correctly.
"""

import requests
import time
import json
from datetime import datetime
from typing import Dict, List, Any

class WebAppHealthChecker:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.results = {}
        self.issues = []
        self.recommendations = []
        
    def run_comprehensive_audit(self):
        """Run complete webapp health check and audit."""
        print("🔍 COMPREHENSIVE WEBAPP HEALTH CHECK & AUDIT")
        print("=" * 60)
        print(f"🌐 Target URL: {self.base_url}")
        print(f"⏰ Audit Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Core system checks
        self.check_webapp_connectivity()
        self.check_trading_engine_status()
        self.check_model_configuration()
        self.check_risk_management_settings()
        
        # Trading functionality checks
        self.check_auto_trading_status()
        self.check_signal_generation()
        self.check_trade_entry_conditions()
        self.check_position_sizing()
        
        # Advanced diagnostics
        self.diagnose_trade_blocking_conditions()
        self.test_forced_trade_functionality()
        
        # Generate comprehensive report
        self.generate_health_report()
        
    def check_webapp_connectivity(self):
        """Test basic webapp connectivity and responsiveness."""
        print("\n🌐 CHECKING WEBAPP CONNECTIVITY...")
        
        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=10)
            if response.status_code == 200:
                print("✅ Webapp is accessible and responding")
                self.results['connectivity'] = 'HEALTHY'
            else:
                print(f"❌ Webapp returned status code: {response.status_code}")
                self.results['connectivity'] = 'ERROR'
                self.issues.append(f"HTTP Status Code: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to webapp - is it running?")
            self.results['connectivity'] = 'FAILED'
            self.issues.append("Webapp not accessible - check if server is running")
        except Exception as e:
            print(f"❌ Connection error: {e}")
            self.results['connectivity'] = 'ERROR'
            self.issues.append(f"Connection error: {str(e)}")
    
    def check_trading_engine_status(self):
        """Check trading engine status and configuration."""
        print("\n🤖 CHECKING TRADING ENGINE STATUS...")
        
        try:
            response = requests.get(f"{self.base_url}/api/trading_status")
            data = response.json()
            
            is_running = data.get('is_running', False)
            is_live_mode = data.get('is_live_mode', False)
            current_price = data.get('current_price', 0)
            
            print(f"   Engine Running: {'✅ YES' if is_running else '❌ NO'}")
            print(f"   Live Mode: {'✅ YES' if is_live_mode else '⚠️ SIMULATION'}")
            print(f"   Current BTC Price: ${current_price:,.2f}")
            
            if not is_running:
                self.issues.append("Trading engine is not running")
                self.recommendations.append("Start trading engine via /api/start_trading")
            
            self.results['engine_status'] = {
                'running': is_running,
                'live_mode': is_live_mode,
                'price': current_price
            }
            
        except Exception as e:
            print(f"❌ Error checking engine status: {e}")
            self.results['engine_status'] = 'ERROR'
            self.issues.append(f"Engine status check failed: {str(e)}")
    
    def check_model_configuration(self):
        """Verify model configuration and parameters."""
        print("\n🧠 CHECKING MODEL CONFIGURATION...")
        
        try:
            response = requests.get(f"{self.base_url}/api/trading_status")
            data = response.json()
            model_info = data.get('model_info', {})
            
            model_id = model_info.get('model_id', 'Unknown')
            composite_score = model_info.get('composite_score', 0)
            tcn_weight = model_info.get('tcn_weight', 0)
            cnn_weight = model_info.get('cnn_weight', 0)
            ppo_weight = model_info.get('ppo_weight', 0)
            
            print(f"   Model ID: {model_id}")
            print(f"   Composite Score: {composite_score}%")
            print(f"   TCN Weight: {tcn_weight}%")
            print(f"   CNN Weight: {cnn_weight}%")
            print(f"   PPO Weight: {ppo_weight}%")
            
            # Validate expected configuration
            if composite_score < 90:
                self.issues.append(f"Composite score ({composite_score}%) below expected 91.4%")
            
            total_weights = tcn_weight + cnn_weight + ppo_weight
            if abs(total_weights - 100) > 1:
                self.issues.append(f"Model weights don't sum to 100% (got {total_weights}%)")
            
            self.results['model_config'] = model_info
            
        except Exception as e:
            print(f"❌ Error checking model config: {e}")
            self.results['model_config'] = 'ERROR'
            self.issues.append(f"Model config check failed: {str(e)}")
    
    def check_risk_management_settings(self):
        """Verify risk management parameters."""
        print("\n⚖️ CHECKING RISK MANAGEMENT SETTINGS...")
        
        try:
            response = requests.get(f"{self.base_url}/api/trading_status")
            data = response.json()
            risk_mgmt = data.get('risk_management', {})
            
            max_daily_trades = risk_mgmt.get('max_daily_trades', 0)
            max_open_positions = risk_mgmt.get('max_open_positions', 0)
            daily_loss_limit = risk_mgmt.get('daily_loss_limit', 0)
            daily_profit_target = risk_mgmt.get('daily_profit_target', 0)
            
            print(f"   Max Daily Trades: {max_daily_trades}")
            print(f"   Max Open Positions: {max_open_positions}")
            print(f"   Daily Loss Limit: ${daily_loss_limit}")
            print(f"   Daily Profit Target: ${daily_profit_target}")
            
            # Check for overly restrictive settings
            if max_daily_trades < 5:
                self.issues.append(f"Max daily trades ({max_daily_trades}) may be too restrictive")
            
            if max_open_positions < 1:
                self.issues.append("Max open positions is 0 - no trades can be opened")
            
            self.results['risk_management'] = risk_mgmt
            
        except Exception as e:
            print(f"❌ Error checking risk management: {e}")
            self.results['risk_management'] = 'ERROR'
            self.issues.append(f"Risk management check failed: {str(e)}")
    
    def check_auto_trading_status(self):
        """Check if auto trading is active and functioning."""
        print("\n🔄 CHECKING AUTO TRADING STATUS...")
        
        try:
            # Check current trading status
            response = requests.get(f"{self.base_url}/api/trading_status")
            data = response.json()
            performance = data.get('performance', {})
            
            total_trades = performance.get('total_trades', 0)
            daily_trades = performance.get('daily_trades', 0)
            open_positions = performance.get('open_positions', 0)
            
            print(f"   Total Trades: {total_trades}")
            print(f"   Daily Trades: {daily_trades}")
            print(f"   Open Positions: {open_positions}")
            
            # Check if trading is happening
            if total_trades == 0:
                self.issues.append("No trades have been executed - auto trading may not be working")
                self.recommendations.append("Check signal generation and entry conditions")
            
            if daily_trades == 0:
                self.issues.append("No trades today - check if conditions are too restrictive")
            
            self.results['auto_trading'] = {
                'total_trades': total_trades,
                'daily_trades': daily_trades,
                'open_positions': open_positions
            }
            
        except Exception as e:
            print(f"❌ Error checking auto trading: {e}")
            self.results['auto_trading'] = 'ERROR'
            self.issues.append(f"Auto trading check failed: {str(e)}")
    
    def check_signal_generation(self):
        """Test signal generation functionality."""
        print("\n📡 CHECKING SIGNAL GENERATION...")
        
        try:
            # Test forced trade to verify signal generation
            response = requests.post(f"{self.base_url}/api/force_test_trade", 
                                   json={'direction': 'BUY'})
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    print("✅ Signal generation working - forced trade successful")
                    trade_id = data.get('trade_id', 'Unknown')
                    confidence = data.get('confidence', 0)
                    print(f"   Trade ID: {trade_id}")
                    print(f"   Confidence: {confidence}%")
                    self.results['signal_generation'] = 'WORKING'
                else:
                    print(f"❌ Forced trade failed: {data.get('message', 'Unknown error')}")
                    self.issues.append("Signal generation failed - forced trade unsuccessful")
                    self.results['signal_generation'] = 'FAILED'
            else:
                print(f"❌ Signal generation test failed with status {response.status_code}")
                self.issues.append(f"Signal generation API returned {response.status_code}")
                self.results['signal_generation'] = 'ERROR'
                
        except Exception as e:
            print(f"❌ Error testing signal generation: {e}")
            self.results['signal_generation'] = 'ERROR'
            self.issues.append(f"Signal generation test failed: {str(e)}")
    
    def check_trade_entry_conditions(self):
        """Analyze trade entry conditions and potential blocking factors."""
        print("\n🚪 CHECKING TRADE ENTRY CONDITIONS...")
        
        try:
            # Get current status to analyze blocking conditions
            response = requests.get(f"{self.base_url}/api/trading_status")
            data = response.json()
            
            performance = data.get('performance', {})
            risk_mgmt = data.get('risk_management', {})
            
            daily_trades = performance.get('daily_trades', 0)
            open_positions = performance.get('open_positions', 0)
            daily_pnl = performance.get('daily_pnl', 0)
            
            max_daily_trades = risk_mgmt.get('max_daily_trades', 10)
            max_open_positions = risk_mgmt.get('max_open_positions', 3)
            daily_loss_limit = risk_mgmt.get('daily_loss_limit', 50)
            daily_profit_target = risk_mgmt.get('daily_profit_target', 100)
            
            print(f"   Daily Trades: {daily_trades}/{max_daily_trades}")
            print(f"   Open Positions: {open_positions}/{max_open_positions}")
            print(f"   Daily P&L: ${daily_pnl:+.2f}")
            print(f"   Loss Limit: ${daily_loss_limit}")
            print(f"   Profit Target: ${daily_profit_target}")
            
            # Check for blocking conditions
            blocking_conditions = []
            
            if daily_trades >= max_daily_trades:
                blocking_conditions.append("Daily trade limit reached")
            
            if open_positions >= max_open_positions:
                blocking_conditions.append("Maximum open positions reached")
            
            if daily_pnl <= -daily_loss_limit:
                blocking_conditions.append("Daily loss limit hit")
            
            if daily_pnl >= daily_profit_target:
                blocking_conditions.append("Daily profit target achieved")
            
            if blocking_conditions:
                print("⚠️ BLOCKING CONDITIONS DETECTED:")
                for condition in blocking_conditions:
                    print(f"     - {condition}")
                self.issues.extend(blocking_conditions)
            else:
                print("✅ No blocking conditions detected")
            
            self.results['entry_conditions'] = {
                'blocking_conditions': blocking_conditions,
                'daily_trades': daily_trades,
                'max_daily_trades': max_daily_trades,
                'open_positions': open_positions,
                'max_open_positions': max_open_positions,
                'daily_pnl': daily_pnl
            }
            
        except Exception as e:
            print(f"❌ Error checking entry conditions: {e}")
            self.results['entry_conditions'] = 'ERROR'
            self.issues.append(f"Entry conditions check failed: {str(e)}")

    def check_position_sizing(self):
        """Verify position sizing calculations."""
        print("\n💰 CHECKING POSITION SIZING...")

        try:
            response = requests.get(f"{self.base_url}/api/cross_margin_analysis")
            data = response.json()

            account_config = data.get('account_config', {})
            buy_position = data.get('buy_position', {})
            accuracy_check = data.get('accuracy_check', {})

            account_size = account_config.get('account_size', 0)
            risk_per_trade = account_config.get('risk_per_trade', 0)

            position_size_btc = buy_position.get('position_size_btc', 0)
            expected_profit = buy_position.get('expected_profit', 0)
            actual_risk = buy_position.get('actual_risk', 0)
            risk_reward_ratio = accuracy_check.get('risk_reward_ratio_buy', 0)

            print(f"   Account Size: ${account_size}")
            print(f"   Risk per Trade: ${risk_per_trade}")
            print(f"   Expected Profit: ${expected_profit}")
            print(f"   Position Size: {position_size_btc:.6f} BTC")
            print(f"   Risk-Reward Ratio: {risk_reward_ratio}:1")

            # Validate position sizing
            if abs(expected_profit - 25.0) > 1.0:
                self.issues.append(f"Expected profit ({expected_profit}) not close to target $25")

            if abs(actual_risk - 10.0) > 1.0:
                self.issues.append(f"Actual risk ({actual_risk}) not close to target $10")

            if abs(risk_reward_ratio - 2.5) > 0.1:
                self.issues.append(f"Risk-reward ratio ({risk_reward_ratio}) not close to target 2.5")

            self.results['position_sizing'] = {
                'account_size': account_size,
                'risk_per_trade': risk_per_trade,
                'expected_profit': expected_profit,
                'position_size_btc': position_size_btc,
                'risk_reward_ratio': risk_reward_ratio
            }

        except Exception as e:
            print(f"❌ Error checking position sizing: {e}")
            self.results['position_sizing'] = 'ERROR'
            self.issues.append(f"Position sizing check failed: {str(e)}")

    def diagnose_trade_blocking_conditions(self):
        """Deep dive into why trades might not be executing."""
        print("\n🔍 DIAGNOSING TRADE BLOCKING CONDITIONS...")

        try:
            # Check if engine is running
            if not self.results.get('engine_status', {}).get('running', False):
                print("❌ CRITICAL: Trading engine is not running")
                self.recommendations.append("Start trading engine: POST /api/start_trading")
                return

            # Check entry conditions
            entry_conditions = self.results.get('entry_conditions', {})
            blocking_conditions = entry_conditions.get('blocking_conditions', [])

            if blocking_conditions:
                print("⚠️ TRADES BLOCKED BY RISK MANAGEMENT:")
                for condition in blocking_conditions:
                    print(f"   - {condition}")

                # Provide specific recommendations
                if "Daily trade limit reached" in blocking_conditions:
                    self.recommendations.append("Increase max_daily_trades or wait for next day")

                if "Maximum open positions reached" in blocking_conditions:
                    self.recommendations.append("Close existing positions or increase max_open_positions")

                if "Daily loss limit hit" in blocking_conditions:
                    self.recommendations.append("Reset daily stats or wait for next day")

                if "Daily profit target achieved" in blocking_conditions:
                    self.recommendations.append("Disable profit target stop or wait for next day")

            else:
                print("✅ No risk management blocks detected")
                print("🔍 Checking signal generation probability...")

                # The system uses random probability for signal generation
                # Conservative model: 8% chance per 2-second cycle
                print("   Signal Generation: 8% probability every 2 seconds")
                print("   Expected Time Between Signals: ~25 seconds")
                print("   Conservative Model: 6.1 trades/day target")

                self.recommendations.append("Monitor for 1-2 minutes to see if signals generate")
                self.recommendations.append("Use force_test_trade to verify system is working")

        except Exception as e:
            print(f"❌ Error in trade blocking diagnosis: {e}")
            self.issues.append(f"Trade blocking diagnosis failed: {str(e)}")

    def test_forced_trade_functionality(self):
        """Test the forced trade functionality to verify system works."""
        print("\n🧪 TESTING FORCED TRADE FUNCTIONALITY...")

        try:
            # Test forced BUY trade
            response = requests.post(f"{self.base_url}/api/force_test_trade",
                                   json={'direction': 'BUY'})

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    print("✅ Forced BUY trade successful")
                    print(f"   Trade ID: {data.get('trade_id', 'Unknown')}")
                    print(f"   Entry Price: ${data.get('entry_price', 0):,.2f}")
                    print(f"   Confidence: {data.get('confidence', 0)}%")

                    # Test cycle test
                    print("\n🔄 Testing cycle test...")
                    cycle_response = requests.post(f"{self.base_url}/api/test_trading_cycle")

                    if cycle_response.status_code == 200:
                        cycle_data = cycle_response.json()
                        if cycle_data.get('status') == 'success':
                            print("✅ Cycle test successful")
                            print(f"   P&L: ${cycle_data.get('pnl', 0):+.2f}")
                            print(f"   Entry: ${cycle_data.get('entry_price', 0):,.2f}")
                            print(f"   Exit: ${cycle_data.get('exit_price', 0):,.2f}")

                            # Validate P&L is close to $25 target
                            pnl = cycle_data.get('pnl', 0)
                            if abs(pnl - 25.0) < 5.0:
                                print("✅ P&L close to $25 target")
                            else:
                                self.issues.append(f"Cycle test P&L ({pnl}) not close to $25 target")
                        else:
                            print(f"❌ Cycle test failed: {cycle_data.get('message', 'Unknown error')}")
                            self.issues.append("Cycle test failed")
                    else:
                        print(f"❌ Cycle test API error: {cycle_response.status_code}")
                        self.issues.append(f"Cycle test API returned {cycle_response.status_code}")

                    self.results['forced_trade_test'] = 'WORKING'
                else:
                    print(f"❌ Forced trade failed: {data.get('message', 'Unknown error')}")
                    self.issues.append("Forced trade functionality failed")
                    self.results['forced_trade_test'] = 'FAILED'
            else:
                print(f"❌ Forced trade API error: {response.status_code}")
                self.issues.append(f"Forced trade API returned {response.status_code}")
                self.results['forced_trade_test'] = 'ERROR'

        except Exception as e:
            print(f"❌ Error testing forced trade: {e}")
            self.results['forced_trade_test'] = 'ERROR'
            self.issues.append(f"Forced trade test failed: {str(e)}")

    def generate_health_report(self):
        """Generate comprehensive health report with recommendations."""
        print("\n" + "=" * 60)
        print("📋 COMPREHENSIVE HEALTH REPORT")
        print("=" * 60)

        # Overall health status
        total_checks = len(self.results)
        healthy_checks = sum(1 for result in self.results.values()
                           if result not in ['ERROR', 'FAILED'])
        health_percentage = (healthy_checks / total_checks * 100) if total_checks > 0 else 0

        print(f"🏥 OVERALL HEALTH: {health_percentage:.1f}% ({healthy_checks}/{total_checks} checks passed)")

        if health_percentage >= 90:
            print("✅ SYSTEM STATUS: HEALTHY")
        elif health_percentage >= 70:
            print("⚠️ SYSTEM STATUS: WARNING - Some issues detected")
        else:
            print("❌ SYSTEM STATUS: CRITICAL - Multiple issues detected")

        # Issues summary
        if self.issues:
            print(f"\n🚨 ISSUES DETECTED ({len(self.issues)}):")
            for i, issue in enumerate(self.issues, 1):
                print(f"   {i}. {issue}")
        else:
            print("\n✅ NO ISSUES DETECTED")

        # Recommendations
        if self.recommendations:
            print(f"\n💡 RECOMMENDATIONS ({len(self.recommendations)}):")
            for i, rec in enumerate(self.recommendations, 1):
                print(f"   {i}. {rec}")

        # Detailed results
        print(f"\n📊 DETAILED RESULTS:")
        for check, result in self.results.items():
            status = "✅" if result not in ['ERROR', 'FAILED'] else "❌"
            print(f"   {status} {check.replace('_', ' ').title()}: {result}")

        print("\n" + "=" * 60)
        print("🔍 AUDIT COMPLETE")
        print("=" * 60)

if __name__ == "__main__":
    checker = WebAppHealthChecker()
    checker.run_comprehensive_audit()
