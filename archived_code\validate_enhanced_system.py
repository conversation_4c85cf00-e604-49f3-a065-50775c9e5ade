"""
Enhanced System Validation Script
Quick validation of all enhanced system components
"""

import os
import sys
import logging
from datetime import datetime

def setup_logging():
    """Setup basic logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_imports():
    """Test all enhanced system imports"""
    print("🔍 Testing Enhanced System Imports...")
    print("="*50)
    
    try:
        # Add config to path
        sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
        
        # Test config import
        print("📋 Testing config import...")
        from trading_config import TradingConfig
        config = TradingConfig()
        print(f"✅ Config loaded: Initial capital ${config.INITIAL_CAPITAL}")
        
        # Test enhanced test trading engine
        print("🧪 Testing enhanced test trading engine...")
        from enhanced_test_trading_engine import TestTradingEngine, TradingMode, TestTrade
        test_engine = TestTradingEngine(config)
        print(f"✅ Test engine created: Mode {test_engine.mode.value}")
        
        # Test validator
        print("✅ Testing test mode validator...")
        from test_mode_validator import TestModeValidator
        validator = TestModeValidator(config)
        print("✅ Validator created successfully")
        
        # Test enhanced auditor
        print("🔍 Testing enhanced code auditor...")
        from comprehensive_code_audit import EnhancedCodeAuditor
        auditor = EnhancedCodeAuditor(config)
        print("✅ Enhanced auditor created successfully")
        
        print("\n🎉 ALL IMPORTS SUCCESSFUL!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality of enhanced components"""
    print("\n🧪 Testing Basic Functionality...")
    print("="*50)
    
    try:
        # Import components
        sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
        from trading_config import TradingConfig
        from enhanced_test_trading_engine import TestTradingEngine, TestTrade
        from test_mode_validator import TestModeValidator
        
        config = TradingConfig()
        
        # Test test engine status
        print("📊 Testing test engine status...")
        test_engine = TestTradingEngine(config)
        status = test_engine.get_test_status()
        print(f"✅ Test engine status: {status['mode']}, Balance: ${status['current_balance']}")
        
        # Test validator configuration
        print("✅ Testing validator configuration...")
        validator = TestModeValidator(config)
        config_validation = validator._validate_configuration(test_engine)
        print(f"✅ Configuration validation: {config_validation['passed']}")
        
        # Test test trade creation
        print("📝 Testing test trade creation...")
        test_trade = TestTrade(
            trade_id="TEST_001",
            symbol="BTCUSDT",
            side="BUY",
            action="BUY",
            entry_price=50000.0,
            exit_price=50125.0,
            position_size=10.0,
            quantity=0.0002,
            risk_amount=10.0,
            target_profit=20.0,
            entry_time=datetime.now().isoformat()
        )
        print(f"✅ Test trade created: {test_trade.trade_id}")
        
        # Test export functionality
        print("📤 Testing export functionality...")
        export_data = test_engine.export_test_results()
        print(f"✅ Export data created: Mode {export_data['mode']}")
        
        print("\n🎉 ALL FUNCTIONALITY TESTS PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Functionality test error: {e}")
        return False

def test_file_structure():
    """Test enhanced system file structure"""
    print("\n📁 Testing Enhanced System File Structure...")
    print("="*50)
    
    required_files = [
        'enhanced_test_trading_engine.py',
        'test_mode_validator.py',
        'enhanced_trading_webapp.py',
        'comprehensive_code_audit.py',
        'launch_enhanced_trading_system.py',
        'test_enhanced_system.py',
        'validate_enhanced_system.py',
        'ENHANCED_SYSTEM_SUMMARY.md'
    ]
    
    missing_files = []
    existing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            existing_files.append(file)
            print(f"✅ {file}")
        else:
            missing_files.append(file)
            print(f"❌ {file} - MISSING")
    
    print(f"\n📊 File Structure Summary:")
    print(f"   Existing files: {len(existing_files)}/{len(required_files)}")
    print(f"   Missing files: {len(missing_files)}")
    
    if missing_files:
        print(f"   Missing: {', '.join(missing_files)}")
        return False
    else:
        print("🎉 ALL REQUIRED FILES PRESENT!")
        return True

def test_core_integration():
    """Test integration with core system files"""
    print("\n🔗 Testing Core System Integration...")
    print("="*50)
    
    core_files = [
        'grid_trading_core.py',
        'grid_feature_engineering.py',
        'grid_composite_metrics.py',
        'binance_data_collector.py',
        'config/trading_config.py'
    ]
    
    integration_score = 0
    total_tests = len(core_files)
    
    for file in core_files:
        if os.path.exists(file):
            print(f"✅ {file} - Available")
            integration_score += 1
        else:
            print(f"❌ {file} - Missing")
    
    integration_percentage = (integration_score / total_tests) * 100
    print(f"\n📊 Integration Score: {integration_percentage:.1f}%")
    
    if integration_percentage >= 80:
        print("🎉 CORE INTEGRATION SUCCESSFUL!")
        return True
    else:
        print("⚠️ CORE INTEGRATION ISSUES DETECTED")
        return False

def generate_validation_report():
    """Generate validation report"""
    print("\n📋 Generating Validation Report...")
    print("="*50)
    
    report = {
        'validation_time': datetime.now().isoformat(),
        'system_version': 'Enhanced Grid Trading System v2.0',
        'validation_results': {
            'imports': False,
            'functionality': False,
            'file_structure': False,
            'core_integration': False
        },
        'overall_status': 'UNKNOWN'
    }
    
    # Run all tests
    report['validation_results']['imports'] = test_imports()
    report['validation_results']['functionality'] = test_basic_functionality()
    report['validation_results']['file_structure'] = test_file_structure()
    report['validation_results']['core_integration'] = test_core_integration()
    
    # Calculate overall status
    passed_tests = sum(report['validation_results'].values())
    total_tests = len(report['validation_results'])
    success_rate = (passed_tests / total_tests) * 100
    
    if success_rate == 100:
        report['overall_status'] = 'FULLY_OPERATIONAL'
        status_emoji = '🎉'
        status_text = 'FULLY OPERATIONAL'
    elif success_rate >= 75:
        report['overall_status'] = 'MOSTLY_OPERATIONAL'
        status_emoji = '✅'
        status_text = 'MOSTLY OPERATIONAL'
    elif success_rate >= 50:
        report['overall_status'] = 'PARTIALLY_OPERATIONAL'
        status_emoji = '⚠️'
        status_text = 'PARTIALLY OPERATIONAL'
    else:
        report['overall_status'] = 'NOT_OPERATIONAL'
        status_emoji = '❌'
        status_text = 'NOT OPERATIONAL'
    
    # Display final report
    print("\n" + "="*60)
    print("🎯 ENHANCED SYSTEM VALIDATION REPORT")
    print("="*60)
    print(f"📅 Validation Time: {report['validation_time']}")
    print(f"🔧 System Version: {report['system_version']}")
    print(f"📊 Success Rate: {success_rate:.1f}%")
    print(f"🎯 Overall Status: {status_emoji} {status_text}")
    print("\n📋 Test Results:")
    
    for test_name, result in report['validation_results'].items():
        emoji = '✅' if result else '❌'
        print(f"   {emoji} {test_name.replace('_', ' ').title()}: {'PASSED' if result else 'FAILED'}")
    
    print("="*60)
    
    if success_rate == 100:
        print("🚀 SYSTEM READY FOR DEPLOYMENT!")
        print("💡 You can now use:")
        print("   • python launch_enhanced_trading_system.py")
        print("   • python enhanced_trading_webapp.py")
        print("   • streamlit run enhanced_trading_webapp.py")
    elif success_rate >= 75:
        print("⚠️ SYSTEM MOSTLY READY - Minor issues detected")
        print("💡 Review failed tests and address issues")
    else:
        print("❌ SYSTEM NOT READY - Major issues detected")
        print("💡 Address failed tests before deployment")
    
    # Save report
    try:
        import json
        with open('enhanced_system_validation_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        print(f"\n📁 Validation report saved: enhanced_system_validation_report.json")
    except Exception as e:
        print(f"⚠️ Could not save report: {e}")
    
    return report

def main():
    """Main validation function"""
    setup_logging()
    
    print("\n" + "="*60)
    print("🎯 ENHANCED TRADING SYSTEM VALIDATION")
    print("="*60)
    print("🔍 Validating all enhanced system components...")
    print("📅 Validation started:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("="*60)
    
    # Generate comprehensive validation report
    report = generate_validation_report()
    
    # Return success status
    return report['overall_status'] in ['FULLY_OPERATIONAL', 'MOSTLY_OPERATIONAL']

if __name__ == "__main__":
    success = main()
    exit_code = 0 if success else 1
    sys.exit(exit_code)
