{"success": true, "target_score": 0.876, "best_score": 0.9249181830732458, "best_profit": 2013.7509303830132, "best_combined_score": 1.6598603582321894, "iterations_completed": 5, "max_iterations": 25, "training_iterations": [{"iteration": 1, "composite_score": 0.6890666709728734, "backtest_results": {"total_trades": 154, "winning_trades": 111, "win_rate": 0.7256266683891495, "total_profit": 1680.5430703008847, "final_balance": 1980.5430703008847, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 3, "tcn_filters": 64, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:39:21.111751"}, {"iteration": 2, "composite_score": 0.775536478582285, "backtest_results": {"total_trades": 165, "winning_trades": 125, "win_rate": 0.760214591432914, "total_profit": 1421.7428415741845, "final_balance": 1721.7428415741845, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:39:21.618386"}, {"iteration": 3, "composite_score": 0.8242629876359564, "backtest_results": {"total_trades": 178, "winning_trades": 138, "win_rate": 0.7797051950543825, "total_profit": 2013.7509303830132, "final_balance": 2313.750930383013, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:39:22.123456"}, {"iteration": 4, "composite_score": 0.8008764862057542, "backtest_results": {"total_trades": 152, "winning_trades": 117, "win_rate": 0.7703505944823017, "total_profit": 1700.0351856544326, "final_balance": 2000.0351856544326, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:39:22.626917"}, {"iteration": 5, "composite_score": 0.9249181830732458, "backtest_results": {"total_trades": 168, "winning_trades": 137, "win_rate": 0.8199672732292984, "total_profit": 1547.1451331060457, "final_balance": 1847.1451331060457, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:39:23.129578"}], "best_model": {"iteration": 5, "composite_score": 0.9249181830732458, "backtest_results": {"total_trades": 168, "winning_trades": 137, "win_rate": 0.8199672732292984, "total_profit": 1547.1451331060457, "final_balance": 1847.1451331060457, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:39:23.129578"}, "best_profit_model": {"iteration": 3, "composite_score": 0.8242629876359564, "backtest_results": {"total_trades": 178, "winning_trades": 138, "win_rate": 0.7797051950543825, "total_profit": 2013.7509303830132, "final_balance": 2313.750930383013, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:39:22.123456"}, "locked_parameters": {"grid_spacing": 0.0025, "risk_reward_ratio": 2.5, "training_days": 60, "testing_days": 30, "tcn_weight": 0.4, "cnn_weight": 0.4, "ppo_weight": 0.2}, "final_hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "results_source": "PURE_OUT_OF_SAMPLE_BACKTEST_ONLY", "reinforcement_learning_applied": true, "hyperparameter_optimization_applied": true, "min_trades_per_day": 5, "min_total_trades": 150, "optimization_criteria": "HIGHEST_COMPOSITE_REWARD_AND_HIGHEST_NET_PROFIT"}