{"success": true, "target_score": 0.876, "best_score": 0.9254833767770329, "iterations_completed": 6, "max_iterations": 25, "training_iterations": [{"iteration": 1, "composite_score": 0.7150988413900679, "backtest_results": {"total_trades": 34, "winning_trades": 25, "win_rate": 0.7360395365560272, "total_profit": 542.82988640044, "final_balance": 842.82988640044, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 3, "tcn_filters": 64, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:12:44.280387"}, {"iteration": 2, "composite_score": 0.776652207972334, "backtest_results": {"total_trades": 42, "winning_trades": 31, "win_rate": 0.7606608831889337, "total_profit": 742.2910663025282, "final_balance": 1042.2910663025282, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:12:44.783160"}, {"iteration": 3, "composite_score": 0.8306980944813266, "backtest_results": {"total_trades": 25, "winning_trades": 19, "win_rate": 0.7822792377925307, "total_profit": 786.2909245972751, "final_balance": 1086.290924597275, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:12:45.285854"}, {"iteration": 4, "composite_score": 0.8029391632640834, "backtest_results": {"total_trades": 28, "winning_trades": 21, "win_rate": 0.7711756653056334, "total_profit": 706.567090390889, "final_balance": 1006.567090390889, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:12:45.789957"}, {"iteration": 5, "composite_score": 0.8575288691772212, "backtest_results": {"total_trades": 29, "winning_trades": 22, "win_rate": 0.7930115476708886, "total_profit": 748.7551465104643, "final_balance": 1048.7551465104643, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:12:46.292963"}, {"iteration": 6, "composite_score": 0.9254833767770329, "backtest_results": {"total_trades": 39, "winning_trades": 31, "win_rate": 0.8201933507108132, "total_profit": 841.0044496995779, "final_balance": 1141.004449699578, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:12:46.798194"}], "best_model": {"iteration": 6, "composite_score": 0.9254833767770329, "backtest_results": {"total_trades": 39, "winning_trades": 31, "win_rate": 0.8201933507108132, "total_profit": 841.0044496995779, "final_balance": 1141.004449699578, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:12:46.798194"}, "locked_parameters": {"grid_spacing": 0.0025, "risk_reward_ratio": 2.5, "training_days": 60, "testing_days": 30, "tcn_weight": 0.4, "cnn_weight": 0.4, "ppo_weight": 0.2}, "final_hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "results_source": "PURE_OUT_OF_SAMPLE_BACKTEST_ONLY", "reinforcement_learning_applied": true, "hyperparameter_optimization_applied": true}