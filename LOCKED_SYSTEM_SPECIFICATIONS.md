# 🔒 LOCKED SYSTEM SPECIFICATIONS - NO DEVIATION ALLOWED

## ⚠️ **CRITICAL: ALL PARAMETERS LOCKED AND IMMUTABLE**

This document contains the **LOCKED SPECIFICATIONS** for the trading system. **NO DEVIATIONS ARE ALLOWED** from these parameters during training, testing, or deployment.

---

## 🎯 **REWARD SYSTEM - LOCKED**

### **Reward Range: 0.0 to 1.0 (CONFIRMED)**
```python
# LOCKED: Reward system operates on 0-1 scale
REWARD_MIN = 0.0        # Minimum reward (worst performance)
REWARD_MAX = 1.0        # Maximum reward (best performance)
REWARD_TARGET = 0.85    # Target threshold (85%)

# All composite scores normalized to 0-1 range
composite = min(composite, 1.0)  # Cap at 1.0 (highest reward)
```

**✅ STATUS**: CONFIRMED - System uses 0-1 reward scale with 1 being highest

---

## 📊 **COMPOSITE SCORE FORMULA - LOCKED**

### **EXACT FORMULA (IMMUTABLE):**
```python
robust_score = (
    0.25 * sortino_norm +           # 25% - Sortino ratio (normalized)
    0.20 * ulcer_index_inv +        # 20% - Ulcer index (inverted)
    0.15 * equity_curve_r2 +        # 15% - Equity curve R²
    0.15 * profit_stability +       # 15% - Profit stability
    0.15 * upward_move_ratio +      # 15% - Upward move ratio
    0.10 * drawdown_duration_inv    # 10% - Drawdown duration (inverted)
)
```

**🔒 LOCKED WEIGHTS**: Cannot be modified under any circumstances

---

## 🎯 **TRADING ACTIONS - LOCKED**

### **EXACTLY 4 ACTIONS (IMMUTABLE):**
1. **BUY at grid level** → Exit next grid level up at 2.5:1 risk-reward
2. **SELL at grid level** → Exit next grid level below at 2.5:1 risk-reward  
3. **DO NOTHING** (HOLD position)
4. **STOP LOSS** at 1x risk amount

**🔒 LOCKED PARAMETERS:**
```python
RISK_REWARD_RATIO = 2.5     # 2.5:1 ratio (LOCKED)
STOP_LOSS_MULTIPLIER = 1.0  # 1x risk (LOCKED)
GRID_SPACING = 0.0025       # 0.25% (LOCKED)
```

---

## 📈 **TECHNICAL INDICATORS - LOCKED**

### **EXACTLY 4 INDICATORS (IMMUTABLE):**
1. **VWAP** (Volume Weighted Average Price) - 24 period
2. **Bollinger Bands** - 20 window, 2 std dev
3. **RSI** (Relative Strength Index) - 14 period
4. **ETH/BTC Ratio** - 0.05 threshold

**🔒 LOCKED PARAMETERS:**
```python
VWAP_PERIOD = 24            # 24 periods (LOCKED)
BB_WINDOW = 20              # 20 window (LOCKED)
BB_STD_DEV = 2              # 2 standard deviations (LOCKED)
RSI_PERIOD = 14             # 14 periods (LOCKED)
ETH_BTC_THRESHOLD = 0.05    # 5% threshold (LOCKED)
```

---

## 🧠 **ENSEMBLE ALGORITHM - LOCKED**

### **TCN + CNN + PPO ENSEMBLE (IMMUTABLE):**
```python
ENSEMBLE_ARCHITECTURE = "TCN-CNN-PPO"
TCN_WEIGHT = 0.40          # 40% contribution (LOCKED)
CNN_WEIGHT = 0.40          # 40% contribution (LOCKED)
PPO_WEIGHT = 0.20          # 20% contribution (LOCKED)
```

**🔒 ENSEMBLE WEIGHTS**: Cannot be modified or rebalanced

---

## 📚 **TRAINING SPECIFICATIONS - LOCKED**

### **DATA SPLITS (IMMUTABLE):**
```python
TRAINING_DAYS = 60          # 60 days training data (LOCKED)
TESTING_DAYS = 30           # 30 days out-of-sample (LOCKED)
VALIDATION_REQUIRED = True  # Backtester validation (LOCKED)
```

### **MODEL SAVING CRITERIA (LOCKED):**
```python
SAVE_HIGHEST_COMPOSITE = True    # Save highest composite reward (LOCKED)
SAVE_HIGHEST_PROFIT = True       # Save highest net profit (LOCKED)
DUAL_CRITERIA_REQUIRED = True    # Both criteria must be tracked (LOCKED)
```

---

## ⚙️ **RISK MANAGEMENT - LOCKED**

### **POSITION SIZING (IMMUTABLE):**
```python
RISK_PER_TRADE = 20.0       # $20 per trade (LOCKED)
MAX_POSITION_PCT = 0.1      # 10% max position size (LOCKED)
MAX_DRAWDOWN = 0.15         # 15% maximum drawdown (LOCKED)
MAX_OPEN_TRADES = 1         # Only 1 trade at a time (LOCKED)
```

### **GRID TRADING (LOCKED):**
```python
GRID_SPACING = 0.0025       # 0.25% spacing (LOCKED)
GRID_LEVELS = 2             # 2 buy/sell levels (LOCKED)
STOP_LOSS_PERCENT = 0.00125 # 0.125% stop loss (LOCKED)
```

---

## 🎯 **PERFORMANCE TARGETS - LOCKED**

### **MINIMUM THRESHOLDS (IMMUTABLE):**
```python
MIN_COMPOSITE_SCORE = 0.85  # 85% minimum composite (LOCKED)
MIN_WIN_RATE = 0.55         # 55% minimum win rate (LOCKED)
MIN_TRADES_PER_DAY = 3.0    # 3 trades/day minimum (LOCKED)
MAX_TRADES_PER_DAY = 8.0    # 8 trades/day maximum (LOCKED)
MIN_PROFIT_FACTOR = 1.2     # 1.2 minimum profit factor (LOCKED)
```

---

## 🔧 **SYSTEM CONFIGURATION - LOCKED**

### **TRADING PAIRS (IMMUTABLE):**
```python
PRIMARY_PAIR = "BTCUSDT"    # BTC/USDT only (LOCKED)
REFERENCE_PAIR = "ETHUSDT"  # For ETH/BTC ratio (LOCKED)
TIMEFRAME = "1h"            # 1-hour candles (LOCKED)
```

### **API CONFIGURATION (LOCKED):**
```python
COMMISSION_RATE = 0.001     # 0.1% commission (LOCKED)
MIN_TRADE_SIZE = 10.0       # $10 minimum (LOCKED)
CROSS_MARGIN_ONLY = True    # Cross margin required (LOCKED)
```

---

## 🚨 **VALIDATION REQUIREMENTS - LOCKED**

### **BACKTESTER INTEGRATION (MANDATORY):**
```python
INTEGRATED_BACKTESTER = True        # Must use integrated backtester (LOCKED)
REAL_TIME_VALIDATION = True         # Real-time signal validation (LOCKED)
OUT_OF_SAMPLE_TESTING = True        # 30-day OOS required (LOCKED)
CONTINUOUS_LEARNING = True          # RL feedback required (LOCKED)
PERFORMANCE_MONITORING = True       # Degradation detection (LOCKED)
```

### **DATA REQUIREMENTS (IMMUTABLE):**
```python
REAL_DATA_ONLY = True               # No synthetic data allowed (LOCKED)
MIN_DATA_POINTS = 720               # 30 days hourly minimum (LOCKED)
BINANCE_API_REQUIRED = True         # Real market data source (LOCKED)
```

---

## 🔒 **ENFORCEMENT MECHANISMS**

### **PARAMETER LOCK VERIFICATION:**
```python
def verify_locked_parameters():
    """Verify all locked parameters are unchanged"""
    assert GRID_SPACING == 0.0025, "GRID_SPACING LOCKED AT 0.25%"
    assert RISK_REWARD_RATIO == 2.5, "RISK_REWARD_RATIO LOCKED AT 2.5:1"
    assert TRAINING_DAYS == 60, "TRAINING_DAYS LOCKED AT 60"
    assert TESTING_DAYS == 30, "TESTING_DAYS LOCKED AT 30"
    assert TCN_WEIGHT == 0.40, "TCN_WEIGHT LOCKED AT 40%"
    assert CNN_WEIGHT == 0.40, "CNN_WEIGHT LOCKED AT 40%"
    assert PPO_WEIGHT == 0.20, "PPO_WEIGHT LOCKED AT 20%"
    # ... all other locked parameters
```

### **DEVIATION DETECTION:**
```python
def detect_parameter_deviation():
    """Detect any unauthorized parameter changes"""
    locked_params = load_locked_specifications()
    current_params = get_current_configuration()
    
    for param, locked_value in locked_params.items():
        if current_params[param] != locked_value:
            raise ValueError(f"LOCKED PARAMETER VIOLATION: {param}")
```

---

## ⚠️ **CRITICAL WARNINGS**

### **🚨 NO DEVIATIONS ALLOWED**
- **Grid Spacing**: Must remain 0.25% - NO EXCEPTIONS
- **Risk-Reward**: Must remain 2.5:1 - NO MODIFICATIONS
- **Ensemble Weights**: Must remain 40/40/20 - NO REBALANCING
- **Training Period**: Must remain 60/30 split - NO CHANGES
- **Indicators**: Must use exact 4 indicators - NO SUBSTITUTIONS

### **🔒 LOCKED FOR CONSISTENCY**
These parameters are locked to ensure:
- Consistent performance measurement
- Reproducible results
- Fair model comparison
- System stability
- Regulatory compliance

---

## 📋 **COMPLIANCE CHECKLIST**

Before any training or deployment:
- [ ] All locked parameters verified unchanged
- [ ] Composite score formula matches exactly
- [ ] Trading actions limited to 4 specified types
- [ ] Technical indicators match exact specifications
- [ ] Ensemble weights are 40/40/20
- [ ] Training uses 60/30 day split
- [ ] Integrated backtester is enabled
- [ ] Real data sources are configured
- [ ] Risk management parameters are locked
- [ ] Performance targets are set correctly

---

## 🎯 **SUMMARY**

**ALL SPECIFICATIONS IN THIS DOCUMENT ARE IMMUTABLE AND LOCKED.**

Any attempt to modify these parameters will result in:
- Training failure
- Validation rejection
- System shutdown
- Compliance violation

**These parameters have been validated and optimized. NO DEVIATIONS ARE PERMITTED.**

---

**🔒 LOCKED SYSTEM SPECIFICATIONS - ENFORCE STRICTLY** 🔒
