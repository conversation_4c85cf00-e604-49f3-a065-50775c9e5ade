"""
Bitcoin Freedom Trading Database
Advanced SQLite database for trade persistence with high performance and data integrity.
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import os
import threading
from contextlib import contextmanager

@dataclass
class TradeRecord:
    """Trade record data class for type safety."""
    trade_id: str
    direction: str
    status: str
    entry_time: str
    exit_time: Optional[str]
    entry_price: float
    exit_price: Optional[float]
    current_price: float
    quantity: float
    position_usd: float
    pnl: float
    duration: Optional[str]
    risk_amount: float
    target_profit: float
    leverage: float
    stop_loss: float
    take_profit: float
    confidence: float
    signal_confidence: float
    model_prediction: str
    binance_order_id: Optional[str]
    is_margin_trade: bool
    account_type: str
    margin_used: float
    unrealized_pnl: float
    realized_pnl: float
    profit_percentage: float
    entry_date: str
    exit_date: Optional[str]
    logged_at: str
    last_updated: str
    is_open: bool
    is_live_trade: bool

class TradingDatabase:
    """High-performance SQLite database for trading data with connection pooling and indexing."""
    
    def __init__(self, db_path: str = "bitcoin_freedom_trades.db"):
        self.db_path = db_path
        self._local = threading.local()
        self._lock = threading.Lock()
        
        # Initialize database
        self._initialize_database()
        print(f"🗄️ Trading Database initialized: {self.db_path}")
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get thread-local database connection with optimizations."""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            # Enable WAL mode for better concurrency
            self._local.connection.execute("PRAGMA journal_mode=WAL")
            # Enable foreign keys
            self._local.connection.execute("PRAGMA foreign_keys=ON")
            # Optimize for performance
            self._local.connection.execute("PRAGMA synchronous=NORMAL")
            self._local.connection.execute("PRAGMA cache_size=10000")
            self._local.connection.execute("PRAGMA temp_store=MEMORY")
            
        return self._local.connection
    
    @contextmanager
    def get_cursor(self):
        """Context manager for database operations with automatic commit/rollback."""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            cursor.close()
    
    def _initialize_database(self):
        """Create database tables with proper indexing and constraints."""
        with self.get_cursor() as cursor:
            # Create trades table with comprehensive schema
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE NOT NULL,
                    direction TEXT NOT NULL CHECK (direction IN ('BUY', 'SELL')),
                    status TEXT NOT NULL,
                    entry_time TEXT NOT NULL,
                    exit_time TEXT,
                    entry_price REAL NOT NULL,
                    exit_price REAL,
                    current_price REAL NOT NULL,
                    quantity REAL NOT NULL,
                    position_usd REAL NOT NULL,
                    pnl REAL NOT NULL DEFAULT 0.0,
                    duration TEXT,
                    risk_amount REAL NOT NULL,
                    target_profit REAL NOT NULL,
                    leverage REAL NOT NULL DEFAULT 1.0,
                    stop_loss REAL NOT NULL DEFAULT 0.0,
                    take_profit REAL NOT NULL DEFAULT 0.0,
                    confidence REAL NOT NULL,
                    signal_confidence REAL NOT NULL,
                    model_prediction TEXT NOT NULL,
                    binance_order_id TEXT,
                    is_margin_trade BOOLEAN NOT NULL DEFAULT 1,
                    account_type TEXT NOT NULL DEFAULT 'Cross Margin',
                    margin_used REAL NOT NULL DEFAULT 0.0,
                    unrealized_pnl REAL NOT NULL DEFAULT 0.0,
                    realized_pnl REAL NOT NULL DEFAULT 0.0,
                    profit_percentage REAL NOT NULL DEFAULT 0.0,
                    entry_date TEXT NOT NULL,
                    exit_date TEXT,
                    logged_at TEXT NOT NULL,
                    last_updated TEXT NOT NULL,
                    is_open BOOLEAN NOT NULL DEFAULT 1,
                    is_live_trade BOOLEAN NOT NULL DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create performance indexes
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_trade_id ON trades(trade_id)",
                "CREATE INDEX IF NOT EXISTS idx_status ON trades(status)",
                "CREATE INDEX IF NOT EXISTS idx_is_open ON trades(is_open)",
                "CREATE INDEX IF NOT EXISTS idx_entry_date ON trades(entry_date)",
                "CREATE INDEX IF NOT EXISTS idx_direction ON trades(direction)",
                "CREATE INDEX IF NOT EXISTS idx_is_live_trade ON trades(is_live_trade)",
                "CREATE INDEX IF NOT EXISTS idx_entry_time ON trades(entry_time)",
                "CREATE INDEX IF NOT EXISTS idx_pnl ON trades(pnl)",
                "CREATE INDEX IF NOT EXISTS idx_composite_status_date ON trades(status, entry_date)",
                "CREATE INDEX IF NOT EXISTS idx_composite_open_date ON trades(is_open, entry_date DESC)"
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            # Create trigger to update updated_at timestamp
            cursor.execute("""
                CREATE TRIGGER IF NOT EXISTS update_trades_timestamp 
                AFTER UPDATE ON trades
                BEGIN
                    UPDATE trades SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
                END
            """)
            
            # Create statistics table for performance metrics
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trade_statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT UNIQUE NOT NULL,
                    total_trades INTEGER NOT NULL DEFAULT 0,
                    open_trades INTEGER NOT NULL DEFAULT 0,
                    closed_trades INTEGER NOT NULL DEFAULT 0,
                    winning_trades INTEGER NOT NULL DEFAULT 0,
                    losing_trades INTEGER NOT NULL DEFAULT 0,
                    total_pnl REAL NOT NULL DEFAULT 0.0,
                    daily_pnl REAL NOT NULL DEFAULT 0.0,
                    win_rate REAL NOT NULL DEFAULT 0.0,
                    avg_profit REAL NOT NULL DEFAULT 0.0,
                    avg_loss REAL NOT NULL DEFAULT 0.0,
                    max_profit REAL NOT NULL DEFAULT 0.0,
                    max_loss REAL NOT NULL DEFAULT 0.0,
                    total_volume REAL NOT NULL DEFAULT 0.0,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_stats_date ON trade_statistics(date)")
    
    def insert_trade(self, trade_data: Dict[str, Any]) -> bool:
        """Insert new trade with data validation and conflict handling."""
        try:
            with self.get_cursor() as cursor:
                # Prepare data with defaults
                now = datetime.now().isoformat()
                
                trade_record = {
                    'trade_id': trade_data.get('trade_id'),
                    'direction': trade_data.get('direction'),
                    'status': trade_data.get('status', 'OPEN'),
                    'entry_time': trade_data.get('entry_time'),
                    'exit_time': trade_data.get('exit_time'),
                    'entry_price': float(trade_data.get('entry_price', 0)),
                    'exit_price': float(trade_data.get('exit_price', 0)) if trade_data.get('exit_price') else None,
                    'current_price': float(trade_data.get('current_price', 0)),
                    'quantity': float(trade_data.get('quantity', 0)),
                    'position_usd': float(trade_data.get('position_usd', 0)),
                    'pnl': float(trade_data.get('pnl', 0)),
                    'duration': trade_data.get('duration'),
                    'risk_amount': float(trade_data.get('risk_amount', 0)),
                    'target_profit': float(trade_data.get('target_profit', 0)),
                    'leverage': float(trade_data.get('leverage', 1.0)),
                    'stop_loss': float(trade_data.get('stop_loss', 0)),
                    'take_profit': float(trade_data.get('take_profit', 0)),
                    'confidence': float(trade_data.get('confidence', 0)),
                    'signal_confidence': float(trade_data.get('signal_confidence', 0)),
                    'model_prediction': trade_data.get('model_prediction', ''),
                    'binance_order_id': trade_data.get('binance_order_id'),
                    'is_margin_trade': bool(trade_data.get('is_margin_trade', True)),
                    'account_type': trade_data.get('account_type', 'Cross Margin'),
                    'margin_used': float(trade_data.get('margin_used', 0)),
                    'unrealized_pnl': float(trade_data.get('unrealized_pnl', 0)),
                    'realized_pnl': float(trade_data.get('realized_pnl', 0)),
                    'profit_percentage': float(trade_data.get('profit_percentage', 0)),
                    'entry_date': trade_data.get('entry_date'),
                    'exit_date': trade_data.get('exit_date'),
                    'logged_at': trade_data.get('logged_at', now),
                    'last_updated': now,
                    'is_open': bool(trade_data.get('is_open', True)),
                    'is_live_trade': bool(trade_data.get('is_live_trade', True))
                }
                
                # Insert with conflict resolution
                cursor.execute("""
                    INSERT OR REPLACE INTO trades (
                        trade_id, direction, status, entry_time, exit_time,
                        entry_price, exit_price, current_price, quantity, position_usd,
                        pnl, duration, risk_amount, target_profit, leverage,
                        stop_loss, take_profit, confidence, signal_confidence, model_prediction,
                        binance_order_id, is_margin_trade, account_type, margin_used,
                        unrealized_pnl, realized_pnl, profit_percentage,
                        entry_date, exit_date, logged_at, last_updated, is_open, is_live_trade
                    ) VALUES (
                        :trade_id, :direction, :status, :entry_time, :exit_time,
                        :entry_price, :exit_price, :current_price, :quantity, :position_usd,
                        :pnl, :duration, :risk_amount, :target_profit, :leverage,
                        :stop_loss, :take_profit, :confidence, :signal_confidence, :model_prediction,
                        :binance_order_id, :is_margin_trade, :account_type, :margin_used,
                        :unrealized_pnl, :realized_pnl, :profit_percentage,
                        :entry_date, :exit_date, :logged_at, :last_updated, :is_open, :is_live_trade
                    )
                """, trade_record)
                
                print(f"💾 Trade inserted to database: {trade_record['trade_id']} ({trade_record['status']})")
                return True
                
        except Exception as e:
            print(f"❌ Error inserting trade to database: {e}")
            return False

    def update_trade(self, trade_id: str, updates: Dict[str, Any]) -> bool:
        """Update existing trade with optimized query."""
        try:
            with self.get_cursor() as cursor:
                # Build dynamic update query
                set_clauses = []
                params = {'trade_id': trade_id}

                for key, value in updates.items():
                    if key != 'trade_id':  # Don't update the primary key
                        set_clauses.append(f"{key} = :{key}")
                        params[key] = value

                if not set_clauses:
                    return False

                # Add last_updated timestamp
                set_clauses.append("last_updated = :last_updated")
                params['last_updated'] = datetime.now().isoformat()

                query = f"""
                    UPDATE trades
                    SET {', '.join(set_clauses)}
                    WHERE trade_id = :trade_id
                """

                cursor.execute(query, params)

                if cursor.rowcount > 0:
                    print(f"💾 Trade updated in database: {trade_id}")
                    return True
                else:
                    print(f"⚠️ Trade not found for update: {trade_id}")
                    return False

        except Exception as e:
            print(f"❌ Error updating trade in database: {e}")
            return False

    def get_all_trades(self, limit: Optional[int] = None, offset: int = 0) -> List[Dict[str, Any]]:
        """Get all trades with optional pagination and optimized query."""
        try:
            with self.get_cursor() as cursor:
                query = """
                    SELECT * FROM trades
                    ORDER BY is_open DESC, entry_time DESC
                """

                if limit:
                    query += f" LIMIT {limit} OFFSET {offset}"

                cursor.execute(query)
                columns = [description[0] for description in cursor.description]

                trades = []
                for row in cursor.fetchall():
                    trade = dict(zip(columns, row))
                    # Convert boolean fields
                    trade['is_open'] = bool(trade['is_open'])
                    trade['is_margin_trade'] = bool(trade['is_margin_trade'])
                    trade['is_live_trade'] = bool(trade['is_live_trade'])
                    trades.append(trade)

                return trades

        except Exception as e:
            print(f"❌ Error loading trades from database: {e}")
            return []

    def get_open_trades(self) -> List[Dict[str, Any]]:
        """Get only open trades with optimized index usage."""
        try:
            with self.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM trades
                    WHERE is_open = 1
                    ORDER BY entry_time DESC
                """)

                columns = [description[0] for description in cursor.description]
                trades = []

                for row in cursor.fetchall():
                    trade = dict(zip(columns, row))
                    trade['is_open'] = bool(trade['is_open'])
                    trade['is_margin_trade'] = bool(trade['is_margin_trade'])
                    trade['is_live_trade'] = bool(trade['is_live_trade'])
                    trades.append(trade)

                return trades

        except Exception as e:
            print(f"❌ Error loading open trades from database: {e}")
            return []

    def get_closed_trades(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get closed trades with limit for performance."""
        try:
            with self.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM trades
                    WHERE is_open = 0
                    ORDER BY exit_time DESC
                    LIMIT ?
                """, (limit,))

                columns = [description[0] for description in cursor.description]
                trades = []

                for row in cursor.fetchall():
                    trade = dict(zip(columns, row))
                    trade['is_open'] = bool(trade['is_open'])
                    trade['is_margin_trade'] = bool(trade['is_margin_trade'])
                    trade['is_live_trade'] = bool(trade['is_live_trade'])
                    trades.append(trade)

                return trades

        except Exception as e:
            print(f"❌ Error loading closed trades from database: {e}")
            return []

    def get_recent_trades(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent trades optimized for dashboard display."""
        try:
            with self.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM trades
                    ORDER BY is_open DESC, entry_time DESC
                    LIMIT ?
                """, (limit,))

                columns = [description[0] for description in cursor.description]
                trades = []

                for row in cursor.fetchall():
                    trade = dict(zip(columns, row))
                    trade['is_open'] = bool(trade['is_open'])
                    trade['is_margin_trade'] = bool(trade['is_margin_trade'])
                    trade['is_live_trade'] = bool(trade['is_live_trade'])
                    trades.append(trade)

                return trades

        except Exception as e:
            print(f"❌ Error loading recent trades from database: {e}")
            return []

    def get_trade_statistics(self) -> Dict[str, Any]:
        """Get comprehensive trading statistics with optimized queries."""
        try:
            with self.get_cursor() as cursor:
                # Get basic counts
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_trades,
                        SUM(CASE WHEN is_open = 1 THEN 1 ELSE 0 END) as open_trades,
                        SUM(CASE WHEN is_open = 0 THEN 1 ELSE 0 END) as closed_trades,
                        SUM(pnl) as total_pnl,
                        AVG(pnl) as avg_pnl,
                        MIN(pnl) as min_pnl,
                        MAX(pnl) as max_pnl,
                        SUM(position_usd) as total_volume
                    FROM trades
                """)

                stats = dict(zip([col[0] for col in cursor.description], cursor.fetchone()))

                # Get win/loss statistics for closed trades
                cursor.execute("""
                    SELECT
                        SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                        SUM(CASE WHEN pnl < 0 THEN 1 ELSE 0 END) as losing_trades,
                        AVG(CASE WHEN pnl > 0 THEN pnl ELSE NULL END) as avg_profit,
                        AVG(CASE WHEN pnl < 0 THEN pnl ELSE NULL END) as avg_loss
                    FROM trades
                    WHERE is_open = 0
                """)

                win_loss_stats = dict(zip([col[0] for col in cursor.description], cursor.fetchone()))
                stats.update(win_loss_stats)

                # Calculate win rate
                closed_trades = stats.get('closed_trades', 0)
                winning_trades = stats.get('winning_trades', 0)
                stats['win_rate'] = (winning_trades / closed_trades * 100) if closed_trades > 0 else 0

                # Get today's statistics
                today = datetime.now().strftime('%Y-%m-%d')
                cursor.execute("""
                    SELECT
                        COUNT(*) as daily_trades,
                        SUM(pnl) as daily_pnl
                    FROM trades
                    WHERE entry_date = ?
                """, (today,))

                daily_stats = dict(zip([col[0] for col in cursor.description], cursor.fetchone()))
                stats.update(daily_stats)

                # Ensure all values are not None
                for key, value in stats.items():
                    if value is None:
                        stats[key] = 0.0 if 'pnl' in key or 'avg' in key else 0

                return stats

        except Exception as e:
            print(f"❌ Error getting trade statistics from database: {e}")
            return {
                'total_trades': 0, 'open_trades': 0, 'closed_trades': 0,
                'total_pnl': 0.0, 'win_rate': 0.0, 'avg_profit': 0.0,
                'avg_loss': 0.0, 'daily_trades': 0, 'daily_pnl': 0.0,
                'total_volume': 0.0, 'winning_trades': 0, 'losing_trades': 0
            }

    def close_trade(self, trade_id: str, exit_price: float, exit_time: str = None, pnl: float = None) -> bool:
        """Close a trade with automatic P&L calculation and duration."""
        if exit_time is None:
            exit_time = datetime.now().isoformat()

        try:
            with self.get_cursor() as cursor:
                # Get the trade to calculate P&L if not provided
                cursor.execute("SELECT * FROM trades WHERE trade_id = ?", (trade_id,))
                trade = cursor.fetchone()

                if not trade:
                    print(f"⚠️ Trade not found for closing: {trade_id}")
                    return False

                columns = [description[0] for description in cursor.description]
                trade_dict = dict(zip(columns, trade))

                # Calculate P&L if not provided
                if pnl is None:
                    entry_price = trade_dict['entry_price']
                    quantity = trade_dict['quantity']
                    direction = trade_dict['direction']

                    if direction == 'BUY':
                        pnl = (exit_price - entry_price) * quantity
                    else:  # SELL
                        pnl = (entry_price - exit_price) * quantity

                # Calculate duration
                duration = "Unknown"
                if trade_dict['entry_time']:
                    try:
                        entry_dt = datetime.fromisoformat(trade_dict['entry_time'].replace('Z', '+00:00'))
                        exit_dt = datetime.fromisoformat(exit_time.replace('Z', '+00:00'))
                        duration_delta = exit_dt - entry_dt

                        total_seconds = int(duration_delta.total_seconds())
                        if total_seconds < 60:
                            duration = f"{total_seconds}s"
                        elif total_seconds < 3600:
                            minutes = total_seconds // 60
                            seconds = total_seconds % 60
                            duration = f"{minutes}m {seconds}s"
                        else:
                            hours = total_seconds // 3600
                            minutes = (total_seconds % 3600) // 60
                            duration = f"{hours}h {minutes}m"
                    except:
                        duration = "Unknown"

                # Update the trade
                updates = {
                    'is_open': False,
                    'status': 'CLOSED',
                    'exit_price': exit_price,
                    'exit_time': exit_time,
                    'exit_date': exit_time.split('T')[0] if 'T' in exit_time else exit_time.split(' ')[0],
                    'pnl': pnl,
                    'realized_pnl': pnl,
                    'unrealized_pnl': 0.0,
                    'duration': duration,
                    'profit_percentage': round((pnl / (trade_dict['entry_price'] * trade_dict['quantity'])) * 100, 2) if pnl else 0
                }

                return self.update_trade(trade_id, updates)

        except Exception as e:
            print(f"❌ Error closing trade in database: {e}")
            return False

    def update_open_trade_realtime(self, trade_id: str, current_price: float, unrealized_pnl: float) -> bool:
        """Update real-time data for open trade efficiently."""
        try:
            updates = {
                'current_price': current_price,
                'unrealized_pnl': unrealized_pnl,
                'pnl': unrealized_pnl  # For open trades, pnl = unrealized_pnl
            }

            # Calculate profit percentage
            with self.get_cursor() as cursor:
                cursor.execute("""
                    SELECT entry_price, quantity FROM trades
                    WHERE trade_id = ? AND is_open = 1
                """, (trade_id,))

                result = cursor.fetchone()
                if result:
                    entry_price, quantity = result
                    if entry_price > 0 and quantity > 0:
                        updates['profit_percentage'] = round((unrealized_pnl / (entry_price * quantity)) * 100, 2)

            return self.update_trade(trade_id, updates)

        except Exception as e:
            print(f"❌ Error updating open trade realtime: {e}")
            return False

    def migrate_from_csv(self, csv_file_path: str) -> bool:
        """Migrate existing CSV data to database with data validation."""
        try:
            import csv

            if not os.path.exists(csv_file_path):
                print(f"⚠️ CSV file not found: {csv_file_path}")
                return False

            migrated_count = 0
            error_count = 0

            print(f"🔄 Starting CSV migration from: {csv_file_path}")

            with open(csv_file_path, 'r', newline='', encoding='utf-8') as file:
                reader = csv.DictReader(file)

                for row in reader:
                    try:
                        # Convert string booleans to actual booleans
                        row['is_open'] = row.get('is_open', 'False').lower() == 'true'
                        row['is_margin_trade'] = row.get('is_margin_trade', 'False').lower() == 'true'
                        row['is_live_trade'] = row.get('is_live_trade', 'True').lower() == 'true'

                        # Convert numeric fields with validation
                        numeric_fields = [
                            'entry_price', 'exit_price', 'current_price', 'quantity',
                            'position_usd', 'pnl', 'risk_amount', 'target_profit',
                            'leverage', 'stop_loss', 'take_profit', 'confidence',
                            'signal_confidence', 'margin_used', 'unrealized_pnl',
                            'realized_pnl', 'profit_percentage'
                        ]

                        for field in numeric_fields:
                            try:
                                if row.get(field) and row[field] != '':
                                    row[field] = float(row[field])
                                else:
                                    row[field] = 0.0
                            except (ValueError, TypeError):
                                row[field] = 0.0

                        # Insert the trade
                        if self.insert_trade(row):
                            migrated_count += 1
                        else:
                            error_count += 1

                    except Exception as e:
                        print(f"❌ Error migrating row: {e}")
                        error_count += 1

            print(f"✅ CSV Migration complete: {migrated_count} trades migrated, {error_count} errors")
            return migrated_count > 0

        except Exception as e:
            print(f"❌ Error during CSV migration: {e}")
            return False

    def backup_to_csv(self, backup_file_path: str) -> bool:
        """Backup database to CSV file."""
        try:
            import csv

            trades = self.get_all_trades()

            if not trades:
                print("⚠️ No trades to backup")
                return False

            # Get all field names from the first trade
            fieldnames = list(trades[0].keys())

            with open(backup_file_path, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(trades)

            print(f"✅ Database backed up to CSV: {backup_file_path} ({len(trades)} trades)")
            return True

        except Exception as e:
            print(f"❌ Error backing up to CSV: {e}")
            return False

    def get_database_info(self) -> Dict[str, Any]:
        """Get database information and performance metrics."""
        try:
            with self.get_cursor() as cursor:
                # Get table info
                cursor.execute("SELECT COUNT(*) FROM trades")
                total_trades = cursor.fetchone()[0]

                # Get database size
                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]
                db_size_bytes = page_count * page_size

                # Get index info
                cursor.execute("PRAGMA index_list(trades)")
                indexes = cursor.fetchall()

                return {
                    'database_path': self.db_path,
                    'total_trades': total_trades,
                    'database_size_mb': round(db_size_bytes / (1024 * 1024), 2),
                    'indexes_count': len(indexes),
                    'wal_mode': True,
                    'connection_pooling': True,
                    'performance_optimized': True
                }

        except Exception as e:
            print(f"❌ Error getting database info: {e}")
            return {}

    def optimize_database(self) -> bool:
        """Optimize database performance with VACUUM and ANALYZE."""
        try:
            with self.get_cursor() as cursor:
                print("🔧 Optimizing database...")

                # Analyze tables for query optimization
                cursor.execute("ANALYZE")

                # Vacuum to reclaim space and optimize
                cursor.execute("VACUUM")

                print("✅ Database optimization complete")
                return True

        except Exception as e:
            print(f"❌ Error optimizing database: {e}")
            return False

    def clear_all_trades(self) -> bool:
        """Clear all trades from the database."""
        try:
            with self.get_cursor() as cursor:
                cursor.execute("DELETE FROM trades")
                deleted_count = cursor.rowcount
                print(f"🗄️ Cleared {deleted_count} trades from database")
                return True
        except Exception as e:
            print(f"❌ Error clearing trades from database: {e}")
            return False

    def close_all_connections(self):
        """Close all database connections safely."""
        try:
            if hasattr(self._local, 'connection'):
                self._local.connection.close()
                delattr(self._local, 'connection')
            print("🔒 Database connections closed")
        except Exception as e:
            print(f"❌ Error closing database connections: {e}")


# Global database instance
trading_db = None

def get_trading_database() -> TradingDatabase:
    """Get global trading database instance (singleton pattern)."""
    global trading_db
    if trading_db is None:
        trading_db = TradingDatabase()
    return trading_db


if __name__ == "__main__":
    # Test the database
    print("🧪 Testing Trading Database")
    print("=" * 50)

    # Initialize database
    db = TradingDatabase("test_trades.db")

    # Test inserting a sample trade
    sample_trade = {
        'trade_id': 'TEST_DB_001',
        'direction': 'BUY',
        'status': 'OPEN',
        'entry_time': datetime.now().isoformat(),
        'entry_date': datetime.now().strftime('%Y-%m-%d'),
        'entry_price': 104500.0,
        'current_price': 104500.0,
        'quantity': 0.00038,
        'position_usd': 39.71,
        'risk_amount': 4.0,
        'target_profit': 25.0,
        'confidence': 65.2,
        'signal_confidence': 65.2,
        'model_prediction': 'BUY Signal',
        'is_open': True,
        'is_margin_trade': True,
        'account_type': 'Cross Margin',
        'is_live_trade': True,
        'leverage': 1.0,
        'stop_loss': 0.0,
        'take_profit': 0.0,
        'margin_used': 0.0,
        'unrealized_pnl': 0.0,
        'realized_pnl': 0.0,
        'profit_percentage': 0.0
    }

    # Insert trade
    success = db.insert_trade(sample_trade)
    print(f"✅ Trade insertion: {'Success' if success else 'Failed'}")

    # Load trades
    trades = db.get_all_trades()
    print(f"✅ Loaded {len(trades)} trades")

    # Get statistics
    stats = db.get_trade_statistics()
    print(f"📊 Statistics: {stats}")

    # Get database info
    info = db.get_database_info()
    print(f"🗄️ Database info: {info}")

    print("🎯 Database test complete")
