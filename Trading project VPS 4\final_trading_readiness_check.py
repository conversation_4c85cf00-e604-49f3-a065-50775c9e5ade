#!/usr/bin/env python3
"""
Final Trading Readiness Check - Tests the actual trading components that will run.
"""

import sys
import time
from datetime import datetime

def print_header(title):
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def test_conservative_elite_model():
    """Test Conservative Elite model is properly loaded."""
    print_header("CONSERVATIVE ELITE MODEL TEST")
    
    try:
        from live_trading_web_app import BestCompositeModel
        
        model = BestCompositeModel()
        
        print(f"✅ Model Type: {model.model_type}")
        print(f"✅ Model ID: {model.model_id}")
        print(f"✅ Composite Score: {model.composite_score*100:.1f}%")
        print(f"✅ Win Rate: {model.win_rate*100:.1f}%")
        print(f"✅ Risk per Trade: ${model.risk_per_trade:.2f}")
        print(f"✅ Profit Target: ${model.profit_target:.2f}")
        print(f"✅ Risk-Reward Ratio: 1:{model.reward_ratio}")
        
        # Verify Conservative Elite specifics
        if "Conservative Elite" in model.model_type:
            print(f"✅ Conservative Elite: CONFIRMED")
        else:
            print(f"❌ Conservative Elite: NOT FOUND")
            return False
            
        if abs(model.composite_score - 0.932) < 0.01:
            print(f"✅ 93.2% Score: CONFIRMED")
        else:
            print(f"❌ Score Mismatch: {model.composite_score*100:.1f}% (expected 93.2%)")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False

def test_trading_engine_creation():
    """Test trading engine can be created and initialized."""
    print_header("TRADING ENGINE CREATION TEST")
    
    try:
        from live_trading_web_app import LiveTradingEngine, BestCompositeModel
        
        model = BestCompositeModel()
        engine = LiveTradingEngine(model)
        
        print(f"✅ Engine Created: {type(engine).__name__}")
        print(f"✅ Model Loaded: {engine.model.model_type}")
        print(f"✅ Balance: ${engine.balance:.2f}")
        print(f"✅ Current Price: ${engine.current_price:.2f}")
        print(f"✅ Open Trades: {len(engine.open_trades)}")
        print(f"✅ Closed Trades: {len(engine.closed_trades)}")
        print(f"✅ Running Status: {engine.is_running}")
        print(f"✅ Live Mode: {engine.is_live_mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ Trading engine creation failed: {e}")
        return False

def test_signal_generation():
    """Test signal generation works."""
    print_header("SIGNAL GENERATION TEST")
    
    try:
        from live_trading_web_app import LiveTradingEngine, BestCompositeModel
        
        model = BestCompositeModel()
        engine = LiveTradingEngine(model)
        
        # Test signal generation
        signal = engine.generate_trade_signal()
        print(f"✅ Signal Generated: {signal if signal else 'HOLD'}")
        
        # Test multiple signals for consistency
        signals = []
        for i in range(3):
            sig = engine.generate_trade_signal()
            signals.append(sig if sig else 'HOLD')
            print(f"   Signal {i+1}: {signals[-1]}")
            time.sleep(0.5)
        
        print(f"✅ Signal Generation: WORKING")
        return True
        
    except Exception as e:
        print(f"❌ Signal generation failed: {e}")
        return False

def test_trading_loop_function():
    """Test the actual trading loop function exists and can be called."""
    print_header("TRADING LOOP FUNCTION TEST")
    
    try:
        from live_trading_web_app import live_trading_loop, LiveTradingEngine, BestCompositeModel
        
        # Check function exists
        print(f"✅ Trading Loop Function: EXISTS")
        print(f"✅ Function Type: {type(live_trading_loop)}")
        
        # Test we can access the global trading engine
        from live_trading_web_app import trading_engine
        print(f"✅ Global Trading Engine: {type(trading_engine).__name__}")
        print(f"✅ Engine Model: {trading_engine.model.model_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ Trading loop test failed: {e}")
        return False

def test_price_data_access():
    """Test price data can be fetched."""
    print_header("PRICE DATA ACCESS TEST")
    
    try:
        import ccxt
        
        exchange = ccxt.binance()
        ticker = exchange.fetch_ticker('BTC/USDT')
        
        print(f"✅ BTC Price: ${ticker['last']:,.2f}")
        print(f"✅ 24h Change: {ticker['percentage']:.2f}%")
        print(f"✅ Volume: {ticker['baseVolume']:,.0f} BTC")
        print(f"✅ Bid: ${ticker['bid']:,.2f}")
        print(f"✅ Ask: ${ticker['ask']:,.2f}")
        
        # Test orderbook
        orderbook = exchange.fetch_order_book('BTC/USDT', limit=5)
        print(f"✅ Order Book: {len(orderbook['bids'])} bids, {len(orderbook['asks'])} asks")
        
        return True
        
    except Exception as e:
        print(f"❌ Price data access failed: {e}")
        return False

def test_webapp_startup():
    """Test webapp can start."""
    print_header("WEBAPP STARTUP TEST")
    
    try:
        from live_trading_web_app import app
        
        print(f"✅ Flask App: CREATED")
        
        # Test critical routes exist
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        critical_routes = [
            '/',
            '/api/trading_status',
            '/api/start_trading',
            '/api/stop_trading',
            '/conservative_elite'
        ]
        
        missing = []
        for route in critical_routes:
            if route in routes:
                print(f"✅ Route {route}: EXISTS")
            else:
                print(f"❌ Route {route}: MISSING")
                missing.append(route)
        
        if missing:
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Webapp startup test failed: {e}")
        return False

def test_data_logging():
    """Test data logging systems."""
    print_header("DATA LOGGING TEST")
    
    try:
        # Test CSV logger
        from trade_csv_logger import TradeCSVLogger
        csv_logger = TradeCSVLogger("test_readiness.csv")
        print(f"✅ CSV Logger: WORKING")
        
        # Test database
        from trade_database import TradingDatabase
        db = TradingDatabase("test_readiness.db")
        print(f"✅ Database: WORKING")
        
        return True
        
    except Exception as e:
        print(f"❌ Data logging test failed: {e}")
        return False

def run_quick_integration_test():
    """Run a quick integration test of the full system."""
    print_header("INTEGRATION TEST")
    
    try:
        from live_trading_web_app import LiveTradingEngine, BestCompositeModel
        import ccxt
        
        # Create system
        model = BestCompositeModel()
        engine = LiveTradingEngine(model)
        
        # Get real price
        exchange = ccxt.binance()
        ticker = exchange.fetch_ticker('BTC/USDT')
        current_price = ticker['last']
        
        print(f"📊 Real BTC Price: ${current_price:,.2f}")
        
        # Update engine with real price
        engine.current_price = current_price
        
        # Test signal generation with real price
        signal = engine.generate_trade_signal()
        print(f"🎯 Signal with Real Price: {signal if signal else 'HOLD'}")
        
        # Test performance stats
        stats = engine.get_performance_stats()
        print(f"📊 Performance Stats: {len(stats)} metrics")
        
        print(f"✅ Integration Test: PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Main readiness check."""
    print("🚀 FINAL TRADING READINESS CHECK")
    print("=" * 80)
    print(f"⏰ Check Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Target: 8-Hour Conservative Elite Trading Test")
    
    tests = [
        ("Conservative Elite Model", test_conservative_elite_model),
        ("Trading Engine Creation", test_trading_engine_creation),
        ("Signal Generation", test_signal_generation),
        ("Trading Loop Function", test_trading_loop_function),
        ("Price Data Access", test_price_data_access),
        ("Webapp Startup", test_webapp_startup),
        ("Data Logging", test_data_logging),
        ("Integration Test", run_quick_integration_test)
    ]
    
    results = {}
    passed = 0
    
    for name, test_func in tests:
        try:
            result = test_func()
            results[name] = result
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {name}: EXCEPTION - {e}")
            results[name] = False
    
    # Final summary
    print_header("FINAL READINESS SUMMARY")
    
    for name, result in results.items():
        status = "✅ READY" if result else "❌ NOT READY"
        print(f"{status} {name}")
    
    total = len(tests)
    print(f"\n📊 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 SYSTEM IS FULLY READY FOR 8-HOUR TEST!")
        print("\n📋 LAUNCH SEQUENCE:")
        print("1. Run: python live_trading_web_app.py")
        print("2. Wait for: 'Running on http://127.0.0.1:5000'")
        print("3. Open: http://localhost:5000/conservative_elite")
        print("4. Click: 'Start Trading' button")
        print("5. Verify: Trading status shows 'Running'")
        print("6. Monitor: First trade should appear within 30 minutes")
        print("7. Expected: 5-6 trades over 8 hours (Conservative Elite)")
        print("8. Win Rate: 93.2% expected performance")
        
        print("\n⚠️ MONITORING CHECKLIST:")
        print("- Check webapp every 30 minutes")
        print("- Verify trades are being logged")
        print("- Monitor P&L progression")
        print("- Ensure no error messages")
        print("- Confirm Conservative Elite is active")
        
        return True
    else:
        print(f"\n❌ {total - passed} CRITICAL ISSUES DETECTED")
        print("❌ System is NOT ready for 8-hour test")
        print("\n🔧 REQUIRED FIXES:")
        for name, result in results.items():
            if not result:
                print(f"   - Fix: {name}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
