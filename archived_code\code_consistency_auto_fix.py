"""
Code Consistency Auto-Fix System
Automatically fixes common consistency issues found in the audit
"""

import os
import re
import logging
from typing import List, Dict, Tuple
from pathlib import Path

class CodeConsistencyAutoFix:
    """Automatically fix code consistency issues"""
    
    def __init__(self):
        self.logger = logging.getLogger('CodeConsistencyAutoFix')
        self.fixes_applied = []
        self.backup_dir = Path('backups')
        self.backup_dir.mkdir(exist_ok=True)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
    
    def run_auto_fixes(self) -> Dict[str, List[str]]:
        """Run all auto-fixes and return results"""
        print("🔧 RUNNING CODE CONSISTENCY AUTO-FIXES...")
        print("=" * 50)
        
        results = {
            'hardcoded_values': self.fix_hardcoded_values(),
            'logging_consistency': self.fix_logging_consistency(),
            'import_consistency': self.fix_import_consistency(),
            'error_handling': self.fix_error_handling(),
            'configuration_usage': self.fix_configuration_usage()
        }
        
        # Generate summary
        total_fixes = sum(len(fixes) for fixes in results.values())
        print(f"\n✅ AUTO-FIX COMPLETE: {total_fixes} issues fixed")
        
        return results
    
    def fix_hardcoded_values(self) -> List[str]:
        """Fix hardcoded values that should be in configuration"""
        fixes = []
        
        print("🔧 Fixing hardcoded values...")
        
        # Files to check and fix
        files_to_fix = [
            'complete_trading_system.py',
            'trade_manager_webapp.py',
            'live_binance_integration.py'
        ]
        
        for file_path in files_to_fix:
            if os.path.exists(file_path):
                fixes.extend(self._fix_hardcoded_in_file(file_path))
        
        return fixes
    
    def _fix_hardcoded_in_file(self, file_path: str) -> List[str]:
        """Fix hardcoded values in a specific file"""
        fixes = []
        
        try:
            # Create backup
            self._create_backup(file_path)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Fix hardcoded port numbers
            if '8081' in content and 'config' not in file_path:
                content = re.sub(r'8081', 'self.config.WEB_PORT', content)
                fixes.append(f"{file_path}: Fixed hardcoded port 8081")
            
            # Fix hardcoded commission rates
            if '0.001' in content and 'config' not in file_path:
                content = re.sub(r'0\.001', 'self.config.COMMISSION_RATE', content)
                fixes.append(f"{file_path}: Fixed hardcoded commission rate")
            
            # Fix hardcoded directories
            content = re.sub(r"'logs/'", "self.config.LOGS_DIR + '/'", content)
            content = re.sub(r"'models/'", "self.config.MODELS_DIR + '/'", content)
            content = re.sub(r"'data/'", "self.config.DATA_DIR + '/'", content)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixes.append(f"{file_path}: Fixed hardcoded directory paths")
        
        except Exception as e:
            self.logger.error(f"Failed to fix hardcoded values in {file_path}: {e}")
        
        return fixes
    
    def fix_logging_consistency(self) -> List[str]:
        """Fix logging consistency issues"""
        fixes = []
        
        print("🔧 Fixing logging consistency...")
        
        files_to_fix = [
            'complete_trading_system.py',
            'ml_training_system.py',
            'live_binance_integration.py',
            'trade_manager_webapp.py'
        ]
        
        for file_path in files_to_fix:
            if os.path.exists(file_path):
                fixes.extend(self._fix_logging_in_file(file_path))
        
        return fixes
    
    def _fix_logging_in_file(self, file_path: str) -> List[str]:
        """Fix logging issues in a specific file"""
        fixes = []
        
        try:
            # Create backup
            self._create_backup(file_path)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Replace print statements with logging (except in main functions)
            lines = content.split('\n')
            new_lines = []
            
            for line in lines:
                # Skip print statements in main() functions or test output
                if ('print(' in line and 
                    'def main(' not in line and 
                    'if __name__' not in line and
                    '# Print' not in line and
                    'print("' in line):
                    
                    # Extract the message
                    match = re.search(r'print\("([^"]+)"\)', line)
                    if match:
                        message = match.group(1)
                        indent = len(line) - len(line.lstrip())
                        
                        # Determine log level based on content
                        if any(word in message.lower() for word in ['error', 'failed', 'exception']):
                            new_line = ' ' * indent + f'self.logger.error("{message}")'
                        elif any(word in message.lower() for word in ['warning', 'warn']):
                            new_line = ' ' * indent + f'self.logger.warning("{message}")'
                        else:
                            new_line = ' ' * indent + f'self.logger.info("{message}")'
                        
                        new_lines.append(new_line)
                        fixes.append(f"{file_path}: Replaced print with logging: {message[:30]}...")
                    else:
                        new_lines.append(line)
                else:
                    new_lines.append(line)
            
            content = '\n'.join(new_lines)
            
            # Ensure logger is initialized
            if 'self.logger' in content and 'logging.getLogger' not in content:
                # Add logger initialization after class definition
                class_match = re.search(r'class\s+\w+.*?:', content)
                if class_match:
                    init_match = re.search(r'def __init__\(self.*?\):', content)
                    if init_match:
                        init_end = content.find('\n', init_match.end())
                        logger_line = '\n        self.logger = logging.getLogger(self.__class__.__name__)'
                        content = content[:init_end] + logger_line + content[init_end:]
                        fixes.append(f"{file_path}: Added logger initialization")
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
        
        except Exception as e:
            self.logger.error(f"Failed to fix logging in {file_path}: {e}")
        
        return fixes
    
    def fix_import_consistency(self) -> List[str]:
        """Fix import consistency issues"""
        fixes = []
        
        print("🔧 Fixing import consistency...")
        
        files_to_fix = [
            'complete_trading_system.py',
            'ml_training_system.py',
            'live_binance_integration.py',
            'trade_manager_webapp.py'
        ]
        
        for file_path in files_to_fix:
            if os.path.exists(file_path):
                fixes.extend(self._fix_imports_in_file(file_path))
        
        return fixes
    
    def _fix_imports_in_file(self, file_path: str) -> List[str]:
        """Fix import issues in a specific file"""
        fixes = []
        
        try:
            # Create backup
            self._create_backup(file_path)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Ensure sys.path.append is present when importing from config
            if 'from trading_config import' in content and 'sys.path.append' not in content:
                # Add sys.path.append after other imports
                import_section_end = content.find('\n\n')
                if import_section_end > 0:
                    sys_path_line = "\n# Add config to path\nsys.path.append(os.path.join(os.path.dirname(__file__), 'config'))"
                    content = content[:import_section_end] + sys_path_line + content[import_section_end:]
                    fixes.append(f"{file_path}: Added sys.path.append for config import")
            
            # Ensure logging import is present when using logging
            if 'logging.' in content and 'import logging' not in content:
                # Add logging import
                first_import = content.find('import ')
                if first_import > 0:
                    content = content[:first_import] + 'import logging\n' + content[first_import:]
                    fixes.append(f"{file_path}: Added missing logging import")
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
        
        except Exception as e:
            self.logger.error(f"Failed to fix imports in {file_path}: {e}")
        
        return fixes
    
    def fix_error_handling(self) -> List[str]:
        """Fix error handling consistency issues"""
        fixes = []
        
        print("🔧 Fixing error handling...")
        
        files_to_fix = [
            'secure_api_manager.py',
            'complete_trading_system.py',
            'ml_training_system.py'
        ]
        
        for file_path in files_to_fix:
            if os.path.exists(file_path):
                fixes.extend(self._fix_error_handling_in_file(file_path))
        
        return fixes
    
    def _fix_error_handling_in_file(self, file_path: str) -> List[str]:
        """Fix error handling in a specific file"""
        fixes = []
        
        try:
            # Create backup
            self._create_backup(file_path)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Fix bare except clauses
            content = re.sub(r'except:', 'except Exception as e:', content)
            if 'except:' in original_content:
                fixes.append(f"{file_path}: Fixed bare except clauses")
            
            # Add logging to exception handlers that don't have it
            lines = content.split('\n')
            new_lines = []
            in_except_block = False
            except_has_logging = False
            
            for i, line in enumerate(lines):
                if 'except ' in line and ':' in line:
                    in_except_block = True
                    except_has_logging = False
                elif in_except_block:
                    if line.strip() == '' or (line.startswith(' ') and len(line.strip()) > 0):
                        if 'logger.' in line or 'logging.' in line or 'print(' in line:
                            except_has_logging = True
                    else:
                        # End of except block
                        if not except_has_logging and in_except_block:
                            # Add logging before this line
                            indent = '        '  # Assume standard indentation
                            log_line = f'{indent}self.logger.error(f"Error in {file_path}: {{e}}")'
                            new_lines.append(log_line)
                            fixes.append(f"{file_path}: Added logging to exception handler")
                        in_except_block = False
                
                new_lines.append(line)
            
            content = '\n'.join(new_lines)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
        
        except Exception as e:
            self.logger.error(f"Failed to fix error handling in {file_path}: {e}")
        
        return fixes
    
    def fix_configuration_usage(self) -> List[str]:
        """Fix configuration usage consistency"""
        fixes = []
        
        print("🔧 Fixing configuration usage...")
        
        # Add missing configuration attributes
        config_additions = self._add_missing_config_attributes()
        fixes.extend(config_additions)
        
        return fixes
    
    def _add_missing_config_attributes(self) -> List[str]:
        """Add missing configuration attributes"""
        fixes = []
        
        config_file = 'config/trading_config.py'
        if not os.path.exists(config_file):
            return fixes
        
        try:
            # Create backup
            self._create_backup(config_file)
            
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Add missing directory configurations
            if 'LOGS_DIR' not in content:
                content += '\n    # Directory Configuration\n'
                content += '    LOGS_DIR = "logs"\n'
                content += '    MODELS_DIR = "models"\n'
                content += '    DATA_DIR = "data"\n'
                fixes.append(f"{config_file}: Added missing directory configurations")
            
            # Add missing web configuration
            if 'WEB_PORT' not in content:
                content += '\n    # Web Interface Configuration\n'
                content += '    WEB_PORT = 8081\n'
                content += '    WEB_HOST = "127.0.0.1"\n'
                fixes.append(f"{config_file}: Added missing web configuration")
            
            if content != original_content:
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(content)
        
        except Exception as e:
            self.logger.error(f"Failed to update config file: {e}")
        
        return fixes
    
    def _create_backup(self, file_path: str):
        """Create backup of file before modification"""
        try:
            backup_path = self.backup_dir / f"{Path(file_path).name}.backup"
            with open(file_path, 'r', encoding='utf-8') as src:
                with open(backup_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
        except Exception as e:
            self.logger.warning(f"Failed to create backup for {file_path}: {e}")
    
    def generate_fix_report(self, results: Dict[str, List[str]]) -> str:
        """Generate a report of all fixes applied"""
        report = "# CODE CONSISTENCY AUTO-FIX REPORT\n\n"
        report += f"**Generated:** {os.path.basename(__file__)}\n"
        report += f"**Date:** {Path().cwd()}\n\n"
        
        total_fixes = sum(len(fixes) for fixes in results.values())
        report += f"**Total Fixes Applied:** {total_fixes}\n\n"
        
        for category, fixes in results.items():
            if fixes:
                report += f"## {category.replace('_', ' ').title()}\n\n"
                for fix in fixes:
                    report += f"- {fix}\n"
                report += "\n"
        
        if total_fixes == 0:
            report += "No consistency issues found that could be automatically fixed.\n"
        
        return report

def main():
    """Run code consistency auto-fixes"""
    print("🔧 CODE CONSISTENCY AUTO-FIX SYSTEM")
    print("=" * 60)
    print("Automatically fixing common consistency issues...")
    print()
    
    # Initialize auto-fix system
    auto_fix = CodeConsistencyAutoFix()
    
    # Run all fixes
    results = auto_fix.run_auto_fixes()
    
    # Generate and save report
    report = auto_fix.generate_fix_report(results)
    
    with open('auto_fix_report.md', 'w') as f:
        f.write(report)
    
    print(f"\n📄 Auto-fix report saved to: auto_fix_report.md")
    print(f"📁 Backups created in: {auto_fix.backup_dir}")
    
    # Summary
    total_fixes = sum(len(fixes) for fixes in results.values())
    if total_fixes > 0:
        print(f"\n🎉 Successfully applied {total_fixes} consistency fixes!")
        print("✅ Code consistency improved")
    else:
        print(f"\n✅ No consistency issues found to fix")
    
    return total_fixes > 0

if __name__ == "__main__":
    success = main()
