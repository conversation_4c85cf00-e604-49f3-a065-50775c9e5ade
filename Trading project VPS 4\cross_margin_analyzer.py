#!/usr/bin/env python3
"""
CROSS MARGIN ACCOUNT ANALYZER
============================
Analyze and optimize cross margin trading setup for Bitcoin Freedom bot.
"""

import os
import sys
import json
import time
import hmac
import hashlib
import requests
from datetime import datetime
from urllib.parse import urlencode

class CrossMarginAnalyzer:
    """Analyze cross margin account for optimal trading setup."""
    
    def __init__(self, api_key, secret_key):
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = "https://api.binance.com"
    
    def _generate_signature(self, query_string):
        """Generate HMAC SHA256 signature."""
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, endpoint, params=None, signed=False):
        """Make API request to Binance."""
        if params is None:
            params = {}
        
        headers = {
            'X-MBX-APIKEY': self.api_key
        }
        
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            query_string = urlencode(params)
            signature = self._generate_signature(query_string)
            params['signature'] = signature
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"API request failed: {e}")
    
    def get_spot_account(self):
        """Get spot account information."""
        return self._make_request("/api/v3/account", signed=True)
    
    def get_margin_account(self):
        """Get margin account information."""
        try:
            return self._make_request("/sapi/v1/margin/account", signed=True)
        except Exception as e:
            print(f"Margin account access failed: {e}")
            return None
    
    def get_cross_margin_assets(self):
        """Get cross margin asset details."""
        try:
            return self._make_request("/sapi/v1/margin/allAssets", signed=True)
        except Exception as e:
            print(f"Cross margin assets failed: {e}")
            return None
    
    def get_margin_pairs(self):
        """Get available margin trading pairs."""
        try:
            return self._make_request("/sapi/v1/margin/allPairs", signed=True)
        except Exception as e:
            print(f"Margin pairs failed: {e}")
            return None
    
    def analyze_spot_account(self):
        """Analyze spot account for trading readiness."""
        print("📊 SPOT ACCOUNT ANALYSIS")
        print("-" * 50)
        
        try:
            account = self.get_spot_account()
            
            # Account permissions
            permissions = account.get('permissions', [])
            print(f"Account Permissions: {', '.join(permissions)}")
            
            # Check for margin trading permission
            if 'MARGIN' in permissions:
                print("✅ Margin Trading: ENABLED")
            else:
                print("❌ Margin Trading: NOT ENABLED")
                print("   Enable margin trading in your Binance account")
            
            # Analyze balances
            balances = account.get('balances', [])
            btc_balance = next((b for b in balances if b['asset'] == 'BTC'), None)
            usdt_balance = next((b for b in balances if b['asset'] == 'USDT'), None)
            
            print("\n💰 SPOT BALANCES:")
            if btc_balance:
                btc_free = float(btc_balance['free'])
                btc_locked = float(btc_balance['locked'])
                print(f"   BTC: {btc_free:.8f} (Free: {btc_free:.8f}, Locked: {btc_locked:.8f})")
            else:
                print("   BTC: 0.********")
            
            if usdt_balance:
                usdt_free = float(usdt_balance['free'])
                usdt_locked = float(usdt_balance['locked'])
                print(f"   USDT: {usdt_free:.2f} (Free: {usdt_free:.2f}, Locked: {usdt_locked:.2f})")
            else:
                print("   USDT: 0.00")
            
            return account, btc_balance, usdt_balance
            
        except Exception as e:
            print(f"❌ Spot account analysis failed: {e}")
            return None, None, None
    
    def analyze_margin_account(self):
        """Analyze margin account for cross margin trading."""
        print("\n🔄 CROSS MARGIN ACCOUNT ANALYSIS")
        print("-" * 50)
        
        try:
            margin_account = self.get_margin_account()
            
            if not margin_account:
                print("❌ Cross Margin Account: NOT ACCESSIBLE")
                print("   Enable margin trading in your Binance account")
                return None
            
            print("✅ Cross Margin Account: ACCESSIBLE")
            
            # Margin level and risk
            margin_level = float(margin_account.get('marginLevel', 0))
            total_asset_of_btc = float(margin_account.get('totalAssetOfBtc', 0))
            total_liability_of_btc = float(margin_account.get('totalLiabilityOfBtc', 0))
            total_net_asset_of_btc = float(margin_account.get('totalNetAssetOfBtc', 0))
            
            print(f"\n📈 MARGIN METRICS:")
            print(f"   Margin Level: {margin_level:.2f}")
            print(f"   Total Assets (BTC): {total_asset_of_btc:.8f}")
            print(f"   Total Liabilities (BTC): {total_liability_of_btc:.8f}")
            print(f"   Net Assets (BTC): {total_net_asset_of_btc:.8f}")
            
            # Risk assessment
            if margin_level >= 3.0:
                print("✅ Risk Level: LOW (Safe for trading)")
            elif margin_level >= 1.5:
                print("⚠️ Risk Level: MEDIUM (Monitor closely)")
            elif margin_level >= 1.1:
                print("🚨 Risk Level: HIGH (Reduce positions)")
            else:
                print("🔴 Risk Level: CRITICAL (Liquidation risk)")
            
            # Analyze margin balances
            user_assets = margin_account.get('userAssets', [])
            btc_margin = next((a for a in user_assets if a['asset'] == 'BTC'), None)
            usdt_margin = next((a for a in user_assets if a['asset'] == 'USDT'), None)
            
            print(f"\n💰 CROSS MARGIN BALANCES:")
            if btc_margin:
                btc_free = float(btc_margin['free'])
                btc_locked = float(btc_margin['locked'])
                btc_borrowed = float(btc_margin['borrowed'])
                btc_interest = float(btc_margin['interest'])
                btc_net = float(btc_margin['netAsset'])
                print(f"   BTC: {btc_net:.8f} (Free: {btc_free:.8f}, Borrowed: {btc_borrowed:.8f})")
            else:
                print("   BTC: 0.********")
            
            if usdt_margin:
                usdt_free = float(usdt_margin['free'])
                usdt_locked = float(usdt_margin['locked'])
                usdt_borrowed = float(usdt_margin['borrowed'])
                usdt_interest = float(usdt_margin['interest'])
                usdt_net = float(usdt_margin['netAsset'])
                print(f"   USDT: {usdt_net:.2f} (Free: {usdt_free:.2f}, Borrowed: {usdt_borrowed:.2f})")
            else:
                print("   USDT: 0.00")
            
            return margin_account, btc_margin, usdt_margin
            
        except Exception as e:
            print(f"❌ Margin account analysis failed: {e}")
            return None, None, None
    
    def check_btc_usdt_pair(self):
        """Check BTC/USDT margin trading availability."""
        print("\n🔍 BTC/USDT MARGIN PAIR ANALYSIS")
        print("-" * 50)
        
        try:
            pairs = self.get_margin_pairs()
            if not pairs:
                print("❌ Could not fetch margin pairs")
                return None
            
            btc_usdt_pair = next((p for p in pairs if p['symbol'] == 'BTCUSDT'), None)
            
            if btc_usdt_pair:
                print("✅ BTC/USDT: Available for margin trading")
                print(f"   Base Asset: {btc_usdt_pair['base']}")
                print(f"   Quote Asset: {btc_usdt_pair['quote']}")
                print(f"   Is Margin Trade: {btc_usdt_pair['isMarginTrade']}")
                print(f"   Is Buy Allowed: {btc_usdt_pair['isBuyAllowed']}")
                print(f"   Is Sell Allowed: {btc_usdt_pair['isSellAllowed']}")
                return btc_usdt_pair
            else:
                print("❌ BTC/USDT: Not available for margin trading")
                return None
                
        except Exception as e:
            print(f"❌ BTC/USDT pair check failed: {e}")
            return None

def load_api_keys():
    """Load API keys from BinanceAPI_2.txt file."""
    try:
        possible_paths = [
            r"C:\Users\<USER>\Documents\Trading system\Trading project VPS 5\BinanceAPI_2.txt",
            "../BinanceAPI_2.txt",
            "BinanceAPI_2.txt"
        ]
        
        for api_file_path in possible_paths:
            if os.path.exists(api_file_path):
                with open(api_file_path, 'r') as f:
                    lines = [line.strip() for line in f.readlines() if line.strip()]
                    if len(lines) >= 2:
                        return lines[0], lines[1]
        
        return None, None
        
    except Exception as e:
        print(f"Error loading API keys: {e}")
        return None, None

def generate_trading_recommendations(spot_account, margin_account, btc_spot, usdt_spot, btc_margin, usdt_margin):
    """Generate optimal trading recommendations."""
    print("\n🎯 OPTIMAL TRADING CONFIGURATION")
    print("=" * 60)
    
    # Calculate available capital
    spot_usdt = float(usdt_spot['free']) if usdt_spot else 0
    spot_btc = float(btc_spot['free']) if btc_spot else 0
    
    margin_usdt = float(usdt_margin['netAsset']) if usdt_margin else 0
    margin_btc = float(btc_margin['netAsset']) if btc_margin else 0
    
    # Get current BTC price for calculations
    try:
        response = requests.get("https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT")
        btc_price = float(response.json()['price'])
    except:
        btc_price = 105000  # Fallback price
    
    total_usdt_value = spot_usdt + margin_usdt + (spot_btc + margin_btc) * btc_price
    
    print(f"💰 TOTAL TRADING CAPITAL ANALYSIS:")
    print(f"   Current BTC Price: ${btc_price:,.2f}")
    print(f"   Spot USDT: ${spot_usdt:.2f}")
    print(f"   Spot BTC Value: ${spot_btc * btc_price:.2f}")
    print(f"   Margin USDT: ${margin_usdt:.2f}")
    print(f"   Margin BTC Value: ${margin_btc * btc_price:.2f}")
    print(f"   TOTAL VALUE: ${total_usdt_value:.2f}")
    
    print(f"\n🤖 RECOMMENDED BOT CONFIGURATION:")
    
    if margin_account and total_usdt_value > 20:
        print("✅ CROSS MARGIN TRADING RECOMMENDED")
        print("   Strategy: Use cross margin for leverage and flexibility")
        
        # Calculate optimal position sizing
        risk_per_trade = min(total_usdt_value * 0.02, 25)  # 2% risk or $25 max
        position_size = risk_per_trade / (btc_price * 0.02)  # Assuming 2% stop loss
        
        print(f"   Account Size: ${total_usdt_value:.2f}")
        print(f"   Risk per Trade: ${risk_per_trade:.2f} (2% of capital)")
        print(f"   Position Size: {position_size:.6f} BTC")
        print(f"   Leverage: 2x-3x (conservative)")
        
        print(f"\n📋 IMPLEMENTATION STEPS:")
        print("   1. Enable cross margin trading in bot")
        print("   2. Set position size to cross margin mode")
        print("   3. Use both BTC and USDT for optimal flexibility")
        print("   4. Monitor margin level closely (keep > 3.0)")
        
    elif total_usdt_value > 10:
        print("✅ SPOT TRADING RECOMMENDED")
        print("   Strategy: Conservative spot trading")
        
        risk_per_trade = min(total_usdt_value * 0.05, 10)  # 5% risk or $10 max
        position_size = risk_per_trade / (btc_price * 0.02)
        
        print(f"   Account Size: ${total_usdt_value:.2f}")
        print(f"   Risk per Trade: ${risk_per_trade:.2f}")
        print(f"   Position Size: {position_size:.6f} BTC")
        
    else:
        print("⚠️ SMALL ACCOUNT - CAREFUL TRADING")
        print("   Strategy: Micro position sizes")
        print(f"   Account Size: ${total_usdt_value:.2f}")
        print("   Risk per Trade: $2-5 maximum")

def main():
    """Main analysis function."""
    print("🔍 CROSS MARGIN TRADING OPTIMIZATION")
    print("=" * 60)
    print(f"Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Load API keys
    api_key, secret_key = load_api_keys()
    if not api_key or not secret_key:
        print("❌ API keys not found")
        return
    
    # Create analyzer
    analyzer = CrossMarginAnalyzer(api_key, secret_key)
    
    # Analyze accounts
    spot_account, btc_spot, usdt_spot = analyzer.analyze_spot_account()
    margin_account, btc_margin, usdt_margin = analyzer.analyze_margin_account()
    btc_usdt_pair = analyzer.check_btc_usdt_pair()
    
    # Generate recommendations
    generate_trading_recommendations(
        spot_account, margin_account, btc_spot, usdt_spot, btc_margin, usdt_margin
    )

if __name__ == "__main__":
    main()
