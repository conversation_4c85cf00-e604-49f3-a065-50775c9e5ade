#!/usr/bin/env python3
"""
Comprehensive Health Check System for Live Trading Web Application
Performs full audit and validation of all system components
"""

import requests
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Any, Tuple
import traceback

class WebAppHealthCheck:
    """Comprehensive health check for the live trading web application."""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.results = {}
        self.errors = []
        self.warnings = []
        
    def run_full_audit(self) -> Dict[str, Any]:
        """Run complete health check audit."""
        print("🔍 STARTING COMPREHENSIVE WEBAPP HEALTH CHECK")
        print("=" * 60)
        
        # Core system checks
        self.check_server_availability()
        self.check_api_endpoints()
        self.check_real_time_data()
        self.check_model_integration()
        self.check_cross_margin_calculations()
        self.check_trading_engine()
        self.check_test_functions()
        self.check_web_interface()
        self.check_data_integrity()
        
        # Generate final report
        return self.generate_health_report()
    
    def check_server_availability(self):
        """Check if the web server is running and responsive."""
        print("🌐 Checking Server Availability...")
        
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                self.results['server'] = {
                    'status': 'HEALTHY',
                    'response_time': response.elapsed.total_seconds(),
                    'status_code': response.status_code
                }
                print(f"   ✅ Server responsive ({response.elapsed.total_seconds():.2f}s)")
            else:
                self.results['server'] = {
                    'status': 'ERROR',
                    'status_code': response.status_code,
                    'error': f"HTTP {response.status_code}"
                }
                self.errors.append(f"Server returned HTTP {response.status_code}")
        except Exception as e:
            self.results['server'] = {
                'status': 'CRITICAL',
                'error': str(e)
            }
            self.errors.append(f"Server unavailable: {e}")
    
    def check_api_endpoints(self):
        """Check all API endpoints are working."""
        print("🔌 Checking API Endpoints...")
        
        endpoints = [
            '/api/trading_status',
            '/api/open_positions', 
            '/api/recent_trades',
            '/api/cross_margin_analysis',
            '/api/trading_test_status'
        ]
        
        endpoint_results = {}
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    endpoint_results[endpoint] = {
                        'status': 'HEALTHY',
                        'response_time': response.elapsed.total_seconds(),
                        'data_keys': list(data.keys()) if isinstance(data, dict) else 'non-dict'
                    }
                    print(f"   ✅ {endpoint} - OK ({response.elapsed.total_seconds():.2f}s)")
                else:
                    endpoint_results[endpoint] = {
                        'status': 'ERROR',
                        'status_code': response.status_code
                    }
                    self.errors.append(f"API endpoint {endpoint} returned {response.status_code}")
            except Exception as e:
                endpoint_results[endpoint] = {
                    'status': 'CRITICAL',
                    'error': str(e)
                }
                self.errors.append(f"API endpoint {endpoint} failed: {e}")
        
        self.results['api_endpoints'] = endpoint_results
    
    def check_real_time_data(self):
        """Check real-time BTC price data integration."""
        print("💰 Checking Real-Time Data Integration...")
        
        try:
            # Check trading status for price data
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            data = response.json()
            
            current_price = data.get('current_price', 0)
            
            if current_price > 50000 and current_price < 200000:  # Reasonable BTC price range
                self.results['real_time_data'] = {
                    'status': 'HEALTHY',
                    'current_btc_price': current_price,
                    'price_source': 'real_api',
                    'last_update': datetime.now().isoformat()
                }
                print(f"   ✅ Real BTC Price: ${current_price:,.2f}")
                
                # Check if price updates over time
                time.sleep(3)
                response2 = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
                data2 = response2.json()
                price2 = data2.get('current_price', 0)
                
                if abs(price2 - current_price) > 0.01:  # Price should update
                    print(f"   ✅ Price updating: ${price2:,.2f}")
                else:
                    self.warnings.append("BTC price may not be updating in real-time")
                    
            else:
                self.results['real_time_data'] = {
                    'status': 'ERROR',
                    'current_price': current_price,
                    'error': 'Price outside reasonable range'
                }
                self.errors.append(f"BTC price ${current_price} outside reasonable range")
                
        except Exception as e:
            self.results['real_time_data'] = {
                'status': 'CRITICAL',
                'error': str(e)
            }
            self.errors.append(f"Real-time data check failed: {e}")
    
    def check_model_integration(self):
        """Check Cycle 1 model integration."""
        print("🤖 Checking Model Integration...")
        
        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            data = response.json()
            
            model_info = data.get('model_info', {})
            
            # Expected Conservative Elite model values
            expected_model_id = "tcn_cnn_ppo_conservative_v3_20250604_111817"
            expected_composite_score = 79.1
            expected_cycle = "Conservative Elite"
            
            model_checks = {
                'model_id_correct': model_info.get('model_id') == expected_model_id,
                'composite_score_correct': abs(model_info.get('composite_score', 0) - expected_composite_score) < 0.1,
                'cycle_correct': model_info.get('cycle') == expected_cycle,
                'tcn_weight_present': 'tcn_weight' in model_info,
                'cnn_weight_present': 'cnn_weight' in model_info,
                'ppo_weight_present': 'ppo_weight' in model_info
            }
            
            all_checks_passed = all(model_checks.values())
            
            self.results['model_integration'] = {
                'status': 'HEALTHY' if all_checks_passed else 'WARNING',
                'model_id': model_info.get('model_id'),
                'composite_score': model_info.get('composite_score'),
                'cycle': model_info.get('cycle'),
                'checks': model_checks,
                'component_weights': {
                    'tcn': model_info.get('tcn_weight'),
                    'cnn': model_info.get('cnn_weight'), 
                    'ppo': model_info.get('ppo_weight')
                }
            }
            
            if all_checks_passed:
                print(f"   ✅ Cycle 1 Model: {model_info.get('model_id')}")
                print(f"   ✅ Composite Score: {model_info.get('composite_score')}%")
            else:
                failed_checks = [k for k, v in model_checks.items() if not v]
                self.warnings.append(f"Model integration issues: {failed_checks}")
                
        except Exception as e:
            self.results['model_integration'] = {
                'status': 'CRITICAL',
                'error': str(e)
            }
            self.errors.append(f"Model integration check failed: {e}")
    
    def check_cross_margin_calculations(self):
        """Check cross margin and grid trading calculations."""
        print("📊 Checking Cross Margin Calculations...")
        
        try:
            response = requests.get(f"{self.base_url}/api/cross_margin_analysis", timeout=5)
            data = response.json()
            
            # Validate key calculations
            buy_position = data.get('buy_position', {})
            sell_position = data.get('sell_position', {})
            accuracy_check = data.get('accuracy_check', {})
            
            validation_checks = {
                'grid_size_correct': abs(data.get('grid_size_percent', 0) - 0.25) < 0.01,
                'risk_reward_ratio_buy': abs(accuracy_check.get('risk_reward_ratio_buy', 0) - 2.5) < 0.1,
                'risk_reward_ratio_sell': abs(accuracy_check.get('risk_reward_ratio_sell', 0) - 2.5) < 0.1,
                'account_size_correct': data.get('account_config', {}).get('account_size') == 300.0,
                'margin_type_correct': data.get('account_config', {}).get('margin_type') == 'CROSS',
                'position_sizes_calculated': buy_position.get('position_size_btc', 0) > 0
            }
            
            all_checks_passed = all(validation_checks.values())
            
            self.results['cross_margin'] = {
                'status': 'HEALTHY' if all_checks_passed else 'WARNING',
                'grid_size_percent': data.get('grid_size_percent'),
                'current_btc_price': data.get('current_btc_price'),
                'validation_checks': validation_checks,
                'buy_risk_reward': accuracy_check.get('risk_reward_ratio_buy'),
                'sell_risk_reward': accuracy_check.get('risk_reward_ratio_sell')
            }
            
            if all_checks_passed:
                print(f"   ✅ Grid Size: {data.get('grid_size_percent')}%")
                print(f"   ✅ Risk:Reward Ratios: {accuracy_check.get('risk_reward_ratio_buy')}:1")
            else:
                failed_checks = [k for k, v in validation_checks.items() if not v]
                self.warnings.append(f"Cross margin calculation issues: {failed_checks}")
                
        except Exception as e:
            self.results['cross_margin'] = {
                'status': 'CRITICAL',
                'error': str(e)
            }
            self.errors.append(f"Cross margin check failed: {e}")

    def check_trading_engine(self):
        """Check trading engine functionality."""
        print("⚡ Checking Trading Engine...")

        try:
            # Check trading status
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            data = response.json()

            # Check open positions
            positions_response = requests.get(f"{self.base_url}/api/open_positions", timeout=5)
            positions_data = positions_response.json()

            # Check recent trades
            trades_response = requests.get(f"{self.base_url}/api/recent_trades", timeout=5)
            trades_data = trades_response.json()

            engine_checks = {
                'trading_status_available': 'is_running' in data,
                'performance_metrics_available': 'performance' in data,
                'positions_endpoint_working': positions_response.status_code == 200,
                'trades_endpoint_working': trades_response.status_code == 200,
                'balance_tracking': data.get('performance', {}).get('equity', 0) > 0,
                'account_size_correct': data.get('performance', {}).get('account_size') == 300.0
            }

            all_checks_passed = all(engine_checks.values())

            self.results['trading_engine'] = {
                'status': 'HEALTHY' if all_checks_passed else 'WARNING',
                'is_running': data.get('is_running'),
                'current_equity': data.get('performance', {}).get('equity'),
                'total_trades': data.get('performance', {}).get('total_trades'),
                'win_rate': data.get('performance', {}).get('win_rate'),
                'open_positions': data.get('performance', {}).get('open_positions'),
                'checks': engine_checks
            }

            if all_checks_passed:
                print(f"   ✅ Trading Engine Status: {data.get('is_running')}")
                print(f"   ✅ Current Equity: ${data.get('performance', {}).get('equity', 0):.2f}")
                print(f"   ✅ Total Trades: {data.get('performance', {}).get('total_trades', 0)}")
            else:
                failed_checks = [k for k, v in engine_checks.items() if not v]
                self.warnings.append(f"Trading engine issues: {failed_checks}")

        except Exception as e:
            self.results['trading_engine'] = {
                'status': 'CRITICAL',
                'error': str(e)
            }
            self.errors.append(f"Trading engine check failed: {e}")

    def check_test_functions(self):
        """Check test trading functions."""
        print("🧪 Checking Test Functions...")

        try:
            # Check test status endpoint
            response = requests.get(f"{self.base_url}/api/trading_test_status", timeout=5)
            data = response.json()

            test_capabilities = data.get('test_capabilities', {})

            test_checks = {
                'force_trade_available': test_capabilities.get('force_trade', False),
                'close_all_available': test_capabilities.get('close_all', False),
                'test_cycle_available': test_capabilities.get('test_cycle', False),
                'reset_stats_available': test_capabilities.get('reset_stats', False)
            }

            all_checks_passed = all(test_checks.values())

            self.results['test_functions'] = {
                'status': 'HEALTHY' if all_checks_passed else 'WARNING',
                'test_capabilities': test_capabilities,
                'checks': test_checks
            }

            if all_checks_passed:
                print("   ✅ All test functions available")
                print("   ✅ Force trade, close all, test cycle, reset stats")
            else:
                missing_functions = [k for k, v in test_checks.items() if not v]
                self.warnings.append(f"Missing test functions: {missing_functions}")

        except Exception as e:
            self.results['test_functions'] = {
                'status': 'CRITICAL',
                'error': str(e)
            }
            self.errors.append(f"Test functions check failed: {e}")

    def check_web_interface(self):
        """Check web interface components."""
        print("🖥️ Checking Web Interface...")

        try:
            # Check main dashboard
            response = requests.get(f"{self.base_url}/", timeout=10)

            if response.status_code == 200:
                html_content = response.text

                # Check for key UI elements
                ui_checks = {
                    'dashboard_loads': 'Live Trading Dashboard' in html_content or 'Trading Dashboard' in html_content,
                    'start_trading_button': 'START TRADING' in html_content,
                    'stop_trading_button': 'STOP TRADING' in html_content,
                    'test_functions_section': 'Test Trading Functions' in html_content or 'FORCE BUY TEST' in html_content,
                    'performance_section': 'Performance' in html_content or 'Equity' in html_content,
                    'btc_price_display': 'BTC:' in html_content or '$' in html_content
                }

                all_checks_passed = all(ui_checks.values())

                self.results['web_interface'] = {
                    'status': 'HEALTHY' if all_checks_passed else 'WARNING',
                    'response_time': response.elapsed.total_seconds(),
                    'content_length': len(html_content),
                    'ui_checks': ui_checks
                }

                if all_checks_passed:
                    print("   ✅ Dashboard loads successfully")
                    print("   ✅ All UI components present")
                else:
                    missing_ui = [k for k, v in ui_checks.items() if not v]
                    self.warnings.append(f"Missing UI components: {missing_ui}")
            else:
                self.results['web_interface'] = {
                    'status': 'ERROR',
                    'status_code': response.status_code
                }
                self.errors.append(f"Dashboard returned HTTP {response.status_code}")

        except Exception as e:
            self.results['web_interface'] = {
                'status': 'CRITICAL',
                'error': str(e)
            }
            self.errors.append(f"Web interface check failed: {e}")

    def check_data_integrity(self):
        """Check data consistency and integrity."""
        print("🔍 Checking Data Integrity...")

        try:
            # Get data from multiple endpoints
            status_response = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            status_data = status_response.json()

            margin_response = requests.get(f"{self.base_url}/api/cross_margin_analysis", timeout=5)
            margin_data = margin_response.json()

            # Check data consistency
            integrity_checks = {
                'btc_price_consistent': abs(
                    status_data.get('current_price', 0) -
                    margin_data.get('current_btc_price', 0)
                ) < 1.0,  # Prices should be within $1
                'account_size_consistent': (
                    status_data.get('performance', {}).get('account_size') ==
                    margin_data.get('account_config', {}).get('account_size')
                ),
                'model_data_complete': all([
                    status_data.get('model_info', {}).get('model_id'),
                    status_data.get('model_info', {}).get('composite_score'),
                    status_data.get('model_info', {}).get('cycle')
                ]),
                'performance_data_complete': all([
                    'equity' in status_data.get('performance', {}),
                    'total_profit' in status_data.get('performance', {}),
                    'win_rate' in status_data.get('performance', {})
                ])
            }

            all_checks_passed = all(integrity_checks.values())

            self.results['data_integrity'] = {
                'status': 'HEALTHY' if all_checks_passed else 'WARNING',
                'checks': integrity_checks,
                'btc_price_status': status_data.get('current_price'),
                'btc_price_margin': margin_data.get('current_btc_price')
            }

            if all_checks_passed:
                print("   ✅ Data consistency verified")
                print("   ✅ All required data fields present")
            else:
                failed_checks = [k for k, v in integrity_checks.items() if not v]
                self.warnings.append(f"Data integrity issues: {failed_checks}")

        except Exception as e:
            self.results['data_integrity'] = {
                'status': 'CRITICAL',
                'error': str(e)
            }
            self.errors.append(f"Data integrity check failed: {e}")

    def generate_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive health report."""
        print("\n" + "=" * 60)
        print("📋 HEALTH CHECK REPORT")
        print("=" * 60)

        # Count statuses
        healthy_count = sum(1 for r in self.results.values() if r.get('status') == 'HEALTHY')
        warning_count = sum(1 for r in self.results.values() if r.get('status') == 'WARNING')
        error_count = sum(1 for r in self.results.values() if r.get('status') == 'ERROR')
        critical_count = sum(1 for r in self.results.values() if r.get('status') == 'CRITICAL')

        total_checks = len(self.results)

        # Overall health status
        if critical_count > 0:
            overall_status = 'CRITICAL'
        elif error_count > 0:
            overall_status = 'ERROR'
        elif warning_count > 0:
            overall_status = 'WARNING'
        else:
            overall_status = 'HEALTHY'

        # Print summary
        print(f"Overall Status: {overall_status}")
        print(f"Total Checks: {total_checks}")
        print(f"✅ Healthy: {healthy_count}")
        print(f"⚠️ Warnings: {warning_count}")
        print(f"❌ Errors: {error_count}")
        print(f"🚨 Critical: {critical_count}")

        # Print detailed results
        print("\nDetailed Results:")
        for component, result in self.results.items():
            status_icon = {
                'HEALTHY': '✅',
                'WARNING': '⚠️',
                'ERROR': '❌',
                'CRITICAL': '🚨'
            }.get(result.get('status'), '❓')

            print(f"{status_icon} {component.upper()}: {result.get('status')}")
            if result.get('error'):
                print(f"   Error: {result['error']}")

        # Print warnings and errors
        if self.warnings:
            print(f"\n⚠️ WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"   • {warning}")

        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"   • {error}")

        # Generate final report
        report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': overall_status,
            'summary': {
                'total_checks': total_checks,
                'healthy': healthy_count,
                'warnings': warning_count,
                'errors': error_count,
                'critical': critical_count
            },
            'detailed_results': self.results,
            'warnings': self.warnings,
            'errors': self.errors,
            'recommendations': self.generate_recommendations()
        }

        return report

    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on health check results."""
        recommendations = []

        # Check for critical issues
        if any(r.get('status') == 'CRITICAL' for r in self.results.values()):
            recommendations.append("🚨 CRITICAL: Address critical issues immediately before trading")

        # Check server issues
        if self.results.get('server', {}).get('status') != 'HEALTHY':
            recommendations.append("🌐 Restart web server and check configuration")

        # Check model integration
        if self.results.get('model_integration', {}).get('status') != 'HEALTHY':
            recommendations.append("🤖 Verify Cycle 1 model is properly loaded and configured")

        # Check real-time data
        if self.results.get('real_time_data', {}).get('status') != 'HEALTHY':
            recommendations.append("💰 Check Bitcoin price API connections and data feeds")

        # Check trading engine
        if self.results.get('trading_engine', {}).get('status') != 'HEALTHY':
            recommendations.append("⚡ Review trading engine configuration and restart if needed")

        # Check cross margin
        if self.results.get('cross_margin', {}).get('status') != 'HEALTHY':
            recommendations.append("📊 Verify cross margin calculations and grid trading parameters")

        # General recommendations
        if len(self.warnings) > 0:
            recommendations.append("⚠️ Review and address all warnings before live trading")

        if len(self.errors) == 0 and len(self.warnings) == 0:
            recommendations.append("✅ System is healthy - Ready for live trading")

        return recommendations


def main():
    """Run the health check."""
    import argparse

    parser = argparse.ArgumentParser(description='Web App Health Check')
    parser.add_argument('--url', default='http://localhost:5000',
                       help='Base URL of the web application')
    parser.add_argument('--output', help='Output file for health report (JSON)')

    args = parser.parse_args()

    # Run health check
    health_checker = WebAppHealthCheck(args.url)
    report = health_checker.run_full_audit()

    # Save report if requested
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"\n📄 Health report saved to: {args.output}")

    # Exit with appropriate code
    if report['overall_status'] in ['CRITICAL', 'ERROR']:
        sys.exit(1)
    elif report['overall_status'] == 'WARNING':
        sys.exit(2)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
