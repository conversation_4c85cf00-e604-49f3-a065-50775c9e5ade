"""
Live Binance Integration System
Real connection to Binance for live trading with cross margin
"""

import os
import sys
import logging
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

try:
    import ccxt
    CCXT_AVAILABLE = True
except ImportError:
    CCXT_AVAILABLE = False
    raise ImportError("CCXT is required for live Binance integration")

try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    raise ImportError("Pandas and NumPy are required")

class BinanceConnector:
    """Live Binance API connection and validation"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('BinanceConnector')
        
        if not config.BINANCE_API_KEY or not config.BINANCE_SECRET_KEY:
            raise ValueError("Binance API keys not configured. Run: python secure_api_manager.py --setup")
        
        # Initialize Binance connection
        self.exchange = ccxt.binance({
            'apiKey': config.BINANCE_API_KEY,
            'secret': config.BINANCE_SECRET_KEY,
            'sandbox': config.BINANCE_TESTNET,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'margin',  # Use cross margin
                'adjustForTimeDifference': True,
            }
        })
        
        self.is_connected = False
        self.account_info = None
        self.balance_info = None
        
    async def test_connection(self) -> bool:
        """Test Binance API connection"""
        try:
            self.logger.info("🔗 Testing Binance API connection...")
            
            # Test basic connectivity
            server_time = self.exchange.fetch_time()
            self.logger.info(f"✅ Server time: {datetime.fromtimestamp(server_time/1000)}")
            
            # Test account access
            self.account_info = self.exchange.fetch_account()
            self.logger.info(f"✅ Account type: {self.account_info.get('type', 'Unknown')}")
            
            # Test balance access
            self.balance_info = self.exchange.fetch_balance()
            self.logger.info(f"✅ Balance access successful")
            
            # Test margin account (if available)
            try:
                margin_account = self.exchange.fetch_margin_account()
                self.logger.info(f"✅ Cross margin account accessible")
                self.logger.info(f"   Total Asset: {margin_account.get('totalAssetOfBtc', 'N/A')} BTC")
                self.logger.info(f"   Total Liability: {margin_account.get('totalLiabilityOfBtc', 'N/A')} BTC")
                self.logger.info(f"   Margin Level: {margin_account.get('marginLevel', 'N/A')}")
            except Exception as e:
                self.logger.warning(f"⚠️ Margin account not accessible: {e}")
            
            # Test market data access
            ticker = self.exchange.fetch_ticker('BTC/USDT')
            self.logger.info(f"✅ Market data: BTC/USDT = ${ticker['last']:,.2f}")
            
            self.is_connected = True
            self.logger.info("🎉 Binance connection test SUCCESSFUL!")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Binance connection test FAILED: {e}")
            self.is_connected = False
            return False
    
    def get_account_balance(self) -> Dict[str, float]:
        """Get current account balance"""
        try:
            if not self.is_connected:
                raise Exception("Not connected to Binance")
            
            balance = self.exchange.fetch_balance()
            
            # Extract relevant balances
            usdt_balance = balance.get('USDT', {}).get('free', 0.0)
            btc_balance = balance.get('BTC', {}).get('free', 0.0)
            
            # Get total portfolio value in USDT
            total_value = usdt_balance
            if btc_balance > 0:
                btc_price = self.exchange.fetch_ticker('BTC/USDT')['last']
                total_value += btc_balance * btc_price
            
            return {
                'USDT': usdt_balance,
                'BTC': btc_balance,
                'total_value_usdt': total_value,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get account balance: {e}")
            return {}
    
    def get_trading_fees(self, symbol: str) -> Dict[str, float]:
        """Get trading fees for symbol"""
        try:
            trading_fees = self.exchange.fetch_trading_fees()
            
            if symbol in trading_fees:
                return {
                    'maker': trading_fees[symbol]['maker'],
                    'taker': trading_fees[symbol]['taker']
                }
            else:
                # Default Binance fees
                return {
                    'maker': self.config.COMMISSION_RATE,  # 0.1%
                    'taker': self.config.COMMISSION_RATE   # 0.1%
                }
                
        except Exception as e:
            self.logger.warning(f"Could not fetch trading fees: {e}")
            return {'maker': self.config.COMMISSION_RATE, 'taker': self.config.COMMISSION_RATE}
    
    def validate_trading_pair(self, symbol: str) -> bool:
        """Validate if trading pair is available"""
        try:
            markets = self.exchange.load_markets()
            return symbol in markets
        except Exception as e:
            self.logger.error(f"Failed to validate trading pair {symbol}: {e}")
            return False

class LiveMarketData:
    """Real-time market data collection"""
    
    def __init__(self, binance_connector: BinanceConnector):
        self.connector = binance_connector
        self.logger = logging.getLogger('LiveMarketData')
        
    def get_current_price(self, symbol: str) -> float:
        """Get current market price"""
        try:
            ticker = self.connector.exchange.fetch_ticker(symbol)
            return ticker['last']
        except Exception as e:
            self.logger.error(f"Failed to get price for {symbol}: {e}")
            return 0.0
    
    def get_recent_ohlcv(self, symbol: str, timeframe: str = '1h', limit: int = 100) -> pd.DataFrame:
        """Get recent OHLCV data for ML predictions"""
        try:
            ohlcv = self.connector.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"Failed to get OHLCV data for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_order_book(self, symbol: str, limit: int = 20) -> Dict:
        """Get current order book"""
        try:
            order_book = self.connector.exchange.fetch_order_book(symbol, limit)
            return {
                'bids': order_book['bids'][:5],  # Top 5 bids
                'asks': order_book['asks'][:5],  # Top 5 asks
                'timestamp': order_book['timestamp']
            }
        except Exception as e:
            self.logger.error(f"Failed to get order book for {symbol}: {e}")
            return {}

class LiveTradeExecutor:
    """Live trade execution on Binance"""
    
    def __init__(self, binance_connector: BinanceConnector, config: TradingConfig):
        self.connector = binance_connector
        self.config = config
        self.logger = logging.getLogger('LiveTradeExecutor')
        
        self.active_orders = {}
        self.trade_history = []
        
    def calculate_position_size(self, symbol: str, risk_amount: float, current_price: float) -> float:
        """Calculate position size based on risk amount"""
        try:
            # Get minimum order size for symbol
            markets = self.connector.exchange.load_markets()
            market_info = markets.get(symbol, {})
            
            min_amount = market_info.get('limits', {}).get('amount', {}).get('min', self.config.COMMISSION_RATE)
            
            # Calculate quantity based on risk amount
            quantity = risk_amount / current_price
            
            # Ensure minimum order size
            if quantity < min_amount:
                self.logger.warning(f"Calculated quantity {quantity} below minimum {min_amount}")
                quantity = min_amount
            
            # Round to appropriate precision
            precision = market_info.get('precision', {}).get('amount', 8)
            quantity = round(quantity, precision)
            
            return quantity
            
        except Exception as e:
            self.logger.error(f"Failed to calculate position size: {e}")
            return 0.0
    
    def place_market_order(self, symbol: str, side: str, quantity: float, 
                          stop_loss: Optional[float] = None, 
                          take_profit: Optional[float] = None) -> Dict:
        """Place a market order with optional stop loss and take profit"""
        try:
            if not self.connector.is_connected:
                raise Exception("Not connected to Binance")
            
            self.logger.info(f"🔄 Placing {side} order: {quantity} {symbol}")
            
            # Place main market order
            order = self.connector.exchange.create_market_order(symbol, side, quantity)
            
            order_id = order['id']
            self.active_orders[order_id] = order
            
            self.logger.info(f"✅ Order placed successfully: {order_id}")
            
            # Place stop loss if specified
            if stop_loss and order['status'] == 'closed':
                try:
                    stop_side = 'sell' if side == 'buy' else 'buy'
                    stop_order = self.connector.exchange.create_stop_loss_order(
                        symbol, stop_side, quantity, stop_loss
                    )
                    self.logger.info(f"✅ Stop loss placed: {stop_order['id']}")
                except Exception as e:
                    self.logger.warning(f"Failed to place stop loss: {e}")
            
            # Place take profit if specified
            if take_profit and order['status'] == 'closed':
                try:
                    tp_side = 'sell' if side == 'buy' else 'buy'
                    tp_order = self.connector.exchange.create_limit_order(
                        symbol, tp_side, quantity, take_profit
                    )
                    self.logger.info(f"✅ Take profit placed: {tp_order['id']}")
                except Exception as e:
                    self.logger.warning(f"Failed to place take profit: {e}")
            
            # Record trade
            trade_record = {
                'order_id': order_id,
                'symbol': symbol,
                'side': side,
                'quantity': quantity,
                'price': order.get('average', order.get('price', 0)),
                'timestamp': datetime.now().isoformat(),
                'status': order['status'],
                'fees': order.get('fees', []),
                'stop_loss': stop_loss,
                'take_profit': take_profit
            }
            
            self.trade_history.append(trade_record)
            
            return {
                'success': True,
                'order': order,
                'trade_record': trade_record
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to place order: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_open_positions(self) -> List[Dict]:
        """Get all open positions"""
        try:
            positions = self.connector.exchange.fetch_positions()
            
            open_positions = []
            for position in positions:
                if float(position.get('contracts', 0)) > 0:
                    open_positions.append({
                        'symbol': position['symbol'],
                        'side': position['side'],
                        'size': position['contracts'],
                        'entry_price': position['entryPrice'],
                        'mark_price': position['markPrice'],
                        'unrealized_pnl': position['unrealizedPnl'],
                        'percentage': position['percentage']
                    })
            
            return open_positions
            
        except Exception as e:
            self.logger.error(f"Failed to get open positions: {e}")
            return []
    
    def close_position(self, symbol: str, quantity: float = None) -> Dict:
        """Close an open position"""
        try:
            # Get current position
            positions = self.get_open_positions()
            position = next((p for p in positions if p['symbol'] == symbol), None)
            
            if not position:
                return {'success': False, 'error': 'No open position found'}
            
            # Determine close side and quantity
            close_side = 'sell' if position['side'] == 'long' else 'buy'
            close_quantity = quantity or abs(float(position['size']))
            
            # Place closing order
            result = self.place_market_order(symbol, close_side, close_quantity)
            
            if result['success']:
                self.logger.info(f"✅ Position closed: {symbol}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to close position: {e}")
            return {'success': False, 'error': str(e)}

class LiveTradingSystem:
    """Complete live trading system integration"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('LiveTradingSystem')
        
        # Initialize components
        self.binance = BinanceConnector(config)
        self.market_data = None
        self.trade_executor = None
        
        self.is_initialized = False
        self.trading_active = False
        
        # Setup logging
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format=config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(os.path.join(config.LOGS_DIR, 'live_trading.log')),
                logging.StreamHandler()
            ]
        )
    
    async def initialize(self) -> bool:
        """Initialize live trading system"""
        try:
            self.logger.info("🚀 Initializing Live Trading System...")
            
            # Test Binance connection
            if not await self.binance.test_connection():
                return False
            
            # Initialize market data and trade executor
            self.market_data = LiveMarketData(self.binance)
            self.trade_executor = LiveTradeExecutor(self.binance, self.config)
            
            # Validate trading pairs
            valid_pairs = []
            for symbol in self.config.TRADING_PAIRS:
                if self.binance.validate_trading_pair(symbol):
                    valid_pairs.append(symbol)
                    self.logger.info(f"✅ {symbol} validated")
                else:
                    self.logger.warning(f"⚠️ {symbol} not available")
            
            if not valid_pairs:
                self.logger.error("❌ No valid trading pairs found")
                return False
            
            self.config.TRADING_PAIRS = valid_pairs
            
            # Get initial account balance
            balance = self.binance.get_account_balance()
            if balance:
                self.logger.info(f"💰 Account Balance: ${balance.get('total_value_usdt', 0):,.2f} USDT")
            
            self.is_initialized = True
            self.logger.info("🎉 Live Trading System initialized successfully!")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize live trading system: {e}")
            return False
    
    def start_trading(self) -> bool:
        """Start live trading"""
        if not self.is_initialized:
            self.logger.error("System not initialized")
            return False
        
        self.trading_active = True
        self.logger.info("🟢 Live trading STARTED")
        return True
    
    def stop_trading(self) -> bool:
        """Stop live trading"""
        self.trading_active = False
        self.logger.info("🔴 Live trading STOPPED")
        return True
    
    def execute_trade_signal(self, symbol: str, signal: str, confidence: float, 
                           risk_amount: float = None) -> Dict:
        """Execute a trade based on ML signal"""
        try:
            if not self.trading_active:
                return {'success': False, 'error': 'Trading not active'}
            
            if confidence < self.config.COMPOSITE_THRESHOLD:
                return {'success': False, 'error': f'Confidence {confidence:.3f} below threshold'}
            
            # Get current price
            current_price = self.market_data.get_current_price(symbol)
            if current_price <= 0:
                return {'success': False, 'error': 'Could not get current price'}
            
            # Calculate position size
            risk_amount = risk_amount or self.config.FIXED_RISK_AMOUNT
            quantity = self.trade_executor.calculate_position_size(symbol, risk_amount, current_price)
            
            if quantity <= 0:
                return {'success': False, 'error': 'Invalid position size'}
            
            # Calculate stop loss and take profit
            stop_loss_pct = self.config.STOP_LOSS_PERCENT
            reward_ratio = self.config.REWARD_RATIO
            
            if signal.upper() == 'BUY':
                stop_loss = current_price * (1 - stop_loss_pct)
                take_profit = current_price * (1 + stop_loss_pct * reward_ratio)
                side = 'buy'
            elif signal.upper() == 'SELL':
                stop_loss = current_price * (1 + stop_loss_pct)
                take_profit = current_price * (1 - stop_loss_pct * reward_ratio)
                side = 'sell'
            else:
                return {'success': False, 'error': 'Invalid signal'}
            
            # Execute trade
            result = self.trade_executor.place_market_order(
                symbol, side, quantity, stop_loss, take_profit
            )
            
            if result['success']:
                self.logger.info(f"🎯 Trade executed: {signal} {symbol} @ ${current_price:.2f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to execute trade signal: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_system_status(self) -> Dict:
        """Get current system status"""
        try:
            balance = self.binance.get_account_balance() if self.is_initialized else {}
            positions = self.trade_executor.get_open_positions() if self.is_initialized else []
            
            return {
                'initialized': self.is_initialized,
                'trading_active': self.trading_active,
                'connected': self.binance.is_connected,
                'account_balance': balance,
                'open_positions': positions,
                'valid_pairs': self.config.TRADING_PAIRS if self.is_initialized else [],
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get system status: {e}")
            return {'error': str(e)}

# Live Trading Engine and Test Mode
# This module manages live trading, test mode, and all risk controls
# Uses binance_data_collector, grid_trading_core, ml_training_system, and grid_composite_metrics

from binance_data_collector import BinanceDataCollector
from grid_trading_core import GridTrader
from ml_training_system import MLTrainingPipeline
from grid_composite_metrics import GridCompositeMetrics

class LiveTradingEngine:
    def __init__(self, config, test_mode=False):
        self.config = config
        self.test_mode = test_mode
        self.collector = BinanceDataCollector(config)
        self.trader = GridTrader(config)
        self.metrics = GridCompositeMetrics()
        self.status = 'STOPPED'
        # ...other state...

    def start(self):
        # Initialization, safety checks, grid setup, model loading
        self.status = 'RUNNING'
        # ...start trading loop (live or test mode)...
        pass

    def stop(self):
        # Immediate stop, close positions, save data, cleanup
        self.status = 'STOPPED'
        # ...cleanup logic...
        pass

    def emergency_stop(self):
        # Emergency halt, close all positions, preserve data
        self.status = 'STOPPED'
        # ...emergency logic...
        pass

    def monitor(self):
        # Real-time status, equity, drawdown, trade count, etc.
        # ...monitoring logic...
        pass

    def run(self):
        # Main loop for live or test mode
        if self.test_mode:
            # Simulated trades, all logic matches live
            pass
        else:
            # Real trades
            pass

async def main():
    """Main live trading system test"""
    config = TradingConfig()
    
    # Create directories
    os.makedirs(config.LOGS_DIR, exist_ok=True)
    
    # Initialize live trading system
    live_system = LiveTradingSystem(config)
    
    self.logger.info("🚀 Testing Live Binance Integration...")
    print("=" * 60)
    
    # Initialize system
    if await live_system.initialize():
        self.logger.info("\n✅ Live trading system ready!")
        
        # Get system status
        status = live_system.get_system_status()
        print(f"\n💰 Account Balance: ${status['account_balance'].get('total_value_usdt', 0):,.2f}")
        print(f"📊 Valid Trading Pairs: {len(status['valid_pairs'])}")
        print(f"🔗 Connection Status: {'Connected' if status['connected'] else 'Disconnected'}")
        
        return True
    else:
        self.logger.error("\n❌ Live trading system initialization failed!")
        return False

if __name__ == "__main__":
    asyncio.run(main())
