<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Freedom Dashboard - Account Information Integration</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
        }
        .header h1 {
            color: #00ff88;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .status-indicators {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }
        .status-live {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
            color: #ff6b6b;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .dashboard-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .dashboard-section h3 {
            color: #00d4aa;
            margin-bottom: 20px;
            font-size: 1.4em;
        }
        .account-type {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #00d4aa;
        }
        .account-label {
            font-weight: 600;
            color: #00d4aa;
        }
        .account-value {
            font-weight: 600;
            padding: 4px 12px;
            border-radius: 4px;
            background: rgba(0, 212, 170, 0.2);
            color: #00ff88;
        }
        .account-value.cross-margin {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .metric-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 8px;
        }
        .metric-value {
            font-size: 1.3em;
            font-weight: bold;
            color: #00ff88;
        }
        .account-warning {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
            color: #ff6b6b;
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
        }
        .demo-note {
            background: rgba(255, 165, 0, 0.2);
            border: 1px solid #ffa500;
            color: #ffa500;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            background: #ff6b6b;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
            transition: all 0.3s;
        }
        .btn:hover {
            background: #ff5252;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bitcoin"></i> Bitcoin Freedom Live Trading</h1>
            <p>Live Trading System</p>
            
            <div class="status-indicators">
                <div class="status-indicator" style="background: rgba(0, 255, 136, 0.2); border: 1px solid #00ff88; color: #00ff88;">
                    <i class="fas fa-circle"></i><span>Trading Active</span>
                </div>
                <div class="status-indicator status-live">
                    <i class="fas fa-exclamation-triangle"></i><span>LIVE CROSS MARGIN</span>
                </div>
                <div class="status-indicator" style="background: rgba(255, 215, 0, 0.2); border: 1px solid #ffd700; color: #ffd700;">
                    <i class="fas fa-lock"></i><span>Model Locked</span>
                </div>
            </div>
        </div>

        <div class="demo-note">
            <strong>📋 INTEGRATION DEMO:</strong> Account Information section now integrated into main Bitcoin Freedom dashboard for real-time monitoring alongside trades
        </div>

        <div class="dashboard-grid">
            <!-- Trading Performance Section -->
            <div class="dashboard-section">
                <h3><i class="fas fa-chart-line"></i> Trading Performance</h3>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">Equity</div>
                        <div class="metric-value">$1,876.23</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Total P&L</div>
                        <div class="metric-value">+$1,576.23</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Win Rate</div>
                        <div class="metric-value">93.2%</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Daily P&L</div>
                        <div class="metric-value">+$127.45</div>
                    </div>
                </div>
            </div>

            <!-- Account Information Section - INTEGRATED -->
            <div class="dashboard-section">
                <h3><i class="fas fa-university"></i> Account Information</h3>
                <div class="account-type">
                    <span class="account-label">Account Type:</span>
                    <span class="account-value cross-margin">Live Cross Margin</span>
                </div>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">USDT Balance</div>
                        <div class="metric-value">$1,247.83</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">BTC Balance</div>
                        <div class="metric-value">0.012456</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Margin Level</div>
                        <div class="metric-value">3.42%</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Network</div>
                        <div class="metric-value">Live Network</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Total Assets</div>
                        <div class="metric-value">$2,543.67</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Net Assets</div>
                        <div class="metric-value">$1,876.23</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Borrowed</div>
                        <div class="metric-value">$667.44</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Interest</div>
                        <div class="metric-value">$2.15</div>
                    </div>
                </div>
                <div class="account-warning">
                    <strong>⚠️ LIVE CROSS MARGIN TRADING ACTIVE</strong><br>
                    <span style="font-size: 0.9em;">Real money and leverage in use - Monitor positions carefully</span>
                </div>
            </div>

            <!-- System Status Section -->
            <div class="dashboard-section">
                <h3><i class="fas fa-cogs"></i> System Status</h3>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">Open Positions</div>
                        <div class="metric-value">1</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Daily Trades</div>
                        <div class="metric-value">3</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Total Trades</div>
                        <div class="metric-value">47</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Conservative Elite Score</div>
                        <div class="metric-value">79.1%</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-note">
            <strong>✅ COMPLETE INTEGRATION:</strong> Account information is now seamlessly integrated into the main Bitcoin Freedom dashboard, providing real-time Binance cross margin account details alongside trading performance and recent trades monitoring.
        </div>
    </div>
</body>
</html>
