#!/usr/bin/env python3
"""
Comprehensive Pre-Flight Check for 8-Hour Trading Test
This script performs all necessary checks to ensure the trading system is ready.
"""

import sys
import time
import json
import traceback
from datetime import datetime, timedelta
import requests

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def print_subsection(title):
    """Print a formatted subsection header."""
    print(f"\n{'-'*40}")
    print(f"📋 {title}")
    print('-'*40)

def check_imports():
    """Check all critical imports."""
    print_section("IMPORT VERIFICATION")
    
    imports = [
        ("Flask", "from flask import Flask"),
        ("CCXT", "import ccxt"),
        ("Pandas", "import pandas as pd"),
        ("NumPy", "import numpy as np"),
        ("Requests", "import requests"),
        ("Trading Engine", "from live_trading_web_app import LiveTradingEngine, BestCompositeModel"),
        ("Binance Connector", "from simple_binance_connector import create_simple_binance_connector"),
        ("Trade Logger", "from trade_csv_logger import TradeCSVLogger"),
        ("AI Monitor", "from ai_signal_monitor import AISignalMonitor"),
        ("Data Cache", "from data_cache_manager import DataCacheManager")
    ]
    
    failed_imports = []
    
    for name, import_statement in imports:
        try:
            exec(import_statement)
            print(f"✅ {name}: OK")
        except Exception as e:
            print(f"❌ {name}: FAILED - {e}")
            failed_imports.append(name)
    
    if failed_imports:
        print(f"\n❌ CRITICAL: {len(failed_imports)} imports failed!")
        return False
    else:
        print(f"\n✅ ALL IMPORTS SUCCESSFUL ({len(imports)} modules)")
        return True

def check_model_configuration():
    """Check Conservative Elite model configuration."""
    print_section("CONSERVATIVE ELITE MODEL CHECK")
    
    try:
        from live_trading_web_app import BestCompositeModel
        model = BestCompositeModel()
        
        checks = [
            ("Model ID", model.model_id, "tcn_cnn_ppo_conservative_v3_20250604_111817"),
            ("Model Type", model.model_type, "Conservative Elite"),
            ("Composite Score", f"{model.composite_score*100:.1f}%", "93.2%"),
            ("Win Rate", f"{model.win_rate*100:.1f}%", "93.2%"),
            ("Trades/Day", model.trades_per_day, 5.8),
            ("Risk per Trade", f"${model.risk_per_trade:.2f}", "$10.00"),
            ("Profit Target", f"${model.profit_target:.2f}", "$25.00"),
            ("Risk-Reward Ratio", f"1:{model.reward_ratio}", "1:2.5")
        ]
        
        all_good = True
        for name, actual, expected in checks:
            if str(expected) in str(actual):
                print(f"✅ {name}: {actual}")
            else:
                print(f"❌ {name}: {actual} (expected: {expected})")
                all_good = False
        
        # Check lock status
        if hasattr(model, 'model_locked') and model.model_locked:
            print(f"✅ Lock Status: LOCKED")
        else:
            print(f"❌ Lock Status: NOT LOCKED")
            all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Model check failed: {e}")
        traceback.print_exc()
        return False

def check_trading_engine():
    """Check trading engine initialization."""
    print_section("TRADING ENGINE CHECK")
    
    try:
        from live_trading_web_app import LiveTradingEngine, BestCompositeModel
        
        model = BestCompositeModel()
        engine = LiveTradingEngine(model)
        
        checks = [
            ("Engine Created", engine is not None),
            ("Model Loaded", engine.model is not None),
            ("Balance Set", engine.balance > 0),
            ("Risk Management", engine.model.risk_per_trade > 0),
            ("Price History", len(engine.price_history) >= 0),
            ("Trade Lists", hasattr(engine, 'open_trades') and hasattr(engine, 'closed_trades'))
        ]
        
        all_good = True
        for name, condition in checks:
            if condition:
                print(f"✅ {name}: OK")
            else:
                print(f"❌ {name}: FAILED")
                all_good = False
        
        # Test signal generation
        print_subsection("Signal Generation Test")
        try:
            # Get current BTC price
            import ccxt
            exchange = ccxt.binance()
            ticker = exchange.fetch_ticker('BTC/USDT')
            current_price = ticker['last']

            print(f"📊 Current BTC Price: ${current_price:,.2f}")

            # Test signal generation using the correct method
            signal = engine.generate_trade_signal()
            print(f"🎯 Signal Generated: {signal}")

            if signal in ['BUY', 'SELL', 'HOLD', None]:
                print(f"✅ Signal Generation: WORKING")
                if signal:
                    print(f"   Signal Type: {signal}")
                else:
                    print(f"   Signal Type: HOLD (no action)")
            else:
                print(f"❌ Signal Generation: INVALID SIGNAL")
                all_good = False
                
        except Exception as e:
            print(f"❌ Signal Generation Test: FAILED - {e}")
            all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Trading engine check failed: {e}")
        traceback.print_exc()
        return False

def check_binance_connectivity():
    """Check Binance API connectivity."""
    print_section("BINANCE CONNECTIVITY CHECK")
    
    try:
        # Test public API (no auth required)
        print_subsection("Public API Test")
        import ccxt
        exchange = ccxt.binance()
        
        # Test ticker fetch
        ticker = exchange.fetch_ticker('BTC/USDT')
        print(f"✅ BTC/USDT Ticker: ${ticker['last']:,.2f}")
        
        # Test orderbook
        orderbook = exchange.fetch_order_book('BTC/USDT', limit=5)
        print(f"✅ Order Book: {len(orderbook['bids'])} bids, {len(orderbook['asks'])} asks")
        
        # Test authenticated API
        print_subsection("Authenticated API Test")
        try:
            from simple_binance_connector import create_simple_binance_connector
            api_key_file = r"C:\Users\<USER>\Documents\Trading system\Trading project VPS 5\BinanceAPI_2.txt"
            
            connector = create_simple_binance_connector(api_key_file, testnet=True)
            if connector:
                print(f"✅ Simple Binance Connector: CONNECTED")
                
                # Test balance fetch
                try:
                    balance = connector.get_account_balance()
                    if balance:
                        print(f"✅ Account Balance: ACCESSIBLE")
                    else:
                        print(f"❌ Account Balance: NOT ACCESSIBLE")
                        return False
                except Exception as e:
                    print(f"❌ Balance Test: {e}")
                    return False
            else:
                print(f"❌ Simple Binance Connector: FAILED")
                return False
                
        except Exception as e:
            print(f"❌ Authenticated API: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Binance connectivity failed: {e}")
        traceback.print_exc()
        return False

def check_data_systems():
    """Check data logging and caching systems."""
    print_section("DATA SYSTEMS CHECK")
    
    try:
        # Check CSV Logger
        print_subsection("CSV Trade Logger")
        from trade_csv_logger import TradeCSVLogger
        csv_logger = TradeCSVLogger("test_trades.csv")
        print(f"✅ CSV Logger: INITIALIZED")
        
        # Check Database
        print_subsection("Trade Database")
        from trade_database import TradingDatabase
        db = TradingDatabase("test_trades.db")
        print(f"✅ Trade Database: INITIALIZED")
        
        # Check Data Cache
        print_subsection("Data Cache Manager")
        from data_cache_manager import DataCacheManager
        cache = DataCacheManager("test_cache")
        print(f"✅ Data Cache: INITIALIZED")
        
        # Test price caching
        try:
            latest_price = cache.get_latest_price('BTCUSDT')
            if latest_price:
                print(f"✅ Price Cache: ${latest_price:,.2f}")
            else:
                print(f"⚠️ Price Cache: EMPTY (will populate during trading)")
        except:
            print(f"⚠️ Price Cache: NOT AVAILABLE")
        
        return True
        
    except Exception as e:
        print(f"❌ Data systems check failed: {e}")
        traceback.print_exc()
        return False

def check_ai_monitoring():
    """Check AI signal monitoring system."""
    print_section("AI MONITORING CHECK")
    
    try:
        from ai_signal_monitor import AISignalMonitor
        
        monitor = AISignalMonitor()
        print(f"✅ AI Monitor: INITIALIZED")
        
        # Check confidence monitoring
        confidence = monitor.get_current_confidence()
        threshold = monitor.confidence_threshold
        
        print(f"📊 Current Confidence: {confidence*100:.1f}%")
        print(f"📊 Threshold: {threshold*100:.0f}%")
        
        if confidence >= threshold:
            print(f"✅ AI Confidence: ABOVE THRESHOLD (READY)")
        else:
            print(f"⚠️ AI Confidence: BELOW THRESHOLD (WAITING)")
        
        return True
        
    except Exception as e:
        print(f"❌ AI monitoring check failed: {e}")
        traceback.print_exc()
        return False

def test_trade_execution_flow():
    """Test the complete trade execution flow without real money."""
    print_section("TRADE EXECUTION FLOW TEST")
    
    try:
        from live_trading_web_app import LiveTradingEngine, BestCompositeModel
        
        # Create test engine
        model = BestCompositeModel()
        engine = LiveTradingEngine(model)
        
        print_subsection("Mock Trade Test")
        
        # Test trade creation
        test_price = 100000.0
        test_signal = "BUY"
        
        print(f"📊 Test Price: ${test_price:,.2f}")
        print(f"🎯 Test Signal: {test_signal}")
        
        # Test signal processing
        if hasattr(engine, 'process_signal'):
            result = engine.process_signal(test_signal, test_price)
            print(f"✅ Signal Processing: {result}")
        else:
            print(f"❌ Signal Processing: METHOD NOT FOUND")
            return False
        
        # Test trade management
        print_subsection("Trade Management Test")
        
        initial_trades = len(engine.open_trades)
        print(f"📊 Initial Open Trades: {initial_trades}")
        
        # Test position limits
        max_positions = getattr(engine.model, 'max_positions', 1)
        print(f"📊 Max Positions Allowed: {max_positions}")
        
        if initial_trades < max_positions:
            print(f"✅ Position Limit: OK (can open new trades)")
        else:
            print(f"⚠️ Position Limit: AT MAXIMUM")
        
        return True
        
    except Exception as e:
        print(f"❌ Trade execution test failed: {e}")
        traceback.print_exc()
        return False

def check_webapp_integration():
    """Check webapp integration and API endpoints."""
    print_section("WEBAPP INTEGRATION CHECK")
    
    try:
        # Import webapp components
        from live_trading_web_app import app
        print(f"✅ Flask App: IMPORTED")
        
        # Check critical routes exist
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(rule.rule)
        
        critical_routes = [
            '/',
            '/api/trading_status',
            '/api/start_trading',
            '/api/stop_trading',
            '/api/recent_trades',
            '/conservative_elite'
        ]
        
        missing_routes = []
        for route in critical_routes:
            if route in routes:
                print(f"✅ Route {route}: EXISTS")
            else:
                print(f"❌ Route {route}: MISSING")
                missing_routes.append(route)
        
        if missing_routes:
            print(f"❌ Missing {len(missing_routes)} critical routes")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Webapp integration check failed: {e}")
        traceback.print_exc()
        return False

def run_quick_signal_test():
    """Run a quick signal generation test with real market data."""
    print_section("REAL-TIME SIGNAL TEST")
    
    try:
        from live_trading_web_app import LiveTradingEngine, BestCompositeModel
        import ccxt
        
        # Get real market data
        exchange = ccxt.binance()
        ticker = exchange.fetch_ticker('BTC/USDT')
        current_price = ticker['last']
        
        print(f"📊 Real BTC Price: ${current_price:,.2f}")
        
        # Create engine
        model = BestCompositeModel()
        engine = LiveTradingEngine(model)
        
        # Generate multiple signals to test consistency
        signals = []
        for i in range(3):
            signal = engine.generate_trade_signal()
            signals.append(signal)
            print(f"🎯 Signal {i+1}: {signal if signal else 'HOLD'}")
            time.sleep(1)  # Small delay between signals
        
        # Check signal consistency
        unique_signals = set(signals)
        if len(unique_signals) == 1:
            print(f"✅ Signal Consistency: STABLE ({signals[0]})")
        else:
            print(f"⚠️ Signal Consistency: VARIABLE ({unique_signals})")
        
        # Check if signals are valid (None is treated as HOLD)
        valid_signals = all(s in ['BUY', 'SELL', 'HOLD', None] for s in signals)
        if valid_signals:
            print(f"✅ Signal Validity: ALL VALID")
        else:
            print(f"❌ Signal Validity: INVALID SIGNALS DETECTED")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Real-time signal test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main pre-flight check function."""
    print("🚀 CONSERVATIVE ELITE 8-HOUR TEST PRE-FLIGHT CHECK")
    print("=" * 80)
    print(f"⏰ Check Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all checks
    checks = [
        ("Import Verification", check_imports),
        ("Model Configuration", check_model_configuration),
        ("Trading Engine", check_trading_engine),
        ("Binance Connectivity", check_binance_connectivity),
        ("Data Systems", check_data_systems),
        ("AI Monitoring", check_ai_monitoring),
        ("Trade Execution Flow", test_trade_execution_flow),
        ("Webapp Integration", check_webapp_integration),
        ("Real-Time Signals", run_quick_signal_test)
    ]
    
    results = {}
    passed = 0
    total = len(checks)
    
    for name, check_func in checks:
        try:
            result = check_func()
            results[name] = result
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {name}: EXCEPTION - {e}")
            results[name] = False
    
    # Final summary
    print_section("PRE-FLIGHT CHECK SUMMARY")
    
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\n📊 OVERALL RESULT: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 ALL CHECKS PASSED!")
        print("✅ System is ready for 8-hour trading test")
        print("\n📋 RECOMMENDED NEXT STEPS:")
        print("1. Start the webapp: python live_trading_web_app.py")
        print("2. Open browser: http://localhost:5000/conservative_elite")
        print("3. Click 'Start Trading' button")
        print("4. Monitor for first trade within 30 minutes")
        print("5. Check status every hour during 8-hour test")
        return True
    else:
        print(f"\n❌ {total - passed} CHECKS FAILED!")
        print("❌ System is NOT ready for trading test")
        print("\n🔧 REQUIRED ACTIONS:")
        for name, result in results.items():
            if not result:
                print(f"   - Fix: {name}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
