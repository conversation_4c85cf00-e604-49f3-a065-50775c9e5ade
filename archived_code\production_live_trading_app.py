#!/usr/bin/env python3
"""
BITCOIN FREEDOM PRODUCTION LIVE TRADING WEB APPLICATION
======================================================
Production-ready version with proper threading support for auto trading.
Uses Waitress WSGI server instead of Flask development server.
"""

import os
import sys
import time
import threading
import signal
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the main trading app
from live_trading_web_app import app, trading_engine, live_trading_loop

class ProductionTradingServer:
    """Production-ready trading server with proper threading."""
    
    def __init__(self):
        self.server = None
        self.trading_thread = None
        self.is_running = False
        
    def start_auto_trading(self):
        """Start auto trading in a separate thread."""
        if not trading_engine.is_running:
            print("🚀 STARTING AUTO TRADING IN PRODUCTION MODE...")
            
            # Set engine to running
            trading_engine.is_running = True
            
            # Create and start trading thread
            self.trading_thread = threading.Thread(
                target=live_trading_loop, 
                daemon=False,  # Not daemon so it doesn't get killed
                name="ProductionTradingLoop"
            )
            
            self.trading_thread.start()
            
            # Verify thread started
            time.sleep(1)
            if self.trading_thread.is_alive():
                print("✅ Auto trading thread started successfully!")
                return True
            else:
                print("❌ Failed to start auto trading thread")
                trading_engine.is_running = False
                return False
        else:
            print("⚠️ Auto trading already running")
            return True
    
    def stop_auto_trading(self):
        """Stop auto trading."""
        if trading_engine.is_running:
            print("🛑 Stopping auto trading...")
            trading_engine.is_running = False
            
            if self.trading_thread and self.trading_thread.is_alive():
                self.trading_thread.join(timeout=5)
                
            print("✅ Auto trading stopped")
    
    def start_server(self, host='0.0.0.0', port=5000):
        """Start the production WSGI server."""
        try:
            # Try to import waitress
            from waitress import serve
            
            print("🌐 STARTING PRODUCTION TRADING SERVER")
            print("=" * 50)
            print(f"🏠 Host: {host}")
            print(f"🔌 Port: {port}")
            print(f"🌍 URL: http://localhost:{port}")
            print("=" * 50)
            
            # Start auto trading
            self.start_auto_trading()
            
            # Setup signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            self.is_running = True
            
            print("🚀 Server starting... Press Ctrl+C to stop")
            
            # Start the WSGI server
            serve(app, host=host, port=port, threads=6)
            
        except ImportError:
            print("❌ Waitress not available, falling back to Flask dev server")
            print("⚠️ WARNING: Threading may not work properly")
            
            # Start auto trading anyway
            self.start_auto_trading()
            
            # Use Flask dev server as fallback
            app.run(host=host, port=port, debug=False, threaded=True)
            
        except Exception as e:
            print(f"❌ Error starting server: {e}")
            self.stop_auto_trading()
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        self.stop_auto_trading()
        self.is_running = False
        sys.exit(0)

def install_waitress():
    """Install waitress if not available."""
    try:
        import waitress
        return True
    except ImportError:
        print("📦 Installing Waitress WSGI server...")
        try:
            import subprocess
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'waitress'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Waitress installed successfully")
                return True
            else:
                print(f"❌ Failed to install Waitress: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error installing Waitress: {e}")
            return False

def main():
    """Main entry point."""
    print("🚀 BITCOIN FREEDOM PRODUCTION LIVE TRADING APPLICATION")
    print("=" * 60)
    print("💰 Real-time BTC trading with auto signal generation")
    print("🤖 Bitcoin Freedom ensemble model")
    print("📊 91.4% composite score")
    print("⚡ Production-ready threading")
    print("=" * 60)
    
    # Install waitress if needed
    install_waitress()
    
    # Create and start server
    server = ProductionTradingServer()
    
    try:
        server.start_server()
    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested by user")
        server.stop_auto_trading()
    except Exception as e:
        print(f"❌ Server error: {e}")
        server.stop_auto_trading()

if __name__ == '__main__':
    main()
