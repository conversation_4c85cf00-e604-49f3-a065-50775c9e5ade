#!/usr/bin/env python3
"""
🚀 INTEGRATED TRADING MODEL WITH BUILT-IN BACKTESTING & RL
=========================================================
Example implementation showing how to integrate the Universal Backtester
directly into any trading model for continuous validation and improvement
"""

import sys
import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

# Import the integrated backtester
from integrated_universal_backtester import (
    IntegratedBacktestEngine, 
    ReinforcementLearningIntegration,
    create_integrated_backtester,
    PerformanceMetrics
)

class IntegratedTradingModel:
    """Example trading model with built-in backtesting and RL"""
    
    def __init__(self, model_config: Dict):
        self.model_config = model_config
        self.model_name = model_config.get('name', 'Integrated Model')
        
        # Initialize integrated backtester
        self.backtester = create_integrated_backtester(model_config)
        
        # Initialize reinforcement learning
        self.rl_system = ReinforcementLearningIntegration(self.backtester)
        
        # Model-specific parameters
        self.signal_threshold = model_config.get('signal_threshold', 0.7)
        self.risk_per_trade = model_config.get('risk_per_trade', 20.0)
        self.max_trades_per_day = model_config.get('max_trades_per_day', 8)
        
        # Trading state
        self.daily_trade_count = 0
        self.last_trade_date = None
        self.trade_counter = 0
        
        print(f"🚀 Initialized {self.model_name} with integrated backtesting & RL")
    
    def generate_trading_signal(self, market_data: Dict) -> Tuple[Optional[str], float, Dict]:
        """Generate trading signal with integrated validation"""
        
        current_price = market_data.get('price', 100000.0)
        timestamp = datetime.now()
        
        # Check daily trade limits
        if not self._check_trade_limits(timestamp):
            return None, 0.0, {'reason': 'Daily trade limit reached'}
        
        # Generate base signal using model logic
        base_signal = self._generate_base_signal(market_data)
        
        if not base_signal['direction']:
            return None, 0.0, {'reason': 'No signal generated'}
        
        # Create signal dictionary for validation
        signal_dict = {
            'direction': base_signal['direction'],
            'confidence': base_signal['confidence'],
            'position_size': self._calculate_position_size(current_price),
            'entry_price': current_price,
            'predicted_outcome': base_signal.get('predicted_outcome', {})
        }
        
        # INTEGRATED BACKTESTING VALIDATION
        should_execute, final_confidence, reason = self.backtester.validate_trade_signal(
            signal_dict, current_price, market_data
        )
        
        # REINFORCEMENT LEARNING ADJUSTMENT
        if self.rl_system.should_explore():
            # Exploration: try different confidence levels
            final_confidence = random.uniform(0.5, 0.9)
            reason += " [EXPLORING]"
        else:
            # Exploitation: use RL-adjusted confidence
            rl_features = {
                'market_volatility': market_data.get('volatility', 0.02),
                'time_of_day': timestamp.hour / 24.0,
                'recent_performance': self.backtester.get_current_performance().confidence_score
            }
            final_confidence = self.rl_system.get_adjusted_confidence(final_confidence, rl_features)
        
        # Final decision
        if should_execute and final_confidence > self.signal_threshold:
            signal_dict['final_confidence'] = final_confidence
            signal_dict['validation_reason'] = reason
            return signal_dict['direction'], final_confidence, signal_dict
        else:
            return None, final_confidence, {'reason': f'Rejected: {reason}'}
    
    def execute_trade(self, signal: Dict, market_data: Dict) -> str:
        """Execute trade and record for backtesting feedback"""
        
        self.trade_counter += 1
        trade_id = f"{self.model_name}_{self.trade_counter:04d}"
        
        # Create trade record
        trade_record = {
            'id': trade_id,
            'direction': signal['direction'],
            'entry_price': signal['entry_price'],
            'position_size': signal['position_size'],
            'confidence': signal['final_confidence'],
            'predicted_outcome': signal.get('predicted_outcome', {}),
            'market_conditions': market_data.copy()
        }
        
        # Record with integrated backtester
        self.backtester.record_trade_execution(trade_record)
        
        # Update daily trade count
        current_date = datetime.now().date()
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        self.daily_trade_count += 1
        
        print(f"📈 Trade Executed: {trade_id} | {signal['direction']} @ ${signal['entry_price']:.2f}")
        print(f"   Confidence: {signal['final_confidence']:.1%} | Reason: {signal.get('validation_reason', 'N/A')}")
        
        return trade_id
    
    def close_trade(self, trade_id: str, exit_price: float, exit_reason: str):
        """Close trade and provide feedback to RL system"""
        
        # Record trade closure
        self.backtester.record_trade_close(trade_id, exit_price, exit_reason)
        
        # Find the closed trade for RL feedback
        closed_trade = None
        for trade in self.backtester.closed_trades:
            if trade.get('id') == trade_id:
                closed_trade = trade
                break
        
        if closed_trade:
            # Provide feedback to RL system
            self.rl_system.update_model_from_feedback(closed_trade)
            
            profit = closed_trade.get('actual_profit', 0)
            print(f"💰 Trade Closed: {trade_id} | P&L: ${profit:+.2f} | Reason: {exit_reason}")
            
            # Check if this triggers model validation
            current_perf = self.backtester.get_current_performance()
            print(f"📊 Current Performance: {current_perf.win_rate:.1%} win rate, {current_perf.confidence_score:.1%} confidence")
    
    def get_model_status(self) -> Dict:
        """Get comprehensive model status including backtesting and RL"""
        
        backtester_status = self.backtester.get_integration_status()
        rl_status = self.rl_system.get_learning_status()
        current_performance = self.backtester.get_current_performance()
        
        return {
            'model_name': self.model_name,
            'model_config': self.model_config,
            'current_performance': current_performance.to_dict(),
            'backtester_status': backtester_status,
            'rl_status': rl_status,
            'daily_trades': self.daily_trade_count,
            'total_trades': len(self.backtester.closed_trades),
            'integration_health': backtester_status['integration_health'],
            'learning_trend': rl_status['learning_trend']
        }
    
    def _generate_base_signal(self, market_data: Dict) -> Dict:
        """Generate base trading signal (model-specific logic)"""
        
        price = market_data.get('price', 100000.0)
        volatility = market_data.get('volatility', 0.02)
        volume = market_data.get('volume', 1000.0)
        
        # Example signal logic (replace with actual model)
        signal_strength = random.uniform(0.3, 0.9)
        
        # Bias based on market conditions
        if volatility < 0.015:  # Low volatility
            signal_strength *= 1.2
        elif volatility > 0.03:  # High volatility
            signal_strength *= 0.8
        
        # Volume confirmation
        if volume > 1500:
            signal_strength *= 1.1
        
        # Generate direction
        direction = None
        if signal_strength > 0.6:
            direction = "BUY" if random.random() > 0.4 else "SELL"
        
        return {
            'direction': direction,
            'confidence': min(signal_strength, 0.95),
            'predicted_outcome': {
                'expected_profit': random.uniform(10, 50) if direction else 0,
                'risk_score': volatility
            }
        }
    
    def _calculate_position_size(self, price: float) -> float:
        """Calculate position size based on risk management"""
        return self.risk_per_trade / (price * 0.001)  # 0.1% stop loss
    
    def _check_trade_limits(self, timestamp: datetime) -> bool:
        """Check if trading limits allow new trade"""
        
        current_date = timestamp.date()
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_trades_per_day

def simulate_integrated_trading_session():
    """Simulate a trading session with integrated backtesting and RL"""
    
    print("🚀 INTEGRATED TRADING MODEL SIMULATION")
    print("=" * 60)
    
    # Create integrated trading model
    model_config = {
        'name': 'Enhanced Conservative Elite',
        'starting_balance': 1000.0,
        'signal_threshold': 0.65,
        'risk_per_trade': 25.0,
        'max_trades_per_day': 6,
        'min_confidence': 0.6,
        'validation_frequency': 12  # Every 12 hours
    }
    
    model = IntegratedTradingModel(model_config)
    
    # Simulate market data and trading
    open_trades = {}
    
    for hour in range(72):  # 3 days of trading
        # Generate market data
        base_price = 100000 + random.uniform(-5000, 5000)
        market_data = {
            'price': base_price,
            'volatility': random.uniform(0.01, 0.04),
            'volume': random.uniform(800, 2000),
            'timestamp': datetime.now() + timedelta(hours=hour)
        }
        
        # Generate trading signal
        direction, confidence, signal_info = model.generate_trading_signal(market_data)
        
        if direction:
            # Execute trade
            trade_id = model.execute_trade(signal_info, market_data)
            open_trades[trade_id] = {
                'entry_price': signal_info['entry_price'],
                'direction': direction,
                'entry_hour': hour
            }
        
        # Close some trades (simulate exits)
        trades_to_close = []
        for trade_id, trade_info in open_trades.items():
            # Random exit after 2-8 hours
            if hour - trade_info['entry_hour'] >= random.randint(2, 8):
                trades_to_close.append(trade_id)
        
        for trade_id in trades_to_close:
            trade_info = open_trades[trade_id]
            # Simulate exit price
            price_change = random.uniform(-0.005, 0.01)  # -0.5% to +1%
            exit_price = trade_info['entry_price'] * (1 + price_change)
            
            model.close_trade(trade_id, exit_price, "TIME_EXIT")
            del open_trades[trade_id]
        
        # Print status every 24 hours
        if hour % 24 == 0 and hour > 0:
            status = model.get_model_status()
            print(f"\n📊 Day {hour//24} Status:")
            print(f"   Performance: {status['current_performance']['win_rate']:.1%} win rate")
            print(f"   Total Trades: {status['total_trades']}")
            print(f"   Integration Health: {status['integration_health']}")
            print(f"   Learning Trend: {status['learning_trend']}")
    
    # Final status
    final_status = model.get_model_status()
    
    print(f"\n🎯 FINAL SIMULATION RESULTS:")
    print("=" * 40)
    print(f"Model: {final_status['model_name']}")
    print(f"Total Trades: {final_status['total_trades']}")
    print(f"Win Rate: {final_status['current_performance']['win_rate']:.1%}")
    print(f"Profit Factor: {final_status['current_performance']['profit_factor']:.2f}")
    print(f"Max Drawdown: {final_status['current_performance']['max_drawdown']:.1%}")
    print(f"Confidence Score: {final_status['current_performance']['confidence_score']:.1%}")
    print(f"Integration Health: {final_status['integration_health']}")
    print(f"RL Updates: {final_status['rl_status']['total_updates']}")
    print(f"Learning Trend: {final_status['learning_trend']}")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"integrated_model_simulation_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(final_status, f, indent=2, default=str)
    
    print(f"\n📄 Results saved to: {filename}")
    
    return model, final_status

def main():
    """Run integrated trading model example"""
    return simulate_integrated_trading_session()

if __name__ == "__main__":
    main()
