"""
Enhanced Trading Webapp with Test Mode
Comprehensive trading interface supporting both live and test trading
"""

import os
import sys
import logging
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import asyncio
import json
from typing import Dict, List, Optional
import time

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import trading components
from enhanced_test_trading_engine import TestTradingEngine, TradingMode
from test_mode_validator import TestModeValidator
from grid_composite_metrics import GridCompositeMetrics

# Import real money trading bot with better error handling
class RealMoneyTradingBot:
    """Fallback real money trading bot class"""
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger('RealMoneyTradingBot')

    def get_status(self):
        return {
            'current_equity': self.config.INITIAL_CAPITAL,
            'total_trades': 0,
            'model_loaded': False,
            'trader_connected': False
        }

    async def start_bot(self):
        self.logger.info("Live trading not available - using fallback mode")
        return False

    async def stop_bot(self):
        return True


class EnhancedTradingWebApp:
    """Enhanced trading webapp with test and live modes"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.logger = logging.getLogger('EnhancedTradingWebApp')
        
        # Initialize trading engines
        self.test_engine = TestTradingEngine(self.config)
        self.live_engine = RealMoneyTradingBot(self.config)
        self.validator = TestModeValidator(self.config)
        self.metrics_calculator = GridCompositeMetrics(self.config)
        
        # Session state initialization
        if 'trading_mode' not in st.session_state:
            st.session_state.trading_mode = 'TEST'
        if 'engine_running' not in st.session_state:
            st.session_state.engine_running = False
        if 'last_update' not in st.session_state:
            st.session_state.last_update = datetime.now()
    
    def run(self):
        """Main webapp interface"""
        st.set_page_config(
            page_title="Enhanced Grid Trading System",
            page_icon="🎯",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Header
        st.title("🎯 Enhanced Grid Trading System")
        st.markdown("**Complete Trading Solution with Test & Live Modes**")
        
        # Sidebar controls
        self._render_sidebar()
        
        # Main content based on selected mode
        if st.session_state.trading_mode == 'TEST':
            self._render_test_mode_interface()
        elif st.session_state.trading_mode == 'LIVE':
            self._render_live_mode_interface()
        elif st.session_state.trading_mode == 'VALIDATION':
            self._render_validation_interface()
        
        # Auto-refresh
        if st.session_state.engine_running:
            time.sleep(1)
            st.rerun()
    
    def _render_sidebar(self):
        """Render sidebar controls"""
        st.sidebar.header("🎮 Trading Controls")
        
        # Mode selection
        new_mode = st.sidebar.selectbox(
            "Trading Mode",
            ['TEST', 'LIVE', 'VALIDATION'],
            index=['TEST', 'LIVE', 'VALIDATION'].index(st.session_state.trading_mode),
            help="Select trading mode: TEST (simulated), LIVE (real money), VALIDATION (test verification)"
        )
        
        if new_mode != st.session_state.trading_mode:
            st.session_state.trading_mode = new_mode
            st.rerun()
        
        # Mode-specific controls
        if st.session_state.trading_mode == 'TEST':
            self._render_test_controls()
        elif st.session_state.trading_mode == 'LIVE':
            self._render_live_controls()
        elif st.session_state.trading_mode == 'VALIDATION':
            self._render_validation_controls()
        
        # System information
        st.sidebar.markdown("---")
        st.sidebar.subheader("📊 System Info")
        st.sidebar.info(f"**Mode:** {st.session_state.trading_mode}")
        st.sidebar.info(f"**Status:** {'Running' if st.session_state.engine_running else 'Stopped'}")
        st.sidebar.info(f"**Last Update:** {st.session_state.last_update.strftime('%H:%M:%S')}")
    
    def _render_test_controls(self):
        """Render test mode controls"""
        st.sidebar.subheader("🧪 Test Trading")
        
        # Start/Stop buttons
        col1, col2 = st.sidebar.columns(2)
        
        with col1:
            if st.button("▶️ Start Test", disabled=st.session_state.engine_running):
                asyncio.run(self._start_test_trading())
        
        with col2:
            if st.button("⏹️ Stop Test", disabled=not st.session_state.engine_running):
                asyncio.run(self._stop_test_trading())
        
        # Test configuration
        st.sidebar.markdown("**Test Configuration**")
        st.sidebar.metric("Initial Capital", f"${self.config.INITIAL_CAPITAL:,.2f}")
        st.sidebar.metric("Risk per Trade", f"${self.config.FIXED_RISK_AMOUNT:.2f}")
        st.sidebar.metric("Target Profit", f"${self.config.TARGET_PROFIT:.2f}")
        st.sidebar.metric("Grid Spacing", f"{self.config.GRID_SPACING:.2%}")
    
    def _render_live_controls(self):
        """Render live mode controls"""
        st.sidebar.subheader("💰 Live Trading")
        
        # API status check
        api_key = os.getenv('BINANCE_API_KEY', '')
        testnet = os.getenv('BINANCE_TESTNET', 'True').lower() == 'true'
        
        if api_key:
            mode_text = "TESTNET" if testnet else "LIVE MONEY"
            color = "normal" if testnet else "inverse"
            st.sidebar.metric("API Mode", mode_text, delta_color=color)
        else:
            st.sidebar.error("⚠️ No API keys configured")
        
        # Start/Stop buttons
        col1, col2 = st.sidebar.columns(2)
        
        with col1:
            if st.button("▶️ Start Live", disabled=st.session_state.engine_running or not api_key):
                asyncio.run(self._start_live_trading())
        
        with col2:
            if st.button("⏹️ Stop Live", disabled=not st.session_state.engine_running):
                asyncio.run(self._stop_live_trading())
        
        # Emergency stop
        if st.sidebar.button("🚨 EMERGENCY STOP", type="primary"):
            asyncio.run(self._emergency_stop())
    
    def _render_validation_controls(self):
        """Render validation mode controls"""
        st.sidebar.subheader("✅ Validation Tools")
        
        if st.sidebar.button("🔍 Run Validation"):
            self._run_comprehensive_validation()
        
        if st.sidebar.button("📊 Compare Results"):
            self._compare_test_vs_live()
        
        if st.sidebar.button("📁 Export Report"):
            self._export_validation_report()
    
    def _render_test_mode_interface(self):
        """Render test mode interface"""
        st.header("🧪 Test Trading Mode")
        st.info("**Test Mode:** All trading logic matches live trading exactly, but trades are simulated")
        
        # Get current status
        status = self.test_engine.get_test_status()
        
        # Key metrics row
        col1, col2, col3, col4, col5, col6 = st.columns(6)
        
        with col1:
            st.metric("Current Balance", f"${status['current_balance']:,.2f}")
        
        with col2:
            pnl = status['total_pnl']
            st.metric("Total P&L", f"${pnl:,.2f}", delta=f"{pnl:+.2f}")
        
        with col3:
            st.metric("Open Trades", status['open_trades'])
        
        with col4:
            st.metric("Closed Trades", status['closed_trades'])
        
        with col5:
            st.metric("Grid Levels", status['grid_levels'])
        
        with col6:
            if status['is_running']:
                uptime = datetime.now() - datetime.fromisoformat(status['start_time'])
                st.metric("Uptime", f"{uptime.seconds // 60}m")
            else:
                st.metric("Status", "Stopped")
        
        # Charts section
        if status['session_data']['equity_history']:
            self._render_test_charts(status['session_data'])
        
        # Recent trades
        self._render_test_trades_table()
    
    def _render_live_mode_interface(self):
        """Render live mode interface"""
        st.header("💰 Live Trading Mode")
        st.warning("**Live Mode:** Real money trading - all trades will be executed on Binance")
        
        # Get current status
        status = self.live_engine.get_status()
        
        # Key metrics row
        col1, col2, col3, col4, col5, col6 = st.columns(6)
        
        with col1:
            current_equity = status.get('current_equity', self.config.INITIAL_CAPITAL)
            st.metric("Current Equity", f"${current_equity:,.2f}")
        
        with col2:
            pnl = current_equity - self.config.INITIAL_CAPITAL
            st.metric("Total P&L", f"${pnl:,.2f}", delta=f"{pnl:+.2f}")
        
        with col3:
            st.metric("Total Trades", status.get('total_trades', 0))
        
        with col4:
            api_key = os.getenv('BINANCE_API_KEY', '')
            testnet = os.getenv('BINANCE_TESTNET', 'True').lower() == 'true'
            mode = "TESTNET" if testnet else "LIVE 💰"
            st.metric("Mode", mode)
        
        with col5:
            st.metric("Model Loaded", "✅" if status.get('model_loaded', False) else "❌")
        
        with col6:
            st.metric("API Connected", "✅" if status.get('trader_connected', False) else "❌")
        
        # Live trading charts and data would go here
        st.info("Live trading interface - charts and real-time data display")
    
    def _render_validation_interface(self):
        """Render validation interface"""
        st.header("✅ Validation & Testing")
        st.info("**Validation Mode:** Verify test trading matches live trading logic exactly")
        
        # Validation results display
        if 'validation_results' in st.session_state:
            self._display_validation_results(st.session_state.validation_results)
        else:
            st.info("Run validation to see results")
        
        # Validation options
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🔍 Quick Validation")
            if st.button("Validate Configuration"):
                results = self.validator._validate_configuration(self.test_engine)
                st.json(results)
        
        with col2:
            st.subheader("📊 Comprehensive Test")
            if st.button("Full System Validation"):
                self._run_comprehensive_validation()
    
    def _render_test_charts(self, session_data: Dict):
        """Render test trading charts"""
        st.subheader("📈 Test Trading Performance")
        
        # Equity curve
        if len(session_data['equity_history']) > 1:
            fig = go.Figure()
            
            timestamps = session_data.get('timestamps', [])
            equity_values = session_data['equity_history']
            
            fig.add_trace(go.Scatter(
                x=timestamps,
                y=equity_values,
                mode='lines',
                name='Equity Curve',
                line=dict(color='green', width=2)
            ))
            
            fig.update_layout(
                title="Test Trading Equity Curve",
                xaxis_title="Time",
                yaxis_title="Account Balance ($)",
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
        
        # Drawdown chart
        if len(session_data['drawdown_history']) > 1:
            fig_dd = go.Figure()
            
            fig_dd.add_trace(go.Scatter(
                x=timestamps,
                y=session_data['drawdown_history'],
                mode='lines',
                name='Drawdown',
                fill='tonexty',
                line=dict(color='red', width=1)
            ))
            
            fig_dd.update_layout(
                title="Test Trading Drawdown",
                xaxis_title="Time",
                yaxis_title="Drawdown (%)",
                height=300
            )
            
            st.plotly_chart(fig_dd, use_container_width=True)
    
    def _render_test_trades_table(self):
        """Render test trades table"""
        st.subheader("📋 Recent Test Trades")
        
        if self.test_engine.closed_trades:
            trades_data = []
            for trade in self.test_engine.closed_trades[-10:]:  # Last 10 trades
                trades_data.append({
                    'Trade ID': trade.trade_id,
                    'Side': trade.side,
                    'Entry Price': f"${trade.entry_price:.2f}",
                    'Exit Price': f"${trade.exit_price:.2f}",
                    'P&L': f"${trade.profit_loss:.2f}",
                    'Status': trade.status,
                    'Exit Reason': trade.exit_reason
                })
            
            df = pd.DataFrame(trades_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("No test trades executed yet")
    
    async def _start_test_trading(self):
        """Start test trading"""
        success = await self.test_engine.start_test_trading()
        if success:
            st.session_state.engine_running = True
            st.success("🧪 Test trading started successfully!")
        else:
            st.error("❌ Failed to start test trading")
    
    async def _stop_test_trading(self):
        """Stop test trading"""
        summary = await self.test_engine.stop_test_trading()
        if summary['success']:
            st.session_state.engine_running = False
            st.success(f"🧪 Test trading stopped. Final balance: ${summary['final_balance']:.2f}")
        else:
            st.error("❌ Failed to stop test trading")
    
    async def _start_live_trading(self):
        """Start live trading"""
        success = await self.live_engine.start_bot()
        if success:
            st.session_state.engine_running = True
            st.success("💰 Live trading started successfully!")
        else:
            st.error("❌ Failed to start live trading")
    
    async def _stop_live_trading(self):
        """Stop live trading"""
        success = await self.live_engine.stop_bot()
        if success:
            st.session_state.engine_running = False
            st.success("💰 Live trading stopped successfully!")
        else:
            st.error("❌ Failed to stop live trading")
    
    async def _emergency_stop(self):
        """Emergency stop all trading"""
        await self.live_engine.stop_bot()
        await self.test_engine.stop_test_trading()
        st.session_state.engine_running = False
        st.error("🚨 EMERGENCY STOP ACTIVATED - All trading halted")
    
    def _run_comprehensive_validation(self):
        """Run comprehensive validation"""
        with st.spinner("Running comprehensive validation..."):
            validation_results = self.validator.run_comprehensive_validation(self.test_engine)
            st.session_state.validation_results = validation_results
            
            if validation_results['overall_passed']:
                st.success("✅ All validations passed!")
            else:
                st.error("❌ Some validations failed")
            
            self._display_validation_results(validation_results)
    
    def _display_validation_results(self, results: Dict):
        """Display validation results"""
        st.subheader("🔍 Validation Results")
        
        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Overall Status", "✅ PASSED" if results['overall_passed'] else "❌ FAILED")
        
        with col2:
            st.metric("Total Tests", results['summary']['total_validations'])
        
        with col3:
            st.metric("Passed", results['summary']['passed_validations'])
        
        with col4:
            st.metric("Failed", results['summary']['failed_validations'])
        
        # Detailed results
        st.json(results)
    
    def _compare_test_vs_live(self):
        """Compare test vs live results"""
        st.info("Test vs Live comparison feature - comparing trading logic consistency")
    
    def _export_validation_report(self):
        """Export validation report"""
        if 'validation_results' in st.session_state:
            filepath = self.validator.save_validation_report(st.session_state.validation_results)
            if filepath:
                st.success(f"📁 Validation report exported: {filepath}")
            else:
                st.error("❌ Failed to export validation report")
        else:
            st.warning("⚠️ No validation results to export")


def main():
    """Main application entry point"""
    app = EnhancedTradingWebApp()
    app.run()


if __name__ == "__main__":
    main()
