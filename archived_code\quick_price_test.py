"""
Quick Bitcoin Price Test
Tests if we can get real Bitcoin prices from Binance
"""

def test_price_methods():
    """Test different methods to get Bitcoin price"""
    print("🔍 TESTING BITCOIN PRICE METHODS")
    print("=" * 40)
    
    # Method 1: ccxt
    print("\n📦 Method 1: ccxt library")
    try:
        import ccxt
        exchange = ccxt.binance()
        ticker = exchange.fetch_ticker('BTC/USDT')
        price1 = ticker['last']
        print(f"   ✅ ccxt: ${price1:,.2f}")
        method1_ok = True
    except Exception as e:
        print(f"   ❌ ccxt failed: {e}")
        method1_ok = False
    
    # Method 2: requests
    print("\n🌐 Method 2: Direct REST API")
    try:
        import requests
        response = requests.get('https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT', timeout=5)
        data = response.json()
        price2 = float(data['lastPrice'])
        change = float(data['priceChangePercent'])
        print(f"   ✅ REST API: ${price2:,.2f} ({change:+.2f}%)")
        method2_ok = True
    except Exception as e:
        print(f"   ❌ REST API failed: {e}")
        method2_ok = False
    
    # Method 3: Webapp function
    print("\n🌐 Method 3: Webapp function")
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        
        # Import the function
        exec("""
@st.cache_data(ttl=10)
def get_live_bitcoin_price():
    from datetime import datetime
    try:
        import ccxt
        exchange = ccxt.binance({'enableRateLimit': True, 'options': {'defaultType': 'spot'}})
        ticker = exchange.fetch_ticker('BTC/USDT')
        price = ticker['last']
        change_24h = ticker['percentage'] if ticker['percentage'] is not None else 0.0
        return {
            'price': price,
            'change_24h': change_24h,
            'timestamp': datetime.now(),
            'source': 'Binance Live API'
        }
    except:
        try:
            import requests
            response = requests.get('https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT', timeout=5)
            data = response.json()
            return {
                'price': float(data['lastPrice']),
                'change_24h': float(data['priceChangePercent']),
                'timestamp': datetime.now(),
                'source': 'Binance REST API'
            }
        except:
            return {
                'price': 50000.0,
                'change_24h': 0.0,
                'timestamp': datetime.now(),
                'source': 'Simulated Data'
            }

# Mock streamlit cache decorator
class MockST:
    @staticmethod
    def cache_data(ttl=10):
        def decorator(func):
            return func
        return decorator

st = MockST()
""")
        
        # Test the function
        btc_data = get_live_bitcoin_price()
        price3 = btc_data['price']
        source = btc_data['source']
        print(f"   ✅ Webapp: ${price3:,.2f} (Source: {source})")
        method3_ok = True
        
    except Exception as e:
        print(f"   ❌ Webapp function failed: {e}")
        method3_ok = False
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 SUMMARY")
    print("=" * 40)
    
    if method1_ok or method2_ok:
        print("✅ SUCCESS: Can get real Bitcoin prices!")
        if method1_ok and method2_ok:
            print("🎉 Both ccxt and REST API working")
        elif method1_ok:
            print("📦 ccxt library working")
        elif method2_ok:
            print("🌐 REST API working")
        
        print("\n🌐 Website Integration:")
        if method3_ok:
            print("   ✅ Webapp function working")
            print("   ✅ Will show real Binance data")
        else:
            print("   ⚠️ Webapp function needs debugging")
        
        return True
    else:
        print("❌ FAILED: Cannot get real Bitcoin prices")
        print("💡 Check internet connection")
        return False

if __name__ == "__main__":
    print("🚀 QUICK BITCOIN PRICE TEST")
    print("Testing if we can get real prices from Binance")
    print()
    
    success = test_price_methods()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS: Real Bitcoin prices available!")
        print("🌐 Your website will show live Binance data!")
    else:
        print("⚠️ WARNING: Using simulated data")
    print("=" * 50)
