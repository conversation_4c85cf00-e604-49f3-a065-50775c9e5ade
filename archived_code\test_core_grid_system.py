"""
Core Grid Trading System Test
Tests the core grid trading functionality without ML dependencies
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import core grid trading components (no ML dependencies)
from grid_trading_core import GridLevelCalculator, GridTradeManager, GridAction, GridLevel
from grid_feature_engineering import GridFeatureEngineering
from grid_composite_metrics import GridCompositeMetrics

class CoreGridTester:
    """Test core grid trading functionality"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('CoreGridTester')
        
        # Test results
        self.test_results = {}
        self.errors = []
        
    def run_core_tests(self) -> Dict[str, bool]:
        """Run core grid trading tests"""
        
        print("🧪 CORE GRID TRADING SYSTEM TESTS")
        print("=" * 50)
        
        tests = [
            ("Configuration Validation", self.test_configuration),
            ("Grid Level Calculator", self.test_grid_calculator),
            ("Feature Engineering", self.test_feature_engineering),
            ("Composite Metrics", self.test_composite_metrics),
            ("Trade Manager", self.test_trade_manager),
            ("End-to-End Simulation", self.test_end_to_end)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Testing: {test_name}")
            try:
                result = test_func()
                self.test_results[test_name] = result
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"   Result: {status}")
            except Exception as e:
                self.test_results[test_name] = False
                self.errors.append(f"{test_name}: {str(e)}")
                print(f"   Result: ❌ ERROR - {e}")
        
        # Summary
        self._print_test_summary()
        
        return self.test_results
    
    def test_configuration(self) -> bool:
        """Test configuration matches specification"""
        
        # Check critical values
        checks = [
            (self.config.INITIAL_CAPITAL == 300, "Starting capital should be $300"),
            (self.config.FIXED_RISK_AMOUNT == 10, "Risk per trade should be $10"),
            (self.config.TARGET_PROFIT == 20, "Profit target should be $20"),
            (self.config.GRID_SPACING == 0.0025, "Grid spacing should be 0.25%"),
            (self.config.TRADING_PAIRS == ['BTCUSDT'], "Should trade BTC only"),
            (self.config.TRAINING_DAYS == 60, "Training should be 60 days"),
            (self.config.TESTING_DAYS == 30, "Testing should be 30 days"),
            (self.config.COMPOSITE_SCORE_THRESHOLD == 0.85, "Threshold should be 85%"),
            (self.config.MAX_CONCURRENT_TRADES == 15, "Max trades should be 15")
        ]
        
        for check, message in checks:
            if not check:
                print(f"   ❌ {message}")
                return False
            else:
                print(f"   ✅ {message}")
        
        return True
    
    def test_grid_calculator(self) -> bool:
        """Test grid level calculation"""
        
        calculator = GridLevelCalculator(self.config)
        
        # Test grid level generation
        test_price = 50000.0
        grid_levels = calculator.calculate_grid_levels(test_price)
        
        if len(grid_levels) == 0:
            print("   ❌ No grid levels generated")
            return False
        
        # Check grid spacing
        level_prices = [level.price for level in grid_levels.values()]
        level_prices.sort()
        
        for i in range(1, len(level_prices)):
            spacing = (level_prices[i] - level_prices[i-1]) / level_prices[i-1]
            if abs(spacing - self.config.GRID_SPACING) > 0.0001:
                print(f"   ❌ Incorrect grid spacing: {spacing:.4f}")
                return False
        
        print(f"   ✅ Generated {len(grid_levels)} grid levels with correct spacing")
        
        # Test grid touch detection
        touched = calculator.check_grid_touch(50125.0, 50000.0, grid_levels)
        if len(touched) == 0:
            print("   ❌ Grid touch detection failed")
            return False
        
        print(f"   ✅ Grid touch detection working")
        
        return True
    
    def test_feature_engineering(self) -> bool:
        """Test feature engineering with 4 indicators"""
        
        feature_engineer = GridFeatureEngineering(self.config)
        
        # Create sample data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
        
        btc_data = pd.DataFrame({
            'open': np.random.uniform(49000, 51000, 100),
            'high': np.random.uniform(50000, 52000, 100),
            'low': np.random.uniform(48000, 50000, 100),
            'close': np.random.uniform(49500, 50500, 100),
            'volume': np.random.uniform(100, 1000, 100)
        }, index=dates)
        
        eth_data = pd.DataFrame({
            'open': np.random.uniform(3200, 3400, 100),
            'high': np.random.uniform(3300, 3500, 100),
            'low': np.random.uniform(3100, 3300, 100),
            'close': np.random.uniform(3200, 3400, 100),
            'volume': np.random.uniform(100, 1000, 100)
        }, index=dates)
        
        # Test feature creation
        features_df = feature_engineer.create_grid_features(btc_data, eth_data)
        
        # Validate features
        if not feature_engineer.validate_features(features_df):
            print("   ❌ Feature validation failed")
            return False
        
        # Check feature count
        feature_columns = [col for col in features_df.columns 
                          if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        if len(feature_columns) != 9:
            print(f"   ❌ Expected 9 features, got {len(feature_columns)}")
            return False
        
        print(f"   ✅ Created {len(feature_columns)} features from 4 indicators")
        print(f"   ✅ Features: {feature_columns}")
        
        # Test current features
        current_features = feature_engineer.get_current_features(btc_data, eth_data)
        if current_features.shape != (1, 9):
            print(f"   ❌ Current features wrong shape: {current_features.shape}")
            return False
        
        print(f"   ✅ Current features extraction working")
        
        return True
    
    def test_composite_metrics(self) -> bool:
        """Test composite metrics calculation"""
        
        metrics_calculator = GridCompositeMetrics(self.config)
        
        # Create sample trade history
        sample_trades = [
            {'profit_loss': 20, 'timestamp': '2024-01-01T10:00:00'},
            {'profit_loss': -10, 'timestamp': '2024-01-01T11:00:00'},
            {'profit_loss': 20, 'timestamp': '2024-01-01T12:00:00'},
            {'profit_loss': 20, 'timestamp': '2024-01-01T13:00:00'},
            {'profit_loss': -10, 'timestamp': '2024-01-01T14:00:00'}
        ]
        
        # Calculate composite score
        metrics = metrics_calculator.calculate_composite_score(sample_trades, 300)
        
        if 'composite_score' not in metrics:
            print("   ❌ Composite score not calculated")
            return False
        
        # Check all 8 metrics present
        required_metrics = [
            'win_rate', 'equity', 'sortino_ratio', 'calmar_ratio',
            'profit_factor', 'max_drawdown', 'risk_of_ruin', 'trade_frequency'
        ]
        
        for metric in required_metrics:
            if metric not in metrics:
                print(f"   ❌ Missing metric: {metric}")
                return False
        
        print(f"   ✅ Composite score: {metrics['composite_score']:.4f}")
        print(f"   ✅ All 8 metrics calculated")
        
        # Test threshold checking
        threshold_met = metrics_calculator.meets_threshold(metrics['composite_score'])
        print(f"   ✅ Threshold check: {threshold_met}")
        
        return True
    
    def test_trade_manager(self) -> bool:
        """Test trade management functionality"""
        
        trade_manager = GridTradeManager(self.config)
        
        # Test position sizing
        test_price = 50000.0
        position_size = trade_manager.calculate_position_size(test_price)
        
        if position_size <= 0:
            print("   ❌ Invalid position size")
            return False
        
        print(f"   ✅ Position size calculation: {position_size:.6f} BTC")
        
        # Test exit level calculation
        exit_price, stop_loss = trade_manager.calculate_exit_levels(test_price, GridAction.BUY)
        
        expected_exit = test_price * (1 + self.config.GRID_SPACING)
        if abs(exit_price - expected_exit) > 1.0:
            print(f"   ❌ Incorrect exit price: {exit_price} vs {expected_exit}")
            return False
        
        print(f"   ✅ Exit levels: Exit ${exit_price:.2f}, Stop ${stop_loss:.2f}")
        
        # Test trade record creation
        level = GridLevel(1, test_price, self.config.GRID_SPACING)
        trade_record = trade_manager.create_trade_record(level, GridAction.BUY, position_size, test_price)
        
        required_fields = ['trade_id', 'action', 'entry_price', 'exit_price', 'position_size']
        for field in required_fields:
            if field not in trade_record:
                print(f"   ❌ Missing trade field: {field}")
                return False
        
        print(f"   ✅ Trade record creation working")
        
        return True
    
    def test_end_to_end(self) -> bool:
        """Test complete end-to-end workflow simulation"""
        
        try:
            print("   🔄 Running end-to-end simulation...")
            
            # Initialize all components
            grid_calculator = GridLevelCalculator(self.config)
            trade_manager = GridTradeManager(self.config)
            feature_engineer = GridFeatureEngineering(self.config)
            metrics_calculator = GridCompositeMetrics(self.config)
            
            # Simulate price movement and grid trading
            current_price = 50000.0
            grid_levels = grid_calculator.calculate_grid_levels(current_price)
            
            # Simulate price touches and trades
            simulated_trades = []
            
            for i in range(5):
                # Simulate price movement
                price_change = np.random.uniform(-0.005, 0.005)  # ±0.5%
                new_price = current_price * (1 + price_change)
                
                # Check grid touches
                touched_levels = grid_calculator.check_grid_touch(new_price, current_price, grid_levels)
                
                for level in touched_levels:
                    # Simulate trade decision (random for test)
                    action = np.random.choice([GridAction.BUY, GridAction.SELL, GridAction.HOLD])
                    
                    if action != GridAction.HOLD and trade_manager.can_open_new_trade():
                        # Create trade
                        position_size = trade_manager.calculate_position_size(new_price)
                        trade_record = trade_manager.create_trade_record(level, action, position_size, new_price)
                        
                        # Simulate trade outcome
                        trade_record['profit_loss'] = np.random.choice([20, -10], p=[0.7, 0.3])
                        trade_record['status'] = 'CLOSED'
                        
                        simulated_trades.append(trade_record)
                
                current_price = new_price
            
            print(f"   ✅ Simulated {len(simulated_trades)} trades")
            
            # Calculate performance
            if simulated_trades:
                metrics = metrics_calculator.calculate_composite_score(simulated_trades, 300)
                print(f"   ✅ Composite score: {metrics.get('composite_score', 0):.4f}")
            
            print("   ✅ End-to-end simulation completed")
            
            return True
            
        except Exception as e:
            print(f"   ❌ End-to-end simulation failed: {e}")
            return False
    
    def _print_test_summary(self):
        """Print comprehensive test summary"""
        
        print("\n" + "=" * 60)
        print("🧪 CORE GRID SYSTEM TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests
        
        print(f"📊 RESULTS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests} ✅")
        print(f"   Failed: {failed_tests} ❌")
        print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for test_name, result in self.test_results.items():
                if not result:
                    print(f"   - {test_name}")
        
        if self.errors:
            print(f"\n🚨 ERRORS:")
            for error in self.errors:
                print(f"   - {error}")
        
        print(f"\n🎯 CORE SYSTEM STATUS:")
        if passed_tests == total_tests:
            print("   ✅ CORE SYSTEM READY")
            print("   All core grid trading tests passed!")
        elif passed_tests >= total_tests * 0.8:
            print("   ⚠️ MOSTLY READY - Minor issues detected")
            print("   Review failed tests before proceeding")
        else:
            print("   ❌ CORE SYSTEM NOT READY")
            print("   Critical issues must be resolved first")

def main():
    """Run core grid system tests"""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Initialize config
    config = TradingConfig()
    
    # Run tests
    tester = CoreGridTester(config)
    results = tester.run_core_tests()
    
    return results

if __name__ == "__main__":
    results = main()
    
    # Exit with appropriate code
    all_passed = all(results.values())
    exit(0 if all_passed else 1)
