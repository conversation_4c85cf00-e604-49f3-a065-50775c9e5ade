#!/usr/bin/env python3
"""
INTELLIGENT MARGIN MANAGEMENT SYSTEM
===================================
Automatically manages margin levels, rebalancing, and risk optimization
for Bitcoin Freedom cross margin trading.
"""

import time
import math
from datetime import datetime
from typing import Dict, Tuple, Optional

class IntelligentMarginManager:
    """Intelligent margin management with automatic rebalancing."""
    
    def __init__(self, binance_connector, initial_risk_per_trade=10, profit_target=25):
        self.binance_connector = binance_connector
        self.initial_risk_per_trade = initial_risk_per_trade
        self.profit_target = profit_target
        
        # Margin management settings (adjusted for real trading conditions)
        self.target_margin_level = 2.0  # Target margin level (was 3.5 - now realistic)
        self.critical_margin_level = 1.3  # Emergency level (was 1.8 - now more practical)
        self.safe_margin_level = 3.0  # Very safe level (was 5.0 - now achievable)
        self.minimum_margin_level = 1.1  # Absolute minimum (new)
        self.rebalance_threshold = 0.3  # Rebalance when margin level changes by this much
        
        # Risk management
        self.max_leverage = 3.0  # Maximum leverage allowed
        self.min_leverage = 1.5  # Minimum leverage for efficiency
        self.emergency_reduction_factor = 0.5  # Reduce positions by 50% in emergency
        
        # Tracking
        self.last_margin_level = 0
        self.last_rebalance_time = 0
        self.rebalance_cooldown = 300  # 5 minutes between rebalances
        
        print("🤖 Intelligent Margin Manager Initialized")
        print(f"   Target Margin Level: {self.target_margin_level}")
        print(f"   Risk per Trade: ${self.initial_risk_per_trade}")
        print(f"   Profit Target: ${self.profit_target}")
    
    def get_margin_status(self) -> Dict:
        """Get current margin account status."""
        try:
            if not self.binance_connector.is_connected:
                return None
            
            # Get margin account info
            balance = self.binance_connector.exchange.fetch_balance({'type': 'margin'})
            margin_info = balance.get('info', {})
            
            margin_level = float(margin_info.get('marginLevel', 0))
            total_asset_btc = float(margin_info.get('totalAssetOfBtc', 0))
            total_liability_btc = float(margin_info.get('totalLiabilityOfBtc', 0))
            total_net_btc = float(margin_info.get('totalNetAssetOfBtc', 0))
            
            # Get current BTC price
            ticker = self.binance_connector.exchange.fetch_ticker('BTC/USDT')
            btc_price = ticker['last']
            
            # Calculate USD values
            total_asset_usd = total_asset_btc * btc_price
            total_liability_usd = total_liability_btc * btc_price
            total_net_usd = total_net_btc * btc_price
            
            # Get individual asset balances
            user_assets = margin_info.get('userAssets', [])
            btc_balance = next((a for a in user_assets if a['asset'] == 'BTC'), None)
            usdt_balance = next((a for a in user_assets if a['asset'] == 'USDT'), None)
            
            return {
                'margin_level': margin_level,
                'total_asset_usd': total_asset_usd,
                'total_liability_usd': total_liability_usd,
                'total_net_usd': total_net_usd,
                'btc_price': btc_price,
                'btc_balance': btc_balance,
                'usdt_balance': usdt_balance,
                'raw_info': margin_info
            }
            
        except Exception as e:
            print(f"❌ Error getting margin status: {e}")
            return None
    
    def calculate_optimal_position_size(self, margin_status: Dict, direction: str = 'BUY') -> Dict:
        """Calculate optimal position size based on current margin status."""
        try:
            margin_level = margin_status['margin_level']
            total_net_usd = margin_status['total_net_usd']
            btc_price = margin_status['btc_price']
            
            # Determine risk level and adjust accordingly (adjusted for real conditions)
            if margin_level >= self.safe_margin_level:  # >= 3.0
                # Very safe - can use higher risk
                risk_multiplier = 1.0
                max_leverage_allowed = self.max_leverage
            elif margin_level >= self.target_margin_level:  # >= 2.0
                # Target level - normal risk
                risk_multiplier = 0.8
                max_leverage_allowed = 2.5
            elif margin_level >= 1.8:
                # Moderate risk - reduce position size
                risk_multiplier = 0.6
                max_leverage_allowed = 2.0
            elif margin_level >= 1.5:
                # Higher risk - significantly reduce
                risk_multiplier = 0.4
                max_leverage_allowed = 1.8
            elif margin_level >= self.critical_margin_level:  # >= 1.3
                # Critical risk - minimal positions
                risk_multiplier = 0.3
                max_leverage_allowed = 1.5
            elif margin_level >= self.minimum_margin_level:  # >= 1.1
                # Emergency - very small positions only
                risk_multiplier = 0.2
                max_leverage_allowed = 1.2
            else:
                # Survival mode - stop trading
                risk_multiplier = 0.1
                max_leverage_allowed = 1.1
            
            # Calculate base risk amount
            base_risk_usd = self.initial_risk_per_trade * risk_multiplier
            
            # Ensure we don't risk more than 2% of net worth
            max_risk_by_net_worth = total_net_usd * 0.02
            actual_risk_usd = min(base_risk_usd, max_risk_by_net_worth)
            
            # Calculate position size
            # Assuming 2% stop loss distance
            stop_loss_percentage = 0.02
            position_size_usd = actual_risk_usd / stop_loss_percentage
            position_size_btc = position_size_usd / btc_price
            
            # Apply leverage constraints
            max_position_by_leverage = total_net_usd * max_leverage_allowed / btc_price
            final_position_size_btc = min(position_size_btc, max_position_by_leverage)
            
            # Calculate effective leverage
            effective_leverage = (final_position_size_btc * btc_price) / total_net_usd
            
            return {
                'position_size_btc': final_position_size_btc,
                'position_size_usd': final_position_size_btc * btc_price,
                'risk_usd': actual_risk_usd,
                'effective_leverage': effective_leverage,
                'risk_multiplier': risk_multiplier,
                'margin_level': margin_level,
                'recommended_action': self._get_recommended_action(margin_level)
            }
            
        except Exception as e:
            print(f"❌ Error calculating position size: {e}")
            return None
    
    def _get_recommended_action(self, margin_level: float) -> str:
        """Get recommended action based on margin level (adjusted for real conditions)."""
        if margin_level >= self.safe_margin_level:  # >= 3.0
            return "SAFE_TRADING"
        elif margin_level >= self.target_margin_level:  # >= 2.0
            return "NORMAL_TRADING"
        elif margin_level >= 1.8:
            return "CAUTIOUS_TRADING"
        elif margin_level >= 1.5:
            return "REDUCE_RISK"
        elif margin_level >= self.critical_margin_level:  # >= 1.3
            return "EMERGENCY_REDUCE"
        elif margin_level >= self.minimum_margin_level:  # >= 1.1
            return "MINIMAL_TRADING"
        else:
            return "STOP_TRADING"
    
    def check_rebalancing_needed(self, margin_status: Dict) -> bool:
        """Check if rebalancing is needed."""
        try:
            current_margin_level = margin_status['margin_level']
            current_time = time.time()
            
            # Check cooldown period
            if current_time - self.last_rebalance_time < self.rebalance_cooldown:
                return False
            
            # Check if margin level changed significantly
            margin_level_change = abs(current_margin_level - self.last_margin_level)
            
            # Rebalance conditions
            needs_rebalance = (
                margin_level_change >= self.rebalance_threshold or
                current_margin_level <= self.critical_margin_level or
                current_margin_level >= self.safe_margin_level * 1.5
            )
            
            return needs_rebalance
            
        except Exception as e:
            print(f"❌ Error checking rebalancing: {e}")
            return False
    
    def execute_rebalancing(self, margin_status: Dict) -> bool:
        """Execute automatic rebalancing."""
        try:
            margin_level = margin_status['margin_level']
            btc_price = margin_status['btc_price']
            
            print(f"🔄 EXECUTING REBALANCING")
            print(f"   Current Margin Level: {margin_level:.2f}")
            print(f"   Target Margin Level: {self.target_margin_level:.2f}")
            
            if margin_level < self.critical_margin_level:
                # Emergency: Reduce positions or add collateral
                return self._emergency_rebalance(margin_status)
            elif margin_level < self.target_margin_level:
                # Need to improve margin level
                return self._improve_margin_level(margin_status)
            elif margin_level > self.safe_margin_level * 1.5:
                # Too conservative, can increase leverage
                return self._optimize_leverage(margin_status)
            else:
                # Minor adjustment
                return self._fine_tune_positions(margin_status)
                
        except Exception as e:
            print(f"❌ Error executing rebalancing: {e}")
            return False
    
    def _emergency_rebalance(self, margin_status: Dict) -> bool:
        """Emergency rebalancing for critical margin levels."""
        print("🚨 EMERGENCY REBALANCING")
        
        # In a real implementation, this would:
        # 1. Close some positions to reduce liability
        # 2. Transfer funds from spot to margin account
        # 3. Reduce leverage on existing positions
        
        # For now, we'll log the action
        print("   Action: Would reduce positions by 50%")
        print("   Action: Would transfer additional collateral")
        
        self.last_rebalance_time = time.time()
        return True
    
    def _improve_margin_level(self, margin_status: Dict) -> bool:
        """Improve margin level through strategic adjustments."""
        print("⚠️ IMPROVING MARGIN LEVEL")
        
        # Actions to improve margin level:
        # 1. Reduce position sizes
        # 2. Close losing positions
        # 3. Add more collateral
        
        print("   Action: Would reduce position sizes by 25%")
        print("   Action: Would close positions with losses > 5%")
        
        self.last_rebalance_time = time.time()
        return True
    
    def _optimize_leverage(self, margin_status: Dict) -> bool:
        """Optimize leverage when margin level is very high."""
        print("📈 OPTIMIZING LEVERAGE")
        
        # When margin level is very high, we can:
        # 1. Increase position sizes slightly
        # 2. Take advantage of better opportunities
        
        print("   Action: Would increase position sizes by 15%")
        print("   Action: Would look for additional opportunities")
        
        self.last_rebalance_time = time.time()
        return True
    
    def _fine_tune_positions(self, margin_status: Dict) -> bool:
        """Fine-tune positions for optimal performance."""
        print("🔧 FINE-TUNING POSITIONS")
        
        # Minor adjustments for optimal performance
        print("   Action: Would make minor position adjustments")
        
        self.last_rebalance_time = time.time()
        return True
    
    def get_trading_recommendation(self, margin_status: Dict) -> Dict:
        """Get comprehensive trading recommendation."""
        try:
            margin_level = margin_status['margin_level']
            position_calc = self.calculate_optimal_position_size(margin_status)
            
            # Determine trading status (adjusted for real conditions)
            if margin_level >= self.safe_margin_level:  # >= 3.0
                trading_status = "ACTIVE"
                status_color = "🟢"
            elif margin_level >= self.target_margin_level:  # >= 2.0
                trading_status = "NORMAL"
                status_color = "🟢"
            elif margin_level >= 1.5:
                trading_status = "CAUTIOUS"
                status_color = "🟡"
            elif margin_level >= self.critical_margin_level:  # >= 1.3
                trading_status = "RESTRICTED"
                status_color = "🟠"
            else:
                trading_status = "EMERGENCY"
                status_color = "🔴"
            
            return {
                'trading_status': trading_status,
                'status_color': status_color,
                'margin_level': margin_level,
                'position_calc': position_calc,
                'rebalancing_needed': self.check_rebalancing_needed(margin_status),
                'recommended_action': position_calc['recommended_action'] if position_calc else "UNKNOWN",
                'risk_assessment': self._assess_risk_level(margin_level)
            }
            
        except Exception as e:
            print(f"❌ Error getting trading recommendation: {e}")
            return None
    
    def _assess_risk_level(self, margin_level: float) -> str:
        """Assess current risk level (adjusted for real conditions)."""
        if margin_level >= self.safe_margin_level:  # >= 3.0
            return "LOW"
        elif margin_level >= self.target_margin_level:  # >= 2.0
            return "MEDIUM"
        elif margin_level >= 1.8:
            return "MEDIUM_HIGH"
        elif margin_level >= 1.5:
            return "HIGH"
        elif margin_level >= self.critical_margin_level:  # >= 1.3
            return "VERY_HIGH"
        elif margin_level >= self.minimum_margin_level:  # >= 1.1
            return "CRITICAL"
        else:
            return "EXTREME"
    
    def update_margin_tracking(self, margin_status: Dict):
        """Update margin level tracking."""
        if margin_status:
            self.last_margin_level = margin_status['margin_level']
    
    def get_status_report(self, margin_status: Dict) -> str:
        """Generate a comprehensive status report."""
        if not margin_status:
            return "❌ Unable to get margin status"
        
        recommendation = self.get_trading_recommendation(margin_status)
        if not recommendation:
            return "❌ Unable to generate recommendation"
        
        report = f"""
🤖 INTELLIGENT MARGIN MANAGER STATUS
{'='*50}
{recommendation['status_color']} Trading Status: {recommendation['trading_status']}
📊 Margin Level: {margin_status['margin_level']:.2f}
💰 Net Worth: ${margin_status['total_net_usd']:.2f}
📈 BTC Price: ${margin_status['btc_price']:.2f}

🎯 POSITION OPTIMIZATION:
   Risk per Trade: ${recommendation['position_calc']['risk_usd']:.2f}
   Position Size: {recommendation['position_calc']['position_size_btc']:.6f} BTC
   Effective Leverage: {recommendation['position_calc']['effective_leverage']:.2f}x
   Risk Multiplier: {recommendation['position_calc']['risk_multiplier']:.2f}

⚠️ RISK ASSESSMENT: {recommendation['risk_assessment']}
🔄 Rebalancing Needed: {'YES' if recommendation['rebalancing_needed'] else 'NO'}
📋 Recommended Action: {recommendation['recommended_action']}
"""
        return report
