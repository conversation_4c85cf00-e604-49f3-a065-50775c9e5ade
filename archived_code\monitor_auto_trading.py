#!/usr/bin/env python3
"""
AUTO TRADING MONITOR
===================
Monitor the auto trading system to see if it's generating signals and taking trades.
"""

import requests
import time
import json
from datetime import datetime

def monitor_auto_trading(duration_minutes=5):
    """Monitor auto trading for specified duration."""
    print("🔍 MONITORING AUTO TRADING SYSTEM")
    print("=" * 50)
    print(f"⏰ Monitoring Duration: {duration_minutes} minutes")
    print(f"🕐 Start Time: {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)
    
    # Get initial state
    try:
        response = requests.get(f"{base_url}/api/trading_status")
        initial_data = response.json()
        initial_trades = initial_data['performance']['total_trades']
        initial_daily_trades = initial_data['performance']['daily_trades']
        
        print(f"📊 INITIAL STATE:")
        print(f"   Engine Running: {initial_data['is_running']}")
        print(f"   Total Trades: {initial_trades}")
        print(f"   Daily Trades: {initial_daily_trades}")
        print(f"   Current Price: ${initial_data['current_price']:,.2f}")
        print()
        
    except Exception as e:
        print(f"❌ Error getting initial state: {e}")
        return
    
    check_count = 0
    last_trade_count = initial_trades
    last_daily_count = initial_daily_trades
    
    while time.time() < end_time:
        try:
            check_count += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            
            # Get current status
            response = requests.get(f"{base_url}/api/trading_status")
            data = response.json()
            
            current_trades = data['performance']['total_trades']
            current_daily = data['performance']['daily_trades']
            current_price = data['current_price']
            open_positions = data['performance']['open_positions']
            
            # Check for new trades
            if current_trades > last_trade_count:
                new_trades = current_trades - last_trade_count
                print(f"🎯 NEW TRADE DETECTED! ({current_time})")
                print(f"   Total Trades: {last_trade_count} → {current_trades} (+{new_trades})")
                
                # Get recent trades to see details
                trades_response = requests.get(f"{base_url}/api/recent_trades")
                recent_trades = trades_response.json()
                
                if recent_trades:
                    latest_trade = recent_trades[-1]
                    print(f"   Latest Trade: {latest_trade['trade_id']}")
                    print(f"   Direction: {latest_trade['direction']}")
                    print(f"   Entry Price: ${latest_trade['entry_price']:,.2f}")
                    print(f"   Confidence: {latest_trade['confidence']}%")
                    print(f"   Status: {latest_trade['status']}")
                    if latest_trade.get('pnl'):
                        print(f"   P&L: ${latest_trade['pnl']:+.2f}")
                
                last_trade_count = current_trades
            
            # Check for daily trade changes
            if current_daily > last_daily_count:
                print(f"📈 Daily trades increased: {last_daily_count} → {current_daily}")
                last_daily_count = current_daily
            
            # Print periodic status
            if check_count % 10 == 0:  # Every 10 checks (20 seconds)
                elapsed = (time.time() - start_time) / 60
                remaining = duration_minutes - elapsed
                print(f"⏱️  STATUS CHECK #{check_count} ({current_time}) - {remaining:.1f}min remaining")
                print(f"   Price: ${current_price:,.2f} | Trades: {current_trades} | Open: {open_positions}")
            
            time.sleep(2)  # Check every 2 seconds
            
        except Exception as e:
            print(f"❌ Error during monitoring: {e}")
            time.sleep(5)
    
    # Final summary
    print("\n" + "=" * 50)
    print("📋 MONITORING SUMMARY")
    print("=" * 50)
    
    try:
        response = requests.get(f"{base_url}/api/trading_status")
        final_data = response.json()
        final_trades = final_data['performance']['total_trades']
        final_daily = final_data['performance']['daily_trades']
        
        trades_generated = final_trades - initial_trades
        daily_generated = final_daily - initial_daily_trades
        
        print(f"⏰ Monitoring Duration: {duration_minutes} minutes")
        print(f"📊 Trades Generated: {trades_generated}")
        print(f"📈 Daily Trades Added: {daily_generated}")
        print(f"💰 Final Price: ${final_data['current_price']:,.2f}")
        print(f"📍 Open Positions: {final_data['performance']['open_positions']}")
        
        if trades_generated > 0:
            print("✅ AUTO TRADING IS WORKING - Trades were generated!")
            
            # Get recent trades for analysis
            trades_response = requests.get(f"{base_url}/api/recent_trades")
            recent_trades = trades_response.json()
            
            if recent_trades:
                print(f"\n📋 RECENT TRADES ANALYSIS:")
                for trade in recent_trades[-trades_generated:]:
                    print(f"   {trade['trade_id']}: {trade['direction']} @ ${trade['entry_price']:,.2f}")
                    print(f"      Confidence: {trade['confidence']}% | Status: {trade['status']}")
                    if trade.get('pnl'):
                        print(f"      P&L: ${trade['pnl']:+.2f}")
        else:
            print("⚠️ NO TRADES GENERATED - Auto trading may not be working")
            print("\n🔍 POSSIBLE ISSUES:")
            print("   1. Signal generation probability too low (8% per 2-second cycle)")
            print("   2. Confidence threshold not being met (>40%)")
            print("   3. Risk management blocking trades")
            print("   4. Auto trading loop not running properly")
            
            print(f"\n💡 RECOMMENDATIONS:")
            print(f"   1. Try forced trade: curl -X POST {base_url}/api/force_test_trade")
            print(f"   2. Check if engine is truly running")
            print(f"   3. Monitor for longer period (signal generation is probabilistic)")
            print(f"   4. Check Flask debug mode (may be restarting threads)")
        
    except Exception as e:
        print(f"❌ Error getting final summary: {e}")
    
    print("=" * 50)
    print("🔍 MONITORING COMPLETE")

if __name__ == "__main__":
    # Monitor for 3 minutes by default
    monitor_auto_trading(3)
