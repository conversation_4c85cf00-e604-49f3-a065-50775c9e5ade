#!/usr/bin/env python3
"""
Real Money Trading System Test Suite
Comprehensive testing before deployment
"""

import os
import sys
import asyncio
import importlib
from pathlib import Path

def test_imports():
    """Test all required imports"""
    print("🧪 Testing Required Imports...")
    
    required_modules = [
        'streamlit',
        'plotly',
        'ccxt',
        'pandas',
        'numpy',
        'cryptography',
        'requests'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ Missing modules: {', '.join(missing_modules)}")
        print("Install with: pip install -r requirements.txt")
        return False
    
    print("✅ All required modules available\n")
    return True

def test_core_components():
    """Test core trading components"""
    print("🧪 Testing Core Components...")
    
    try:
        # Test config
        sys.path.append('config')
        from trading_config import TradingConfig
        config = TradingConfig()
        print("   ✅ TradingConfig")
        
        # Test grid trading core
        from grid_trading_core import GridLevelCalculator, GridTradeManager
        grid_calc = GridLevelCalculator(config)
        trade_manager = GridTradeManager(config)
        print("   ✅ Grid Trading Core")
        
        # Test feature engineering
        from grid_feature_engineering import GridFeatureEngineering
        feature_eng = GridFeatureEngineering(config)
        print("   ✅ Feature Engineering")
        
        # Test metrics
        from grid_composite_metrics import GridCompositeMetrics
        metrics = GridCompositeMetrics(config)
        print("   ✅ Composite Metrics")
        
        # Test data collector
        from binance_data_collector import BinanceDataCollector
        data_collector = BinanceDataCollector(config)
        print("   ✅ Binance Data Collector")
        
        print("✅ All core components loaded successfully\n")
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Component error: {e}")
        return False

def test_webapp_structure():
    """Test webapp file structure"""
    print("🧪 Testing Webapp Structure...")
    
    try:
        from real_money_trading_webapp import RealMoneyTradingBot, RealMoneyTradingWebapp
        print("   ✅ Webapp classes imported")
        
        # Test bot initialization
        sys.path.append('config')
        from trading_config import TradingConfig
        config = TradingConfig()
        
        bot = RealMoneyTradingBot(config)
        print("   ✅ RealMoneyTradingBot initialized")
        
        # Test webapp initialization
        webapp = RealMoneyTradingWebapp()
        print("   ✅ RealMoneyTradingWebapp initialized")
        
        print("✅ Webapp structure test passed\n")
        return True
        
    except Exception as e:
        print(f"   ❌ Webapp error: {e}")
        return False

def test_security_manager():
    """Test secure API manager"""
    print("🧪 Testing Security Manager...")
    
    try:
        from secure_api_manager import SecureAPIManager
        
        api_manager = SecureAPIManager()
        print("   ✅ SecureAPIManager initialized")
        
        # Test credentials interface
        creds = api_manager.get_webapp_credentials()
        print("   ✅ Webapp credentials interface")
        
        # Test configuration check
        is_configured = api_manager.is_configured()
        print(f"   ✅ Configuration check: {is_configured}")
        
        print("✅ Security manager test passed\n")
        return True
        
    except Exception as e:
        print(f"   ❌ Security manager error: {e}")
        return False

def test_file_structure():
    """Test required file structure"""
    print("🧪 Testing File Structure...")
    
    required_files = [
        'real_money_trading_webapp.py',
        'secure_api_manager.py',
        'launch_webapp.py',
        'launch_real_money_webapp.bat',
        'requirements.txt',
        'grid_trading_core.py',
        'binance_data_collector.py',
        'REAL_MONEY_WEBAPP_GUIDE.md'
    ]
    
    missing_files = []
    
    for file in required_files:
        if Path(file).exists():
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ Missing files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files present\n")
    return True

def test_directories():
    """Test and create required directories"""
    print("🧪 Testing Directory Structure...")
    
    required_dirs = ['config', 'data', 'logs', 'models']
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f"   📁 Created {dir_name}")
        else:
            print(f"   ✅ {dir_name}")
    
    print("✅ Directory structure ready\n")
    return True

async def test_binance_connection():
    """Test Binance connection (optional)"""
    print("🧪 Testing Binance Connection...")
    
    try:
        sys.path.append('config')
        from trading_config import TradingConfig
        from binance_data_collector import BinanceDataCollector
        
        config = TradingConfig()
        data_collector = BinanceDataCollector(config)
        
        # Test connection (will work even without API keys)
        try:
            connection_ok = await data_collector.test_connection()
            if connection_ok:
                print("   ✅ Binance connection successful")
            else:
                print("   ⚠️ Binance connection failed (API keys not configured)")
        except:
            print("   ⚠️ Binance connection test skipped (no API keys)")
        
        print("✅ Binance connection test completed\n")
        return True
        
    except Exception as e:
        print(f"   ⚠️ Binance test error: {e}")
        print("   (This is expected without API configuration)\n")
        return True

def main():
    """Run all tests"""
    print("=" * 60)
    print("🎯 REAL MONEY TRADING SYSTEM - DEPLOYMENT TEST")
    print("=" * 60)
    print()
    
    tests = [
        ("Import Test", test_imports),
        ("Core Components", test_core_components),
        ("Webapp Structure", test_webapp_structure),
        ("Security Manager", test_security_manager),
        ("File Structure", test_file_structure),
        ("Directory Structure", test_directories),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                print(f"❌ {test_name} FAILED\n")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}\n")
    
    # Async test
    print("🧪 Running Async Tests...")
    try:
        asyncio.run(test_binance_connection())
        passed_tests += 1
        total_tests += 1
    except Exception as e:
        print(f"❌ Binance Connection Test ERROR: {e}\n")
    
    print("=" * 60)
    print(f"📊 TEST RESULTS: {passed_tests}/{total_tests} PASSED")
    print("=" * 60)
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print()
        print("✅ System ready for deployment!")
        print("🚀 Run: launch_real_money_webapp.bat")
        print("🌐 Or: python launch_webapp.py")
        print()
        print("⚠️  REMEMBER:")
        print("   1. Configure API keys in webapp")
        print("   2. Start with TESTNET mode")
        print("   3. Monitor actively during trading")
        print("   4. Use emergency stop if needed")
        
    else:
        print("❌ SOME TESTS FAILED!")
        print("🔧 Fix issues before deployment")
        print("📖 Check REAL_MONEY_WEBAPP_GUIDE.md")
    
    print("=" * 60)
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
