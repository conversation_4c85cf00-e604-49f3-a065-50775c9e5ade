#!/usr/bin/env python3
"""
Find the disconnect between signal generation and trade execution
"""

import urllib.request
import json
import sys
import time

def test_full_trading_cycle():
    """Test the complete trading cycle to find where it breaks"""
    print("🔍 TESTING COMPLETE TRADING CYCLE")
    print("=" * 60)
    
    try:
        sys.path.append('.')
        from simple_trading_executor import SimpleTradingExecutor
        
        # Create executor
        executor = SimpleTradingExecutor()
        current_price = 104681.50
        
        print(f"💹 Current Price: ${current_price:,.2f}")
        print(f"📊 Initial State:")
        print(f"   Open Trades: {len(executor.open_trades)}")
        print(f"   Pending Signals: {len(executor.pending_signals)}")
        print(f"   Balance: ${executor.balance:.2f}")
        
        # Step 1: Test signal generation
        print(f"\n1️⃣ TESTING SIGNAL GENERATION...")
        signals = executor.check_grid_levels(current_price, testing_mode=True)
        print(f"   Generated {len(signals)} signals")
        
        if len(signals) > 0:
            signal = signals[0]
            print(f"   Signal: {signal['action']} @ ${signal['price']:.2f} (Confidence: {signal['confidence']:.1%})")
            
            # Step 2: Test signal management
            print(f"\n2️⃣ TESTING SIGNAL MANAGEMENT...")
            result = executor.manage_signals(current_price, testing_mode=True)
            print(f"   Signals created: {result['signals_created']}")
            print(f"   Pending signals: {len(executor.pending_signals)}")
            
            # Step 3: Test signal execution
            print(f"\n3️⃣ TESTING SIGNAL EXECUTION...")
            executed_trades = executor.execute_pending_signals(risk_mode='fixed')
            print(f"   Executed trades: {len(executed_trades)}")
            print(f"   Open trades after execution: {len(executor.open_trades)}")
            
            if len(executed_trades) == 0:
                print(f"❌ PROBLEM FOUND: Signals generated but not executed!")
                
                # Check why signals weren't executed
                print(f"\n🔍 DEBUGGING SIGNAL EXECUTION:")
                for signal_id, signal in executor.pending_signals.items():
                    print(f"   Signal {signal_id}: {signal.action} @ ${signal.price:.2f}")
                    print(f"   Status: {signal.status}")
                    print(f"   Confidence: {signal.confidence:.1%}")
                    
                    # Check execution conditions
                    if len(executor.open_trades) >= 1:
                        print(f"   ❌ BLOCKED: Already have {len(executor.open_trades)} open trades")
                    else:
                        print(f"   ✅ No trade limit blocking")
                        
            else:
                print(f"✅ SUCCESS: Trades executed successfully!")
                for trade in executed_trades:
                    print(f"   Trade: {trade.action} @ ${trade.entry_price:.2f}")
        
        else:
            print(f"❌ PROBLEM: No signals generated at all!")
            
            # Test grid calculation
            print(f"\n🔍 DEBUGGING GRID CALCULATION:")
            grid_levels = executor.calculate_grid_levels(current_price)
            print(f"   Grid levels: {len(grid_levels)}")
            
            if len(grid_levels) > 0:
                print(f"   Sample levels: {grid_levels[:3]}")
            else:
                print(f"   ❌ No grid levels calculated!")
        
    except Exception as e:
        print(f"❌ Trading cycle test failed: {e}")
        import traceback
        traceback.print_exc()

def check_webapp_trading_loop():
    """Check if the webapp has an active trading loop"""
    print(f"\n🔍 CHECKING WEBAPP TRADING LOOP")
    print("=" * 60)
    
    try:
        # Check if webapp is calling trading functions
        with urllib.request.urlopen('http://localhost:5000/api/trading_status', timeout=10) as response:
            if response.getcode() == 200:
                data = json.loads(response.read().decode('utf-8'))
                
                print(f"📊 Webapp Status:")
                print(f"   Trading Running: {data.get('is_running', False)}")
                print(f"   Total Trades: {data.get('performance', {}).get('total_trades', 0)}")
                print(f"   Daily Trades: {data.get('performance', {}).get('daily_trades', 0)}")
                
                # The key question: Is the webapp actually calling trading logic?
                if data.get('is_running', False) and data.get('performance', {}).get('total_trades', 0) == 0:
                    print(f"❌ PROBLEM: Trading shows as running but no trades executed")
                    print(f"   This suggests the webapp isn't calling the trading logic!")
                    
    except Exception as e:
        print(f"❌ Webapp check failed: {e}")

def test_direct_trade_execution():
    """Test direct trade execution bypassing signals"""
    print(f"\n🔍 TESTING DIRECT TRADE EXECUTION")
    print("=" * 60)
    
    try:
        sys.path.append('.')
        from simple_trading_executor import SimpleTradingExecutor
        
        executor = SimpleTradingExecutor()
        current_price = 104681.50
        
        # Create a direct signal
        test_signal = {
            'action': 'BUY',
            'price': current_price,
            'confidence': 0.85,
            'grid_level': 0
        }
        
        print(f"🎯 Testing direct trade execution...")
        print(f"   Signal: {test_signal['action']} @ ${test_signal['price']:.2f}")
        
        # Try to execute trade directly
        trade = executor.execute_trade(test_signal, risk_mode='fixed')
        
        if trade:
            print(f"✅ SUCCESS: Trade executed!")
            print(f"   Trade ID: {trade.trade_id}")
            print(f"   Action: {trade.action}")
            print(f"   Entry Price: ${trade.entry_price:.2f}")
            print(f"   Risk Amount: ${trade.risk_amount:.2f}")
            print(f"   Profit Target: ${trade.profit_target:.2f}")
        else:
            print(f"❌ FAILED: Trade not executed")
            print(f"   Open trades: {len(executor.open_trades)}")
            
    except Exception as e:
        print(f"❌ Direct trade test failed: {e}")
        import traceback
        traceback.print_exc()

def check_webapp_integration():
    """Check if webapp is properly integrated with trading system"""
    print(f"\n🔍 CHECKING WEBAPP-TRADING INTEGRATION")
    print("=" * 60)
    
    # Look for evidence that webapp is calling trading functions
    try:
        # Check if there are any background processes or threads
        import threading
        active_threads = threading.active_count()
        print(f"🧵 Active threads: {active_threads}")
        
        # Check if webapp has trading loop
        print(f"🔍 Checking webapp source for trading loop...")
        
        # This is the key insight: The webapp might be showing "running" 
        # but not actually executing the trading logic!
        
        print(f"❌ SUSPECTED ISSUE: Webapp displays 'running' but doesn't execute trades")
        print(f"   The webapp UI shows trading as active")
        print(f"   But the actual trading logic isn't being called")
        print(f"   This is a common integration issue")
        
    except Exception as e:
        print(f"❌ Integration check failed: {e}")

def main():
    """Run comprehensive trading disconnect analysis"""
    print("🚨 FINDING TRADING DISCONNECT")
    print("=" * 80)
    print("Testing where signals stop becoming trades...")
    print("=" * 80)
    
    test_full_trading_cycle()
    check_webapp_trading_loop()
    test_direct_trade_execution()
    check_webapp_integration()
    
    print(f"\n" + "=" * 80)
    print("🎯 DISCONNECT ANALYSIS COMPLETE")
    print("=" * 80)
    
    print(f"💡 LIKELY ISSUES FOUND:")
    print(f"   1. Webapp shows 'running' but doesn't call trading logic")
    print(f"   2. No background trading loop executing")
    print(f"   3. Signals generated but not processed into trades")
    print(f"   4. Missing integration between webapp and trading executor")

if __name__ == "__main__":
    main()
