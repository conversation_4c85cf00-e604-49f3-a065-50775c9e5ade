#!/usr/bin/env python3
"""
INTELLIGENT PORTFOLIO REBALANCER
================================
Maintains optimal BTC/USDT balance for continuous cross margin trading.
Ensures the bot always has both assets available for buying and selling.
"""

import time
import math
from datetime import datetime
from typing import Dict, Tuple, Optional

class IntelligentPortfolioRebalancer:
    """Intelligent rebalancing system for BTC/USDT cross margin trading."""
    
    def __init__(self, binance_connector, target_btc_ratio=0.5, risk_per_trade=10):
        self.binance_connector = binance_connector
        self.target_btc_ratio = target_btc_ratio  # Target 50% BTC, 50% USDT
        self.risk_per_trade = risk_per_trade
        
        # Conservative rebalancing thresholds (minimize fees)
        self.rebalance_threshold = 0.25  # Only rebalance when ratio differs by 25% (was 15%)
        self.min_trade_buffer = 2  # Keep enough for 2 trades minimum (was 3)
        self.emergency_threshold = 0.35  # Emergency rebalance at 35% imbalance (was 25%)
        self.critical_threshold = 0.45  # Critical rebalance at 45% imbalance (new)

        # Fee-conscious settings (adjusted for real trading conditions)
        self.min_margin_level = 1.2  # Don't rebalance if margin level too low (was 2.0)
        self.max_rebalance_size = 0.15  # Max 15% of portfolio per rebalance (conservative)
        self.rebalance_cooldown = 1800  # 30 minutes between rebalances
        self.min_rebalance_amount = 15  # Minimum $15 rebalance to justify fees (was $20)

        # Trading capacity thresholds (when rebalancing becomes necessary)
        self.min_usdt_for_buy = risk_per_trade * 2  # Need 2x risk amount in USDT
        self.min_btc_value_for_sell = risk_per_trade * 2  # Need 2x risk amount in BTC value
        
        # Tracking
        self.last_rebalance_time = 0
        self.rebalance_count = 0
        
        print("🔄 Intelligent Portfolio Rebalancer Initialized (FEE-CONSCIOUS)")
        print(f"   Target BTC Ratio: {self.target_btc_ratio*100:.1f}%")
        print(f"   Risk per Trade: ${self.risk_per_trade}")
        print(f"   Rebalance Threshold: {self.rebalance_threshold*100:.1f}% (conservative)")
        print(f"   Emergency Threshold: {self.emergency_threshold*100:.1f}%")
        print(f"   Critical Threshold: {self.critical_threshold*100:.1f}%")
        print(f"   Minimum Rebalance: ${self.min_rebalance_amount} (to justify fees)")
        print(f"   Cooldown Period: {self.rebalance_cooldown/60:.0f} minutes")
    
    def get_portfolio_status(self) -> Dict:
        """Get current portfolio status and balance ratios."""
        try:
            if not self.binance_connector.is_connected:
                return None

            # Check if this is a Simple Binance Connector or CCXT
            if hasattr(self.binance_connector, 'get_portfolio_balance_for_rebalancing'):
                # Simple Binance Connector
                balance_data = self.binance_connector.get_portfolio_balance_for_rebalancing()
                if not balance_data:
                    return None

                margin_info = balance_data.get('info', {})

                # Get current BTC price
                btc_price = self.binance_connector.get_ticker_price('BTCUSDT')
                if not btc_price:
                    return None

            else:
                # CCXT connector
                balance = self.binance_connector.exchange.fetch_balance({'type': 'margin'})
                margin_info = balance.get('info', {})

                # Get current BTC price
                ticker = self.binance_connector.exchange.fetch_ticker('BTC/USDT')
                btc_price = ticker['last']

            # Get individual asset balances
            user_assets = margin_info.get('userAssets', [])
            btc_balance = next((a for a in user_assets if a['asset'] == 'BTC'), None)
            usdt_balance = next((a for a in user_assets if a['asset'] == 'USDT'), None)

            # Calculate net balances (free - borrowed)
            btc_net = 0
            usdt_net = 0

            if btc_balance:
                btc_free = float(btc_balance['free'])
                btc_borrowed = float(btc_balance.get('borrowed', '0'))
                btc_net = btc_free - btc_borrowed

            if usdt_balance:
                usdt_free = float(usdt_balance['free'])
                usdt_borrowed = float(usdt_balance.get('borrowed', '0'))
                usdt_net = usdt_free - usdt_borrowed

            # Convert to USD values
            btc_value_usd = btc_net * btc_price
            usdt_value_usd = usdt_net
            total_value_usd = btc_value_usd + usdt_value_usd

            # Calculate current ratios
            btc_ratio = btc_value_usd / total_value_usd if total_value_usd > 0 else 0
            usdt_ratio = usdt_value_usd / total_value_usd if total_value_usd > 0 else 0

            # Calculate imbalance
            btc_imbalance = btc_ratio - self.target_btc_ratio

            # Get margin level
            margin_level = float(margin_info.get('marginLevel', 999.0))

            return {
                'btc_net': btc_net,
                'usdt_net': usdt_net,
                'btc_value_usd': btc_value_usd,
                'usdt_value_usd': usdt_value_usd,
                'total_value_usd': total_value_usd,
                'btc_ratio': btc_ratio,
                'usdt_ratio': usdt_ratio,
                'btc_imbalance': btc_imbalance,
                'btc_price': btc_price,
                'margin_level': margin_level,
                'raw_balance': balance_data if hasattr(self.binance_connector, 'get_portfolio_balance_for_rebalancing') else balance
            }

        except Exception as e:
            print(f"❌ Error getting portfolio status: {e}")
            return None
    
    def check_rebalancing_needed(self, portfolio_status: Dict) -> Tuple[bool, str]:
        """Check if rebalancing is needed and return reason. Very conservative to minimize fees."""
        try:
            current_time = time.time()

            # Check cooldown period (30 minutes)
            if current_time - self.last_rebalance_time < self.rebalance_cooldown:
                return False, "COOLDOWN_ACTIVE"

            # Check margin level safety
            if portfolio_status['margin_level'] < self.min_margin_level:
                return False, "MARGIN_LEVEL_TOO_LOW"

            btc_imbalance = abs(portfolio_status['btc_imbalance'])

            # CRITICAL: Only rebalance if we literally cannot trade
            trading_capacity = self.check_trading_capacity(portfolio_status)

            # Priority 1: CRITICAL - Cannot execute any trades
            if not trading_capacity['can_buy'] and not trading_capacity['can_sell']:
                return True, "CRITICAL_NO_TRADING_CAPACITY"

            # Priority 2: EMERGENCY - Can only trade in one direction AND severe imbalance
            if (not trading_capacity['can_buy'] or not trading_capacity['can_sell']) and btc_imbalance >= self.critical_threshold:
                return True, "EMERGENCY_ONE_DIRECTION_ONLY"

            # Priority 3: SEVERE IMBALANCE - Only if extremely unbalanced (45%+)
            if btc_imbalance >= self.critical_threshold:
                # Check if rebalance amount justifies fees
                rebalance_calc = self.calculate_rebalance_amounts(portfolio_status)
                if rebalance_calc and rebalance_calc['trade_amount_usd'] >= self.min_rebalance_amount:
                    return True, "SEVERE_IMBALANCE"

            # Priority 4: TRADING CAPACITY WARNING - Can trade but running low
            if trading_capacity['max_buy_trades'] <= 1 or trading_capacity['max_sell_trades'] <= 1:
                if btc_imbalance >= self.emergency_threshold:
                    return True, "LOW_TRADING_CAPACITY"

            # Otherwise, don't rebalance (save on fees)
            return False, "BALANCED_NO_REBALANCE_NEEDED"

        except Exception as e:
            print(f"❌ Error checking rebalancing: {e}")
            return False, "ERROR"
    
    def check_trading_capacity(self, portfolio_status: Dict) -> Dict:
        """Check if we have enough of both assets for trading. Conservative approach."""
        try:
            btc_net = portfolio_status['btc_net']
            usdt_net = portfolio_status['usdt_net']
            btc_price = portfolio_status['btc_price']

            # Use conservative thresholds (2x risk amount minimum)
            min_usdt_for_buy = self.min_usdt_for_buy  # 2x risk amount
            min_btc_value_for_sell = self.min_btc_value_for_sell  # 2x risk amount
            min_btc_needed = min_btc_value_for_sell / btc_price

            # Check if we can buy (need sufficient USDT)
            can_buy = usdt_net >= min_usdt_for_buy

            # Check if we can sell (need sufficient BTC)
            can_sell = (btc_net * btc_price) >= min_btc_value_for_sell

            # Calculate how many trades we can make (conservative)
            max_buy_trades = int(usdt_net / self.risk_per_trade) if usdt_net > 0 else 0
            max_sell_trades = int((btc_net * btc_price) / self.risk_per_trade) if btc_net > 0 else 0

            # Additional safety check - ensure we have buffer for fees
            fee_buffer = 0.001  # 0.1% fee buffer
            usdt_after_fees = usdt_net * (1 - fee_buffer)
            btc_value_after_fees = (btc_net * btc_price) * (1 - fee_buffer)

            return {
                'can_buy': can_buy,
                'can_sell': can_sell,
                'max_buy_trades': max_buy_trades,
                'max_sell_trades': max_sell_trades,
                'min_btc_needed': min_btc_needed,
                'min_usdt_needed': min_usdt_for_buy,
                'btc_available': btc_net,
                'usdt_available': usdt_net,
                'usdt_after_fees': usdt_after_fees,
                'btc_value_after_fees': btc_value_after_fees,
                'fee_buffer_applied': True
            }

        except Exception as e:
            print(f"❌ Error checking trading capacity: {e}")
            return {'can_buy': False, 'can_sell': False}
    
    def calculate_rebalance_amounts(self, portfolio_status: Dict) -> Dict:
        """Calculate optimal rebalance amounts."""
        try:
            total_value_usd = portfolio_status['total_value_usd']
            btc_value_usd = portfolio_status['btc_value_usd']
            usdt_value_usd = portfolio_status['usdt_value_usd']
            btc_price = portfolio_status['btc_price']
            btc_imbalance = portfolio_status['btc_imbalance']
            
            # Calculate target values
            target_btc_value_usd = total_value_usd * self.target_btc_ratio
            target_usdt_value_usd = total_value_usd * (1 - self.target_btc_ratio)
            
            # Calculate required adjustments
            btc_adjustment_usd = target_btc_value_usd - btc_value_usd
            usdt_adjustment_usd = target_usdt_value_usd - usdt_value_usd
            
            # Limit rebalance size for safety
            max_rebalance_usd = total_value_usd * self.max_rebalance_size
            
            if abs(btc_adjustment_usd) > max_rebalance_usd:
                btc_adjustment_usd = max_rebalance_usd * (1 if btc_adjustment_usd > 0 else -1)
                usdt_adjustment_usd = -btc_adjustment_usd
            
            # Convert to actual amounts
            if btc_adjustment_usd > 0:
                # Need to buy BTC (sell USDT)
                action = "BUY_BTC"
                trade_amount_usd = btc_adjustment_usd
                trade_amount_btc = trade_amount_usd / btc_price
            else:
                # Need to sell BTC (buy USDT)
                action = "SELL_BTC"
                trade_amount_usd = abs(btc_adjustment_usd)
                trade_amount_btc = trade_amount_usd / btc_price
            
            return {
                'action': action,
                'trade_amount_usd': trade_amount_usd,
                'trade_amount_btc': trade_amount_btc,
                'btc_adjustment_usd': btc_adjustment_usd,
                'usdt_adjustment_usd': usdt_adjustment_usd,
                'target_btc_value_usd': target_btc_value_usd,
                'target_usdt_value_usd': target_usdt_value_usd,
                'current_imbalance_pct': btc_imbalance * 100
            }
            
        except Exception as e:
            print(f"❌ Error calculating rebalance amounts: {e}")
            return None
    
    def execute_rebalance(self, portfolio_status: Dict, rebalance_calc: Dict) -> bool:
        """Execute the rebalancing trade with fee considerations."""
        try:
            action = rebalance_calc['action']
            trade_amount_btc = rebalance_calc['trade_amount_btc']
            trade_amount_usd = rebalance_calc['trade_amount_usd']

            # Calculate estimated fees (Binance margin trading: ~0.1%)
            estimated_fee_usd = trade_amount_usd * 0.001

            print(f"🔄 EXECUTING FEE-CONSCIOUS REBALANCE")
            print(f"   Action: {action}")
            print(f"   Amount: {trade_amount_btc:.6f} BTC (${trade_amount_usd:.2f})")
            print(f"   Estimated Fee: ${estimated_fee_usd:.2f}")
            print(f"   Current Imbalance: {rebalance_calc['current_imbalance_pct']:.1f}%")
            print(f"   Justification: Fee cost acceptable for this rebalance")

            # Double-check if rebalance is still worth it after fee consideration
            if trade_amount_usd < self.min_rebalance_amount:
                print(f"   ⚠️ Rebalance amount ${trade_amount_usd:.2f} below minimum ${self.min_rebalance_amount}")
                print(f"   ❌ Skipping rebalance to avoid unnecessary fees")
                return False

            # Execute the actual rebalancing trade
            try:
                if action == "BUY_BTC":
                    print(f"   📈 EXECUTING BUY {trade_amount_btc:.6f} BTC with ${trade_amount_usd:.2f} USDT")
                    print(f"   💰 Net cost after fees: ${trade_amount_usd + estimated_fee_usd:.2f}")

                    # Check if this is Simple Binance Connector or CCXT
                    if hasattr(self.binance_connector, 'place_margin_order'):
                        # Simple Binance Connector
                        order = self.binance_connector.place_margin_order(
                            'BTCUSDT', 'BUY', 'MARKET', trade_amount_btc
                        )
                    else:
                        # CCXT connector
                        order = self.binance_connector.exchange.create_market_buy_order(
                            'BTC/USDT', trade_amount_btc
                        )

                    if order:
                        print(f"   ✅ BUY order executed: {order.get('orderId', 'Unknown')}")
                    else:
                        print(f"   ❌ BUY order failed")
                        return False

                else:  # SELL_BTC
                    print(f"   📉 EXECUTING SELL {trade_amount_btc:.6f} BTC for ${trade_amount_usd:.2f} USDT")
                    print(f"   💰 Net proceeds after fees: ${trade_amount_usd - estimated_fee_usd:.2f}")

                    # Check if this is Simple Binance Connector or CCXT
                    if hasattr(self.binance_connector, 'place_margin_order'):
                        # Simple Binance Connector
                        order = self.binance_connector.place_margin_order(
                            'BTCUSDT', 'SELL', 'MARKET', trade_amount_btc
                        )
                    else:
                        # CCXT connector
                        order = self.binance_connector.exchange.create_market_sell_order(
                            'BTC/USDT', trade_amount_btc
                        )

                    if order:
                        print(f"   ✅ SELL order executed: {order.get('orderId', 'Unknown')}")
                    else:
                        print(f"   ❌ SELL order failed")
                        return False

            except Exception as order_error:
                print(f"   ❌ Order execution failed: {order_error}")
                return False

            # Update tracking
            self.last_rebalance_time = time.time()
            self.rebalance_count += 1

            print(f"✅ Fee-conscious rebalance #{self.rebalance_count} completed")
            print(f"   Next rebalance allowed in {self.rebalance_cooldown/60:.0f} minutes")
            return True

        except Exception as e:
            print(f"❌ Error executing rebalance: {e}")
            return False
    
    def get_rebalance_recommendation(self, portfolio_status: Dict) -> Dict:
        """Get comprehensive rebalancing recommendation."""
        try:
            needs_rebalance, reason = self.check_rebalancing_needed(portfolio_status)
            trading_capacity = self.check_trading_capacity(portfolio_status)
            
            recommendation = {
                'needs_rebalance': needs_rebalance,
                'reason': reason,
                'trading_capacity': trading_capacity,
                'portfolio_status': portfolio_status,
                'rebalance_calc': None
            }
            
            if needs_rebalance and reason not in ['COOLDOWN_ACTIVE', 'MARGIN_LEVEL_TOO_LOW', 'ERROR']:
                rebalance_calc = self.calculate_rebalance_amounts(portfolio_status)
                recommendation['rebalance_calc'] = rebalance_calc
            
            return recommendation
            
        except Exception as e:
            print(f"❌ Error getting rebalance recommendation: {e}")
            return None
    
    def get_status_report(self, portfolio_status: Dict) -> str:
        """Generate comprehensive status report."""
        try:
            if not portfolio_status:
                return "❌ Unable to get portfolio status"
            
            recommendation = self.get_rebalance_recommendation(portfolio_status)
            trading_capacity = recommendation['trading_capacity']
            
            # Status indicators
            balance_status = "🟢 BALANCED" if not recommendation['needs_rebalance'] else "🟡 NEEDS REBALANCE"
            if recommendation['reason'] == "EMERGENCY_IMBALANCE":
                balance_status = "🔴 EMERGENCY REBALANCE"
            
            buy_status = "✅" if trading_capacity['can_buy'] else "❌"
            sell_status = "✅" if trading_capacity['can_sell'] else "❌"
            
            report = f"""
🔄 INTELLIGENT PORTFOLIO REBALANCER STATUS
{'='*60}
{balance_status}
📊 Portfolio Balance:
   Total Value: ${portfolio_status['total_value_usd']:.2f}
   BTC: {portfolio_status['btc_net']:.6f} (${portfolio_status['btc_value_usd']:.2f}) - {portfolio_status['btc_ratio']*100:.1f}%
   USDT: ${portfolio_status['usdt_net']:.2f} - {portfolio_status['usdt_ratio']*100:.1f}%
   
🎯 Target Allocation:
   BTC Target: {self.target_btc_ratio*100:.1f}%
   Current Imbalance: {portfolio_status['btc_imbalance']*100:.1f}%
   
🎮 Trading Capacity:
   Can Buy BTC: {buy_status} ({trading_capacity['max_buy_trades']} trades available)
   Can Sell BTC: {sell_status} ({trading_capacity['max_sell_trades']} trades available)
   
📋 Rebalancing Status:
   Needs Rebalance: {'YES' if recommendation['needs_rebalance'] else 'NO'}
   Reason: {recommendation['reason']}
   Last Rebalance: {self.rebalance_count} total rebalances
   
🛡️ Safety Metrics:
   Margin Level: {portfolio_status['margin_level']:.2f}
   BTC Price: ${portfolio_status['btc_price']:.2f}
"""
            
            if recommendation['rebalance_calc']:
                calc = recommendation['rebalance_calc']
                report += f"""
🔄 Recommended Rebalance:
   Action: {calc['action']}
   Amount: {calc['trade_amount_btc']:.6f} BTC (${calc['trade_amount_usd']:.2f})
   This will improve balance by {abs(calc['current_imbalance_pct']):.1f}%
"""
            
            return report
            
        except Exception as e:
            return f"❌ Error generating status report: {e}"
