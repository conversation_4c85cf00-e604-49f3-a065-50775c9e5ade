"""
Enhanced Trading System Launcher
Launches the complete enhanced trading system with test mode capabilities
"""

import os
import sys
import logging
import asyncio
import subprocess
from datetime import datetime
from typing import Dict, Optional

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import enhanced components
from enhanced_test_trading_engine import TestTradingEngine
from test_mode_validator import TestModeValidator
from comprehensive_code_audit import EnhancedCodeAuditor


class EnhancedTradingSystemLauncher:
    """Enhanced trading system launcher with full capabilities"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.logger = logging.getLogger('EnhancedTradingSystemLauncher')
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.config.LOGS_DIR, 'enhanced_system.log')),
                logging.StreamHandler()
            ]
        )
        
        # Initialize components
        self.test_engine = TestTradingEngine(self.config)
        self.validator = TestModeValidator(self.config)
        self.auditor = EnhancedCodeAuditor(self.config)
    
    def display_welcome_banner(self):
        """Display welcome banner"""
        print("\n" + "="*80)
        print("🎯 ENHANCED GRID TRADING SYSTEM - VPS 4")
        print("="*80)
        print("🚀 Complete Trading Solution with Test & Live Modes")
        print("🧪 Advanced Test Trading with Validation")
        print("💰 Real Money Trading Capabilities")
        print("🔍 Comprehensive Code Audit & Cleanup")
        print("✅ Full System Validation")
        print("="*80)
        print(f"📅 Launch Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 Config Directory: {self.config.DATA_DIR}")
        print(f"📊 Logs Directory: {self.config.LOGS_DIR}")
        print("="*80)
    
    def display_main_menu(self):
        """Display main menu options"""
        print("\n🎮 MAIN MENU - Select an option:")
        print("1. 🧪 Launch Test Trading Mode")
        print("2. 💰 Launch Live Trading Mode")
        print("3. 🌐 Launch Enhanced Web Interface")
        print("4. 🔍 Run Code Audit & Cleanup")
        print("5. ✅ Run System Validation")
        print("6. 📊 View System Status")
        print("7. 🛠️ System Maintenance")
        print("8. 📚 View Documentation")
        print("9. ❌ Exit")
        print("-" * 50)
    
    async def launch_test_trading(self):
        """Launch test trading mode"""
        print("\n🧪 LAUNCHING TEST TRADING MODE...")
        print("="*50)
        
        try:
            # Initialize test engine
            success = await self.test_engine.start_test_trading()
            
            if success:
                print("✅ Test trading started successfully!")
                print("📊 Test trading is now running with simulated trades")
                print("🔗 Access the web interface for real-time monitoring")
                
                # Run for a short demo period
                print("\n⏱️ Running test trading demo for 30 seconds...")
                for i in range(30):
                    await asyncio.sleep(1)
                    if i % 5 == 0:
                        cycle_result = await self.test_engine.process_test_trading_cycle()
                        if cycle_result['success']:
                            print(f"📈 Cycle {i//5 + 1}: Price=${cycle_result['current_price']:.2f}, "
                                  f"Balance=${cycle_result['current_balance']:.2f}")
                
                # Stop test trading
                summary = await self.test_engine.stop_test_trading()
                print(f"\n🏁 Test trading completed!")
                print(f"💰 Final Balance: ${summary['final_balance']:.2f}")
                print(f"📊 Total Trades: {summary['total_trades']}")
                
            else:
                print("❌ Failed to start test trading")
                
        except Exception as e:
            self.logger.error(f"Error in test trading: {e}")
            print(f"❌ Test trading error: {e}")
    
    def launch_live_trading(self):
        """Launch live trading mode"""
        print("\n💰 LAUNCHING LIVE TRADING MODE...")
        print("="*50)
        
        # Check API configuration
        api_key = os.getenv('BINANCE_API_KEY', '')
        testnet = os.getenv('BINANCE_TESTNET', 'True').lower() == 'true'
        
        if not api_key:
            print("⚠️ WARNING: No Binance API key configured")
            print("📝 Please set BINANCE_API_KEY environment variable")
            print("🔧 Running in simulation mode instead")
            return
        
        mode = "TESTNET" if testnet else "LIVE MONEY"
        print(f"🔗 API Mode: {mode}")
        
        if not testnet:
            confirm = input("⚠️ LIVE MONEY MODE - Are you sure? (type 'YES' to confirm): ")
            if confirm != 'YES':
                print("❌ Live trading cancelled")
                return
        
        print("🚀 Starting live trading...")
        print("📊 Use the web interface for monitoring and control")
        
        # Launch real money webapp
        try:
            subprocess.run([sys.executable, 'real_money_trading_webapp.py'], check=True)
        except Exception as e:
            print(f"❌ Error launching live trading: {e}")
    
    def launch_web_interface(self):
        """Launch enhanced web interface"""
        print("\n🌐 LAUNCHING ENHANCED WEB INTERFACE...")
        print("="*50)
        
        try:
            print("🚀 Starting enhanced web interface...")
            print("📊 Features available:")
            print("   • Test Trading Mode")
            print("   • Live Trading Mode") 
            print("   • Validation Tools")
            print("   • Real-time Monitoring")
            print("   • Performance Analytics")
            
            # Launch enhanced webapp
            subprocess.run([sys.executable, '-m', 'streamlit', 'run', 'enhanced_trading_webapp.py'], check=True)
            
        except Exception as e:
            print(f"❌ Error launching web interface: {e}")
    
    def run_code_audit(self):
        """Run comprehensive code audit"""
        print("\n🔍 RUNNING COMPREHENSIVE CODE AUDIT...")
        print("="*50)
        
        try:
            # Run audit
            audit_results = self.auditor.run_full_audit()
            
            # Display summary
            print(f"\n📊 AUDIT SUMMARY:")
            print(f"Files analyzed: {audit_results['files_analyzed']}")
            print(f"Issues found: {audit_results['issues_found']}")
            print(f"Unused files: {len(audit_results['unused_files'])}")
            print(f"Duplicate groups: {len(audit_results['duplicate_files'])}")
            print(f"Inconsistencies: {len(audit_results['inconsistencies'])}")
            
            # Save report
            report_file = self.auditor.save_audit_report()
            print(f"📁 Report saved: {report_file}")
            
            # Ask for cleanup
            if audit_results['unused_files']:
                response = input(f"\n🗑️ Remove {len(audit_results['unused_files'])} unused files? (y/N): ")
                if response.lower() == 'y':
                    cleanup_results = self.auditor.execute_cleanup(dry_run=False)
                    print(f"✅ Cleanup complete. Removed {cleanup_results['files_removed']} files")
                else:
                    print("ℹ️ Cleanup skipped")
            
        except Exception as e:
            print(f"❌ Audit error: {e}")
    
    def run_system_validation(self):
        """Run comprehensive system validation"""
        print("\n✅ RUNNING SYSTEM VALIDATION...")
        print("="*50)
        
        try:
            # Run validation
            validation_results = self.validator.run_comprehensive_validation(self.test_engine)
            
            # Display results
            print(f"\n📊 VALIDATION SUMMARY:")
            print(f"Overall Status: {'✅ PASSED' if validation_results['overall_passed'] else '❌ FAILED'}")
            print(f"Total Tests: {validation_results['summary']['total_validations']}")
            print(f"Passed: {validation_results['summary']['passed_validations']}")
            print(f"Failed: {validation_results['summary']['failed_validations']}")
            print(f"Warnings: {validation_results['summary']['warnings']}")
            
            # Save validation report
            report_file = self.validator.save_validation_report(validation_results)
            print(f"📁 Validation report saved: {report_file}")
            
        except Exception as e:
            print(f"❌ Validation error: {e}")
    
    def view_system_status(self):
        """View current system status"""
        print("\n📊 SYSTEM STATUS...")
        print("="*50)
        
        # Test engine status
        test_status = self.test_engine.get_test_status()
        print(f"🧪 Test Engine: {'Running' if test_status['is_running'] else 'Stopped'}")
        print(f"💰 Test Balance: ${test_status['current_balance']:.2f}")
        print(f"📊 Test Trades: {test_status['closed_trades']}")
        
        # File system status
        print(f"\n📁 File System:")
        print(f"Data Directory: {self.config.DATA_DIR}")
        print(f"Logs Directory: {self.config.LOGS_DIR}")
        print(f"Models Directory: {self.config.MODELS_DIR}")
        
        # API status
        api_key = os.getenv('BINANCE_API_KEY', '')
        testnet = os.getenv('BINANCE_TESTNET', 'True').lower() == 'true'
        print(f"\n🔗 API Configuration:")
        print(f"API Key: {'Configured' if api_key else 'Not configured'}")
        print(f"Mode: {'TESTNET' if testnet else 'LIVE'}")
    
    def system_maintenance(self):
        """System maintenance options"""
        print("\n🛠️ SYSTEM MAINTENANCE...")
        print("="*50)
        print("1. Clear logs")
        print("2. Reset test data")
        print("3. Backup system")
        print("4. Update dependencies")
        print("5. Return to main menu")
        
        choice = input("\nSelect maintenance option: ")
        
        if choice == '1':
            self._clear_logs()
        elif choice == '2':
            self._reset_test_data()
        elif choice == '3':
            self._backup_system()
        elif choice == '4':
            self._update_dependencies()
        else:
            return
    
    def _clear_logs(self):
        """Clear log files"""
        try:
            log_files = [f for f in os.listdir(self.config.LOGS_DIR) if f.endswith('.log')]
            for log_file in log_files:
                os.remove(os.path.join(self.config.LOGS_DIR, log_file))
            print(f"✅ Cleared {len(log_files)} log files")
        except Exception as e:
            print(f"❌ Error clearing logs: {e}")
    
    def _reset_test_data(self):
        """Reset test trading data"""
        try:
            # Reset test engine
            self.test_engine = TestTradingEngine(self.config)
            print("✅ Test data reset successfully")
        except Exception as e:
            print(f"❌ Error resetting test data: {e}")
    
    def _backup_system(self):
        """Create system backup"""
        print("💾 Creating system backup...")
        print("ℹ️ Backup functionality - implementation pending")
    
    def _update_dependencies(self):
        """Update system dependencies"""
        print("📦 Updating dependencies...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt', '--upgrade'], check=True)
            print("✅ Dependencies updated successfully")
        except Exception as e:
            print(f"❌ Error updating dependencies: {e}")
    
    def view_documentation(self):
        """View system documentation"""
        print("\n📚 SYSTEM DOCUMENTATION...")
        print("="*50)
        print("📄 Available documentation:")
        print("• GRID_TRADING_SYSTEM_SPECIFICATION.md")
        print("• REBUILD_IMPLEMENTATION_PLAN.md")
        print("• COMPREHENSIVE_CODE_AUDIT_REPORT.md")
        print("• README files")
        
        doc_choice = input("\nEnter filename to view (or press Enter to return): ")
        if doc_choice and os.path.exists(doc_choice):
            try:
                with open(doc_choice, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"\n📄 {doc_choice}:")
                print("-" * 50)
                print(content[:2000])  # Show first 2000 characters
                if len(content) > 2000:
                    print("\n... (truncated)")
            except Exception as e:
                print(f"❌ Error reading file: {e}")
    
    async def run(self):
        """Main application loop"""
        self.display_welcome_banner()
        
        while True:
            try:
                self.display_main_menu()
                choice = input("Enter your choice (1-9): ").strip()
                
                if choice == '1':
                    await self.launch_test_trading()
                elif choice == '2':
                    self.launch_live_trading()
                elif choice == '3':
                    self.launch_web_interface()
                elif choice == '4':
                    self.run_code_audit()
                elif choice == '5':
                    self.run_system_validation()
                elif choice == '6':
                    self.view_system_status()
                elif choice == '7':
                    self.system_maintenance()
                elif choice == '8':
                    self.view_documentation()
                elif choice == '9':
                    print("\n👋 Goodbye! Thank you for using the Enhanced Trading System")
                    break
                else:
                    print("❌ Invalid choice. Please select 1-9.")
                
                input("\nPress Enter to continue...")
                
            except KeyboardInterrupt:
                print("\n\n🛑 System interrupted by user")
                break
            except Exception as e:
                self.logger.error(f"Unexpected error: {e}")
                print(f"❌ Unexpected error: {e}")


async def main():
    """Main entry point"""
    launcher = EnhancedTradingSystemLauncher()
    await launcher.run()


if __name__ == "__main__":
    asyncio.run(main())
