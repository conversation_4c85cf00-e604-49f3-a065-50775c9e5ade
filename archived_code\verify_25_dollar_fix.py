"""
Verification Script - Confirm $25 Target Profit Fix
Verifies that all references now show $25 instead of $20 for 2.5:1 RR
"""

import os
import sys
import json

def verify_config_file():
    """Verify trading config shows 2.5:1 ratio and $25 target"""
    print("🔍 Checking trading_config.py...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
        from trading_config import TradingConfig
        
        config = TradingConfig()
        
        print(f"   REWARD_RATIO: {config.REWARD_RATIO}")
        print(f"   FIXED_RISK_AMOUNT: ${config.FIXED_RISK_AMOUNT}")
        print(f"   TARGET_PROFIT: ${config.TARGET_PROFIT}")
        
        if config.REWARD_RATIO == 2.5 and config.TARGET_PROFIT == 25.0:
            print("   ✅ Config file correctly shows 2.5:1 ratio and $25 target")
            return True
        else:
            print("   ❌ Config file still has incorrect values")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking config: {e}")
        return False

def verify_webapp_config():
    """Verify webapp ML config shows $25 average win"""
    print("\n🔍 Checking webapp_ml_config.json...")
    
    try:
        config_path = os.path.join('data', 'webapp_ml_config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                ml_config = json.load(f)
            
            avg_win = ml_config.get('avg_win', 0)
            risk_reward_ratio = ml_config.get('risk_reward_ratio', 0)
            
            print(f"   avg_win: ${avg_win}")
            print(f"   risk_reward_ratio: {risk_reward_ratio}:1")
            
            if avg_win == 25.0 and risk_reward_ratio == 2.5:
                print("   ✅ Webapp config correctly shows $25 avg win and 2.5:1 RR")
                return True
            else:
                print("   ❌ Webapp config has incorrect values")
                return False
        else:
            print("   ❌ Webapp config file not found")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking webapp config: {e}")
        return False

def verify_webapp_display():
    """Check if webapp file has been updated"""
    print("\n🔍 Checking simple_enhanced_webapp.py...")
    
    try:
        with open('simple_enhanced_webapp.py', 'r') as f:
            content = f.read()
        
        # Check for $25.00 target profit
        if 'Target Profit", "$25.00"' in content:
            print("   ✅ Webapp shows $25.00 target profit")
            target_profit_ok = True
        else:
            print("   ❌ Webapp still shows incorrect target profit")
            target_profit_ok = False
        
        # Check for $25.00 in trade P&L
        if '${25.00 if i % 3 != 0 else -10.00}' in content:
            print("   ✅ Webapp shows $25.00 in trade P&L")
            trade_pl_ok = True
        else:
            print("   ❌ Webapp still shows incorrect trade P&L")
            trade_pl_ok = False
        
        return target_profit_ok and trade_pl_ok
        
    except Exception as e:
        print(f"   ❌ Error checking webapp file: {e}")
        return False

def main():
    """Main verification function"""
    print("🔧 VERIFYING $25 TARGET PROFIT FIX")
    print("=" * 50)
    
    # Check all components
    config_ok = verify_config_file()
    webapp_config_ok = verify_webapp_config()
    webapp_display_ok = verify_webapp_display()
    
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    if config_ok and webapp_config_ok and webapp_display_ok:
        print("✅ ALL CHECKS PASSED!")
        print("🎯 System correctly configured for 2.5:1 risk-reward ratio")
        print("💰 Target profit: $25 per trade")
        print("⚖️ Risk per trade: $10")
        print("📈 Risk-reward ratio: 2.5:1")
        print("\n🚀 Website will now correctly display $25 profit per trade!")
        return True
    else:
        print("❌ SOME CHECKS FAILED!")
        print("🔧 Please review the issues above")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 VERIFICATION COMPLETE - $25 TARGET PROFIT CONFIRMED!")
    else:
        print("\n⚠️ VERIFICATION FAILED - PLEASE CHECK ISSUES ABOVE")
    
    input("\nPress Enter to exit...")
