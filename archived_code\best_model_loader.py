"""
Best Model Loader
Loads and manages the best trained ML model for live trading
"""

import os
import sys
import json
import logging
import joblib
import numpy as np
from typing import Dict, Optional, Any
from datetime import datetime

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

class BestModelLoader:
    """Loads and manages the best trained model for live trading"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('BestModelLoader')
        self.model = None
        self.scaler = None
        self.model_info = None
        self.model_type = None
        
    def load_best_model(self) -> Optional[Dict]:
        """Load the best performing model"""
        try:
            # Check for different model types in order of preference
            model_candidates = [
                self._load_ensemble_model(),
                self._load_simple_grid_model(),
                self._load_sklearn_model(),
                self._load_pytorch_model()
            ]
            
            best_model = None
            best_score = 0
            
            for candidate in model_candidates:
                if candidate and candidate.get('score', 0) > best_score:
                    best_model = candidate
                    best_score = candidate['score']
            
            if best_model:
                self.model = best_model['model']
                self.scaler = best_model.get('scaler')
                self.model_info = best_model
                self.model_type = best_model['model_type']
                
                self.logger.info(f"✅ Best model loaded: {self.model_type} (Score: {best_score:.4f})")
                return best_model
            else:
                self.logger.warning("⚠️ No trained models found")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to load best model: {e}")
            return None
    
    def _load_ensemble_model(self) -> Optional[Dict]:
        """Load ensemble model (TCN+CNN+PPO)"""
        try:
            model_path = os.path.join(self.config.MODELS_DIR, 'ensemble_model.joblib')
            if os.path.exists(model_path):
                model_data = joblib.load(model_path)
                
                return {
                    'model': model_data.get('model'),
                    'scaler': model_data.get('scaler'),
                    'score': model_data.get('composite_score', 0),
                    'model_type': 'Ensemble (TCN+CNN+PPO)',
                    'trained_on': model_data.get('trained_on', 'Unknown'),
                    'features': model_data.get('features', [])
                }
        except Exception as e:
            self.logger.debug(f"Ensemble model not available: {e}")
            return None
    
    def _load_simple_grid_model(self) -> Optional[Dict]:
        """Load simple grid model"""
        try:
            model_path = os.path.join(self.config.MODELS_DIR, 'simple_grid_model.json')
            if os.path.exists(model_path):
                with open(model_path, 'r') as f:
                    model_data = json.load(f)
                
                # Create a simple predictor function
                def simple_predictor(features):
                    # Simple grid trading logic
                    if isinstance(features, dict):
                        rsi = features.get('rsi', 50)
                        price = features.get('price', 0)
                        sma_20 = features.get('sma_20', price)
                        
                        # Simple decision logic
                        if rsi < 30 and price < sma_20:
                            return 0.8  # Strong buy
                        elif rsi > 70 and price > sma_20:
                            return 0.2  # Strong sell
                        else:
                            return 0.5  # Hold
                    return 0.5
                
                return {
                    'model': simple_predictor,
                    'scaler': None,
                    'score': model_data.get('composite_score', 0.75),
                    'model_type': 'Simple Grid Model',
                    'trained_on': model_data.get('trained_on', datetime.now().isoformat()),
                    'features': ['rsi', 'price', 'sma_20']
                }
        except Exception as e:
            self.logger.debug(f"Simple grid model not available: {e}")
            return None
    
    def _load_sklearn_model(self) -> Optional[Dict]:
        """Load scikit-learn model"""
        try:
            model_path = os.path.join(self.config.MODELS_DIR, 'BTCUSDT_model.joblib')
            scaler_path = os.path.join(self.config.MODELS_DIR, 'BTCUSDT_scaler.joblib')
            
            if os.path.exists(model_path):
                model = joblib.load(model_path)
                scaler = joblib.load(scaler_path) if os.path.exists(scaler_path) else None
                
                # Try to get model score from metadata
                score = 0.7  # Default score
                try:
                    metadata_path = os.path.join(self.config.MODELS_DIR, 'BTCUSDT_metadata.json')
                    if os.path.exists(metadata_path):
                        with open(metadata_path, 'r') as f:
                            metadata = json.load(f)
                            score = metadata.get('score', 0.7)
                except:
                    pass
                
                return {
                    'model': model,
                    'scaler': scaler,
                    'score': score,
                    'model_type': 'Scikit-Learn Model',
                    'trained_on': 'Unknown',
                    'features': ['technical_indicators']
                }
        except Exception as e:
            self.logger.debug(f"Scikit-learn model not available: {e}")
            return None
    
    def _load_pytorch_model(self) -> Optional[Dict]:
        """Load PyTorch model"""
        try:
            model_path = os.path.join(self.config.MODELS_DIR, 'pytorch_model.pth')
            if os.path.exists(model_path):
                try:
                    import torch
                    model = torch.load(model_path, map_location='cpu')
                    
                    return {
                        'model': model,
                        'scaler': None,
                        'score': 0.65,  # Default score
                        'model_type': 'PyTorch Model',
                        'trained_on': 'Unknown',
                        'features': ['sequence_data']
                    }
                except ImportError:
                    self.logger.debug("PyTorch not available")
                    return None
        except Exception as e:
            self.logger.debug(f"PyTorch model not available: {e}")
            return None
    
    def predict(self, features: Dict) -> float:
        """Make prediction using the loaded model"""
        try:
            if not self.model:
                return 0.5  # Neutral prediction
            
            if self.model_type == 'Simple Grid Model':
                return self.model(features)
            
            elif self.model_type == 'Scikit-Learn Model':
                # Convert features to array format
                feature_array = self._features_to_array(features)
                if self.scaler:
                    feature_array = self.scaler.transform([feature_array])
                else:
                    feature_array = [feature_array]
                
                prediction = self.model.predict_proba(feature_array)[0]
                return prediction[1] if len(prediction) > 1 else prediction[0]
            
            elif self.model_type == 'Ensemble (TCN+CNN+PPO)':
                # Use ensemble prediction
                feature_array = self._features_to_array(features)
                if hasattr(self.model, 'predict'):
                    return float(self.model.predict([feature_array])[0])
                else:
                    return 0.5
            
            else:
                return 0.5
                
        except Exception as e:
            self.logger.error(f"Prediction failed: {e}")
            return 0.5  # Safe default
    
    def _features_to_array(self, features: Dict) -> np.ndarray:
        """Convert feature dictionary to array"""
        try:
            # Standard feature order for consistency
            feature_keys = ['price', 'volume', 'rsi', 'sma_20', 'bb_upper', 'bb_lower', 'eth_btc_ratio']
            
            feature_array = []
            for key in feature_keys:
                value = features.get(key, 0)
                if value is None or np.isnan(value):
                    value = 0
                feature_array.append(float(value))
            
            return np.array(feature_array)
            
        except Exception as e:
            self.logger.error(f"Feature conversion failed: {e}")
            return np.zeros(7)  # Return zero array as fallback
    
    def get_model_info(self) -> Dict:
        """Get information about the loaded model"""
        if self.model_info:
            return {
                'model_type': self.model_type,
                'score': self.model_info.get('score', 0),
                'trained_on': self.model_info.get('trained_on', 'Unknown'),
                'features': self.model_info.get('features', []),
                'loaded_at': datetime.now().isoformat(),
                'is_loaded': True
            }
        else:
            return {
                'model_type': 'None',
                'score': 0,
                'trained_on': 'N/A',
                'features': [],
                'loaded_at': 'N/A',
                'is_loaded': False
            }
    
    def validate_model(self) -> bool:
        """Validate that the model is working correctly"""
        try:
            if not self.model:
                return False
            
            # Test prediction with dummy data
            test_features = {
                'price': 50000.0,
                'volume': 1000.0,
                'rsi': 50.0,
                'sma_20': 49000.0,
                'bb_upper': 51000.0,
                'bb_lower': 47000.0,
                'eth_btc_ratio': 0.06
            }
            
            prediction = self.predict(test_features)
            
            # Check if prediction is valid
            if isinstance(prediction, (int, float)) and 0 <= prediction <= 1:
                self.logger.info(f"✅ Model validation successful (test prediction: {prediction:.3f})")
                return True
            else:
                self.logger.error(f"❌ Model validation failed (invalid prediction: {prediction})")
                return False
                
        except Exception as e:
            self.logger.error(f"Model validation failed: {e}")
            return False
    
    def get_prediction_confidence(self, features: Dict) -> Dict:
        """Get prediction with confidence metrics"""
        try:
            prediction = self.predict(features)
            
            # Calculate confidence based on how far from neutral (0.5) the prediction is
            confidence = abs(prediction - 0.5) * 2
            
            # Determine action
            if prediction > 0.6:
                action = 'BUY'
                strength = 'Strong' if prediction > 0.8 else 'Moderate'
            elif prediction < 0.4:
                action = 'SELL'
                strength = 'Strong' if prediction < 0.2 else 'Moderate'
            else:
                action = 'HOLD'
                strength = 'Neutral'
            
            return {
                'prediction': prediction,
                'confidence': confidence,
                'action': action,
                'strength': strength,
                'model_type': self.model_type,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get prediction confidence: {e}")
            return {
                'prediction': 0.5,
                'confidence': 0.0,
                'action': 'HOLD',
                'strength': 'Error',
                'model_type': 'None',
                'timestamp': datetime.now().isoformat()
            }
