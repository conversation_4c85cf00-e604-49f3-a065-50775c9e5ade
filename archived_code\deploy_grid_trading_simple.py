"""
Deploy Grid Trading System - Simplified Version
Deploys grid trading with simple decision algorithm (no ML dependencies)
"""

import os
import sys
import logging
import asyncio
import json
from datetime import datetime
from typing import Dict, Optional

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import grid trading components
from grid_trading_core import GridLevelCalculator, GridTradeManager, GridAction
from grid_feature_engineering import GridFeatureEngineering
from grid_composite_metrics import GridCompositeMetrics

class SimpleGridTradingDeployment:
    """Simplified grid trading deployment without ML dependencies"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('SimpleGridDeployment')
        
        # Initialize components
        self.grid_calculator = GridLevelCalculator(config)
        self.trade_manager = GridTradeManager(config)
        self.feature_engineer = GridFeatureEngineering(config)
        self.metrics_calculator = GridCompositeMetrics(config)
        
        # Deployment state
        self.deployment_active = False
        self.safety_checks_passed = False
        
    async def deploy_simple_grid_trading(self) -> bool:
        """Deploy simplified grid trading system"""
        
        print("🚀 SIMPLIFIED GRID TRADING DEPLOYMENT")
        print("=" * 50)
        
        try:
            # Phase 1: Pre-deployment safety checks
            print("\n🔍 Phase 1: Safety Checks")
            if not await self._run_safety_checks():
                print("❌ Safety checks failed - deployment aborted")
                return False
            
            # Phase 2: System validation
            print("\n🔧 Phase 2: System Validation")
            if not await self._validate_system():
                print("❌ System validation failed - deployment aborted")
                return False
            
            # Phase 3: Final confirmation
            print("\n⚠️ Phase 3: Final Confirmation")
            if not self._get_final_confirmation():
                print("❌ Deployment cancelled by user")
                return False
            
            # Phase 4: Start grid trading
            print("\n🎯 Phase 4: Starting Grid Trading")
            if not await self._start_simple_grid_trading():
                print("❌ Failed to start grid trading")
                return False
            
            print("\n✅ SIMPLIFIED GRID TRADING DEPLOYMENT SUCCESSFUL!")
            print("🎉 Grid trading system is now active!")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Deployment failed: {e}")
            print(f"❌ DEPLOYMENT FAILED: {e}")
            return False
    
    async def _run_safety_checks(self) -> bool:
        """Run comprehensive safety checks"""
        
        checks = [
            ("Configuration Validation", self._check_configuration),
            ("Capital Verification", self._check_capital),
            ("Risk Parameters", self._check_risk_parameters),
            ("System Dependencies", self._check_dependencies)
        ]
        
        all_passed = True
        
        for check_name, check_func in checks:
            print(f"   🔍 {check_name}...", end=" ")
            try:
                result = await check_func() if asyncio.iscoroutinefunction(check_func) else check_func()
                if result:
                    print("✅")
                else:
                    print("❌")
                    all_passed = False
            except Exception as e:
                print(f"❌ Error: {e}")
                all_passed = False
        
        self.safety_checks_passed = all_passed
        return all_passed
    
    def _check_configuration(self) -> bool:
        """Verify configuration matches specification"""
        
        required_values = {
            'INITIAL_CAPITAL': 300,
            'FIXED_RISK_AMOUNT': 10,
            'TARGET_PROFIT': 20,
            'GRID_SPACING': 0.0025,
            'MAX_CONCURRENT_TRADES': 15,
            'COMPOSITE_SCORE_THRESHOLD': 0.85
        }
        
        for attr, expected in required_values.items():
            actual = getattr(self.config, attr, None)
            if actual != expected:
                self.logger.error(f"Config mismatch: {attr} = {actual}, expected {expected}")
                return False
        
        return True
    
    def _check_capital(self) -> bool:
        """Verify starting capital is correct"""
        
        if self.config.INITIAL_CAPITAL != 300:
            self.logger.error(f"Capital should be $300, got ${self.config.INITIAL_CAPITAL}")
            return False
        
        # Check risk management
        max_risk = self.config.MAX_CONCURRENT_TRADES * self.config.FIXED_RISK_AMOUNT
        if max_risk > self.config.INITIAL_CAPITAL * 0.6:  # Max 60% at risk
            self.logger.error(f"Risk too high: ${max_risk} vs ${self.config.INITIAL_CAPITAL}")
            return False
        
        return True
    
    def _check_risk_parameters(self) -> bool:
        """Verify risk parameters are safe"""
        
        # Check risk/reward ratio
        if self.config.TARGET_PROFIT / self.config.FIXED_RISK_AMOUNT != 2.0:
            self.logger.error("Risk/reward ratio should be 2:1")
            return False
        
        # Check grid spacing
        if self.config.GRID_SPACING != 0.0025:
            self.logger.error("Grid spacing should be exactly 0.25%")
            return False
        
        return True
    
    def _check_dependencies(self) -> bool:
        """Check system dependencies"""
        
        try:
            import pandas as pd
            import numpy as np
            return True
        except ImportError as e:
            self.logger.error(f"Missing dependency: {e}")
            return False
    
    async def _validate_system(self) -> bool:
        """Validate system components"""
        
        try:
            # Test grid calculator
            test_price = 50000.0
            grid_levels = self.grid_calculator.calculate_grid_levels(test_price)
            
            if len(grid_levels) == 0:
                self.logger.error("Grid calculator failed")
                return False
            
            print(f"   📊 Grid System: {len(grid_levels)} levels at 0.25% spacing")
            
            # Test trade manager
            position_size = self.trade_manager.calculate_position_size(test_price)
            if position_size <= 0:
                self.logger.error("Trade manager failed")
                return False
            
            print(f"   💰 Trade Manager: ${position_size * test_price:.2f} position size")
            
            # Test feature engineering
            import pandas as pd
            import numpy as np
            
            dates = pd.date_range(start='2024-01-01', periods=50, freq='1min')
            btc_data = pd.DataFrame({
                'open': np.random.uniform(49000, 51000, 50),
                'high': np.random.uniform(50000, 52000, 50),
                'low': np.random.uniform(48000, 50000, 50),
                'close': np.random.uniform(49500, 50500, 50),
                'volume': np.random.uniform(100, 1000, 50)
            }, index=dates)
            
            eth_data = pd.DataFrame({
                'open': np.random.uniform(3200, 3400, 50),
                'high': np.random.uniform(3300, 3500, 50),
                'low': np.random.uniform(3100, 3300, 50),
                'close': np.random.uniform(3200, 3400, 50),
                'volume': np.random.uniform(100, 1000, 50)
            }, index=dates)
            
            features = self.feature_engineer.create_grid_features(btc_data, eth_data)
            if not self.feature_engineer.validate_features(features):
                self.logger.error("Feature engineering failed")
                return False
            
            print(f"   🔧 Feature Engineering: 4 indicators, 9 features")
            
            # Test metrics calculator
            sample_trades = [
                {'profit_loss': 20, 'timestamp': '2024-01-01T10:00:00'},
                {'profit_loss': -10, 'timestamp': '2024-01-01T11:00:00'}
            ]
            
            metrics = self.metrics_calculator.calculate_composite_score(sample_trades, 300)
            if 'composite_score' not in metrics:
                self.logger.error("Metrics calculator failed")
                return False
            
            print(f"   📈 Metrics Calculator: {metrics['composite_score']:.4f} composite score")
            
            return True
            
        except Exception as e:
            self.logger.error(f"System validation failed: {e}")
            return False
    
    def _get_final_confirmation(self) -> bool:
        """Get final user confirmation"""
        
        print("\n⚠️ FINAL CONFIRMATION REQUIRED")
        print("=" * 40)
        print("You are about to start GRID TRADING!")
        print(f"Starting Capital: ${self.config.INITIAL_CAPITAL}")
        print(f"Risk per Trade: ${self.config.FIXED_RISK_AMOUNT}")
        print(f"Grid Spacing: {self.config.GRID_SPACING * 100:.2f}%")
        print("Decision Algorithm: Simple grid-based (no ML)")
        print("\nThis system will:")
        print("- Create grid levels every 0.25%")
        print("- Make BUY/SELL decisions based on grid position")
        print("- Risk $10 per trade for $20 profit target")
        print("- Track performance with 8-metric composite score")
        
        print("\n🚨 WARNING: This is a simplified demo system!")
        print("For production use, train ML models first.")
        
        # Simple confirmation
        confirm = input("\nType 'START' to begin simplified grid trading: ").strip()
        return confirm == 'START'
    
    async def _start_simple_grid_trading(self) -> bool:
        """Start the simplified grid trading system"""
        
        try:
            # Initialize grid trading
            current_price = 50000.0  # Demo price
            grid_levels = self.grid_calculator.calculate_grid_levels(current_price)
            
            self.deployment_active = True
            
            # Log deployment
            deployment_log = {
                'timestamp': datetime.now().isoformat(),
                'system_type': 'simplified_grid_trading',
                'starting_capital': self.config.INITIAL_CAPITAL,
                'risk_per_trade': self.config.FIXED_RISK_AMOUNT,
                'grid_spacing': self.config.GRID_SPACING,
                'max_trades': self.config.MAX_CONCURRENT_TRADES,
                'grid_levels': len(grid_levels)
            }
            
            os.makedirs('logs', exist_ok=True)
            with open('logs/simple_deployment.json', 'w') as f:
                json.dump(deployment_log, f, indent=2)
            
            print(f"   ✅ Grid trading initialized!")
            print(f"   📊 Grid levels: {len(grid_levels)}")
            print(f"   💰 Capital: ${self.config.INITIAL_CAPITAL}")
            print(f"   📁 Logs: logs/simple_deployment.json")
            
            # Start monitoring loop
            await self._run_monitoring_loop()
            
            return True
                
        except Exception as e:
            self.logger.error(f"Failed to start grid trading: {e}")
            return False
    
    async def _run_monitoring_loop(self):
        """Run simple monitoring loop"""
        
        print("\n🔄 GRID TRADING MONITORING")
        print("Press Ctrl+C to stop trading")
        print("=" * 40)
        
        try:
            iteration = 0
            while self.deployment_active:
                iteration += 1
                
                # Simulate price movement
                import numpy as np
                price_change = np.random.uniform(-0.003, 0.003)  # ±0.3%
                current_price = 50000 * (1 + price_change)
                
                print(f"Iteration {iteration}: Price ${current_price:.2f}")
                
                # Simple grid decision logic
                grid_levels = self.grid_calculator.calculate_grid_levels(current_price)
                
                # Check for grid touches (simplified)
                if iteration % 5 == 0:  # Simulate occasional trades
                    action = np.random.choice(['BUY', 'SELL', 'HOLD'], p=[0.3, 0.3, 0.4])
                    if action != 'HOLD':
                        position_size = self.trade_manager.calculate_position_size(current_price)
                        profit_loss = np.random.choice([20, -10], p=[0.7, 0.3])
                        print(f"   🎯 {action} {position_size:.6f} BTC - P&L: ${profit_loss}")
                
                await asyncio.sleep(2)  # 2 second intervals
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping grid trading...")
            self.deployment_active = False
        except Exception as e:
            print(f"\n❌ Error in monitoring loop: {e}")
            self.deployment_active = False
    
    def get_deployment_status(self) -> Dict:
        """Get current deployment status"""
        
        return {
            'deployment_active': self.deployment_active,
            'safety_checks_passed': self.safety_checks_passed,
            'system_type': 'simplified_grid_trading'
        }

async def main():
    """Main deployment function"""
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Initialize config
    config = TradingConfig()
    
    # Create deployment manager
    deployment = SimpleGridTradingDeployment(config)
    
    # Run deployment
    success = await deployment.deploy_simple_grid_trading()
    
    if success:
        print("\n🎉 SIMPLIFIED DEPLOYMENT SUCCESSFUL!")
        print("Grid trading system demonstrated successfully!")
    else:
        print("\n❌ DEPLOYMENT FAILED!")
        print("Review the errors and try again.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
