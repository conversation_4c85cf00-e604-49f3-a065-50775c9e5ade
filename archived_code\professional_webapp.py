"""
Professional Grid Trading Webapp
Designed for seamless real money trading execution
"""

import streamlit as st
import time
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime, timedelta
from professional_trading_executor import get_professional_trading_executor, TradingState, TradeStatus

# Page configuration
st.set_page_config(
    page_title="Professional Grid Trading System",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'trading_executor' not in st.session_state:
    st.session_state.trading_executor = get_professional_trading_executor(300.0)
    
if 'last_update' not in st.session_state:
    st.session_state.last_update = time.time()

def main():
    """Main application"""
    
    # Header
    st.title("💰 Professional Grid Trading System")
    st.markdown("**Real Money Trading Ready** | One Trade at a Time | 2.5:1 Risk-Reward")
    st.markdown("🤖 **Powered by 91% Composite ML Model** | TCN (40%) + CNN (40%) + PPO (20%) | Ensemble_Enhanced_RR")
    
    # Get executor
    executor = st.session_state.trading_executor
    
    # Sidebar controls
    with st.sidebar:
        st.header("🎛️ Trading Controls")
        
        # System status
        status_color = "🟢" if executor.state == TradingState.RUNNING else "🔴"
        st.metric("System Status", f"{status_color} {executor.state.value}")
        
        # Control buttons
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("▶️ Start Trading", disabled=(executor.state == TradingState.RUNNING)):
                if executor.start_trading():
                    st.success("✅ Trading Started!")
                    st.rerun()
                else:
                    st.error("❌ Failed to start trading")
        
        with col2:
            if st.button("⏹️ Stop Trading", disabled=(executor.state == TradingState.STOPPED)):
                if executor.stop_trading():
                    st.success("✅ Trading Stopped!")
                    st.rerun()
                else:
                    st.error("❌ Failed to stop trading")
        
        # Reset button
        if st.button("🔄 Reset System"):
            executor.reset()
            st.success("✅ System Reset!")
            st.rerun()
        
        # Manual cycle trigger
        if st.button("⚡ Force Trading Cycle") and executor.state == TradingState.RUNNING:
            cycle_result = executor.process_trading_cycle()
            st.json(cycle_result)
            st.rerun()
    
    # Main content area
    col1, col2, col3 = st.columns([2, 2, 1])
    
    # Current trade monitoring
    with col1:
        st.subheader("📊 Current Trade Monitor")
        
        if executor.current_trade and executor.current_trade.status == TradeStatus.OPEN:
            trade = executor.current_trade
            
            # Trade details
            st.info(f"**{trade.trade_id}** - {trade.action}")
            
            # Trade metrics
            col_a, col_b = st.columns(2)
            with col_a:
                st.metric("Entry Price", f"${trade.entry_price:.2f}")
                st.metric("Stop Loss", f"${trade.stop_loss:.2f}")
            with col_b:
                st.metric("Take Profit", f"${trade.take_profit:.2f}")
                st.metric("Risk Amount", f"${trade.risk_amount:.2f}")
            
            # Current P&L calculation
            current_price = executor.get_current_price()
            if trade.action == "BUY":
                current_pnl = (current_price - trade.entry_price) * trade.quantity
            else:
                current_pnl = (trade.entry_price - current_price) * trade.quantity
            
            pnl_color = "green" if current_pnl >= 0 else "red"
            st.metric("Current P&L", f"${current_pnl:.2f}", delta=f"${current_pnl:.2f}")
            
            # Trade duration
            duration = datetime.now() - trade.entry_time
            st.metric("Duration", f"{duration.seconds // 60}m {duration.seconds % 60}s")
            
        else:
            st.info("🔍 **No Active Trade**")
            st.write("System is monitoring for trading opportunities...")
    
    # Performance dashboard
    with col2:
        st.subheader("📈 Performance Dashboard")
        
        perf = executor.get_performance_summary()
        
        # Key metrics
        col_x, col_y = st.columns(2)
        with col_x:
            st.metric("Balance", f"${perf['current_balance']:.2f}")
            st.metric("Total Trades", perf['total_trades'])
        with col_y:
            st.metric("Total Profit", f"${perf['total_profit']:.2f}")
            st.metric("Win Rate", f"{perf['win_rate']:.1%}")
        
        # Return percentage
        return_color = "green" if perf['return_pct'] >= 0 else "red"
        st.metric("Return", f"{perf['return_pct']:.2f}%", delta=f"{perf['return_pct']:.2f}%")
        
        # Trade breakdown
        if perf['total_trades'] > 0:
            st.write("**Trade Breakdown:**")
            st.write(f"✅ Winning: {perf['winning_trades']}")
            st.write(f"❌ Losing: {perf['losing_trades']}")
    
    # Live price monitor
    with col3:
        st.subheader("💹 Live Price")
        
        current_price = executor.get_current_price()
        st.metric("BTC/USDT", f"${current_price:,.2f}")
        
        # Price update time
        st.caption(f"Updated: {datetime.now().strftime('%H:%M:%S')}")
    
    # Trading cycle execution
    if executor.state == TradingState.RUNNING:
        # Auto-execute trading cycle
        cycle_result = executor.process_trading_cycle()
        
        # Show cycle result in expander
        with st.expander("🔄 Latest Trading Cycle", expanded=False):
            st.json(cycle_result)
    
    # Trade history
    st.subheader("📋 Trade History")
    
    if executor.trade_history:
        # Convert to DataFrame
        history_data = []
        for trade in executor.trade_history:
            history_data.append({
                'Trade ID': trade.trade_id,
                'Action': trade.action,
                'Entry Price': f"${trade.entry_price:.2f}",
                'Exit Price': f"${trade.exit_price:.2f}" if trade.exit_price else "N/A",
                'P&L': f"${trade.profit_loss:.2f}" if trade.profit_loss else "N/A",
                'Status': trade.status.value,
                'Exit Reason': trade.exit_reason or "N/A",
                'Entry Time': trade.entry_time.strftime('%H:%M:%S'),
                'Exit Time': trade.exit_time.strftime('%H:%M:%S') if trade.exit_time else "N/A"
            })
        
        df = pd.DataFrame(history_data)
        st.dataframe(df, use_container_width=True)
        
        # Equity curve
        if len(executor.trade_history) > 1:
            st.subheader("📊 Equity Curve")
            
            # Calculate cumulative P&L
            cumulative_pnl = []
            balance = executor.initial_balance
            times = []
            
            for trade in executor.trade_history:
                if trade.profit_loss is not None:
                    balance += trade.profit_loss
                    cumulative_pnl.append(balance)
                    times.append(trade.exit_time)
            
            if cumulative_pnl:
                fig = go.Figure()
                fig.add_trace(go.Scatter(
                    x=times,
                    y=cumulative_pnl,
                    mode='lines+markers',
                    name='Account Balance',
                    line=dict(color='green', width=2)
                ))
                
                fig.update_layout(
                    title="Account Balance Over Time",
                    xaxis_title="Time",
                    yaxis_title="Balance ($)",
                    height=400
                )
                
                st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No trades executed yet. Start trading to see history.")
    
    # Auto-refresh every 5 seconds when trading
    if executor.state == TradingState.RUNNING:
        time.sleep(2)  # Small delay to prevent too frequent updates
        st.rerun()

# System information
with st.expander("ℹ️ System Information", expanded=False):
    st.write("**Professional Grid Trading System**")
    st.write("🤖 **ML Model Integration:**")
    st.write("- **91% Composite Score** - Ensemble_Enhanced_RR model")
    st.write("- **TCN (40%)** - Temporal Convolutional Network for trend analysis")
    st.write("- **CNN (40%)** - Convolutional Neural Network for pattern recognition")
    st.write("- **PPO (20%)** - Proximal Policy Optimization for risk management")
    st.write("- **Grid-based decisions** - 0.25% spacing with ML confidence >75%")
    st.write("")
    st.write("⚙️ **Trading Features:**")
    st.write("- **One trade at a time** - Proper risk management")
    st.write("- **2.5:1 Risk-Reward** - 0.1% risk, 0.25% reward")
    st.write("- **Real-time monitoring** - Automatic trade management")
    st.write("- **CSV logging** - Complete trade records")
    st.write("- **Thread-safe** - Ready for real money trading")
    st.write("- **Professional design** - Suitable for live trading")

if __name__ == "__main__":
    main()
