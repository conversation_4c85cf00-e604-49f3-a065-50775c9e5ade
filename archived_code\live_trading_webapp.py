"""
Professional Live Trading Web Interface
Flask-based dashboard for real money Binance cross margin trading
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List
from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_socketio import Socket<PERSON>, emit
import threading
import time

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
try:
    from trading_config import TradingConfig
except ImportError:
    # Create dummy config
    class TradingConfig:
        def __init__(self):
            self.INITIAL_CAPITAL = 300
            self.FIXED_RISK_AMOUNT = 10

# Import trading components
try:
    from simple_trading_executor import get_trading_executor
    from live_binance_cross_margin import LiveBinanceCrossMarginTrader
    from best_model_loader import BestModelLoader
    from grid_trading_core import GridLevelCalculator, GridAction
except ImportError as e:
    print(f"⚠️ Some modules not available: {e}")
    # Create dummy classes for now
    class LiveBinanceCrossMarginTrader:
        def __init__(self, *args, **kwargs): pass
        def test_connection(self): return False
        def is_connected(self): return False

    class BestModelLoader:
        def __init__(self, *args, **kwargs):
            self.model = None
        def load_best_model(self): return None

    class GridLevelCalculator:
        def __init__(self, *args, **kwargs): pass
        def calculate_grid_levels(self, price): return {}

    class GridAction:
        BUY = "BUY"
        SELL = "SELL"
        HOLD = "HOLD"

    # Import simple trading executor as fallback
    try:
        from simple_trading_executor import get_trading_executor
    except ImportError:
        def get_trading_executor(*args, **kwargs):
            return None

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'trading_system_secret_key_2024'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global trading system
trading_system = None
config = TradingConfig()

class LiveTradingWebApp:
    """Professional live trading web application with corrected grid trading logic"""

    def __init__(self):
        self.config = TradingConfig()
        self.trader = None
        self.model_loader = BestModelLoader(self.config)
        self.grid_calculator = GridLevelCalculator(self.config)

        # Initialize the corrected trading executor
        self.trading_executor = get_trading_executor()
        if self.trading_executor:
            self.trading_executor.reset()  # Start fresh

        self.is_running = False
        self.trading_mode = 'production'  # 'testing' or 'production'
        self.session_data = {
            'trades': [],
            'equity_history': [self.config.INITIAL_CAPITAL],
            'balance_history': [],
            'performance_metrics': {},
            'current_positions': {},
            'grid_levels': {},
            'start_time': None,
            'cycle_count': 0
        }

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('LiveTradingWebApp')

    def initialize_trader(self, api_key: str, secret_key: str) -> bool:
        """Initialize Binance cross margin trader"""
        try:
            self.trader = LiveBinanceCrossMarginTrader(
                api_key=api_key,
                secret_key=secret_key,
                config=self.config
            )

            # Test connection
            if self.trader.test_connection():
                self.logger.info("✅ Binance connection established")
                return True
            else:
                self.logger.error("❌ Failed to connect to Binance")
                return False

        except Exception as e:
            self.logger.error(f"Failed to initialize trader: {e}")
            return False

    def load_best_model(self) -> bool:
        """Load the best trained model"""
        try:
            model_info = self.model_loader.load_best_model()
            if model_info:
                self.logger.info(f"✅ Best model loaded: {model_info['model_type']} - Score: {model_info['score']:.4f}")
                return True
            else:
                self.logger.warning("⚠️ No trained model found - using fallback logic")
                return False
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            return False

    def start_trading(self) -> bool:
        """Start live trading"""
        if not self.trader:
            return False

        try:
            self.is_running = True
            self.session_data['start_time'] = datetime.now()

            # Start trading thread
            trading_thread = threading.Thread(target=self._trading_loop, daemon=True)
            trading_thread.start()

            self.logger.info("🚀 Live trading started")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start trading: {e}")
            self.is_running = False
            return False

    def stop_trading(self):
        """Stop live trading"""
        self.is_running = False
        self.logger.info("🛑 Live trading stopped")

    def _trading_loop(self):
        """Main trading loop implementing corrected grid trading logic"""
        while self.is_running:
            try:
                self.session_data['cycle_count'] += 1
                cycle_num = self.session_data['cycle_count']

                self.logger.info(f"\n🔄 === TRADING CYCLE {cycle_num} START ===")

                if self.trading_executor:
                    # Use corrected trading executor with proper grid logic
                    testing_mode = (self.trading_mode == 'testing')

                    # Execute one complete trading cycle
                    cycle_result = self.trading_executor.process_trading_cycle(
                        risk_mode='fixed',
                        testing_mode=testing_mode
                    )

                    # Update session data from cycle results
                    self._update_from_cycle_result(cycle_result)

                    self.logger.info(f"🎯 Cycle {cycle_num} Complete: "
                                   f"Executed={cycle_result['executed_trades']}, "
                                   f"Closed={cycle_result['closed_trades']}, "
                                   f"Balance=${cycle_result['current_balance']:.2f}")

                else:
                    # Fallback to original logic if executor not available
                    current_price = self.trader.get_current_price('BTCUSDT') if self.trader else 100000
                    if current_price:
                        grid_levels = self.grid_calculator.calculate_grid_levels(current_price)
                        self.session_data['grid_levels'] = grid_levels
                        self._check_trading_opportunities(current_price, grid_levels)

                # Update session data
                self._update_session_data()

                # Emit real-time updates
                socketio.emit('trading_update', self._get_realtime_update())

                time.sleep(10)  # 10-second cycle interval (as per grid trading spec)

            except Exception as e:
                self.logger.error(f"Error in trading loop: {e}")
                time.sleep(5)

    def _check_trading_opportunities(self, current_price: float, grid_levels: Dict):
        """Check for trading opportunities at grid levels"""
        try:
            for level_id, level in grid_levels.items():
                # Check if price touched this grid level
                if abs(current_price - level.price) / level.price < 0.001:  # 0.1% tolerance

                    # Get trading decision from best model
                    action = self._get_trading_decision(current_price, level)

                    if action != GridAction.HOLD:
                        self._execute_trade(action, current_price, level)

        except Exception as e:
            self.logger.error(f"Error checking trading opportunities: {e}")

    def _get_trading_decision(self, current_price: float, grid_level) -> GridAction:
        """Get trading decision from best model"""
        try:
            if self.model_loader.model:
                # Use best trained model for decision
                features = self.trader.get_market_features()
                prediction = self.model_loader.predict(features)

                # Convert prediction to grid action
                if prediction > 0.6:  # Strong buy signal
                    return GridAction.BUY
                elif prediction < 0.4:  # Strong sell signal
                    return GridAction.SELL
                else:
                    return GridAction.HOLD
            else:
                # Fallback logic
                if current_price < grid_level.price:
                    return GridAction.BUY
                else:
                    return GridAction.SELL

        except Exception as e:
            self.logger.error(f"Error getting trading decision: {e}")
            return GridAction.HOLD

    def _execute_trade(self, action: GridAction, current_price: float, grid_level):
        """Execute real trade on Binance"""
        try:
            # Calculate position size
            position_size = self.config.FIXED_RISK_AMOUNT / current_price

            # Execute trade
            if action == GridAction.BUY:
                order = self.trader.place_cross_margin_buy_order('BTCUSDT', position_size)
            else:
                order = self.trader.place_cross_margin_sell_order('BTCUSDT', position_size)

            if order:
                # Record trade
                trade_record = {
                    'timestamp': datetime.now().isoformat(),
                    'action': action.name,
                    'symbol': 'BTCUSDT',
                    'price': current_price,
                    'quantity': position_size,
                    'order_id': order.get('orderId'),
                    'grid_level': grid_level.price,
                    'status': 'EXECUTED'
                }

                self.session_data['trades'].append(trade_record)
                self.logger.info(f"🎯 TRADE EXECUTED: {action.name} {position_size:.6f} BTC @ ${current_price:.2f}")

        except Exception as e:
            self.logger.error(f"Failed to execute trade: {e}")

    def _update_from_cycle_result(self, cycle_result: Dict):
        """Update session data from trading cycle results"""
        try:
            if cycle_result['success']:
                # Update equity history
                current_balance = cycle_result['current_balance']
                self.session_data['equity_history'].append(current_balance)
                self.session_data['balance_history'].append(current_balance)

                # Add new trades to session
                if 'new_trades' in cycle_result:
                    for trade in cycle_result['new_trades']:
                        trade_record = {
                            'timestamp': datetime.now().isoformat(),
                            'trade_id': trade.trade_id,
                            'action': trade.action,
                            'entry_price': trade.entry_price,
                            'risk_amount': trade.risk_amount,
                            'profit_target': trade.profit_target,
                            'take_profit_price': trade.take_profit_price,
                            'stop_loss_price': trade.stop_loss_price,
                            'status': 'OPEN'
                        }
                        self.session_data['trades'].append(trade_record)

                # Update closed trades
                if 'closed_today' in cycle_result:
                    for trade in cycle_result['closed_today']:
                        # Find and update the trade record
                        for trade_record in self.session_data['trades']:
                            if trade_record.get('trade_id') == trade.trade_id:
                                trade_record.update({
                                    'exit_price': trade.exit_price,
                                    'exit_reason': trade.exit_reason,
                                    'pnl': trade.pnl,
                                    'status': 'CLOSED',
                                    'exit_timestamp': datetime.now().isoformat()
                                })
                                break

                # Update performance metrics
                self.session_data['performance_metrics'] = {
                    'total_trades': cycle_result['total_trades'],
                    'win_rate': cycle_result['win_rate'],
                    'total_pnl': cycle_result['total_pnl'],
                    'open_trades': cycle_result['open_trades'],
                    'current_price': cycle_result['current_price']
                }

        except Exception as e:
            self.logger.error(f"Error updating from cycle result: {e}")

    def _get_realtime_update(self) -> Dict:
        """Get real-time update data for websocket"""
        try:
            if self.trading_executor:
                current_price = self.trading_executor.get_current_price()
                current_balance = self.trading_executor.balance
                open_trades = len(self.trading_executor.open_trades)
                total_trades = self.trading_executor.total_trades
                total_pnl = current_balance - self.trading_executor.initial_balance
            else:
                current_price = 100000  # Fallback
                current_balance = self.session_data['equity_history'][-1] if self.session_data['equity_history'] else 300
                open_trades = 0
                total_trades = len(self.session_data['trades'])
                total_pnl = current_balance - 300

            return {
                'price': current_price,
                'equity': current_balance,
                'trades_count': total_trades,
                'open_trades': open_trades,
                'total_pnl': total_pnl,
                'is_running': self.is_running,
                'trading_mode': self.trading_mode,
                'cycle_count': self.session_data['cycle_count'],
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error getting real-time update: {e}")
            return {
                'price': 100000,
                'equity': 300,
                'trades_count': 0,
                'open_trades': 0,
                'total_pnl': 0,
                'is_running': self.is_running,
                'trading_mode': self.trading_mode,
                'cycle_count': 0,
                'timestamp': datetime.now().isoformat()
            }

    def _update_session_data(self):
        """Update session performance data"""
        try:
            if self.trader:
                # Get current account balance
                balance = self.trader.get_cross_margin_balance()
                if balance:
                    total_balance = balance.get('totalAssetOfBtc', 0) * self.trader.get_current_price('BTCUSDT')
                    self.session_data['balance_history'].append(total_balance)
                    self.session_data['equity_history'].append(total_balance)

                # Update positions
                positions = self.trader.get_cross_margin_positions()
                self.session_data['current_positions'] = positions

        except Exception as e:
            self.logger.error(f"Error updating session data: {e}")

    def get_status(self) -> Dict:
        """Get current trading status"""
        try:
            if self.trading_executor:
                current_balance = self.trading_executor.balance
                total_trades = self.trading_executor.total_trades
                open_trades = len(self.trading_executor.open_trades)
                total_pnl = current_balance - self.trading_executor.initial_balance
                win_rate = self.trading_executor.winning_trades / max(self.trading_executor.total_trades, 1)
                current_price = self.trading_executor.get_current_price()
            else:
                current_balance = self.session_data['equity_history'][-1] if self.session_data['equity_history'] else self.config.INITIAL_CAPITAL
                total_trades = len(self.session_data['trades'])
                open_trades = len([t for t in self.session_data['trades'] if t.get('status') == 'OPEN'])
                total_pnl = current_balance - self.config.INITIAL_CAPITAL
                win_rate = 0.0
                current_price = 100000

            return {
                'is_running': self.is_running,
                'trading_mode': self.trading_mode,
                'start_time': self.session_data['start_time'].isoformat() if self.session_data['start_time'] else None,
                'total_trades': total_trades,
                'open_trades': open_trades,
                'current_equity': current_balance,
                'total_pnl': total_pnl,
                'win_rate': win_rate,
                'current_price': current_price,
                'cycle_count': self.session_data['cycle_count'],
                'model_loaded': self.model_loader.model is not None,
                'trader_connected': self.trader is not None and self.trader.is_connected(),
                'executor_loaded': self.trading_executor is not None
            }
        except Exception as e:
            self.logger.error(f"Error getting status: {e}")
            return {
                'is_running': False,
                'trading_mode': 'testing',
                'error': str(e)
            }

    def set_trading_mode(self, mode: str) -> bool:
        """Set trading mode (testing or production)"""
        if mode in ['testing', 'production']:
            self.trading_mode = mode
            self.logger.info(f"🎯 Trading mode set to: {mode}")
            return True
        return False

# Initialize global trading system
trading_system = LiveTradingWebApp()

@app.route('/')
def dashboard():
    """Main dashboard"""
    return render_template('dashboard.html',
                         config=config,
                         status=trading_system.get_status())

@app.route('/api/status')
def api_status():
    """Get trading status"""
    return jsonify(trading_system.get_status())

@app.route('/api/configure', methods=['POST'])
def api_configure():
    """Configure API keys"""
    data = request.get_json()
    api_key = data.get('api_key')
    secret_key = data.get('secret_key')

    if trading_system.initialize_trader(api_key, secret_key):
        trading_system.load_best_model()
        return jsonify({'success': True, 'message': 'Configuration successful'})
    else:
        return jsonify({'success': False, 'message': 'Configuration failed'})

@app.route('/api/start', methods=['POST'])
def api_start():
    """Start trading"""
    if trading_system.start_trading():
        return jsonify({'success': True, 'message': 'Trading started'})
    else:
        return jsonify({'success': False, 'message': 'Failed to start trading'})

@app.route('/api/stop', methods=['POST'])
def api_stop():
    """Stop trading"""
    trading_system.stop_trading()
    return jsonify({'success': True, 'message': 'Trading stopped'})

@app.route('/api/trades')
def api_trades():
    """Get trade history"""
    return jsonify(trading_system.session_data['trades'])

@app.route('/api/performance')
def api_performance():
    """Get performance data"""
    return jsonify({
        'equity_history': trading_system.session_data['equity_history'],
        'balance_history': trading_system.session_data['balance_history'],
        'performance_metrics': trading_system.session_data['performance_metrics']
    })

@app.route('/api/mode', methods=['POST'])
def api_set_mode():
    """Set trading mode (testing or production)"""
    data = request.get_json()
    mode = data.get('mode', 'testing')

    if trading_system.set_trading_mode(mode):
        return jsonify({'success': True, 'message': f'Trading mode set to {mode}', 'mode': mode})
    else:
        return jsonify({'success': False, 'message': 'Invalid trading mode'})

@app.route('/api/reset', methods=['POST'])
def api_reset():
    """Reset trading system"""
    try:
        if trading_system.trading_executor:
            trading_system.trading_executor.reset()

        # Reset session data
        trading_system.session_data = {
            'trades': [],
            'equity_history': [trading_system.config.INITIAL_CAPITAL],
            'balance_history': [],
            'performance_metrics': {},
            'current_positions': {},
            'grid_levels': {},
            'start_time': None,
            'cycle_count': 0
        }

        return jsonify({'success': True, 'message': 'Trading system reset successfully'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Reset failed: {str(e)}'})

@app.route('/api/grid_levels')
def api_grid_levels():
    """Get current grid levels"""
    try:
        if trading_system.trading_executor:
            current_price = trading_system.trading_executor.get_current_price()
            grid_base = getattr(trading_system.trading_executor, 'grid_base_price', current_price)

            # Calculate grid levels
            grid_levels = []
            for i in range(-5, 6):
                level_price = grid_base * (1 + (i * 0.0025))  # 0.25% spacing
                grid_levels.append({
                    'level': i,
                    'price': level_price,
                    'distance_percent': i * 0.25,
                    'is_current': abs(current_price - level_price) / level_price < 0.001
                })

            return jsonify({
                'success': True,
                'current_price': current_price,
                'grid_base_price': grid_base,
                'grid_levels': grid_levels
            })
        else:
            return jsonify({'success': False, 'message': 'Trading executor not available'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error getting grid levels: {str(e)}'})

@app.route('/api/open_trades')
def api_open_trades():
    """Get current open trades"""
    try:
        if trading_system.trading_executor:
            open_trades = []
            for trade_id, trade in trading_system.trading_executor.open_trades.items():
                open_trades.append({
                    'trade_id': trade_id,
                    'action': trade.action,
                    'entry_price': trade.entry_price,
                    'take_profit_price': trade.take_profit_price,
                    'stop_loss_price': trade.stop_loss_price,
                    'risk_amount': trade.risk_amount,
                    'profit_target': trade.profit_target,
                    'entry_time': trade.entry_time.isoformat(),
                    'age_seconds': (datetime.now() - trade.entry_time).total_seconds()
                })

            return jsonify({
                'success': True,
                'open_trades': open_trades,
                'count': len(open_trades)
            })
        else:
            return jsonify({'success': False, 'message': 'Trading executor not available'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error getting open trades: {str(e)}'})

if __name__ == '__main__':
    print("🚀 Starting Live Trading Web Application...")
    print("📊 Dashboard: http://localhost:5000")
    print("💰 Ready for real money cross margin trading!")

    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
