{"timestamp": "2025-06-08T19:18:42.732407", "duration_seconds": 2.85513, "overall_status": "CRITICAL", "total_checks": 26, "issues_count": 4, "warnings_count": 5, "results": {"webapp_connectivity": "HEALTHY", "template_correct": "HEALTHY", "api__api_trading_status": "HEALTHY", "api__api_health_check": "HEALTHY", "api__api_preflight_check": "HEALTHY", "api__api_recent_trades": "HEALTHY", "binance_connection": "ERROR", "account_balance": "ERROR", "price_data": "HEALTHY", "database_file": "HEALTHY", "trades_table": "HEALTHY", "recent_trades_count": 0, "recent_trades_api": "ERROR", "trading_engine_running": "HEALTHY", "model_configuration": "HEALTHY", "model_metrics": "HEALTHY", "risk_management": "HEALTHY", "dashboard_ui": "ERROR", "real_time_updates": "WARNING", "health_check_interaction": "HEALTHY", "preflight_interaction": "HEALTHY", "response_time__": 31.**************, "response_time__api_trading_status": 29.***************, "response_time__api_health_check": 44.***************, "error_handling_404": "HEALTHY", "data_accuracy": "HEALTHY"}, "issues": ["Binance API connection failed", "Account balance not available", "Recent trades API returned invalid data format", "Missing UI elements: Status section, Balance section"], "warnings": ["Missing risk parameter: max_open_trades", "Missing risk parameter: risk_per_trade", "Missing risk parameter: leverage", "Real-time timestamps not updating", "Missing data field: account_balance"], "recommendations": ["Check Binance API credentials and network connectivity"]}