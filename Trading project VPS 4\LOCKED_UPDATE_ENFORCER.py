#!/usr/bin/env python3
"""
🔒 LOCKED UPDATE ENFORCER - PREVENTS TIME-WASTING ITERATIONS
===========================================================
This system LOCKS IN the mandatory update protocol and prevents any updates
that don't follow the proper validation sequence.
"""

import os
import json
import hashlib
from datetime import datetime
from typing import Dict, List, Optional
import functools

class UpdateEnforcer:
    """LOCKED SYSTEM - Enforces mandatory update protocol"""
    
    def __init__(self):
        self.protocol_file = "MANDATORY_UPDATE_PROTOCOL.py"
        self.validation_required = True
        self.current_session = None
        self.validation_state = {
            'phase_1_complete': False,
            'phase_2_complete': False,
            'phase_3_complete': False,
            'phase_4_complete': False,
            'phase_5_complete': False
        }
    
    def require_validation(func):
        """Decorator that enforces validation protocol"""
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            if not self.validation_required:
                return func(self, *args, **kwargs)
            
            if not self.current_session:
                raise Exception("❌ VALIDATION REQUIRED: Must run phase_1_pre_change_validation() first")
            
            return func(self, *args, **kwargs)
        return wrapper
    
    def lock_file_integrity(self):
        """Verify critical files haven't been corrupted"""
        
        critical_files = {
            "bitcoin_freedom_best_model.py": None,
            "templates/best_composite_score_dashboard.html": None,
            "models/webapp_best_model_metadata.json": None,
            "MANDATORY_UPDATE_PROTOCOL.py": None
        }
        
        integrity_report = {
            'timestamp': datetime.now().isoformat(),
            'files_checked': len(critical_files),
            'files_intact': 0,
            'corrupted_files': [],
            'missing_files': []
        }
        
        for file_path in critical_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if len(content) > 100:  # Basic sanity check
                            integrity_report['files_intact'] += 1
                        else:
                            integrity_report['corrupted_files'].append(file_path)
                except Exception as e:
                    integrity_report['corrupted_files'].append(f"{file_path}: {e}")
            else:
                integrity_report['missing_files'].append(file_path)
        
        return integrity_report
    
    def create_change_checkpoint(self, description: str) -> str:
        """Create a checkpoint before making changes"""
        
        checkpoint_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        checkpoint_dir = f"checkpoints/checkpoint_{checkpoint_id}"
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Save current state
        checkpoint_data = {
            'checkpoint_id': checkpoint_id,
            'description': description,
            'timestamp': datetime.now().isoformat(),
            'files_backed_up': [],
            'integrity_check': self.lock_file_integrity()
        }
        
        # Backup critical files
        critical_files = [
            "bitcoin_freedom_best_model.py",
            "templates/best_composite_score_dashboard.html", 
            "models/webapp_best_model_metadata.json"
        ]
        
        for file_path in critical_files:
            if os.path.exists(file_path):
                backup_path = f"{checkpoint_dir}/{os.path.basename(file_path)}"
                try:
                    with open(file_path, 'r', encoding='utf-8') as src:
                        with open(backup_path, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
                    checkpoint_data['files_backed_up'].append(file_path)
                except Exception as e:
                    print(f"⚠️ Backup failed for {file_path}: {e}")
        
        # Save checkpoint metadata
        with open(f"{checkpoint_dir}/checkpoint_metadata.json", 'w') as f:
            json.dump(checkpoint_data, f, indent=2)
        
        print(f"📋 Checkpoint created: {checkpoint_id}")
        return checkpoint_id
    
    def validate_change_request(self, change_type: str, target_files: List[str]) -> Dict:
        """Validate that a change request is safe and follows protocol"""
        
        validation_result = {
            'approved': False,
            'reason': '',
            'requirements': [],
            'risks': [],
            'checkpoint_required': True
        }
        
        # Check if files exist
        missing_files = [f for f in target_files if not os.path.exists(f)]
        if missing_files:
            validation_result['reason'] = f"Missing files: {missing_files}"
            return validation_result
        
        # Check file integrity
        integrity = self.lock_file_integrity()
        if integrity['corrupted_files'] or integrity['missing_files']:
            validation_result['reason'] = f"File integrity issues: {integrity}"
            return validation_result
        
        # Determine requirements based on change type
        if change_type == "UI_UPDATE":
            validation_result['requirements'] = [
                "Phase 1: Pre-change validation",
                "Phase 2: Incremental change validation", 
                "Phase 3: Webapp restart validation",
                "Phase 4: Change verification",
                "Phase 5: Final validation"
            ]
            validation_result['risks'] = [
                "UI may not reflect changes without restart",
                "JavaScript may need cache clearing",
                "Template changes require webapp reload"
            ]
        
        elif change_type == "BACKEND_UPDATE":
            validation_result['requirements'] = [
                "Phase 1: Pre-change validation",
                "Phase 2: Incremental change validation",
                "Phase 3: Webapp restart validation (MANDATORY)",
                "Phase 4: Change verification", 
                "Phase 5: Final validation"
            ]
            validation_result['risks'] = [
                "Backend changes require webapp restart",
                "API endpoints may break",
                "Database connections may be affected"
            ]
        
        elif change_type == "METADATA_UPDATE":
            validation_result['requirements'] = [
                "Phase 1: Pre-change validation",
                "Phase 2: File validation",
                "Phase 3: Webapp restart validation",
                "Phase 4: Data verification",
                "Phase 5: Final validation"
            ]
            validation_result['risks'] = [
                "Metadata changes may not load without restart",
                "JSON format errors can break system",
                "Cache may serve old data"
            ]
        
        validation_result['approved'] = True
        validation_result['reason'] = "Change request approved with requirements"
        return validation_result

class LockedUpdateWorkflow:
    """LOCKED WORKFLOW - Must be followed for all updates"""
    
    def __init__(self):
        self.enforcer = UpdateEnforcer()
        self.current_checkpoint = None
        self.workflow_state = "READY"
        
    def step_1_validate_request(self, change_description: str, change_type: str, target_files: List[str]) -> bool:
        """STEP 1: Validate the change request"""
        
        print("🔒 STEP 1: VALIDATING CHANGE REQUEST")
        print("=" * 50)
        print(f"Description: {change_description}")
        print(f"Type: {change_type}")
        print(f"Files: {target_files}")
        
        validation = self.enforcer.validate_change_request(change_type, target_files)
        
        if not validation['approved']:
            print(f"❌ CHANGE REQUEST REJECTED: {validation['reason']}")
            return False
        
        print("✅ CHANGE REQUEST APPROVED")
        print("📋 Requirements:")
        for req in validation['requirements']:
            print(f"   - {req}")
        
        print("⚠️ Risks:")
        for risk in validation['risks']:
            print(f"   - {risk}")
        
        self.workflow_state = "VALIDATED"
        return True
    
    def step_2_create_checkpoint(self, description: str) -> bool:
        """STEP 2: Create safety checkpoint"""
        
        if self.workflow_state != "VALIDATED":
            print("❌ Must complete step 1 first")
            return False
        
        print("\n🔒 STEP 2: CREATING SAFETY CHECKPOINT")
        print("=" * 50)
        
        self.current_checkpoint = self.enforcer.create_change_checkpoint(description)
        self.workflow_state = "CHECKPOINT_CREATED"
        return True
    
    def step_3_execute_protocol(self) -> bool:
        """STEP 3: Execute mandatory update protocol"""
        
        if self.workflow_state != "CHECKPOINT_CREATED":
            print("❌ Must complete steps 1-2 first")
            return False
        
        print("\n🔒 STEP 3: EXECUTING MANDATORY PROTOCOL")
        print("=" * 50)
        print("⚠️ CRITICAL: Follow MANDATORY_UPDATE_PROTOCOL.py exactly")
        print("⚠️ NO SHORTCUTS - ALL 5 PHASES REQUIRED")
        
        # Import and run the protocol
        try:
            from MANDATORY_UPDATE_PROTOCOL import MandatoryUpdateProtocol
            
            protocol = MandatoryUpdateProtocol()
            
            # This is where the actual protocol execution happens
            print("📋 Protocol loaded - ready for execution")
            print("📋 Use protocol.phase_1_pre_change_validation() to start")
            
            self.protocol = protocol
            self.workflow_state = "PROTOCOL_READY"
            return True
            
        except Exception as e:
            print(f"❌ Protocol loading failed: {e}")
            return False
    
    def step_4_verify_completion(self) -> bool:
        """STEP 4: Verify all phases completed successfully"""
        
        if self.workflow_state != "PROTOCOL_READY":
            print("❌ Must complete steps 1-3 first")
            return False
        
        print("\n🔒 STEP 4: VERIFYING COMPLETION")
        print("=" * 50)
        
        # Check if all validation phases were completed
        if hasattr(self, 'protocol'):
            if len(self.protocol.validation_log) >= 5:  # Should have entries from all phases
                print("✅ All protocol phases appear to have been executed")
                self.workflow_state = "COMPLETED"
                return True
            else:
                print(f"❌ Incomplete protocol execution: {len(self.protocol.validation_log)} steps logged")
                return False
        else:
            print("❌ Protocol not properly initialized")
            return False
    
    def emergency_rollback(self) -> bool:
        """Emergency rollback to checkpoint"""
        
        if not self.current_checkpoint:
            print("❌ No checkpoint available for rollback")
            return False
        
        print("🚨 EMERGENCY ROLLBACK INITIATED")
        print("=" * 50)
        
        checkpoint_dir = f"checkpoints/checkpoint_{self.current_checkpoint}"
        
        if not os.path.exists(checkpoint_dir):
            print(f"❌ Checkpoint directory not found: {checkpoint_dir}")
            return False
        
        try:
            # Restore files from checkpoint
            critical_files = [
                "bitcoin_freedom_best_model.py",
                "templates/best_composite_score_dashboard.html",
                "models/webapp_best_model_metadata.json"
            ]
            
            for file_path in critical_files:
                backup_path = f"{checkpoint_dir}/{os.path.basename(file_path)}"
                if os.path.exists(backup_path):
                    with open(backup_path, 'r', encoding='utf-8') as src:
                        with open(file_path, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
                    print(f"✅ Restored: {file_path}")
            
            print("✅ ROLLBACK COMPLETED")
            self.workflow_state = "ROLLED_BACK"
            return True
            
        except Exception as e:
            print(f"❌ ROLLBACK FAILED: {e}")
            return False

# USAGE EXAMPLE - THIS IS HOW ALL UPDATES MUST BE DONE
def locked_update_example():
    """Example of the LOCKED workflow that MUST be followed"""
    
    workflow = LockedUpdateWorkflow()
    
    # STEP 1: Validate the change request
    success = workflow.step_1_validate_request(
        change_description="Update UI to show Advanced Retrained Model data",
        change_type="UI_UPDATE",
        target_files=[
            "templates/best_composite_score_dashboard.html",
            "bitcoin_freedom_best_model.py"
        ]
    )
    
    if not success:
        return False
    
    # STEP 2: Create safety checkpoint
    success = workflow.step_2_create_checkpoint("UI update for Advanced Retrained Model")
    if not success:
        return False
    
    # STEP 3: Execute protocol
    success = workflow.step_3_execute_protocol()
    if not success:
        return False
    
    print("\n🔒 READY FOR MANUAL CHANGES")
    print("=" * 50)
    print("📋 NOW FOLLOW THESE STEPS EXACTLY:")
    print("1. Run: protocol.phase_1_pre_change_validation()")
    print("2. Make your changes incrementally")
    print("3. Run: protocol.phase_2_incremental_change_validation()")
    print("4. Restart webapp if needed")
    print("5. Run: protocol.phase_3_webapp_restart_validation()")
    print("6. Run: protocol.phase_4_change_verification(expected_changes)")
    print("7. Run: protocol.phase_5_final_validation()")
    
    return True

if __name__ == "__main__":
    print("🔒 LOCKED UPDATE ENFORCER LOADED")
    print("=" * 50)
    print("This system prevents time-wasting update iterations")
    print("ALL updates must follow the locked workflow")
    print("Run locked_update_example() to see proper usage")
