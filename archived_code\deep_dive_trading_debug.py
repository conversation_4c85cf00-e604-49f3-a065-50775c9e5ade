#!/usr/bin/env python3
"""
Deep dive debugging to find why no trades are executing
"""

import urllib.request
import json
import sys
import os
import traceback

def check_webapp_trading_endpoints():
    """Check if webapp trading endpoints are working"""
    print("🔍 DEEP DIVE: WEBAPP TRADING ENDPOINTS")
    print("=" * 60)
    
    endpoints_to_test = [
        ('/api/trading_status', 'Trading Status'),
        ('/api/start_trading', 'Start Trading'),
        ('/api/stop_trading', 'Stop Trading'),
        ('/api/recent_trades', 'Recent Trades'),
        ('/api/models', 'Models'),
        ('/health', 'Health Check')
    ]
    
    for endpoint, name in endpoints_to_test:
        try:
            if endpoint in ['/api/start_trading', '/api/stop_trading']:
                # These are POST endpoints, skip for now
                print(f"⏭️  {name}: POST endpoint (skipping)")
                continue
                
            with urllib.request.urlopen(f'http://localhost:5000{endpoint}', timeout=10) as response:
                if response.getcode() == 200:
                    data = json.loads(response.read().decode('utf-8'))
                    print(f"✅ {name}: Working")
                    
                    if endpoint == '/api/trading_status':
                        print(f"   Running: {data.get('is_running', False)}")
                        print(f"   Live Mode: {data.get('is_live_mode', False)}")
                        print(f"   Model: {data.get('model_info', {}).get('model_id', 'Unknown')}")
                        
                else:
                    print(f"❌ {name}: HTTP {response.getcode()}")
                    
        except Exception as e:
            print(f"❌ {name}: {e}")

def check_trading_system_files():
    """Check if trading system files exist and are accessible"""
    print("\n🔍 DEEP DIVE: TRADING SYSTEM FILES")
    print("=" * 60)
    
    critical_files = [
        'live_trading_web_app.py',
        'simple_trading_executor.py',
        'professional_trading_executor.py',
        'fully_automated_trading_system.py',
        'models/webapp_model_metadata.json'
    ]
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path}: Exists ({size:,} bytes)")
        else:
            print(f"❌ {file_path}: Missing")

def check_model_loading():
    """Check if models are properly loaded"""
    print("\n🔍 DEEP DIVE: MODEL LOADING")
    print("=" * 60)
    
    try:
        # Try to import and check model components
        sys.path.append('.')
        
        # Check if we can import trading components
        try:
            from simple_trading_executor import SimpleTradingExecutor
            print("✅ SimpleTradingExecutor: Import successful")
        except Exception as e:
            print(f"❌ SimpleTradingExecutor: Import failed - {e}")
        
        try:
            from professional_trading_executor import ProfessionalTradingExecutor
            print("✅ ProfessionalTradingExecutor: Import successful")
        except Exception as e:
            print(f"❌ ProfessionalTradingExecutor: Import failed - {e}")
            
        # Check model metadata
        if os.path.exists('models/webapp_model_metadata.json'):
            with open('models/webapp_model_metadata.json', 'r') as f:
                metadata = json.load(f)
            print(f"✅ Model Metadata: Loaded ({len(metadata.get('all_models', []))} models)")
        else:
            print("❌ Model Metadata: File missing")
            
    except Exception as e:
        print(f"❌ Model Loading Error: {e}")
        traceback.print_exc()

def test_signal_generation():
    """Test if signal generation is working"""
    print("\n🔍 DEEP DIVE: SIGNAL GENERATION TEST")
    print("=" * 60)
    
    try:
        # Try to create a trading executor and test signal generation
        sys.path.append('.')
        from simple_trading_executor import SimpleTradingExecutor
        
        executor = SimpleTradingExecutor()
        current_price = 104681.50
        
        print(f"💹 Testing with price: ${current_price:,.2f}")
        
        # Test signal generation
        signals = executor.check_grid_levels(current_price, testing_mode=True)
        print(f"📊 Generated signals: {len(signals)}")
        
        for i, signal in enumerate(signals):
            print(f"   Signal {i+1}: {signal['action']} @ ${signal['price']:.2f} (Confidence: {signal['confidence']:.1%})")
        
        if len(signals) == 0:
            print("❌ NO SIGNALS GENERATED - This could be the problem!")
            
            # Test grid level calculation
            grid_levels = executor.calculate_grid_levels(current_price)
            print(f"📊 Grid levels calculated: {len(grid_levels)}")
            
            if len(grid_levels) == 0:
                print("❌ NO GRID LEVELS - Grid calculation broken!")
            else:
                print("✅ Grid levels exist, checking why no signals...")
                
                # Check each grid level
                for level in grid_levels[:5]:  # Check first 5
                    distance = abs(current_price - level) / current_price
                    print(f"   Grid ${level:.2f}: Distance {distance:.4f} ({distance*100:.2f}%)")
        
    except Exception as e:
        print(f"❌ Signal Generation Test Failed: {e}")
        traceback.print_exc()

def check_trading_loop_status():
    """Check if trading loop is actually running"""
    print("\n🔍 DEEP DIVE: TRADING LOOP STATUS")
    print("=" * 60)
    
    try:
        # Check if there's a background trading process
        import subprocess
        
        # Check for Python processes
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True)
        
        python_processes = result.stdout.count('python.exe')
        print(f"🐍 Python processes running: {python_processes}")
        
        if python_processes < 2:
            print("⚠️  Expected at least 2 Python processes (webapp + trading loop)")
            print("❌ Trading loop may not be running!")
        else:
            print("✅ Multiple Python processes detected")
            
    except Exception as e:
        print(f"❌ Process check failed: {e}")

def test_manual_trade_execution():
    """Test if we can manually trigger a trade"""
    print("\n🔍 DEEP DIVE: MANUAL TRADE TEST")
    print("=" * 60)
    
    try:
        # Try to start trading via API
        import urllib.request
        
        # Create a POST request to start trading
        req = urllib.request.Request('http://localhost:5000/api/start_trading', 
                                   data=b'{}', 
                                   headers={'Content-Type': 'application/json'})
        req.get_method = lambda: 'POST'
        
        with urllib.request.urlopen(req, timeout=10) as response:
            if response.getcode() == 200:
                data = json.loads(response.read().decode('utf-8'))
                print(f"✅ Start Trading API: {data}")
            else:
                print(f"❌ Start Trading API: HTTP {response.getcode()}")
                
    except Exception as e:
        print(f"❌ Manual trade test failed: {e}")

def main():
    """Run comprehensive debugging"""
    print("🚨 DEEP DIVE ANALYSIS: WHY NO TRADES EXECUTING")
    print("=" * 80)
    print("This will check every component of the trading system...")
    print("=" * 80)
    
    # Run all checks
    check_webapp_trading_endpoints()
    check_trading_system_files()
    check_model_loading()
    test_signal_generation()
    check_trading_loop_status()
    test_manual_trade_execution()
    
    print("\n" + "=" * 80)
    print("🎯 DEEP DIVE ANALYSIS COMPLETE")
    print("=" * 80)
    print("Review the results above to identify the root cause.")
    print("Look for any ❌ errors or missing components.")

if __name__ == "__main__":
    main()
