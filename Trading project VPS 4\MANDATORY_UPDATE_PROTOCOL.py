#!/usr/bin/env python3
"""
🔒 MANDATORY UPDATE PROTOCOL - LOCKED SYSTEM
===========================================
This protocol MUST be followed for ALL system updates to prevent time-wasting iterations.
NO EXCEPTIONS - ALL CHANGES MUST GO THROUGH THIS VALIDATION SYSTEM.
"""

import requests
import time
import json
import os
import subprocess
from datetime import datetime
from typing import Dict, List, Tuple, Optional

class MandatoryUpdateProtocol:
    """LOCKED PROTOCOL - Must be used for all system updates"""
    
    def __init__(self, webapp_url: str = "http://localhost:5001"):
        self.webapp_url = webapp_url
        self.validation_log = []
        self.current_session = datetime.now().strftime('%Y%m%d_%H%M%S')
        
    def log_step(self, step: str, status: str, details: str = ""):
        """Log each validation step"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'step': step,
            'status': status,
            'details': details
        }
        self.validation_log.append(entry)
        print(f"📋 {step}: {status} - {details}")
    
    def phase_1_pre_change_validation(self) -> Dict:
        """PHASE 1: MANDATORY pre-change validation - NO EXCEPTIONS"""
        
        print("🔒 PHASE 1: PRE-CHANGE VALIDATION (MANDATORY)")
        print("=" * 60)
        
        validation_results = {
            'webapp_accessible': False,
            'current_state_captured': False,
            'files_backed_up': False,
            'dependencies_checked': False,
            'baseline_established': True
        }
        
        # 1.1 Check webapp accessibility
        try:
            response = requests.get(f"{self.webapp_url}/api/trading_status", timeout=5)
            if response.status_code == 200:
                validation_results['webapp_accessible'] = True
                self.baseline_data = response.json()
                self.log_step("Webapp Access", "✅ PASS", f"Status: {response.status_code}")
            else:
                self.log_step("Webapp Access", "❌ FAIL", f"Status: {response.status_code}")
                return validation_results
        except Exception as e:
            self.log_step("Webapp Access", "❌ FAIL", f"Error: {e}")
            return validation_results
        
        # 1.2 Capture current UI state
        try:
            # Take screenshot equivalent - capture current data
            self.current_ui_state = {
                'model_name': self.baseline_data.get('model_name'),
                'win_rate': self.baseline_data.get('win_rate'),
                'composite_score': self.baseline_data.get('composite_score'),
                'trading_mode': self.baseline_data.get('trading_mode'),
                'is_running': self.baseline_data.get('is_running'),
                'backtester_active': self.baseline_data.get('backtester_active'),
                'targets_achieved': self.baseline_data.get('targets_achieved')
            }
            validation_results['current_state_captured'] = True
            self.log_step("UI State Capture", "✅ PASS", "Current state saved")
        except Exception as e:
            self.log_step("UI State Capture", "❌ FAIL", f"Error: {e}")
            return validation_results
        
        # 1.3 Create backup of critical files
        try:
            backup_dir = f"backups/session_{self.current_session}"
            os.makedirs(backup_dir, exist_ok=True)
            
            critical_files = [
                "bitcoin_freedom_best_model.py",
                "templates/best_composite_score_dashboard.html",
                "models/webapp_best_model_metadata.json"
            ]
            
            for file_path in critical_files:
                if os.path.exists(file_path):
                    backup_path = f"{backup_dir}/{os.path.basename(file_path)}"
                    with open(file_path, 'r', encoding='utf-8') as src:
                        with open(backup_path, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
            
            validation_results['files_backed_up'] = True
            self.log_step("File Backup", "✅ PASS", f"Backed up to {backup_dir}")
        except Exception as e:
            self.log_step("File Backup", "❌ FAIL", f"Error: {e}")
            return validation_results
        
        # 1.4 Check dependencies
        try:
            # Verify critical imports work
            import flask
            import sqlite3
            validation_results['dependencies_checked'] = True
            self.log_step("Dependencies", "✅ PASS", "All critical imports available")
        except Exception as e:
            self.log_step("Dependencies", "❌ FAIL", f"Missing: {e}")
            return validation_results
        
        print("✅ PHASE 1 COMPLETE - READY FOR CHANGES")
        return validation_results
    
    def phase_2_incremental_change_validation(self, change_description: str) -> bool:
        """PHASE 2: MANDATORY incremental change validation"""
        
        print(f"\n🔒 PHASE 2: INCREMENTAL CHANGE VALIDATION")
        print(f"Change: {change_description}")
        print("=" * 60)
        
        # 2.1 Wait for file system to settle
        time.sleep(2)
        
        # 2.2 Check if webapp needs restart
        try:
            response = requests.get(f"{self.webapp_url}/api/trading_status", timeout=5)
            if response.status_code != 200:
                self.log_step("Change Detection", "⚠️ RESTART NEEDED", "Webapp not responding")
                return False
            else:
                self.log_step("Change Detection", "✅ PASS", "Webapp still responsive")
                return True
        except Exception as e:
            self.log_step("Change Detection", "⚠️ RESTART NEEDED", f"Error: {e}")
            return False
    
    def phase_3_webapp_restart_validation(self) -> bool:
        """PHASE 3: MANDATORY webapp restart and validation"""
        
        print(f"\n🔒 PHASE 3: WEBAPP RESTART VALIDATION")
        print("=" * 60)
        
        # 3.1 Wait for restart
        print("⏳ Waiting for webapp restart...")
        time.sleep(10)
        
        # 3.2 Verify webapp is accessible
        max_attempts = 6
        for attempt in range(max_attempts):
            try:
                response = requests.get(f"{self.webapp_url}/api/trading_status", timeout=5)
                if response.status_code == 200:
                    self.log_step("Restart Validation", "✅ PASS", f"Webapp accessible after restart (attempt {attempt + 1})")
                    self.post_restart_data = response.json()
                    return True
                else:
                    self.log_step("Restart Attempt", "⚠️ RETRY", f"Status {response.status_code} (attempt {attempt + 1})")
            except Exception as e:
                self.log_step("Restart Attempt", "⚠️ RETRY", f"Error: {e} (attempt {attempt + 1})")
            
            time.sleep(5)
        
        self.log_step("Restart Validation", "❌ FAIL", "Webapp not accessible after restart")
        return False
    
    def phase_4_change_verification(self, expected_changes: Dict) -> Dict:
        """PHASE 4: MANDATORY change verification"""
        
        print(f"\n🔒 PHASE 4: CHANGE VERIFICATION (MANDATORY)")
        print("=" * 60)
        
        verification_results = {
            'changes_applied': False,
            'ui_updated': False,
            'data_correct': False,
            'functionality_working': False
        }
        
        # 4.1 Verify data changes
        try:
            current_data = self.post_restart_data
            changes_detected = []
            
            for key, expected_value in expected_changes.items():
                current_value = current_data.get(key)
                if current_value != expected_value:
                    if current_value != self.current_ui_state.get(key):
                        changes_detected.append(f"{key}: {self.current_ui_state.get(key)} → {current_value}")
                    else:
                        self.log_step("Change Verification", "❌ FAIL", f"{key} not updated: expected {expected_value}, got {current_value}")
                        return verification_results
                else:
                    changes_detected.append(f"{key}: ✅ {expected_value}")
            
            if changes_detected:
                verification_results['changes_applied'] = True
                verification_results['data_correct'] = True
                self.log_step("Data Verification", "✅ PASS", f"Changes detected: {', '.join(changes_detected)}")
            else:
                self.log_step("Data Verification", "❌ FAIL", "No changes detected in API data")
                return verification_results
                
        except Exception as e:
            self.log_step("Data Verification", "❌ FAIL", f"Error: {e}")
            return verification_results
        
        # 4.2 Verify UI functionality
        try:
            # Test critical endpoints
            endpoints_to_test = [
                "/api/trading_status",
                "/api/recent_trades",
                "/"  # Main page
            ]
            
            all_working = True
            for endpoint in endpoints_to_test:
                try:
                    response = requests.get(f"{self.webapp_url}{endpoint}", timeout=5)
                    if response.status_code != 200:
                        all_working = False
                        self.log_step("Endpoint Test", "❌ FAIL", f"{endpoint}: Status {response.status_code}")
                except Exception as e:
                    all_working = False
                    self.log_step("Endpoint Test", "❌ FAIL", f"{endpoint}: {e}")
            
            if all_working:
                verification_results['functionality_working'] = True
                verification_results['ui_updated'] = True
                self.log_step("Functionality Test", "✅ PASS", "All endpoints working")
            else:
                self.log_step("Functionality Test", "❌ FAIL", "Some endpoints not working")
                return verification_results
                
        except Exception as e:
            self.log_step("Functionality Test", "❌ FAIL", f"Error: {e}")
            return verification_results
        
        print("✅ PHASE 4 COMPLETE - CHANGES VERIFIED")
        return verification_results
    
    def phase_5_final_validation(self) -> bool:
        """PHASE 5: MANDATORY final validation"""
        
        print(f"\n🔒 PHASE 5: FINAL VALIDATION (MANDATORY)")
        print("=" * 60)
        
        # 5.1 Generate validation report
        report = {
            'session_id': self.current_session,
            'timestamp': datetime.now().isoformat(),
            'validation_log': self.validation_log,
            'baseline_state': self.current_ui_state,
            'final_state': self.post_restart_data,
            'success': True
        }
        
        # 5.2 Save validation report
        report_file = f"validation_reports/session_{self.current_session}.json"
        os.makedirs("validation_reports", exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.log_step("Final Validation", "✅ COMPLETE", f"Report saved: {report_file}")
        
        print("🎉 ALL PHASES COMPLETE - UPDATE SUCCESSFULLY VALIDATED")
        return True
    
    def emergency_rollback(self):
        """Emergency rollback to previous state"""
        
        print("🚨 EMERGENCY ROLLBACK INITIATED")
        print("=" * 60)
        
        try:
            backup_dir = f"backups/session_{self.current_session}"
            
            critical_files = [
                "bitcoin_freedom_best_model.py",
                "templates/best_composite_score_dashboard.html",
                "models/webapp_best_model_metadata.json"
            ]
            
            for file_path in critical_files:
                backup_path = f"{backup_dir}/{os.path.basename(file_path)}"
                if os.path.exists(backup_path):
                    with open(backup_path, 'r', encoding='utf-8') as src:
                        with open(file_path, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
                    self.log_step("Rollback", "✅ RESTORED", file_path)
            
            print("✅ ROLLBACK COMPLETE - SYSTEM RESTORED")
            return True
            
        except Exception as e:
            print(f"❌ ROLLBACK FAILED: {e}")
            return False

# MANDATORY USAGE PROTOCOL
def mandatory_update_protocol_example():
    """Example of how this MUST be used for all updates"""
    
    protocol = MandatoryUpdateProtocol()
    
    # PHASE 1: ALWAYS run pre-change validation
    pre_validation = protocol.phase_1_pre_change_validation()
    if not all(pre_validation.values()):
        print("❌ PRE-VALIDATION FAILED - CANNOT PROCEED")
        return False
    
    # PHASE 2: Make incremental changes with validation
    # (This is where actual file edits happen)
    change_ok = protocol.phase_2_incremental_change_validation("Updated model name in template")
    if not change_ok:
        # PHASE 3: Restart webapp if needed
        restart_ok = protocol.phase_3_webapp_restart_validation()
        if not restart_ok:
            protocol.emergency_rollback()
            return False
    
    # PHASE 4: Verify changes worked
    expected_changes = {
        'model_name': 'Advanced Retrained Model (TARGETS ACHIEVED)',
        'targets_achieved': True
    }
    verification = protocol.phase_4_change_verification(expected_changes)
    if not all(verification.values()):
        print("❌ CHANGE VERIFICATION FAILED")
        protocol.emergency_rollback()
        return False
    
    # PHASE 5: Final validation
    final_ok = protocol.phase_5_final_validation()
    return final_ok

if __name__ == "__main__":
    # Test the protocol
    mandatory_update_protocol_example()
