#!/usr/bin/env python3
"""
LIVE CROSS MARGIN STATUS CHECKER
================================
Check if the system is running in live real money cross margin mode.
"""

import requests
import json
import time
from datetime import datetime

def check_live_cross_margin_status():
    """Check if live real money cross margin mode is active."""
    print("🔍 CHECKING LIVE REAL MONEY CROSS MARGIN STATUS")
    print("=" * 70)
    print(f"Check Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    base_url = "http://localhost:5000"
    
    # Check 1: Webapp Connectivity
    print("📡 STEP 1: Webapp Connectivity")
    print("-" * 50)
    try:
        response = requests.get(f"{base_url}/api/trading_status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Webapp: RUNNING")
            print(f"   URL: {base_url}")
            print(f"   Response: HTTP 200 OK")
            
            # Extract key status information
            is_running = data.get('is_running', False)
            is_live_mode = data.get('is_live_mode', False)
            current_price = data.get('current_price', 0)
            
            print(f"\n📊 Trading Engine Status:")
            print(f"   Trading Running: {'✅ YES' if is_running else '❌ NO'}")
            print(f"   Live Mode: {'✅ YES' if is_live_mode else '❌ NO (Simulation)'}")
            print(f"   BTC Price: ${current_price:,.2f}")
            
            if data.get('model_info'):
                model_info = data['model_info']
                print(f"   Model Score: {model_info.get('composite_score', 0):.1f}%")
                print(f"   Model Type: {model_info.get('model_type', 'Unknown')}")
                print(f"   Live Ready: {'✅' if model_info.get('live_ready') else '❌'}")
                
        else:
            print(f"❌ Webapp: HTTP {response.status_code}")
            print("   The webapp is not responding correctly")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Webapp: CONNECTION REFUSED")
        print("   The webapp is not running at http://localhost:5000")
        print("   Please start the webapp first")
        return False
    except Exception as e:
        print(f"❌ Webapp: {e}")
        return False
    
    # Check 2: Binance Connection Status
    print("\n📡 STEP 2: Binance Connection Status")
    print("-" * 50)
    try:
        response = requests.get(f"{base_url}/api/binance_status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            
            ccxt_available = data.get('ccxt_available', False)
            simple_binance_available = data.get('simple_binance_available', False)
            connected = data.get('connected', False)
            connector_type = data.get('connector_type', 'Unknown')
            testnet = data.get('testnet', None)
            
            print(f"🔧 Available Connectors:")
            print(f"   CCXT: {'✅ Available' if ccxt_available else '❌ Not Available'}")
            print(f"   Simple Binance: {'✅ Available' if simple_binance_available else '❌ Not Available'}")
            
            print(f"\n🔗 Connection Status:")
            print(f"   Connected: {'✅ YES' if connected else '❌ NO'}")
            print(f"   Connector Type: {connector_type}")
            print(f"   Network: {'🧪 TESTNET' if testnet else '💰 LIVE' if testnet is False else '❓ Unknown'}")
            
            if data.get('account_balance'):
                balance = data['account_balance']
                print(f"\n💰 Account Balance:")
                print(f"   USDT: ${balance.get('USDT', 0):.2f}")
                print(f"   BTC: {balance.get('BTC', 0):.6f}")
                print(f"   Total USDT: ${balance.get('total_usdt', 0):.2f}")
            else:
                print(f"\n💰 Account Balance: Not available")
                
        else:
            print(f"❌ Binance Status: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Binance Status: {e}")
    
    # Check 3: Margin Manager Status
    print("\n🤖 STEP 3: Margin Manager Status")
    print("-" * 50)
    try:
        response = requests.get(f"{base_url}/api/margin_status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            
            margin_active = data.get('margin_manager_active', False)
            print(f"Margin Manager: {'✅ ACTIVE' if margin_active else '❌ INACTIVE'}")
            
            if margin_active:
                margin_level = data.get('margin_level', 0)
                trading_status = data.get('trading_status', 'Unknown')
                risk_assessment = data.get('risk_assessment', 'Unknown')
                total_net_usd = data.get('total_net_usd', 0)
                
                print(f"   Margin Level: {margin_level:.2f}")
                print(f"   Trading Status: {trading_status}")
                print(f"   Risk Assessment: {risk_assessment}")
                print(f"   Total Net USD: ${total_net_usd:.2f}")
                
                # Interpret margin level
                if margin_level < 1.3:
                    print(f"   ⚠️ WARNING: Margin level very low - emergency mode")
                elif margin_level < 1.5:
                    print(f"   ⚠️ CAUTION: Margin level low - conservative mode")
                elif margin_level < 2.0:
                    print(f"   📊 INFO: Margin level moderate - cautious mode")
                else:
                    print(f"   ✅ GOOD: Margin level safe for normal trading")
                    
            else:
                message = data.get('message', 'Unknown')
                print(f"   Message: {message}")
                
        else:
            print(f"❌ Margin Manager: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Margin Manager: {e}")
    
    # Check 4: Portfolio Rebalancer Status
    print("\n🔄 STEP 4: Portfolio Rebalancer Status")
    print("-" * 50)
    try:
        response = requests.get(f"{base_url}/api/portfolio_status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            
            rebalancer_active = data.get('portfolio_rebalancer_active', False)
            print(f"Portfolio Rebalancer: {'✅ ACTIVE' if rebalancer_active else '❌ INACTIVE'}")
            
            if rebalancer_active:
                total_value = data.get('total_value_usd', 0)
                btc_ratio = data.get('btc_ratio', 0) * 100
                usdt_ratio = data.get('usdt_ratio', 0) * 100
                can_buy = data.get('can_buy', False)
                can_sell = data.get('can_sell', False)
                needs_rebalance = data.get('needs_rebalance', False)
                
                print(f"   Total Portfolio Value: ${total_value:.2f}")
                print(f"   BTC Ratio: {btc_ratio:.1f}%")
                print(f"   USDT Ratio: {usdt_ratio:.1f}%")
                print(f"   Can Buy: {'✅' if can_buy else '❌'}")
                print(f"   Can Sell: {'✅' if can_sell else '❌'}")
                print(f"   Needs Rebalance: {'⚠️ YES' if needs_rebalance else '✅ NO'}")
                
            else:
                message = data.get('message', 'Unknown')
                print(f"   Message: {message}")
                
        else:
            print(f"❌ Portfolio Rebalancer: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Portfolio Rebalancer: {e}")
    
    # Check 5: Recent Trading Activity
    print("\n📈 STEP 5: Recent Trading Activity")
    print("-" * 50)
    try:
        response = requests.get(f"{base_url}/api/recent_trades", timeout=5)
        if response.status_code == 200:
            trades = response.json()
            
            if trades:
                print(f"Recent Trades: {len(trades)} trades found")
                
                # Show last 3 trades
                for i, trade in enumerate(trades[-3:]):
                    trade_id = trade.get('trade_id', 'Unknown')
                    direction = trade.get('direction', 'Unknown')
                    pnl = trade.get('pnl', 0)
                    status = trade.get('status', 'Unknown')
                    entry_time = trade.get('entry_time', 'Unknown')
                    
                    print(f"   Trade {i+1}: {direction} | P&L: ${pnl:+.2f} | Status: {status} | Time: {entry_time}")
            else:
                print("Recent Trades: No trades found")
                
        else:
            print(f"❌ Recent Trades: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Recent Trades: {e}")
    
    # Final Assessment
    print("\n🎯 FINAL ASSESSMENT: LIVE CROSS MARGIN STATUS")
    print("=" * 70)
    
    # Determine overall status
    try:
        # Get key status indicators
        trading_response = requests.get(f"{base_url}/api/trading_status", timeout=5)
        binance_response = requests.get(f"{base_url}/api/binance_status", timeout=5)
        margin_response = requests.get(f"{base_url}/api/margin_status", timeout=5)
        
        if all(r.status_code == 200 for r in [trading_response, binance_response, margin_response]):
            trading_data = trading_response.json()
            binance_data = binance_response.json()
            margin_data = margin_response.json()
            
            is_live_mode = trading_data.get('is_live_mode', False)
            is_connected = binance_data.get('connected', False)
            is_testnet = binance_data.get('testnet', True)
            margin_active = margin_data.get('margin_manager_active', False)
            
            print(f"📊 SYSTEM STATUS SUMMARY:")
            print(f"   Live Mode: {'✅ ENABLED' if is_live_mode else '❌ DISABLED (Simulation)'}")
            print(f"   Binance Connected: {'✅ YES' if is_connected else '❌ NO'}")
            print(f"   Network: {'🧪 TESTNET' if is_testnet else '💰 LIVE MONEY'}")
            print(f"   Cross Margin: {'✅ ACTIVE' if margin_active else '❌ INACTIVE'}")
            
            # Overall status
            if is_live_mode and is_connected and not is_testnet and margin_active:
                print(f"\n🚀 RESULT: LIVE REAL MONEY CROSS MARGIN MODE IS ACTIVE!")
                print(f"   ✅ Your system is trading with real money")
                print(f"   ✅ Cross margin features are enabled")
                print(f"   ✅ All intelligent systems are active")
                return True
            elif is_live_mode and is_connected and is_testnet and margin_active:
                print(f"\n🧪 RESULT: TESTNET CROSS MARGIN MODE IS ACTIVE")
                print(f"   ✅ Your system is in testnet mode (safe testing)")
                print(f"   ✅ Cross margin features are enabled")
                print(f"   ✅ All intelligent systems are active")
                return True
            elif not is_live_mode:
                print(f"\n📊 RESULT: SIMULATION MODE IS ACTIVE")
                print(f"   ⚠️ Your system is in simulation mode")
                print(f"   ⚠️ No real money trading")
                print(f"   💡 Enable live mode to start real trading")
                return False
            elif not is_connected:
                print(f"\n❌ RESULT: NOT CONNECTED TO BINANCE")
                print(f"   ⚠️ Your system cannot execute real trades")
                print(f"   💡 Check Binance connection and API keys")
                return False
            else:
                print(f"\n⚠️ RESULT: PARTIAL ACTIVATION")
                print(f"   ⚠️ Some features may not be fully active")
                print(f"   💡 Check individual components above")
                return False
        else:
            print(f"❌ RESULT: CANNOT DETERMINE STATUS")
            print(f"   ⚠️ Some API endpoints are not responding")
            return False
            
    except Exception as e:
        print(f"❌ RESULT: ERROR CHECKING STATUS")
        print(f"   Error: {e}")
        return False

if __name__ == "__main__":
    success = check_live_cross_margin_status()
    
    print("\n" + "=" * 70)
    if success:
        print("🎯 LIVE CROSS MARGIN TRADING IS ACTIVE!")
    else:
        print("⚠️ LIVE CROSS MARGIN TRADING IS NOT ACTIVE")
    print("=" * 70)
