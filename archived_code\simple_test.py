"""
Simple test to verify basic functionality
"""

import os
import sys
import traceback

def test_imports():
    """Test basic imports"""
    print("🧪 Testing Basic Imports...")
    
    try:
        import numpy as np
        print("✅ NumPy imported successfully")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ Pandas imported successfully")
    except ImportError as e:
        print(f"❌ Pandas import failed: {e}")
        return False
    
    try:
        import tensorflow as tf
        print("✅ TensorFlow imported successfully")
    except ImportError as e:
        print(f"❌ TensorFlow import failed: {e}")
        return False
    
    return True

def test_config():
    """Test configuration loading"""
    print("\n⚙️ Testing Configuration...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
        from trading_config import TradingConfig
        
        config = TradingConfig()
        print(f"✅ Config loaded - Threshold: {config.COMPOSITE_THRESHOLD}")
        return True
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

def test_model_files():
    """Test if model files exist and can be imported"""
    print("\n🤖 Testing Model Files...")
    
    model_files = [
        'models/tcn_model.py',
        'models/cnn_model.py', 
        'models/ppo_agent.py',
        'models/ensemble_model.py'
    ]
    
    for model_file in model_files:
        if os.path.exists(model_file):
            print(f"✅ {model_file} exists")
        else:
            print(f"❌ {model_file} missing")
            return False
    
    # Test imports
    try:
        from models.tcn_model import create_tcn_model
        print("✅ TCN model import successful")
    except Exception as e:
        print(f"❌ TCN model import failed: {e}")
        return False
    
    try:
        from models.cnn_model import create_cnn_model
        print("✅ CNN model import successful")
    except Exception as e:
        print(f"❌ CNN model import failed: {e}")
        return False
    
    try:
        from models.ppo_agent import create_ppo_agent
        print("✅ PPO agent import successful")
    except Exception as e:
        print(f"❌ PPO agent import failed: {e}")
        return False
    
    try:
        from models.ensemble_model import create_ensemble_trader
        print("✅ Ensemble model import successful")
    except Exception as e:
        print(f"❌ Ensemble model import failed: {e}")
        return False
    
    return True

def test_advanced_ml_system():
    """Test advanced ML system import"""
    print("\n🚀 Testing Advanced ML System...")
    
    try:
        from advanced_ml_training_system import AdvancedMLTradingSystem
        print("✅ Advanced ML system import successful")
        return True
    except Exception as e:
        print(f"❌ Advanced ML system import failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run simple tests"""
    print("🧪 SIMPLE TEST SUITE - ADVANCED ML TRADING SYSTEM")
    print("="*60)
    
    tests = [
        ("Basic Imports", test_imports),
        ("Configuration", test_config),
        ("Model Files", test_model_files),
        ("Advanced ML System", test_advanced_ml_system)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 All basic tests passed! System components are working.")
    else:
        print("⚠️ Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
