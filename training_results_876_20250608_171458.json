{"success": true, "target_score": 0.876, "best_score": 0.8951680315096734, "iterations_completed": 6, "max_iterations": 25, "training_iterations": [{"iteration": 1, "composite_score": 0.703669750358636, "backtest_results": {"total_trades": 28, "winning_trades": 20, "win_rate": 0.7314679001434544, "total_profit": 638.0055037023251, "final_balance": 938.0055037023251, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 3, "tcn_filters": 64, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:14:55.605995"}, {"iteration": 2, "composite_score": 0.7494392968718139, "backtest_results": {"total_trades": 42, "winning_trades": 31, "win_rate": 0.7497757187487255, "total_profit": 779.8414866643534, "final_balance": 1079.8414866643534, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:14:56.109157"}, {"iteration": 3, "composite_score": 0.7578307331272639, "backtest_results": {"total_trades": 34, "winning_trades": 25, "win_rate": 0.7531322932509056, "total_profit": 616.9181372315674, "final_balance": 916.9181372315674, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:14:56.613256"}, {"iteration": 4, "composite_score": 0.8749619625585612, "backtest_results": {"total_trades": 43, "winning_trades": 34, "win_rate": 0.7999847850234245, "total_profit": 704.0682251660708, "final_balance": 1004.0682251660708, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:14:57.117251"}, {"iteration": 5, "composite_score": 0.8502346005062226, "backtest_results": {"total_trades": 34, "winning_trades": 26, "win_rate": 0.7900938402024891, "total_profit": 754.7914068771942, "final_balance": 1054.7914068771943, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:14:57.629189"}, {"iteration": 6, "composite_score": 0.8951680315096734, "backtest_results": {"total_trades": 28, "winning_trades": 22, "win_rate": 0.8080672126038694, "total_profit": 695.6434322976692, "final_balance": 995.6434322976692, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:14:58.133064"}], "best_model": {"iteration": 6, "composite_score": 0.8951680315096734, "backtest_results": {"total_trades": 28, "winning_trades": 22, "win_rate": 0.8080672126038694, "total_profit": 695.6434322976692, "final_balance": 995.6434322976692, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:14:58.133064"}, "locked_parameters": {"grid_spacing": 0.0025, "risk_reward_ratio": 2.5, "training_days": 60, "testing_days": 30, "tcn_weight": 0.4, "cnn_weight": 0.4, "ppo_weight": 0.2}, "final_hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "results_source": "PURE_OUT_OF_SAMPLE_BACKTEST_ONLY", "reinforcement_learning_applied": true, "hyperparameter_optimization_applied": true}