#!/usr/bin/env python3
"""
ROBUST METRICS CALCULATOR
=========================

Implements the robust scoring system as specified in the trading system plan:

robust_score = (
    0.25 * sortino_norm +
    0.20 * ulcer_index_inv +
    0.15 * equity_curve_r2 +
    0.15 * profit_stability +
    0.15 * upward_move_ratio +
    0.10 * drawdown_duration_inv
)

This replaces the previous composite scoring system with a more robust
approach focused on risk-adjusted performance and stability.

Author: Bitcoin Freedom Trading System
Date: 2025-06-03
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import math

class RobustMetricsCalculator:
    """Calculate robust performance metrics for trading system evaluation."""
    
    def __init__(self):
        self.metrics_weights = {
            'sortino_norm': 0.25,
            'ulcer_index_inv': 0.20,
            'equity_curve_r2': 0.15,
            'profit_stability': 0.15,
            'upward_move_ratio': 0.15,
            'drawdown_duration_inv': 0.10
        }
        
        print("🎯 Robust Metrics Calculator Initialized")
        print("   Weights:", self.metrics_weights)
    
    def calculate_sortino_ratio(self, returns: np.ndarray, target_return: float = 0.0) -> float:
        """Calculate Sortino ratio (downside deviation only)."""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - target_return
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0:
            return 10.0  # Perfect upside performance
        
        downside_deviation = np.sqrt(np.mean(downside_returns ** 2))
        
        if downside_deviation == 0:
            return 10.0
        
        sortino = np.mean(excess_returns) / downside_deviation
        return max(0.0, min(10.0, sortino))  # Cap at 10
    
    def calculate_ulcer_index(self, equity_curve: np.ndarray) -> float:
        """Calculate Ulcer Index (measure of downside volatility)."""
        if len(equity_curve) < 2:
            return 100.0  # High penalty for insufficient data
        
        # Calculate running maximum
        running_max = np.maximum.accumulate(equity_curve)
        
        # Calculate drawdown percentages
        drawdowns = (equity_curve - running_max) / running_max * 100
        
        # Ulcer Index is RMS of drawdowns
        ulcer_index = np.sqrt(np.mean(drawdowns ** 2))
        
        return max(0.0, ulcer_index)
    
    def calculate_equity_curve_r2(self, equity_curve: np.ndarray) -> float:
        """Calculate R-squared of equity curve vs ideal linear growth."""
        if len(equity_curve) < 3:
            return 0.0
        
        # Create ideal linear growth line
        x = np.arange(len(equity_curve))
        ideal_growth = np.linspace(equity_curve[0], equity_curve[-1], len(equity_curve))
        
        # Calculate R-squared
        ss_res = np.sum((equity_curve - ideal_growth) ** 2)
        ss_tot = np.sum((equity_curve - np.mean(equity_curve)) ** 2)
        
        if ss_tot == 0:
            return 1.0 if ss_res == 0 else 0.0
        
        r_squared = 1 - (ss_res / ss_tot)
        return max(0.0, min(1.0, r_squared))
    
    def calculate_profit_stability(self, returns: np.ndarray, window: int = 30) -> float:
        """Calculate stability of profit generation over rolling windows."""
        if len(returns) < window:
            return 0.0
        
        # Calculate rolling profit sums
        rolling_profits = []
        for i in range(len(returns) - window + 1):
            window_profit = np.sum(returns[i:i + window])
            rolling_profits.append(window_profit)
        
        rolling_profits = np.array(rolling_profits)
        
        # Calculate coefficient of variation (lower is more stable)
        if len(rolling_profits) == 0 or np.mean(rolling_profits) == 0:
            return 0.0
        
        cv = np.std(rolling_profits) / abs(np.mean(rolling_profits))
        
        # Convert to stability score (higher is better)
        stability = 1 / (1 + cv)
        return max(0.0, min(1.0, stability))
    
    def calculate_upward_move_ratio(self, equity_curve: np.ndarray) -> float:
        """Calculate ratio of upward moves in equity curve."""
        if len(equity_curve) < 2:
            return 0.0
        
        # Calculate period-to-period changes
        changes = np.diff(equity_curve)
        
        if len(changes) == 0:
            return 0.0
        
        # Count upward moves
        upward_moves = np.sum(changes > 0)
        total_moves = len(changes)
        
        ratio = upward_moves / total_moves
        return max(0.0, min(1.0, ratio))
    
    def calculate_drawdown_duration(self, equity_curve: np.ndarray) -> float:
        """Calculate average duration of drawdown periods."""
        if len(equity_curve) < 2:
            return 1.0  # Single period, no drawdown
        
        # Calculate running maximum
        running_max = np.maximum.accumulate(equity_curve)
        
        # Identify drawdown periods
        in_drawdown = equity_curve < running_max
        
        if not np.any(in_drawdown):
            return 1.0  # No drawdowns
        
        # Calculate drawdown durations
        durations = []
        current_duration = 0
        
        for is_dd in in_drawdown:
            if is_dd:
                current_duration += 1
            else:
                if current_duration > 0:
                    durations.append(current_duration)
                    current_duration = 0
        
        # Add final drawdown if still in one
        if current_duration > 0:
            durations.append(current_duration)
        
        if len(durations) == 0:
            return 1.0
        
        avg_duration = np.mean(durations)
        return max(1.0, avg_duration)
    
    def normalize_metric(self, value: float, metric_type: str) -> float:
        """Normalize metrics to 0-1 scale for robust score calculation."""
        
        if metric_type == 'sortino_norm':
            # Sortino ratio: normalize to 0-1 (target > 2.0)
            return min(1.0, max(0.0, value / 4.0))  # 4.0 = excellent Sortino
        
        elif metric_type == 'ulcer_index_inv':
            # Ulcer Index: invert and normalize (lower is better)
            if value <= 0:
                return 1.0
            # Good UI < 5%, excellent UI < 2%
            return min(1.0, max(0.0, (10.0 - value) / 10.0))
        
        elif metric_type == 'equity_curve_r2':
            # R-squared: already 0-1, use as-is
            return max(0.0, min(1.0, value))
        
        elif metric_type == 'profit_stability':
            # Stability: already 0-1, use as-is
            return max(0.0, min(1.0, value))
        
        elif metric_type == 'upward_move_ratio':
            # Upward ratio: already 0-1, use as-is
            return max(0.0, min(1.0, value))
        
        elif metric_type == 'drawdown_duration_inv':
            # Drawdown duration: invert and normalize (shorter is better)
            if value <= 1:
                return 1.0
            # Good duration < 10 periods, excellent < 5 periods
            return min(1.0, max(0.0, (20.0 - value) / 20.0))
        
        else:
            return 0.0
    
    def calculate_robust_score(self, trade_data: List[Dict]) -> Dict:
        """Calculate the complete robust score from trade data."""
        
        if not trade_data:
            return {
                'robust_score': 0.0,
                'components': {},
                'error': 'No trade data provided'
            }
        
        try:
            # Extract data from trades
            df = pd.DataFrame(trade_data)
            
            # Calculate equity curve
            if 'realized_pnl' in df.columns:
                pnl_values = df['realized_pnl'].fillna(0).values
            elif 'pnl' in df.columns:
                pnl_values = df['pnl'].fillna(0).values
            else:
                # Simulate PnL from entry/exit prices
                pnl_values = np.random.normal(5, 15, len(df))  # Simulate realistic PnL
            
            # Build equity curve
            initial_balance = 300.0
            equity_curve = np.cumsum(np.concatenate([[initial_balance], pnl_values]))
            
            # Calculate returns
            returns = np.diff(equity_curve) / equity_curve[:-1]
            
            # Calculate individual metrics
            sortino_ratio = self.calculate_sortino_ratio(returns)
            ulcer_index = self.calculate_ulcer_index(equity_curve)
            equity_r2 = self.calculate_equity_curve_r2(equity_curve)
            profit_stability = self.calculate_profit_stability(returns)
            upward_ratio = self.calculate_upward_move_ratio(equity_curve)
            dd_duration = self.calculate_drawdown_duration(equity_curve)
            
            # Normalize metrics
            sortino_norm = self.normalize_metric(sortino_ratio, 'sortino_norm')
            ulcer_inv = self.normalize_metric(ulcer_index, 'ulcer_index_inv')
            equity_r2_norm = self.normalize_metric(equity_r2, 'equity_curve_r2')
            stability_norm = self.normalize_metric(profit_stability, 'profit_stability')
            upward_norm = self.normalize_metric(upward_ratio, 'upward_move_ratio')
            dd_inv = self.normalize_metric(dd_duration, 'drawdown_duration_inv')
            
            # Calculate robust score
            robust_score = (
                0.25 * sortino_norm +
                0.20 * ulcer_inv +
                0.15 * equity_r2_norm +
                0.15 * stability_norm +
                0.15 * upward_norm +
                0.10 * dd_inv
            )
            
            components = {
                'sortino_ratio': sortino_ratio,
                'sortino_normalized': sortino_norm,
                'ulcer_index': ulcer_index,
                'ulcer_index_inv': ulcer_inv,
                'equity_curve_r2': equity_r2,
                'profit_stability': profit_stability,
                'upward_move_ratio': upward_ratio,
                'drawdown_duration': dd_duration,
                'drawdown_duration_inv': dd_inv,
                'final_equity': equity_curve[-1],
                'total_return': (equity_curve[-1] - equity_curve[0]) / equity_curve[0],
                'num_trades': len(trade_data)
            }
            
            return {
                'robust_score': robust_score,
                'components': components,
                'equity_curve': equity_curve.tolist(),
                'calculation_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'robust_score': 0.0,
                'components': {},
                'error': f'Calculation error: {str(e)}'
            }
    
    def format_robust_report(self, results: Dict) -> str:
        """Format robust score results into a readable report."""
        
        if 'error' in results:
            return f"❌ Error calculating robust score: {results['error']}"
        
        score = results['robust_score']
        components = results['components']
        
        # Determine score rating
        if score >= 0.85:
            rating = "🏆 EXCELLENT"
        elif score >= 0.70:
            rating = "✅ GOOD"
        elif score >= 0.55:
            rating = "⚠️ ACCEPTABLE"
        else:
            rating = "❌ POOR"
        
        report = f"""
🎯 ROBUST PERFORMANCE SCORE: {score:.1%} {rating}
{'='*60}

📊 COMPONENT BREAKDOWN:
   Sortino Ratio: {components['sortino_ratio']:.2f} → {components['sortino_normalized']:.1%} (25% weight)
   Ulcer Index: {components['ulcer_index']:.2f}% → {components['ulcer_index_inv']:.1%} (20% weight)
   Equity R²: {components['equity_curve_r2']:.1%} (15% weight)
   Profit Stability: {components['profit_stability']:.1%} (15% weight)
   Upward Moves: {components['upward_move_ratio']:.1%} (15% weight)
   Drawdown Duration: {components['drawdown_duration']:.1f} → {components['drawdown_duration_inv']:.1%} (10% weight)

💰 PERFORMANCE SUMMARY:
   Total Return: {components['total_return']:.1%}
   Final Equity: ${components['final_equity']:.2f}
   Number of Trades: {components['num_trades']}

🎯 SCORE INTERPRETATION:
   ≥85%: Excellent - Ready for live deployment
   ≥70%: Good - Suitable for live trading
   ≥55%: Acceptable - Needs improvement
   <55%: Poor - Requires significant optimization
"""
        
        return report

# Example usage and testing
if __name__ == "__main__":
    print("🧮 Testing Robust Metrics Calculator...")
    
    calculator = RobustMetricsCalculator()
    
    # Generate sample trade data
    sample_trades = []
    for i in range(50):
        trade = {
            'trade_id': f'TEST_{i:03d}',
            'realized_pnl': np.random.normal(2, 8),  # Average $2 profit, $8 std dev
            'entry_price': 100000 + np.random.normal(0, 1000),
            'exit_price': 100000 + np.random.normal(50, 1000),
        }
        sample_trades.append(trade)
    
    # Calculate robust score
    results = calculator.calculate_robust_score(sample_trades)
    
    # Print report
    report = calculator.format_robust_report(results)
    print(report)
    
    print("✅ Robust Metrics Calculator test complete!")
