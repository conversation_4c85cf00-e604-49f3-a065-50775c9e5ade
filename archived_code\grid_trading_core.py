"""
Grid Trading Core System
Implements fixed 0.25% grid spacing with BUY/SELL/HOLD decisions
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from enum import Enum
import dataclasses

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

class GridAction(Enum):
    """Grid trading actions"""
    BUY = 1
    SELL = -1
    HOLD = 0

class GridLevel:
    """Represents a single grid level"""
    
    def __init__(self, level_id: int, price: float, grid_spacing: float):
        self.level_id = level_id
        self.price = price
        self.grid_spacing = grid_spacing
        self.touches = 0
        self.last_action = GridAction.HOLD
        self.last_touch_time = None
        
    def __str__(self):
        return f"Grid Level {self.level_id}: ${self.price:.2f}"

class GridLevelCalculator:
    """Calculates and manages fixed 0.25% grid levels"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('GridLevelCalculator')
        self.grid_spacing = config.GRID_SPACING  # 0.0025 (0.25%)
        self.levels_above = config.GRID_LEVELS_ABOVE
        self.levels_below = config.GRID_LEVELS_BELOW
        
    def calculate_grid_levels(self, current_price: float) -> Dict[int, GridLevel]:
        """Calculate all grid levels around current price"""
        grid_levels = {}
        
        # Calculate base price (nearest grid level)
        base_level_id = self._get_base_level_id(current_price)
        base_price = self._calculate_level_price(base_level_id, current_price)
        
        # Create grid levels above and below
        for i in range(-self.levels_below, self.levels_above + 1):
            level_id = base_level_id + i
            level_price = base_price * (1 + (i * self.grid_spacing))
            
            grid_levels[level_id] = GridLevel(level_id, level_price, self.grid_spacing)
        
        self.logger.info(f"Created {len(grid_levels)} grid levels around ${current_price:.2f}")
        return grid_levels
    
    def _get_base_level_id(self, current_price: float) -> int:
        """Get the base level ID for current price"""
        # Use a reference price to create consistent level IDs
        reference_price = 50000.0  # $50,000 BTC reference
        level_id = int((current_price - reference_price) / (reference_price * self.grid_spacing))
        return level_id
    
    def _calculate_level_price(self, level_id: int, current_price: float) -> float:
        """Calculate price for a specific level ID"""
        reference_price = 50000.0
        return reference_price * (1 + (level_id * self.grid_spacing))
    
    def get_nearest_grid_level(self, price: float, grid_levels: Dict[int, GridLevel]) -> Optional[GridLevel]:
        """Find the nearest grid level to given price"""
        if not grid_levels:
            return None
        
        min_distance = float('inf')
        nearest_level = None
        
        for level in grid_levels.values():
            distance = abs(price - level.price)
            if distance < min_distance:
                min_distance = distance
                nearest_level = level
        
        return nearest_level
    
    def check_grid_touch(self, current_price: float, previous_price: float, 
                        grid_levels: Dict[int, GridLevel]) -> List[GridLevel]:
        """Check if price has touched any grid levels"""
        touched_levels = []
        
        # Check each grid level for price crossing
        for level in grid_levels.values():
            if self._price_crossed_level(current_price, previous_price, level.price):
                level.touches += 1
                level.last_touch_time = datetime.now()
                touched_levels.append(level)
                
                self.logger.info(f"Grid touch detected: Level {level.level_id} at ${level.price:.2f}")
        
        return touched_levels
    
    def _price_crossed_level(self, current_price: float, previous_price: float, level_price: float) -> bool:
        """Check if price crossed a specific level"""
        # Price crossed from below
        if previous_price <= level_price <= current_price:
            return True
        # Price crossed from above
        if previous_price >= level_price >= current_price:
            return True
        return False

class GridTradeManager:
    """Manages grid trading positions and exits"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('GridTradeManager')
        self.active_trades = {}
        self.trade_history = []
        
    def calculate_position_size(self, current_price: float, risk_amount: float = None) -> float:
        """Calculate position size for grid trade"""
        risk_amount = risk_amount or self.config.FIXED_RISK_AMOUNT
        
        # Position size = Risk Amount / (Price * Grid Spacing)
        # This ensures exactly $10 risk for 0.25% move
        position_size = risk_amount / (current_price * self.config.GRID_SPACING)
        
        # Check against maximum position based on available capital
        max_position_value = self.config.INITIAL_CAPITAL * 0.9  # 90% of capital for safety
        max_position_size = max_position_value / current_price
        
        final_position_size = min(position_size, max_position_size)
        
        self.logger.info(f"Position size: {final_position_size:.6f} BTC (${final_position_size * current_price:.2f})")
        return final_position_size
    
    def calculate_exit_levels(self, entry_price: float, action: GridAction) -> Tuple[float, float]:
        """Calculate exit levels for grid trade"""
        grid_spacing = self.config.GRID_SPACING
        
        if action == GridAction.BUY:
            # BUY: Exit 1 grid level above (+0.25%)
            exit_price = entry_price * (1 + grid_spacing)
            stop_loss = entry_price * (1 - grid_spacing)  # 1 grid below
        elif action == GridAction.SELL:
            # SELL: Exit 1 grid level below (-0.25%)
            exit_price = entry_price * (1 - grid_spacing)
            stop_loss = entry_price * (1 + grid_spacing)  # 1 grid above
        else:
            raise ValueError("Invalid action for exit calculation")
        
        return exit_price, stop_loss
    
    def create_trade_record(self, level: GridLevel, action: GridAction, 
                          position_size: float, entry_price: float) -> Dict:
        """Create a trade record for tracking"""
        exit_price, stop_loss = self.calculate_exit_levels(entry_price, action)
        
        trade_record = {
            'trade_id': f"{level.level_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'grid_level_id': level.level_id,
            'grid_price': level.price,
            'action': action.name,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'stop_loss': stop_loss,
            'position_size': position_size,
            'risk_amount': self.config.FIXED_RISK_AMOUNT,
            'target_profit': self.config.TARGET_PROFIT,
            'timestamp': datetime.now().isoformat(),
            'status': 'OPEN'
        }
        
        return trade_record
    
    def check_exit_conditions(self, current_price: float) -> List[Dict]:
        """Check if any active trades should be closed"""
        trades_to_close = []
        
        for trade_id, trade in self.active_trades.items():
            # Check take profit
            if ((trade['action'] == 'BUY' and current_price >= trade['exit_price']) or
                (trade['action'] == 'SELL' and current_price <= trade['exit_price'])):
                
                trade['exit_reason'] = 'TAKE_PROFIT'
                trade['exit_price_actual'] = current_price
                trade['profit_loss'] = self.config.TARGET_PROFIT
                trades_to_close.append(trade)
                
            # Check stop loss
            elif ((trade['action'] == 'BUY' and current_price <= trade['stop_loss']) or
                  (trade['action'] == 'SELL' and current_price >= trade['stop_loss'])):
                
                trade['exit_reason'] = 'STOP_LOSS'
                trade['exit_price_actual'] = current_price
                trade['profit_loss'] = -self.config.FIXED_RISK_AMOUNT
                trades_to_close.append(trade)
        
        # Remove closed trades from active trades
        for trade in trades_to_close:
            if trade['trade_id'] in self.active_trades:
                del self.active_trades[trade['trade_id']]
                trade['status'] = 'CLOSED'
                self.trade_history.append(trade)
        
        return trades_to_close
    
    def get_active_trade_count(self) -> int:
        """Get number of active trades"""
        return len(self.active_trades)
    
    def can_open_new_trade(self) -> bool:
        """Check if new trade can be opened"""
        return self.get_active_trade_count() < self.config.MAX_CONCURRENT_TRADES
    
    def get_trading_summary(self) -> Dict:
        """Get summary of trading activity"""
        total_trades = len(self.trade_history)
        if total_trades == 0:
            return {'total_trades': 0, 'win_rate': 0, 'total_pnl': 0}
        
        winning_trades = sum(1 for trade in self.trade_history if trade['profit_loss'] > 0)
        total_pnl = sum(trade['profit_loss'] for trade in self.trade_history)
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': total_trades - winning_trades,
            'win_rate': winning_trades / total_trades,
            'total_pnl': total_pnl,
            'active_trades': self.get_active_trade_count()
        }


def simulate_grid_trades(
    price_data: pd.DataFrame, # Must contain 'timestamp', 'open', 'high', 'low', 'close', 'volume'
    initial_capital: float,
    grid_spacing: float,
    risk_per_trade: float,
    profit_per_trade: float, # Target profit per trade
    ml_actions: Optional[List[int]] = None # List of actions from ML model (-1 SELL, 0 HOLD, 1 BUY)
) -> Tuple[List[Dict], float]:
    """
    Simulates grid trading based on historical price data and ML model actions.

    Args:
        price_data: DataFrame with OHLCV data and 'timestamp'.
        initial_capital: Starting capital for the simulation.
        grid_spacing: Fixed percentage for grid lines (e.g., 0.0025 for 0.25%).
        risk_per_trade: Fixed dollar amount to risk per trade.
        profit_per_trade: Fixed dollar amount for take profit.
        ml_actions: Optional list of actions (SELL=-1, HOLD=0, BUY=1) from an ML model,
                    corresponding to each row in price_data. If None, a simple
                    grid logic (buy at lower, sell at upper) might be implied,
                    but for ML-enhanced system, this should be provided.

    Returns:
        A tuple containing:
        - trade_history: A list of dictionaries, each representing a trade.
        - final_equity: The equity at the end of the simulation.
    """
    logger = logging.getLogger('GridTradingSimulator')
    logger.info(f"Starting grid simulation: Initial Capital ${initial_capital}, Grid Spacing {grid_spacing*100}%")

    if 'close' not in price_data.columns or 'timestamp' not in price_data.columns:
        logger.error("Price data must contain 'close' and 'timestamp' columns.")
        return [], initial_capital
        
    if ml_actions is not None and len(ml_actions) != len(price_data):
        logger.error("Length of ml_actions must match length of price_data.")
        return [], initial_capital

    balance = initial_capital
    trade_history = []
    open_positions = [] # List of open trades: {'id', 'type', 'entry_price', 'size', 'stop_loss', 'take_profit', 'entry_time'}
    trade_id_counter = 0
    
    # Initial grid setup (can be dynamic, but spec says fixed 0.25%)
    # For simplicity, grid lines are not explicitly tracked here but implied by entry/exit logic.
    # Decisions are made based on ML actions at each data point.

    for i, row in price_data.iterrows():
        current_price = row['close'] # Use close price for decision making and execution
        current_time = row['timestamp']

        # Process existing open positions: check SL/TP
        new_open_positions = []
        for pos in open_positions:
            closed = False
            pnl = 0
            exit_price = None

            if pos['type'] == 'LONG':
                if current_price <= pos['stop_loss']:
                    exit_price = pos['stop_loss']
                    pnl = (exit_price - pos['entry_price']) * pos['size']
                    closed = True
                    logger.debug(f"LONG SL hit: Entry {pos['entry_price']}, Exit {exit_price}, Size {pos['size']}, PnL {pnl}")
                elif current_price >= pos['take_profit']:
                    exit_price = pos['take_profit']
                    pnl = (exit_price - pos['entry_price']) * pos['size']
                    closed = True
                    logger.debug(f"LONG TP hit: Entry {pos['entry_price']}, Exit {exit_price}, Size {pos['size']}, PnL {pnl}")
            
            elif pos['type'] == 'SHORT':
                if current_price >= pos['stop_loss']: # SL for short is above entry
                    exit_price = pos['stop_loss']
                    pnl = (pos['entry_price'] - exit_price) * pos['size'] # (entry - exit) for short
                    closed = True
                    logger.debug(f"SHORT SL hit: Entry {pos['entry_price']}, Exit {exit_price}, Size {pos['size']}, PnL {pnl}")
                elif current_price <= pos['take_profit']: # TP for short is below entry
                    exit_price = pos['take_profit']
                    pnl = (pos['entry_price'] - exit_price) * pos['size']
                    closed = True
                    logger.debug(f"SHORT TP hit: Entry {pos['entry_price']}, Exit {exit_price}, Size {pos['size']}, PnL {pnl}")

            if closed:
                balance += pnl
                trade_history.append({
                    'trade_id': pos['id'],
                    'type': pos['type'],
                    'entry_price': pos['entry_price'],
                    'exit_price': exit_price,
                    'size': pos['size'],
                    'profit_loss': pnl,
                    'entry_time': pos['entry_time'],
                    'exit_time': current_time,
                    'reason': 'SL/TP'
                })
            else:
                new_open_positions.append(pos)
        open_positions = new_open_positions

        # Consider new trades based on ML action for the current candle
        if ml_actions is not None:
            action = GridAction(ml_actions[i]) # Convert int to GridAction enum
            
            # Position Sizing (simplified from spec for backtesting)
            # position_size = risk_per_trade / (btc_price × 0.0025_grid_spacing_as_SL_distance)
            # This implies SL is one grid away.
            # The spec's position sizing: $10_risk / (btc_price × 0.0025)
            # Let's use a fixed size for simplicity or derive from risk.
            # If SL is one grid away (0.25%), then price_change_for_sl = current_price * grid_spacing
            # Size = risk_per_trade / price_change_for_sl 
            
            if current_price <= 0: continue # Avoid division by zero if price is invalid

            price_change_for_sl = current_price * grid_spacing 
            if price_change_for_sl <= 0: continue # Avoid if price_change_for_sl is zero or negative

            position_size = risk_per_trade / price_change_for_sl
            
            # Max position constraint from spec (simplified: ensure not risking too much of balance)
            # max_allowable_pos_value = balance * 0.1 # Example: risk 10% of balance on one asset
            # max_pos_by_capital = max_allowable_pos_value / current_price
            # position_size = min(position_size, max_pos_by_capital)


            if action == GridAction.BUY:
                # Max concurrent trades check (simplified)
                if len(open_positions) < 15: # Max 15 concurrent trades from spec
                    trade_id_counter += 1
                    entry_price = current_price
                    sl_price = entry_price * (1 - grid_spacing) # SL is one grid level down
                    tp_price = entry_price * (1 + grid_spacing * (profit_per_trade / risk_per_trade)) # TP based on R:R

                    open_positions.append({
                        'id': trade_id_counter, 'type': 'LONG', 'entry_price': entry_price,
                        'size': position_size, 'stop_loss': sl_price, 'take_profit': tp_price,
                        'entry_time': current_time
                    })
                    logger.debug(f"NEW LONG: Price {entry_price}, Size {position_size}, SL {sl_price}, TP {tp_price}")

            elif action == GridAction.SELL:
                if len(open_positions) < 15:
                    trade_id_counter += 1
                    entry_price = current_price
                    sl_price = entry_price * (1 + grid_spacing) # SL is one grid level up
                    tp_price = entry_price * (1 - grid_spacing * (profit_per_trade / risk_per_trade)) # TP based on R:R

                    open_positions.append({
                        'id': trade_id_counter, 'type': 'SHORT', 'entry_price': entry_price,
                        'size': position_size, 'stop_loss': sl_price, 'take_profit': tp_price,
                        'entry_time': current_time
                    })
                    logger.debug(f"NEW SHORT: Price {entry_price}, Size {position_size}, SL {sl_price}, TP {tp_price}")
            
            # HOLD action: do nothing

    # Close any remaining open positions at the end of the simulation using last price
    last_price = price_data['close'].iloc[-1]
    last_time = price_data['timestamp'].iloc[-1]
    for pos in open_positions:
        pnl = 0
        exit_price = last_price
        if pos['type'] == 'LONG':
            pnl = (exit_price - pos['entry_price']) * pos['size']
        elif pos['type'] == 'SHORT':
            pnl = (pos['entry_price'] - exit_price) * pos['size']
        
        balance += pnl
        trade_history.append({
            'trade_id': pos['id'], 'type': pos['type'], 'entry_price': pos['entry_price'],
            'exit_price': exit_price, 'size': pos['size'], 'profit_loss': pnl,
            'entry_time': pos['entry_time'], 'exit_time': last_time, 'reason': 'End of simulation'
        })
    
    logger.info(f"Grid simulation finished. Final Equity: ${balance:.2f}, Total Trades: {len(trade_history)}")
    return trade_history, balance


def simulate_grid_trades_for_ppo(
    price_data: pd.DataFrame, # Must contain 'timestamp', 'open', 'high', 'low', 'close', 'volume' and FEATURES
    initial_capital: float,
    grid_spacing: float,
    risk_per_trade: float,
    profit_per_trade: float,
    ml_actions: List[int] # List of actions from PPO (0:HOLD, 1:BUY, 2:SELL)
) -> Tuple[List[Dict], float]:
    """
    Specialized simulation for PPO environment callback.
    PPO actions are 0 (HOLD), 1 (BUY), 2 (SELL). These need to be mapped to GridAction values.
    This function is largely similar to `simulate_grid_trades` but ensures correct action mapping.
    """
    
    # Map PPO actions (0,1,2) to GridTradingCore actions (-1,0,1)
    # 0 (PPO_HOLD) -> 0 (GridAction.HOLD)
    # 1 (PPO_BUY)  -> 1 (GridAction.BUY)
    # 2 (PPO_SELL) -> -1 (GridAction.SELL)
    
    mapped_ml_actions = []
    for ppo_action in ml_actions:
        if ppo_action == 1: # PPO BUY
            mapped_ml_actions.append(GridAction.BUY.value)
        elif ppo_action == 2: # PPO SELL
            mapped_ml_actions.append(GridAction.SELL.value)
        else: # PPO HOLD (0)
            mapped_ml_actions.append(GridAction.HOLD.value)
            
    return simulate_grid_trades(
        price_data=price_data,
        initial_capital=initial_capital,
        grid_spacing=grid_spacing,
        risk_per_trade=risk_per_trade,
        profit_per_trade=profit_per_trade,
        ml_actions=mapped_ml_actions
    )


# Example Usage (can be removed or kept for testing)
if __name__ == '__main__':
    import logging
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Create dummy price data
    num_candles = 1000
    price_df = pd.DataFrame({
        'timestamp': pd.to_datetime(pd.date_range(start='2023-01-01', periods=num_candles, freq='min')),
        'open': np.random.rand(num_candles) * 100 + 20000,
        'high': np.random.rand(num_candles) * 50 + (np.random.rand(num_candles) * 100 + 20000),
        'low': (np.random.rand(num_candles) * 100 + 20000) - np.random.rand(num_candles) * 50,
        'close': np.random.rand(num_candles) * 100 + 20000,
        'volume': np.random.rand(num_candles) * 10 + 1
    })
    # Ensure high is highest and low is lowest
    price_df['high'] = price_df[['open', 'close']].max(axis=1) + np.random.rand(num_candles) * 20
    price_df['low'] = price_df[['open', 'close']].min(axis=1) - np.random.rand(num_candles) * 20


    # Dummy ML actions (random for testing: -1, 0, 1)
    dummy_actions = np.random.choice([-1, 0, 1], size=num_candles).tolist()

    initial_cap = 300.0
    grid_sp = 0.0025
    risk_val = 10.0
    profit_val = 20.0

    print(f"Running simulation with {num_candles} candles...")
    trade_history, final_equity = simulate_grid_trades(
        price_data=price_df,
        initial_capital=initial_cap,
        grid_spacing=grid_sp,
        risk_per_trade=risk_val,
        profit_per_trade=profit_val,
        ml_actions=dummy_actions
    )

    print(f"Simulation Complete. Final Equity: ${final_equity:.2f}")
    print(f"Total Trades: {len(trade_history)}")
    # for trade in trade_history[:5]: # Print first 5 trades
    #     print(trade)

    # Test PPO specific simulation
    dummy_ppo_actions = np.random.choice([0, 1, 2], size=num_candles).tolist() # 0:H, 1:B, 2:S
    print(f"Running PPO simulation with {num_candles} candles...")
    trade_history_ppo, final_equity_ppo = simulate_grid_trades_for_ppo(
        price_data=price_df,
        initial_capital=initial_cap,
        grid_spacing=grid_sp,
        risk_per_trade=risk_val,
        profit_per_trade=profit_val,
        ml_actions=dummy_ppo_actions
    )
    print(f"PPO Simulation Complete. Final Equity: ${final_equity_ppo:.2f}")
    print(f"Total PPO Trades: {len(trade_history_ppo)}")
