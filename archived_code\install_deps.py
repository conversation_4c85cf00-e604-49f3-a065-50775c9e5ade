#!/usr/bin/env python3
"""
Quick dependency installer for the real money trading webapp
"""
import subprocess
import sys

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    """Install required packages"""
    packages = [
        "streamlit>=1.28.0",
        "plotly>=5.17.0", 
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "ccxt>=4.1.0",
        "python-dotenv>=1.0.0",
        "cryptography>=41.0.0"
    ]
    
    print("🚀 Installing dependencies for Real Money Trading Webapp...")
    print("=" * 60)
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("=" * 60)
    print(f"📊 Installation Summary: {success_count}/{len(packages)} packages installed")
    
    if success_count == len(packages):
        print("✅ All dependencies installed successfully!")
        print("🌐 Ready to launch webapp with: streamlit run real_money_trading_webapp.py")
    else:
        print("⚠️ Some packages failed to install. Check the output above.")

if __name__ == "__main__":
    main()
