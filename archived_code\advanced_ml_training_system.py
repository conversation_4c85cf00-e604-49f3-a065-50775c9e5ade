"""
Advanced ML Training System with TCN, CNN, and PPO
Complete implementation with grid search optimization and live Bitcoin data
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import json
import joblib
from sklearn.model_selection import train_test_split, ParameterGrid
import optuna

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import data collection from original system
from ml_training_system import DataCollector

# Import grid trading components
from grid_feature_engineering import GridFeatureEngineering
from grid_composite_metrics import GridCompositeMetrics
from grid_trading_core import GridLevelCalculator, GridAction, simulate_grid_trades # Added simulate_grid_trades

# Import our advanced models
from models.ensemble_model import EnsembleTrader, create_ensemble_trader

class AdvancedMLTradingSystem:
    """Advanced ML training system with TCN, CNN, and PPO models"""

    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('AdvancedMLTradingSystem')

        # Initialize components
        self.data_collector = DataCollector(config)
        # Ensure GridFeatureEngineering is initialized correctly
        self.feature_engineer = GridFeatureEngineering(config)
        self.metrics_calculator = GridCompositeMetrics(config)
        self.grid_calculator = GridLevelCalculator(config)

        # Model storage with grid trading focus
        self.ensemble_models = {}
        self.training_results = {}
        self.best_models = {}
        self.best_composite_model = {'model': None, 'score': 0.0, 'net_profit': 0.0}
        self.best_profit_model = {'model': None, 'score': 0.0, 'net_profit': 0.0}

        # Setup logging
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format=config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(os.path.join(config.LOGS_DIR, 'advanced_ml_training.log')),
                logging.StreamHandler()
            ]
        )

        self.logger.info("🚀 Advanced ML Trading System initialized")

    def collect_and_prepare_data(self, symbol: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Collect and prepare grid trading data with BTC and ETH"""
        self.logger.info(f"📊 Collecting grid trading data for {symbol}...")

        # Calculate total days needed (training + testing + buffer)
        total_days = self.config.TRAINING_DAYS + self.config.TESTING_DAYS + 20

        # Collect BTC data (primary trading pair)
        btc_df = self.data_collector.collect_historical_data('BTCUSDT', self.config.DATA_FREQUENCY, total_days)

        # Collect ETH data (for ETH/BTC ratio)
        eth_df = self.data_collector.collect_historical_data('ETHUSDT', self.config.DATA_FREQUENCY, total_days)

        if len(btc_df) < 100 or len(eth_df) < 100:
            raise ValueError(f"Insufficient data: BTC={len(btc_df)}, ETH={len(eth_df)} rows")

        self.logger.info(f"Collected BTC: {len(btc_df)}, ETH: {len(eth_df)} data points")

        # Grid feature engineering with 4 indicators only
        features_df = self.feature_engineer.create_grid_features(btc_df, eth_df)

        if not self.feature_engineer.validate_features(features_df):
            raise ValueError("Feature validation failed - not matching specification")

        # Split data according to trading plan (60 days training, 30 days testing)
        # For 1-minute data: 60 days = 86,400 minutes, 30 days = 43,200 minutes
        training_minutes = self.config.TRAINING_DAYS * 24 * 60
        testing_minutes = self.config.TESTING_DAYS * 24 * 60

        # Ensure we have enough data
        if len(features_df) < training_minutes + testing_minutes:
            self.logger.warning(f"Limited data for {symbol}, adjusting split...")
            split_ratio = 0.67  # 67% training, 33% testing (close to 60/30 split)
            split_idx = int(len(features_df) * split_ratio)
        else:
            split_idx = len(features_df) - testing_minutes

        train_data = features_df.iloc[:split_idx].copy()
        test_data = features_df.iloc[split_idx:].copy()

        self.logger.info(f"Grid data split: {len(train_data)} training, {len(test_data)} testing")
        self.logger.info(f"Features: {[col for col in train_data.columns if col not in ['open', 'high', 'low', 'close', 'volume']]}")

        return train_data, test_data

    def grid_search_optimization(self,
                                symbol: str,
                                train_data: pd.DataFrame,
                                val_data: pd.DataFrame) -> Dict[str, Any]:
        """Perform grid search optimization for ensemble parameters"""

        self.logger.info(f"🔍 Starting grid search optimization for {symbol}...")

        # Define comprehensive parameter grid
        param_grid = {
            'tcn_params': [
                {'sequence_length': 60, 'num_filters': 64, 'num_blocks': 4, 'dropout_rate': 0.2},
                {'sequence_length': 50, 'num_filters': 32, 'num_blocks': 3, 'dropout_rate': 0.1},
                {'sequence_length': 70, 'num_filters': 128, 'num_blocks': 5, 'dropout_rate': 0.3},
                {'sequence_length': 40, 'num_filters': 96, 'num_blocks': 6, 'dropout_rate': 0.25}
            ],
            'cnn_params': [
                {'window_size': 50, 'num_filters': [32, 64, 128], 'dropout_rate': 0.3, 'kernel_sizes': [3, 5, 7]},
                {'window_size': 40, 'num_filters': [16, 32, 64], 'dropout_rate': 0.2, 'kernel_sizes': [2, 4, 6]},
                {'window_size': 60, 'num_filters': [64, 128, 256], 'dropout_rate': 0.4, 'kernel_sizes': [4, 6, 8]},
                {'window_size': 45, 'num_filters': [24, 48, 96], 'dropout_rate': 0.25, 'kernel_sizes': [3, 5, 9]}
            ],
            'ppo_params': [
                {'learning_rate': 3e-4, 'n_steps': 2048, 'batch_size': 64, 'gamma': 0.99},
                {'learning_rate': 1e-4, 'n_steps': 1024, 'batch_size': 32, 'gamma': 0.95},
                {'learning_rate': 5e-4, 'n_steps': 4096, 'batch_size': 128, 'gamma': 0.98},
                {'learning_rate': 2e-4, 'n_steps': 1536, 'batch_size': 96, 'gamma': 0.97}
            ],
            'ensemble_weights': [
                {'tcn_weight': 0.4, 'cnn_weight': 0.4, 'ppo_weight': 0.2},
                {'tcn_weight': 0.5, 'cnn_weight': 0.3, 'ppo_weight': 0.2},
                {'tcn_weight': 0.3, 'cnn_weight': 0.5, 'ppo_weight': 0.2},
                {'tcn_weight': 0.35, 'cnn_weight': 0.35, 'ppo_weight': 0.3}
            ]
        }

        best_score = 0.0
        best_params = None
        best_model = None
        total_combinations = len(param_grid['tcn_params']) * len(param_grid['cnn_params']) * len(param_grid['ppo_params']) * len(param_grid['ensemble_weights'])
        current_combination = 0

        self.logger.info(f"Testing {total_combinations} parameter combinations...")

        # Grid search with progress tracking
        for tcn_param in param_grid['tcn_params']:
            for cnn_param in param_grid['cnn_params']:
                for ppo_param in param_grid['ppo_params']:
                    for ensemble_weights in param_grid['ensemble_weights']:

                        current_combination += 1
                        progress = (current_combination / total_combinations) * 100

                        try:
                            self.logger.info(f"Testing combination {current_combination}/{total_combinations} ({progress:.1f}%)")

                            # Create ensemble with current parameters
                            ensemble = create_ensemble_trader(
                                tcn_weight=ensemble_weights['tcn_weight'],
                                cnn_weight=ensemble_weights['cnn_weight'],
                                ppo_weight=ensemble_weights['ppo_weight']
                            )

                            # Initialize models with specific parameters
                            num_features = len([col for col in train_data.columns
                                              if col not in ['label', 'future_return', 'open', 'high', 'low', 'close', 'volume']])

                            ensemble.initialize_models(
                                sequence_length=tcn_param['sequence_length'],
                                window_size=cnn_param['window_size'],
                                num_features=num_features,
                                # Pass PPO context
                                reward_calculator=self.metrics_calculator,
                                initial_capital=self.config.INITIAL_CAPITAL,
                                grid_spacing=self.config.GRID_SPACING,
                                risk_per_trade=self.config.RISK_PER_TRADE,
                                profit_per_trade=self.config.PROFIT_PER_TRADE
                            )

                            # Update model parameters
                            if hasattr(ensemble.tcn_model, 'dropout_rate'):
                                ensemble.tcn_model.dropout_rate = tcn_param['dropout_rate']
                            if hasattr(ensemble.cnn_model, 'dropout_rate'):
                                ensemble.cnn_model.dropout_rate = cnn_param['dropout_rate']

                            # Train ensemble (reduced epochs for grid search)
                            results = ensemble.train_ensemble(
                                train_data, val_data,
                                tcn_epochs=15,  # Reduced for speed
                                cnn_epochs=15,
                                ppo_timesteps=5000
                            )

                            # Calculate comprehensive score
                            score = self._calculate_grid_search_score(results, val_data, ensemble)

                            if score > best_score:
                                best_score = score
                                best_params = {
                                    'tcn': tcn_param,
                                    'cnn': cnn_param,
                                    'ppo': ppo_param,
                                    'ensemble_weights': ensemble_weights
                                }
                                best_model = ensemble

                                self.logger.info(f"🎯 New best score: {score:.4f}")

                        except Exception as e:
                            self.logger.warning(f"Parameter combination {current_combination} failed: {e}")
                            continue

        self.logger.info(f"Grid search completed. Best score: {best_score:.4f}")

        return {
            'best_score': best_score,
            'best_params': best_params,
            'best_model': best_model,
            'total_combinations_tested': current_combination
        }

    def _calculate_grid_search_score(self,
                                   training_results: Dict[str, Any],
                                   val_data: pd.DataFrame,
                                   ensemble: EnsembleTrader) -> float:
        """Calculate comprehensive score for grid search"""

        try:
            # Get ensemble evaluation metrics
            if 'ensemble' in training_results and isinstance(training_results['ensemble'], dict):
                ensemble_metrics = training_results['ensemble']
            else:
                # Fallback: evaluate ensemble directly
                ensemble_metrics = ensemble._evaluate_ensemble(val_data)

            if 'error' in ensemble_metrics:
                return 0.0

            # Calculate weighted composite score
            weights = {
                'accuracy': 0.25,      # Win rate
                'precision': 0.20,     # Risk-adjusted returns
                'recall': 0.15,        # Profit factor
                'f1_score': 0.10,      # Recovery factor
            }

            score = 0.0
            for metric, weight in weights.items():
                if metric in ensemble_metrics:
                    score += ensemble_metrics[metric] * weight

            # Add model-specific bonuses
            model_bonus = 0.0

            # TCN performance bonus
            if 'tcn' in training_results and 'train_metrics' in training_results['tcn']:
                tcn_acc = training_results['tcn']['train_metrics'].get('accuracy', 0)
                model_bonus += tcn_acc * 0.1

            # CNN performance bonus
            if 'cnn' in training_results and 'train_metrics' in training_results['cnn']:
                cnn_acc = training_results['cnn']['train_metrics'].get('accuracy', 0)
                model_bonus += cnn_acc * 0.1

            # PPO performance bonus
            if 'ppo' in training_results and 'train_results' in training_results['ppo']:
                ppo_reward = training_results['ppo']['train_results'].get('mean_reward', 0)
                if ppo_reward > 0:
                    model_bonus += 0.1

            # Consistency bonus
            if 'num_predictions' in ensemble_metrics and ensemble_metrics['num_predictions'] > 50:
                consistency_bonus = 0.1
                score += consistency_bonus

            total_score = score + model_bonus

            return min(total_score, 1.0)  # Cap at 1.0

        except Exception as e:
            self.logger.warning(f"Score calculation failed: {e}")
            return 0.0

    def train_symbol_models(self, symbol: str) -> Dict[str, Any]:
        """Train all models for a specific symbol"""

        self.logger.info(f"🤖 Training advanced models for {symbol}...")

        try:
            # Collect and prepare data
            train_data, test_data = self.collect_and_prepare_data(symbol)

            # Split training data for validation
            # Ensure 'label' or a similar target variable is created by feature engineering if needed by models
            # For grid trading, the 'label' might be the optimal action (BUY/SELL/HOLD)
            # This needs to be generated based on future price movements and grid logic.
            # For now, assuming feature_engineer.create_grid_features adds necessary columns.

            # Generate labels for training/testing if not already present
            # This is a placeholder; actual label generation will be complex
            if 'label' not in train_data.columns:
                 train_data['label'] = np.random.randint(0, 3, size=len(train_data)) # 0:HOLD, 1:BUY, 2:SELL
            if 'label' not in test_data.columns:
                 test_data['label'] = np.random.randint(0, 3, size=len(test_data))


            train_split, val_split = train_test_split(train_data, test_size=0.2, shuffle=False)

            # Perform grid search optimization
            grid_results = self.grid_search_optimization(symbol, train_split, val_split)

            if grid_results['best_model'] is None:
                self.logger.error(f"Grid search failed for {symbol}")
                return {'error': 'Grid search optimization failed'}

            # Train final model with best parameters on full training data
            self.logger.info(f"Training final model with best parameters for {symbol}...")

            best_ensemble = grid_results['best_model']

            # Re-initialize with best parameters and train on full training data
            num_features = len([col for col in train_data.columns
                              if col not in ['label', 'future_return', 'open', 'high', 'low', 'close', 'volume']])

            final_ensemble = create_ensemble_trader()
            final_ensemble.initialize_models(
                sequence_length=grid_results['best_params']['tcn']['sequence_length'],
                window_size=grid_results['best_params']['cnn']['window_size'],
                num_features=num_features,
                # Pass PPO context
                reward_calculator=self.metrics_calculator,
                initial_capital=self.config.INITIAL_CAPITAL,
                grid_spacing=self.config.GRID_SPACING,
                risk_per_trade=self.config.RISK_PER_TRADE,
                profit_per_trade=self.config.PROFIT_PER_TRADE
            )

            # Full training
            final_results = final_ensemble.train_ensemble(
                train_data, test_data, # Use full train_data for final training
                tcn_epochs=100,
                cnn_epochs=100,
                ppo_timesteps=100000
                # PPO context is set during initialize_models, not needed here
            )

            # Optimize ensemble weights
            self.logger.info(f"Optimizing ensemble weights for {symbol}...")
            weight_optimization = final_ensemble.optimize_weights(
                train_data, # Use train_data for weight optimization
                test_data, 
                n_trials=50,
                # Pass the composite score calculator for evaluation during optimization
                reward_calculator=self.metrics_calculator,
                initial_capital=self.config.INITIAL_CAPITAL,
                grid_spacing=self.config.GRID_SPACING,
                risk_per_trade=self.config.RISK_PER_TRADE,
                profit_per_trade=self.config.PROFIT_PER_TRADE
            )

            # Final evaluation on test data
            # This should simulate grid trading and calculate the composite score
            self.logger.info(f"Simulating grid trading on test data for {symbol}...")
            
            # The ensemble model's predict_ensemble should output BUY/SELL/HOLD actions
            # The simulation needs the raw price data from test_data, not just features
            
            # Assuming test_data contains 'open', 'high', 'low', 'close', 'volume' columns
            # and features used by the model.
            
            # Create a stream of features for the model to predict on
            feature_columns = [col for col in test_data.columns if col not in ['open', 'high', 'low', 'close', 'volume', 'timestamp', 'label', 'future_return']]
            
            actions = []
            for i in range(len(test_data)):
                # We need to ensure that `predict_ensemble` can take a single row of features
                # or that we prepare data in the way `predict_ensemble` expects.
                # The current `predict_ensemble` in `ensemble_model.py` seems to expect a DataFrame
                # and prepares sequences/windows internally. This might be inefficient for step-by-step simulation.
                # For now, let's pass a DataFrame representing the current point and necessary history.
                # This needs to be aligned with how TCN/CNN models expect input.
                
                # Placeholder: This part needs careful implementation based on how models consume data.
                # For simplicity, let's assume predict_ensemble can take the current feature set.
                # This is a significant simplification.
                if i < max(final_ensemble.sequence_length if final_ensemble.tcn_model else 0, 
                           final_ensemble.window_size if final_ensemble.cnn_model else 0):
                    actions.append(GridAction.HOLD.value) # Not enough history for TCN/CNN
                    continue

                # Construct the input data for the model for the current timestep
                # This needs to be a DataFrame with the same columns the model was trained on.
                # The exact historical length depends on TCN's sequence_length and CNN's window_size.
                # For PPO, it depends on its observation space.
                # This is a placeholder for creating the correct model input for the current step.
                current_model_input_df = test_data.iloc[max(0, i - final_ensemble.sequence_length):i+1][feature_columns]
                
                if current_model_input_df.empty:
                    actions.append(GridAction.HOLD.value)
                    continue

                prediction_result = final_ensemble.predict_ensemble(current_model_input_df) 
                
                # Map prediction_result['ensemble_prediction'] (0 or 1 from current ensemble)
                # to GridAction (BUY=1, SELL=-1, HOLD=0)
                # This mapping needs to be defined based on how models are trained.
                # Assuming 0 maps to HOLD, 1 maps to BUY for now. Needs SELL.
                # The spec says BUY/SELL/HOLD. The ensemble currently predicts 0 or 1.
                # This needs to be changed in the ensemble model and its components.
                # For now, let's assume: 0 -> HOLD, 1 -> BUY, (needs a SELL signal)
                # Let's assume predict_ensemble is modified to return -1, 0, 1
                raw_prediction = prediction_result['ensemble_prediction'] # This should be -1, 0, or 1
                
                # Temporary mapping if predict_ensemble returns 0 or 1
                if raw_prediction == 1: # BUY
                    action = GridAction.BUY.value
                # elif raw_prediction == -1: # SELL (assuming model can output this)
                #    action = GridAction.SELL.value
                else: # HOLD (or if model outputs 0)
                    action = GridAction.HOLD.value
                actions.append(action)

            # Simulate trades based on these actions
            # `simulate_grid_trades` needs to be implemented in `grid_trading_core.py`
            # It should take price data, actions, and grid parameters, and return trade history
            
            # Pass the original test_data columns needed for simulation (e.g., 'close' prices)
            price_data_for_simulation = test_data[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
            price_data_for_simulation['action'] = actions
            
            trade_history, final_equity = simulate_grid_trades(
                price_data=price_data_for_simulation,
                initial_capital=self.config.INITIAL_CAPITAL,
                grid_spacing=self.config.GRID_SPACING,
                risk_per_trade=self.config.RISK_PER_TRADE,
                profit_per_trade=self.config.PROFIT_PER_TRADE,
                ml_actions=actions # Pass the list of actions
            )
            
            # Calculate composite score using the actual trade_history from simulation
            test_metrics_from_simulation = self.metrics_calculator.calculate_composite_score(
                trade_history, self.config.INITIAL_CAPITAL
            )
            composite_score = test_metrics_from_simulation.get('composite_score', 0.0)
            net_profit = final_equity - self.config.INITIAL_CAPITAL # Actual net profit from simulation

            self.logger.info(f"Test simulation composite score: {composite_score:.4f}, Net profit: ${net_profit:.2f}")

            # Update best model tracking
            self._update_best_models(final_ensemble, composite_score, net_profit, symbol)

            # Store results
            results = {
                'symbol': symbol,
                'grid_search': grid_results,
                'final_training': final_results,
                'weight_optimization': weight_optimization,
                'test_metrics': test_metrics_from_simulation, # Use metrics from simulation
                'composite_score': composite_score,
                'net_profit': net_profit, # Changed from estimated_profit
                'meets_threshold': composite_score >= self.config.COMPOSITE_SCORE_THRESHOLD,
                'model': final_ensemble
            }

            # Save model based on performance (always save best models)
            model_path = os.path.join(self.config.MODELS_DIR, f"{symbol}_advanced")

            if composite_score >= self.config.COMPOSITE_SCORE_THRESHOLD:
                final_ensemble.save_ensemble(model_path)
                self.logger.info(f"✅ {symbol} model saved - MEETS THRESHOLD (Score: {composite_score:.3f})")
            elif (composite_score >= self.best_composite_model['score'] or
                  net_profit >= self.best_profit_model['net_profit']): # Changed from estimated_profit
                final_ensemble.save_ensemble(f"{model_path}_best_available")
                self.logger.info(f"💾 {symbol} model saved - BEST AVAILABLE (Score: {composite_score:.3f})")
            else:
                self.logger.warning(f"⚠️ {symbol} model below threshold (Score: {composite_score:.3f})")

            self.ensemble_models[symbol] = final_ensemble
            self.training_results[symbol] = results

            return results

        except Exception as e:
            self.logger.error(f"Failed to train models for {symbol}: {e}")
            return {'error': str(e)}

    def _calculate_composite_score(self, test_results: Dict[str, Any],
                                  trade_history: List[Dict] = None,
                                  initial_capital: float = None) -> float:
        """Calculate composite score using grid trading metrics"""

        if initial_capital is None:
            initial_capital = self.config.INITIAL_CAPITAL

        if trade_history is not None: # Prioritize actual trade history
            grid_metrics = self.metrics_calculator.calculate_composite_score(
                trade_history, initial_capital
            )
            self.logger.debug(f"Calculated composite score from trade_history: {grid_metrics.get('composite_score', 0.0):.4f}")
            return grid_metrics.get('composite_score', 0.0)

        # Fallback: estimate from ML metrics (if no trade_history)
        if 'error' in test_results:
            self.logger.warning("Error in test_results, returning 0.0 for composite score.")
            return 0.0
        
        ml_metrics = test_results
        self.logger.debug(f"Estimating composite score from ML metrics: {ml_metrics}")

        estimated_metrics = {
            'win_rate': ml_metrics.get('accuracy', 0.0), 
            'equity': ml_metrics.get('precision', 0.0) * 0.2, # Simplified proxy
            'sortino_ratio': ml_metrics.get('f1_score', 0.0) * 0.5, # Simplified proxy
            'calmar_ratio': ml_metrics.get('recall', 0.0) * 0.5, # Simplified proxy
            'profit_factor': ml_metrics.get('precision', 0.0), # Simplified proxy
            'max_drawdown': max(0, 1 - ml_metrics.get('accuracy', 1.0) * 0.2), # Inverted, ensure 0-1
            'risk_of_ruin': max(0, 1 - ml_metrics.get('accuracy', 1.0) * 0.1), # Inverted, ensure 0-1
            'trade_frequency': 0.5  # Neutral assumption if not directly measurable
        }
        
        # Normalize estimated metrics to be between 0 and 1
        for key in estimated_metrics:
            estimated_metrics[key] = max(0.0, min(1.0, estimated_metrics[key]))


        # Calculate using grid metrics calculator
        grid_score = self.metrics_calculator._calculate_weighted_composite(estimated_metrics)

        self.logger.debug(f"Estimated grid composite score: {grid_score:.4f}")
        return grid_score

    def _update_best_models(self, model, composite_score: float, net_profit: float, symbol: str):
        """Update best composite and best profit model tracking"""

        # Update best composite score model
        if composite_score > self.best_composite_model['score']:
            self.best_composite_model = {
                'model': model,
                'score': composite_score,
                'net_profit': net_profit,
                'symbol': symbol,
                'timestamp': datetime.now().isoformat()
            }
            self.logger.info(f"🏆 NEW BEST COMPOSITE: {symbol} - Score: {composite_score:.4f}")

        # Update best net profit model
        if net_profit > self.best_profit_model['net_profit']:
            self.best_profit_model = {
                'model': model,
                'score': composite_score,
                'net_profit': net_profit,
                'symbol': symbol,
                'timestamp': datetime.now().isoformat()
            }
            self.logger.info(f"💰 NEW BEST PROFIT: {symbol} - Profit: ${net_profit:.2f}")

    def get_deployment_model(self) -> Dict[str, Any]:
        """Get the best model for deployment based on specification criteria"""

        # Primary: Model with highest composite score AND highest net profit
        if (self.best_composite_model['model'] is not None and
            self.best_composite_model['score'] >= self.config.COMPOSITE_SCORE_THRESHOLD):

            self.logger.info(f"🚀 DEPLOYING: Best composite model (Score: {self.best_composite_model['score']:.4f})")
            return self.best_composite_model

        # Fallback: Best available model (even if <85%)
        elif self.best_composite_model['model'] is not None:
            self.logger.info(f"🔄 DEPLOYING: Best available composite model (Score: {self.best_composite_model['score']:.4f})")
            return self.best_composite_model

        elif self.best_profit_model['model'] is not None:
            self.logger.info(f"🔄 DEPLOYING: Best profit model (Profit: ${self.best_profit_model['net_profit']:.2f})")
            return self.best_profit_model

        else:
            self.logger.error("❌ No models available for deployment")
            return {'model': None, 'error': 'No trained models available'}

    def train_all_symbols(self) -> Dict[str, Dict[str, Any]]:
        """Train models for all trading pairs"""

        self.logger.info("🚀 Starting training for all symbols...")

        all_results = {}
        successful_models = 0
        threshold_met = 0

        for symbol in self.config.TRADING_PAIRS:
            self.logger.info(f"Processing {symbol}...")

            results = self.train_symbol_models(symbol)
            all_results[symbol] = results

            if 'error' not in results:
                successful_models += 1
                if results.get('meets_threshold', False):
                    threshold_met += 1
                    self.best_models[symbol] = results['model']

        # Generate summary report with grid trading focus
        summary = {
            'total_symbols': len(self.config.TRADING_PAIRS),
            'successful_models': successful_models,
            'threshold_met': threshold_met,
            'success_rate': (threshold_met / max(successful_models, 1)) * 100,
            'best_composite_score': self.best_composite_model['score'],
            'best_net_profit': self.best_profit_model['net_profit'],
            'deployment_ready': self.best_composite_model['model'] is not None,
            'deployment_model': self.get_deployment_model(),
            'timestamp': datetime.now().isoformat()
        }

        all_results['summary'] = summary

        # Save training report
        self._save_training_report(all_results)

        # Log final results
        self.logger.info(f"🎉 Grid Trading Training completed:")
        self.logger.info(f"   Models meeting threshold: {threshold_met}/{successful_models}")
        self.logger.info(f"   Best composite score: {self.best_composite_model['score']:.4f}")
        self.logger.info(f"   Best net profit: ${self.best_profit_model['net_profit']:.2f}")
        self.logger.info(f"   Deployment ready: {'✅ YES' if summary['deployment_ready'] else '❌ NO'}")

        return all_results

    def get_grid_prediction(self, btc_data: pd.DataFrame, eth_data: pd.DataFrame) -> Tuple[GridAction, float]:
        """Get grid trading prediction (BUY/SELL/HOLD) from best model"""

        # Get deployment model
        deployment_model = self.get_deployment_model()

        if deployment_model['model'] is None:
            raise ValueError("No trained model available for prediction")

        ensemble = deployment_model['model']

        # Engineer grid features (4 indicators only)
        current_features = self.feature_engineer.get_current_features(btc_data, eth_data)

        # Get ensemble prediction
        prediction_result = ensemble.predict_ensemble(current_features)

        # Convert to grid action
        ensemble_pred_value = prediction_result['ensemble_prediction'] # Should be -1, 0, or 1
        confidence = prediction_result['confidence']

        # Map prediction to GridAction enum
        if ensemble_pred_value == 1: # BUY
            action = GridAction.BUY
        elif ensemble_pred_value == -1: # SELL
            action = GridAction.SELL
        else: # HOLD
            action = GridAction.HOLD

        self.logger.debug(f"Grid prediction: {action.name} (raw: {ensemble_pred_value}, conf: {confidence:.3f})")

        return action, confidence

    def get_prediction(self, symbol: str, current_data: pd.DataFrame) -> Tuple[float, float]:
        """Legacy prediction method - kept for compatibility.
        For grid trading, use get_grid_prediction.
        This method's output (float, float) is not directly compatible with GridAction.
        """
        self.logger.warning("Using legacy get_prediction. Output may not be suitable for GridAction decisions.")
        # For grid trading, we need both BTC and ETH data
        self.logger.warning("Using legacy prediction method - consider using get_grid_prediction()")

        if symbol not in self.best_models:
            # Try to load model
            model_path = os.path.join(self.config.MODELS_DIR, f"{symbol}_advanced")
            if os.path.exists(f"{model_path}_ensemble_config.joblib"):
                ensemble = create_ensemble_trader()
                ensemble.load_ensemble(model_path)
                self.best_models[symbol] = ensemble
            else:
                raise ValueError(f"No trained model available for {symbol}")

        ensemble = self.best_models[symbol]

        # Try to create grid features (will need ETH data)
        try:
            # Assume current_data is BTC data, try to get ETH data
            eth_data = self.data_collector.collect_historical_data('ETHUSDT', self.config.DATA_FREQUENCY, 1)
            features_df = self.feature_engineer.create_grid_features(current_data, eth_data)

            # Get latest features
            current_features = self.feature_engineer.get_current_features(current_data, eth_data)

            # Get ensemble prediction
            prediction_result = ensemble.predict_ensemble(current_features)

            prediction = prediction_result['ensemble_probability']
            confidence = prediction_result['confidence']

            return prediction, confidence

        except Exception as e:
            self.logger.error(f"Failed to get prediction: {e}")
            return 0.5, 0.0  # Neutral prediction

    def _save_training_report(self, results: Dict[str, Any]):
        """Save detailed training report"""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.config.REPORTS_DIR, f"advanced_ml_training_{timestamp}.json")

        # Prepare serializable results
        serializable_results = {}
        for symbol, result in results.items():
            if symbol == 'summary':
                serializable_results[symbol] = result
            else:
                # Remove non-serializable model objects
                clean_result = {k: v for k, v in result.items() if k != 'model'}
                serializable_results[symbol] = clean_result

        # Save report
        os.makedirs(self.config.REPORTS_DIR, exist_ok=True)
        with open(report_file, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)

        self.logger.info(f"Training report saved to {report_file}")

    def print_training_summary(self):
        """Print training summary"""

        if not self.training_results:
            print("No training results available")
            return

        summary_report = self.training_results.get('summary', {})
        best_composite_score = summary_report.get('best_composite_score', 0.0)
        best_net_profit = summary_report.get('best_net_profit', 0.0)
        deployment_ready = summary_report.get('deployment_ready', False)


        print("\\n" + "="*80)
        print("🤖 ADVANCED ML TRAINING SUMMARY")
        print("="*80)

        successful = 0
        threshold_met = 0

        for symbol, results in self.training_results.items():
            if 'error' not in results:
                successful += 1
                score = results.get('composite_score', 0.0)
                meets_threshold = results.get('meets_threshold', False)

                if meets_threshold:
                    threshold_met += 1
                    status = "✅ PASS"
                else:
                    status = "❌ FAIL"

                print(f"{symbol}: Score {score:.3f} {status}")
            else:
                print(f"{symbol}: ❌ ERROR - {results['error']}")

        print(f"\n📊 RESULTS:")
        print(f"Models Trained: {summary_report.get('successful_models',0)}/{summary_report.get('total_symbols',0)}")
        print(f"Threshold ({self.config.TARGET_COMPOSITE_SCORE * 100}%) Met: {summary_report.get('threshold_met',0)}/{summary_report.get('successful_models',0)}")
        print(f"Success Rate: {summary_report.get('success_rate', 0.0):.1f}%")
        print(f"Best Composite Score Achieved: {best_composite_score:.4f}")
        print(f"Best Net Profit Achieved: ${best_net_profit:.2f}")


        if deployment_ready:
            deployment_model_info = summary_report.get('deployment_model', {})
            model_type = "Best Composite Model"
            if deployment_model_info.get('score',0) < self.config.TARGET_COMPOSITE_SCORE and deployment_model_info.get('net_profit',0) == best_net_profit :
                 model_type = "Best Profit Model (Fallback)"
            elif deployment_model_info.get('score',0) < self.config.TARGET_COMPOSITE_SCORE :
                 model_type = "Best Available Composite (Fallback)"

            print(f"\\n🚀 Deployment Model ({model_type}):")
            print(f"   Symbol: {deployment_model_info.get('symbol', 'N/A')}")
            print(f"   Score: {deployment_model_info.get('score', 0.0):.4f}")
            print(f"   Net Profit: ${deployment_model_info.get('net_profit', 0.0):.2f}")
            print(f"   Timestamp: {deployment_model_info.get('timestamp', 'N/A')}")
        else:
            print(f"\\n⚠️ No models met the deployment criteria (Target: {self.config.TARGET_COMPOSITE_SCORE*100}% composite score).")

        print("="*80)

def main():
    """Main training execution"""
    config = TradingConfig()
    config.TARGET_COMPOSITE_SCORE = 0.85 # Ensure this is used from config if defined

    # Create directories
    os.makedirs(config.MODELS_DIR, exist_ok=True)
    os.makedirs(config.LOGS_DIR, exist_ok=True)
    os.makedirs(config.REPORTS_DIR, exist_ok=True)

    # Initialize advanced ML system
    ml_system = AdvancedMLTradingSystem(config)

    print("🚀 Starting Advanced ML Training System...")
    print("=" * 80)
    print(f"🎯 Models: TCN + CNN + PPO Ensemble")
    print(f"📈 Target Composite Score: {config.TARGET_COMPOSITE_SCORE * 100}%") # Use attribute from config
    print(f"💰 Initial Capital: ${config.INITIAL_CAPITAL}")
    print(f"🛠️ Grid Spacing: {config.GRID_SPACING*100}%")
    print(f"ρί Risk/Trade: ${config.RISK_PER_TRADE}, Profit/Trade: ${config.PROFIT_PER_TRADE}")
    print("=" * 80)

    # Train all models
    results = ml_system.train_all_symbols()

    # Print summary
    ml_system.print_training_summary()

    return results

if __name__ == "__main__":
    main()
