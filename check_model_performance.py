#!/usr/bin/env python3
"""
Check Current Model Out-of-Sample Performance
Analyzes why the trading system isn't generating trades
"""

import requests
import json
import os
import glob
from datetime import datetime

def check_model_performance():
    print('📊 CURRENT MODEL OUT-OF-SAMPLE PERFORMANCE ANALYSIS')
    print('='*70)
    print(f'Analysis Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print()

    base_url = 'http://localhost:5000'

    # Get current model information
    print('🤖 DEPLOYED MODEL PERFORMANCE:')
    print('-' * 50)
    try:
        response = requests.get(f'{base_url}/api/trading_status', timeout=10)
        if response.status_code == 200:
            data = response.json()
            model_info = data.get('model_info', {})
            
            model_id = model_info.get('model_id', 'Unknown')
            composite_score = model_info.get('composite_score', 0)
            target_trades = model_info.get('target_trades_per_day', 0)
            confidence_threshold = model_info.get('confidence_threshold', 0)
            
            print(f'Model ID: {model_id}')
            print(f'Composite Score: {composite_score:.1f}%')
            print(f'Target Trades/Day: {target_trades}')
            print(f'Confidence Threshold: {confidence_threshold}%')
            
            # Check if model meets deployment criteria
            print(f'\n📈 DEPLOYMENT CRITERIA CHECK:')
            print(f'   Composite Score: {composite_score:.1f}% (Required: ≥85%)')
            print(f'   Status: {"✅ MEETS" if composite_score >= 85 else "❌ BELOW"} threshold')
            print(f'   Target Frequency: {target_trades} trades/day (Required: ≥5)')
            print(f'   Status: {"✅ MEETS" if target_trades >= 5 else "❌ BELOW"} frequency requirement')
            
            # Overall assessment
            meets_criteria = composite_score >= 85 and target_trades >= 5
            print(f'\n🎯 OVERALL MODEL STATUS:')
            if meets_criteria:
                print(f'   ✅ MODEL MEETS ALL DEPLOYMENT CRITERIA')
                print(f'   ✅ Should be generating trades regularly')
            else:
                print(f'   ❌ MODEL DOES NOT MEET DEPLOYMENT CRITERIA')
                print(f'   ⚠️ This explains why no trades are being generated')
                
        else:
            print(f'❌ Trading Status API Error: HTTP {response.status_code}')
            
    except Exception as e:
        print(f'❌ Error getting model info: {e}')

    # Check live trading performance
    print(f'\n🔍 LIVE TRADING PERFORMANCE:')
    print('-' * 50)
    try:
        response = requests.get(f'{base_url}/api/trading_status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            performance = data.get('performance', {})
            
            total_trades = performance.get('total_trades', 0)
            win_rate = performance.get('win_rate', 0)
            total_profit = performance.get('total_profit', 0)
            
            print(f'Total Trades Executed: {total_trades}')
            print(f'Live Win Rate: {win_rate*100:.1f}%')
            print(f'Live Total Profit: ${total_profit:.2f}')
            
            if total_trades == 0:
                print(f'\n⚠️ ISSUE IDENTIFIED: NO TRADES EXECUTED')
                print(f'   Possible causes:')
                print(f'   1. Model confidence threshold too high')
                print(f'   2. Model not generating signals')
                print(f'   3. Market conditions outside training data')
                print(f'   4. Model performance below deployment threshold')
            elif total_trades < 5:
                print(f'\n⚠️ ISSUE IDENTIFIED: LOW TRADE FREQUENCY')
                print(f'   Expected: 5+ trades/day')
                print(f'   Actual: {total_trades} total trades')
            else:
                print(f'\n✅ Trade frequency appears normal')
                
    except Exception as e:
        print(f'❌ Error analyzing performance: {e}')

    # Check for recent training reports
    print(f'\n📋 RECENT TRAINING RESULTS:')
    print('-' * 50)
    try:
        reports_dir = 'Trading project VPS 4/reports'
        if os.path.exists(reports_dir):
            report_files = glob.glob(f'{reports_dir}/*training*.json')
            report_files.sort(key=os.path.getmtime, reverse=True)
            
            if report_files:
                latest_report = report_files[0]
                print(f'Latest Training Report: {os.path.basename(latest_report)}')
                
                try:
                    with open(latest_report, 'r') as f:
                        report_data = json.load(f)
                        
                    if 'out_of_sample_performance' in report_data:
                        oos_perf = report_data['out_of_sample_performance']
                        print(f'\n📊 OUT-OF-SAMPLE PERFORMANCE:')
                        print(f'   Win Rate: {oos_perf.get("win_rate", 0)*100:.1f}%')
                        print(f'   Total Trades: {oos_perf.get("total_trades", 0)}')
                        print(f'   Net Profit: ${oos_perf.get("net_profit", 0):.2f}')
                        print(f'   Trades/Day: {oos_perf.get("trades_per_day", 0):.1f}')
                        print(f'   Max Drawdown: {oos_perf.get("max_drawdown", 0)*100:.1f}%')
                        print(f'   Composite Score: {oos_perf.get("composite_score", 0)*100:.1f}%')
                    else:
                        print(f'   No out-of-sample performance data found in report')
                        
                except Exception as e:
                    print(f'   Error reading report: {e}')
            else:
                print(f'   No training reports found in {reports_dir}')
        else:
            print(f'   Reports directory not found: {reports_dir}')
            
    except Exception as e:
        print(f'❌ Error checking training reports: {e}')

    # Check model files
    print(f'\n📁 MODEL FILES CHECK:')
    print('-' * 50)
    try:
        models_dir = 'Trading project VPS 4/models'
        if os.path.exists(models_dir):
            model_files = []
            for ext in ['*.pkl', '*.joblib', '*.json', '*.zip']:
                model_files.extend(glob.glob(f'{models_dir}/{ext}'))
            
            if model_files:
                print(f'   Found {len(model_files)} model files:')
                for model_file in sorted(model_files)[-5:]:  # Show last 5
                    file_size = os.path.getsize(model_file) / 1024  # KB
                    mod_time = datetime.fromtimestamp(os.path.getmtime(model_file))
                    print(f'   - {os.path.basename(model_file)} ({file_size:.1f}KB, {mod_time.strftime("%Y-%m-%d %H:%M")})')
            else:
                print(f'   ❌ No model files found')
        else:
            print(f'   ❌ Models directory not found: {models_dir}')
            
    except Exception as e:
        print(f'❌ Error checking model files: {e}')

    print(f'\n🎯 OUT-OF-SAMPLE PERFORMANCE SUMMARY:')
    print('='*70)
    print(f'Based on the analysis above, the current model may not meet')
    print(f'the required criteria for consistent trade generation.')
    print()
    print(f'💡 RECOMMENDATIONS:')
    print(f'   1. Check if model composite score ≥ 85%')
    print(f'   2. Verify model was trained on sufficient out-of-sample data')
    print(f'   3. Consider retraining with more recent market data')
    print(f'   4. Review confidence thresholds and signal generation')

if __name__ == '__main__':
    check_model_performance()
