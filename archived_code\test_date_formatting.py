#!/usr/bin/env python3
"""
Test Date/Time Formatting Functions
Tests the date and time formatting functions used in the webapp.
"""

import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_date_time_formatting():
    """Test the date/time formatting functions."""
    print("🧪 TESTING DATE/TIME FORMATTING")
    print("=" * 50)
    
    # Import the formatting functions
    try:
        from live_trading_web_app import format_time_from_db, format_date_from_db
        print("✅ Successfully imported formatting functions")
    except Exception as e:
        print(f"❌ Failed to import formatting functions: {e}")
        return False
    
    # Test cases for time formatting
    print("\n📋 Test 1: Time Formatting")
    time_test_cases = [
        ("2024-06-06T14:30:25", "14:30:25"),
        ("2024-06-06T14:30:25.123456", "14:30:25"),
        ("14:30:25", "14:30:25"),
        ("14:30", "14:30:00"),
        ("", "Unknown"),
        (None, "Unknown"),
        ("Invalid", "Unknown"),
        (datetime(2024, 6, 6, 14, 30, 25), "14:30:25"),
        ("2024-06-06 14:30:25", "14:30:25")
    ]
    
    for input_val, expected in time_test_cases:
        result = format_time_from_db(input_val)
        status = "✅" if result == expected else "❌"
        print(f"   {status} Input: {input_val} → Output: {result} (Expected: {expected})")
        if result != expected:
            return False
    
    # Test cases for date formatting
    print("\n📋 Test 2: Date Formatting")
    date_test_cases = [
        ("2024-06-06T14:30:25", "2024-06-06"),
        ("2024-06-06T14:30:25.123456", "2024-06-06"),
        ("2024-06-06", "2024-06-06"),
        ("", None),
        (None, None),
        ("Invalid", None),
        (datetime(2024, 6, 6, 14, 30, 25), "2024-06-06"),
        ("2024-06-06 14:30:25", "2024-06-06")
    ]
    
    for input_val, expected in date_test_cases:
        result = format_date_from_db(input_val)
        status = "✅" if result == expected else "❌"
        print(f"   {status} Input: {input_val} → Output: {result} (Expected: {expected})")
        if result != expected:
            return False
    
    # Test with real database-like data
    print("\n📋 Test 3: Database-like Data")
    db_trade_sample = {
        'entry_time': '2024-06-06T14:30:25.123456',
        'exit_time': '2024-06-06T15:45:30.789012',
        'entry_date': '2024-06-06',
        'exit_date': '2024-06-06'
    }
    
    formatted_entry_time = format_time_from_db(db_trade_sample.get('entry_time'))
    formatted_exit_time = format_time_from_db(db_trade_sample.get('exit_time'))
    formatted_entry_date = format_date_from_db(db_trade_sample.get('entry_date'))
    formatted_exit_date = format_date_from_db(db_trade_sample.get('exit_date'))
    
    print(f"   Entry Time: {formatted_entry_time} (Expected: 14:30:25)")
    print(f"   Exit Time: {formatted_exit_time} (Expected: 15:45:30)")
    print(f"   Entry Date: {formatted_entry_date} (Expected: 2024-06-06)")
    print(f"   Exit Date: {formatted_exit_date} (Expected: 2024-06-06)")
    
    if (formatted_entry_time == "14:30:25" and 
        formatted_exit_time == "15:45:30" and 
        formatted_entry_date == "2024-06-06" and 
        formatted_exit_date == "2024-06-06"):
        print("   ✅ Database-like data formatting: Success")
    else:
        print("   ❌ Database-like data formatting: Failed")
        return False
    
    # Test with empty/null database data
    print("\n📋 Test 4: Empty/Null Database Data")
    empty_trade_sample = {
        'entry_time': '',
        'exit_time': None,
        'entry_date': '',
        'exit_date': None
    }
    
    formatted_entry_time = format_time_from_db(empty_trade_sample.get('entry_time'))
    formatted_exit_time = format_time_from_db(empty_trade_sample.get('exit_time'))
    formatted_entry_date = format_date_from_db(empty_trade_sample.get('entry_date'))
    formatted_exit_date = format_date_from_db(empty_trade_sample.get('exit_date'))
    
    print(f"   Entry Time: {formatted_entry_time} (Expected: Unknown)")
    print(f"   Exit Time: {formatted_exit_time} (Expected: Unknown)")
    print(f"   Entry Date: {formatted_entry_date} (Expected: None)")
    print(f"   Exit Date: {formatted_exit_date} (Expected: None)")
    
    if (formatted_entry_time == "Unknown" and 
        formatted_exit_time == "Unknown" and 
        formatted_entry_date is None and 
        formatted_exit_date is None):
        print("   ✅ Empty/Null data formatting: Success")
    else:
        print("   ❌ Empty/Null data formatting: Failed")
        return False
    
    print("\n" + "=" * 50)
    print("🎯 DATE/TIME FORMATTING TEST RESULTS:")
    print("✅ All tests passed successfully!")
    print("📅 Date formatting working correctly")
    print("🕐 Time formatting working correctly")
    print("🔧 Ready to fix 'Invalid Date' issues in webapp")
    print("=" * 50)
    
    return True

if __name__ == '__main__':
    success = test_date_time_formatting()
    if success:
        print("\n🎉 Date/time formatting is working correctly!")
        print("💡 The 'Invalid Date' issue should now be resolved in the webapp")
        sys.exit(0)
    else:
        print("\n❌ Date/time formatting has issues!")
        sys.exit(1)
