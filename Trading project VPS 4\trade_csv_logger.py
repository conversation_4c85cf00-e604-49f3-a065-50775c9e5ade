#!/usr/bin/env python3
"""
TRADE CSV LOGGER
================
Persistent CSV logging system for all trades to prevent data loss.
Automatically saves and reloads trade history.
"""

import csv
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

class TradeCSVLogger:
    """CSV logger for persistent trade storage."""
    
    def __init__(self, csv_file_path: str = "trade_history.csv"):
        self.csv_file_path = csv_file_path
        self.fieldnames = [
            # Basic trade info
            'trade_id', 'direction', 'status', 'entry_time', 'exit_time',
            'entry_price', 'exit_price', 'current_price', 'quantity',
            'position_usd', 'pnl', 'duration',
            
            # Risk management
            'risk_amount', 'target_profit', 'leverage', 'stop_loss', 'take_profit',
            
            # AI model info
            'confidence', 'signal_confidence', 'model_prediction',
            
            # Binance integration
            'binance_order_id', 'is_margin_trade', 'account_type', 'margin_used',
            
            # P&L tracking
            'unrealized_pnl', 'realized_pnl', 'profit_percentage',
            
            # Timestamps
            'entry_date', 'exit_date', 'logged_at', 'last_updated',
            
            # Status flags
            'is_open', 'is_live_trade'
        ]
        
        # Create CSV file with headers if it doesn't exist
        self._initialize_csv()
        print(f"📝 Trade CSV Logger initialized: {self.csv_file_path}")
    
    def _initialize_csv(self):
        """Initialize CSV file with headers if it doesn't exist."""
        if not os.path.exists(self.csv_file_path):
            with open(self.csv_file_path, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, fieldnames=self.fieldnames)
                writer.writeheader()
            print(f"✅ Created new CSV trade log: {self.csv_file_path}")
        else:
            print(f"✅ Using existing CSV trade log: {self.csv_file_path}")
    
    def log_trade(self, trade_data: Dict[str, Any], is_update: bool = False):
        """Log a trade to CSV file."""
        try:
            # Prepare trade data for CSV
            csv_row = self._prepare_trade_for_csv(trade_data)
            
            if is_update:
                # Update existing trade
                self._update_trade_in_csv(csv_row)
            else:
                # Add new trade
                self._append_trade_to_csv(csv_row)
            
            print(f"📝 Trade logged to CSV: {csv_row['trade_id']} ({csv_row['status']})")
            
        except Exception as e:
            print(f"❌ Error logging trade to CSV: {e}")
    
    def _prepare_trade_for_csv(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare trade data for CSV format."""
        now = datetime.now().isoformat()
        
        csv_row = {}
        for field in self.fieldnames:
            csv_row[field] = trade_data.get(field, '')
        
        # Set timestamps
        csv_row['logged_at'] = csv_row.get('logged_at', now)
        csv_row['last_updated'] = now
        
        # Ensure boolean fields are properly formatted
        csv_row['is_open'] = str(trade_data.get('is_open', False))
        csv_row['is_margin_trade'] = str(trade_data.get('is_margin_trade', False))
        csv_row['is_live_trade'] = str(trade_data.get('is_live_trade', True))
        
        return csv_row
    
    def _append_trade_to_csv(self, csv_row: Dict[str, Any]):
        """Append new trade to CSV file."""
        with open(self.csv_file_path, 'a', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=self.fieldnames)
            writer.writerow(csv_row)
    
    def _update_trade_in_csv(self, updated_row: Dict[str, Any]):
        """Update existing trade in CSV file."""
        # Read all trades
        trades = self.load_all_trades()
        
        # Find and update the trade
        trade_id = updated_row['trade_id']
        updated = False
        
        for i, trade in enumerate(trades):
            if trade['trade_id'] == trade_id:
                trades[i] = updated_row
                updated = True
                break
        
        if not updated:
            # Trade not found, append as new
            trades.append(updated_row)
        
        # Write all trades back to CSV
        self._write_all_trades_to_csv(trades)
    
    def _write_all_trades_to_csv(self, trades: List[Dict[str, Any]]):
        """Write all trades to CSV file."""
        with open(self.csv_file_path, 'w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=self.fieldnames)
            writer.writeheader()
            writer.writerows(trades)
    
    def load_all_trades(self) -> List[Dict[str, Any]]:
        """Load all trades from CSV file."""
        trades = []
        
        try:
            if os.path.exists(self.csv_file_path):
                with open(self.csv_file_path, 'r', newline='', encoding='utf-8') as file:
                    reader = csv.DictReader(file)
                    for row in reader:
                        # Convert string booleans back to actual booleans
                        row['is_open'] = row.get('is_open', 'False').lower() == 'true'
                        row['is_margin_trade'] = row.get('is_margin_trade', 'False').lower() == 'true'
                        row['is_live_trade'] = row.get('is_live_trade', 'True').lower() == 'true'
                        
                        # Convert numeric fields
                        numeric_fields = ['entry_price', 'exit_price', 'current_price', 'quantity', 
                                        'position_usd', 'pnl', 'risk_amount', 'target_profit', 
                                        'leverage', 'stop_loss', 'take_profit', 'confidence',
                                        'signal_confidence', 'margin_used', 'unrealized_pnl', 
                                        'realized_pnl', 'profit_percentage']
                        
                        for field in numeric_fields:
                            try:
                                if row.get(field) and row[field] != '':
                                    row[field] = float(row[field])
                                else:
                                    row[field] = 0.0
                            except (ValueError, TypeError):
                                row[field] = 0.0
                        
                        trades.append(row)
                        
            print(f"📊 Loaded {len(trades)} trades from CSV")
            
        except Exception as e:
            print(f"❌ Error loading trades from CSV: {e}")
        
        return trades
    
    def get_open_trades(self) -> List[Dict[str, Any]]:
        """Get only open trades from CSV."""
        all_trades = self.load_all_trades()
        return [trade for trade in all_trades if trade.get('is_open', False)]
    
    def get_closed_trades(self) -> List[Dict[str, Any]]:
        """Get only closed trades from CSV."""
        all_trades = self.load_all_trades()
        return [trade for trade in all_trades if not trade.get('is_open', False)]
    
    def close_trade(self, trade_id: str, exit_price: float, exit_time: str = None, pnl: float = None):
        """Mark a trade as closed in CSV - updates existing entry instead of creating duplicate."""
        if exit_time is None:
            exit_time = datetime.now().isoformat()

        # Load all trades
        trades = self.load_all_trades()
        trade_found = False

        # Find and update the trade
        for trade in trades:
            if trade['trade_id'] == trade_id:
                # Update the existing trade entry
                trade['is_open'] = False
                trade['status'] = 'CLOSED'
                trade['exit_price'] = exit_price
                trade['exit_time'] = exit_time
                trade['exit_date'] = exit_time.split('T')[0] if 'T' in exit_time else exit_time.split(' ')[0]

                if pnl is not None:
                    trade['pnl'] = pnl
                    trade['realized_pnl'] = pnl
                    trade['unrealized_pnl'] = 0.0

                # Calculate duration
                if trade.get('entry_time'):
                    try:
                        entry_dt = datetime.fromisoformat(trade['entry_time'].replace('Z', '+00:00'))
                        exit_dt = datetime.fromisoformat(exit_time.replace('Z', '+00:00'))
                        duration = exit_dt - entry_dt

                        total_seconds = int(duration.total_seconds())
                        if total_seconds < 60:
                            trade['duration'] = f"{total_seconds}s"
                        elif total_seconds < 3600:
                            minutes = total_seconds // 60
                            seconds = total_seconds % 60
                            trade['duration'] = f"{minutes}m {seconds}s"
                        else:
                            hours = total_seconds // 3600
                            minutes = (total_seconds % 3600) // 60
                            trade['duration'] = f"{hours}h {minutes}m"
                    except:
                        trade['duration'] = 'Unknown'

                trade['last_updated'] = datetime.now().isoformat()
                trade_found = True
                break

        if trade_found:
            # Write back to CSV
            self._write_all_trades_to_csv(trades)
            print(f"📝 Trade updated to CLOSED in CSV: {trade_id}")
        else:
            print(f"⚠️ Trade not found for closing: {trade_id}")

    def update_open_trade_realtime(self, trade_id: str, current_price: float, unrealized_pnl: float):
        """Update real-time data for open trade without creating duplicates."""
        try:
            # Load all trades
            trades = self.load_all_trades()
            trade_found = False

            # Find and update the open trade
            for trade in trades:
                if trade['trade_id'] == trade_id and trade.get('is_open', False):
                    # Update real-time fields only
                    trade['current_price'] = current_price
                    trade['unrealized_pnl'] = unrealized_pnl
                    trade['pnl'] = unrealized_pnl  # For open trades, pnl = unrealized_pnl

                    # Calculate profit percentage
                    entry_price = trade.get('entry_price', 0)
                    quantity = trade.get('quantity', 0)
                    if entry_price > 0 and quantity > 0:
                        trade['profit_percentage'] = round((unrealized_pnl / (entry_price * quantity)) * 100, 2)

                    trade['last_updated'] = datetime.now().isoformat()
                    trade_found = True
                    break

            if trade_found:
                # Write back to CSV
                self._write_all_trades_to_csv(trades)
                # Don't print every update to avoid spam

        except Exception as e:
            print(f"❌ Error updating open trade real-time data: {e}")

    def backup_csv(self, backup_suffix: str = None):
        """Create a backup of the CSV file."""
        if backup_suffix is None:
            backup_suffix = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        backup_path = f"{self.csv_file_path}.backup_{backup_suffix}"
        
        try:
            import shutil
            shutil.copy2(self.csv_file_path, backup_path)
            print(f"💾 CSV backup created: {backup_path}")
            return backup_path
        except Exception as e:
            print(f"❌ Error creating CSV backup: {e}")
            return None
    
    def get_trade_statistics(self) -> Dict[str, Any]:
        """Get trading statistics from CSV data."""
        trades = self.load_all_trades()
        
        if not trades:
            return {
                'total_trades': 0,
                'open_trades': 0,
                'closed_trades': 0,
                'total_pnl': 0.0,
                'win_rate': 0.0,
                'avg_profit': 0.0,
                'avg_loss': 0.0
            }
        
        open_trades = [t for t in trades if t.get('is_open', False)]
        closed_trades = [t for t in trades if not t.get('is_open', False)]
        
        total_pnl = sum(t.get('pnl', 0) for t in trades)
        winning_trades = [t for t in closed_trades if t.get('pnl', 0) > 0]
        losing_trades = [t for t in closed_trades if t.get('pnl', 0) < 0]
        
        win_rate = (len(winning_trades) / len(closed_trades) * 100) if closed_trades else 0
        avg_profit = sum(t.get('pnl', 0) for t in winning_trades) / len(winning_trades) if winning_trades else 0
        avg_loss = sum(t.get('pnl', 0) for t in losing_trades) / len(losing_trades) if losing_trades else 0
        
        return {
            'total_trades': len(trades),
            'open_trades': len(open_trades),
            'closed_trades': len(closed_trades),
            'total_pnl': total_pnl,
            'win_rate': win_rate,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'avg_profit': avg_profit,
            'avg_loss': avg_loss
        }

    def clear_all_trades(self) -> bool:
        """Clear all trades from the CSV file."""
        try:
            # Write empty CSV with just headers
            with open(self.csv_file_path, 'w', newline='', encoding='utf-8') as file:
                writer = csv.DictWriter(file, fieldnames=self.fieldnames)
                writer.writeheader()
            print(f"📄 Cleared all trades from CSV: {self.csv_file_path}")
            return True
        except Exception as e:
            print(f"❌ Error clearing trades from CSV: {e}")
            return False

# Global CSV logger instance
csv_logger = TradeCSVLogger("trade_history.csv")

if __name__ == "__main__":
    # Test the CSV logger
    print("🧪 Testing Trade CSV Logger")
    print("=" * 50)
    
    # Test logging a sample trade
    sample_trade = {
        'trade_id': 'TEST_001',
        'direction': 'BUY',
        'status': 'OPEN',
        'entry_time': datetime.now().isoformat(),
        'entry_price': 104500.0,
        'quantity': 0.00038,
        'position_usd': 39.71,
        'risk_amount': 4.0,
        'target_profit': 25.0,
        'confidence': 65.2,
        'is_open': True,
        'is_margin_trade': True,
        'account_type': 'Cross Margin'
    }
    
    logger = TradeCSVLogger("test_trades.csv")
    logger.log_trade(sample_trade)
    
    # Load and display
    trades = logger.load_all_trades()
    print(f"✅ Loaded {len(trades)} trades")
    
    # Get statistics
    stats = logger.get_trade_statistics()
    print(f"📊 Statistics: {stats}")
    
    print("🎯 CSV Logger test complete")
