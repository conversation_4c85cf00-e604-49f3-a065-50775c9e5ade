#!/usr/bin/env python3
"""
Live Trade Manager Web Application - VPS 4
Real-time trading execution and monitoring system
"""

import sys
import os
import logging
import json
from datetime import datetime, timedelta
from flask import Flask, render_template_string, jsonify, request, redirect, url_for
from threading import Thread, Lock
import time
import queue
import sqlite3
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional
import numpy as np

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

@dataclass
class Trade:
    """Enhanced trade data structure with commission tracking"""
    id: str
    symbol: str
    side: str  # 'BUY' or 'SELL'
    position_size: float  # Dollar amount
    entry_price: float
    quantity: float  # Actual crypto quantity
    exit_price: Optional[float] = None
    status: str = 'PENDING'  # 'PENDING', 'FILLED', 'CANCELLED', 'FAILED'

    # Timing
    entry_time: str = ""
    exit_time: Optional[str] = None
    duration_minutes: Optional[float] = None

    # Commission calculations (0.1% each way)
    entry_commission: float = 0.0
    exit_commission: float = 0.0
    total_commission: float = 0.0

    # P&L calculations
    gross_pnl: float = 0.0      # Before commissions
    net_pnl: float = 0.0        # After commissions
    gross_pnl_pct: float = 0.0  # Percentage return before commissions
    net_pnl_pct: float = 0.0    # Percentage return after commissions

    # Balance tracking
    balance_before: float = 0.0
    balance_after: float = 0.0

    # Model data
    model_prediction: Optional[float] = None
    confidence: Optional[float] = None
    signal_strength: Optional[float] = None

    # Manual override tracking
    is_manual_override: bool = False
    default_size: float = 10.0
    override_reason: str = ""

    # Legacy fields for compatibility
    price: float = 0.0  # Maps to entry_price
    profit_loss: float = 0.0  # Maps to net_pnl
    timestamp: str = ""  # Maps to entry_time
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

@dataclass
class Position:
    """Position data structure"""
    symbol: str
    side: str
    quantity: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    timestamp: str

class TradeDatabase:
    """SQLite database for trade management"""

    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Initialize database tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Trades table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id TEXT PRIMARY KEY,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                quantity REAL NOT NULL,
                price REAL NOT NULL,
                status TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                profit_loss REAL DEFAULT 0.0,
                stop_loss REAL,
                take_profit REAL,
                model_prediction REAL,
                confidence REAL
            )
        ''')

        # Positions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS positions (
                symbol TEXT PRIMARY KEY,
                side TEXT NOT NULL,
                quantity REAL NOT NULL,
                entry_price REAL NOT NULL,
                current_price REAL NOT NULL,
                unrealized_pnl REAL NOT NULL,
                timestamp TEXT NOT NULL
            )
        ''')

        # Performance metrics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance (
                date TEXT PRIMARY KEY,
                total_trades INTEGER DEFAULT 0,
                winning_trades INTEGER DEFAULT 0,
                losing_trades INTEGER DEFAULT 0,
                total_pnl REAL DEFAULT 0.0,
                win_rate REAL DEFAULT 0.0,
                avg_profit REAL DEFAULT 0.0,
                avg_loss REAL DEFAULT 0.0,
                max_drawdown REAL DEFAULT 0.0
            )
        ''')

        conn.commit()
        conn.close()

    def add_trade(self, trade: Trade):
        """Add a new trade to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO trades
            (id, symbol, side, quantity, price, status, timestamp, profit_loss,
             stop_loss, take_profit, model_prediction, confidence)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            trade.id, trade.symbol, trade.side, trade.quantity, trade.price,
            trade.status, trade.timestamp, trade.profit_loss, trade.stop_loss,
            trade.take_profit, trade.model_prediction, trade.confidence
        ))

        conn.commit()
        conn.close()

    def insert_trade(self, trade: Trade):
        """Insert trade (alias for add_trade for compatibility)"""
        self.add_trade(trade)

    def get_recent_trades(self, limit: int = 100) -> List[dict]:
        """Get recent trades as dictionaries"""
        trades = self.get_trades(limit)
        return [asdict(trade) for trade in trades]

    def get_trades(self, limit: int = 100) -> List[Trade]:
        """Get recent trades"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM trades
            ORDER BY timestamp DESC
            LIMIT ?
        ''', (limit,))

        rows = cursor.fetchall()
        conn.close()

        trades = []
        for row in rows:
            trade = Trade(
                id=row[0],
                symbol=row[1],
                side=row[2],
                position_size=row[7] or 10.0,  # Use profit_loss as position_size fallback
                entry_price=row[4],  # Use price as entry_price
                quantity=row[3],
                exit_price=None,
                status=row[5],
                entry_time=row[6],  # Use timestamp as entry_time
                exit_time=None,
                duration_minutes=None,
                entry_commission=0.0,
                exit_commission=0.0,
                total_commission=0.0,
                gross_pnl=row[7] or 0.0,
                net_pnl=row[7] or 0.0,
                gross_pnl_pct=0.0,
                net_pnl_pct=0.0,
                balance_before=1000.0,
                balance_after=1000.0 + (row[7] or 0.0),
                model_prediction=row[10],
                confidence=row[11],
                signal_strength=None,
                is_manual_override=False,
                default_size=10.0,
                override_reason="",
                # Legacy compatibility
                price=row[4],
                profit_loss=row[7] or 0.0,
                timestamp=row[6],
                stop_loss=row[8],
                take_profit=row[9]
            )
            trades.append(trade)

        return trades

    def update_position(self, position: Position):
        """Update position in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO positions
            (symbol, side, quantity, entry_price, current_price, unrealized_pnl, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            position.symbol, position.side, position.quantity, position.entry_price,
            position.current_price, position.unrealized_pnl, position.timestamp
        ))

        conn.commit()
        conn.close()

    def get_positions(self) -> List[Position]:
        """Get all open positions"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM positions')
        rows = cursor.fetchall()
        conn.close()

        positions = []
        for row in rows:
            position = Position(
                symbol=row[0], side=row[1], quantity=row[2], entry_price=row[3],
                current_price=row[4], unrealized_pnl=row[5], timestamp=row[6]
            )
            positions.append(position)

        return positions

class CommissionCalculator:
    """Commission calculation engine for 0.1% per trade"""

    COMMISSION_RATE = 0.001  # 0.1%

    def calculate_trade_commission(self, position_size: float) -> dict:
        """Calculate entry and exit commissions"""
        entry_commission = round(position_size * self.COMMISSION_RATE, 4)
        exit_commission = round(position_size * self.COMMISSION_RATE, 4)
        total_commission = round(entry_commission + exit_commission, 4)

        return {
            'entry_commission': entry_commission,
            'exit_commission': exit_commission,
            'total_commission': total_commission,
            'commission_rate_round_trip': self.COMMISSION_RATE * 2
        }

    def calculate_net_pnl(self, gross_pnl: float, total_commission: float) -> float:
        """Calculate net P&L after commissions"""
        return round(gross_pnl - total_commission, 4)

    def break_even_threshold(self, position_size: float) -> float:
        """Calculate break-even threshold (0.2% to overcome commissions)"""
        return position_size * (self.COMMISSION_RATE * 2)

class ManualTradeController:
    """Manual trade size override controller"""

    def __init__(self, account_balance: float):
        self.account_balance = account_balance
        self.default_trade_size = 10.0
        self.max_trade_size_pct = 0.05  # 5% of account
        self.daily_override_limit_pct = 0.10  # 10% of account
        self.manual_overrides = {}  # symbol -> size mapping
        self.daily_override_total = 0.0

    def set_manual_trade_size(self, symbol: str, size: float, duration: str = "next_trade_only") -> dict:
        """Set manual trade size override"""

        # Validate trade size
        validation = self.validate_trade_size(size)
        if not validation['valid']:
            return {'success': False, 'error': validation['error']}

        # Check daily limits
        daily_check = self.check_daily_override_limit(size)
        if not daily_check['allowed']:
            return {'success': False, 'error': f"Daily override limit exceeded. Used: ${daily_check['used']:.2f}, Limit: ${daily_check['limit']:.2f}"}

        # Set override
        if duration == "next_trade_only":
            self.manual_overrides[symbol] = {'size': size, 'temporary': True}
        else:
            self.manual_overrides[symbol] = {'size': size, 'temporary': False}

        return {
            'success': True,
            'symbol': symbol,
            'size': size,
            'duration': duration,
            'risk_pct': validation['risk_pct'],
            'confirmation_required': size > 50.0
        }

    def get_trade_size(self, symbol: str) -> dict:
        """Get trade size for symbol (default or override)"""
        if symbol in self.manual_overrides:
            override = self.manual_overrides[symbol]

            # Remove temporary override after use
            if override['temporary']:
                size = override['size']
                del self.manual_overrides[symbol]
                return {
                    'size': size,
                    'is_override': True,
                    'default_size': self.default_trade_size,
                    'override_reason': 'Manual override (one-time)'
                }
            else:
                return {
                    'size': override['size'],
                    'is_override': True,
                    'default_size': self.default_trade_size,
                    'override_reason': 'Manual override (persistent)'
                }

        return {
            'size': self.default_trade_size,
            'is_override': False,
            'default_size': self.default_trade_size,
            'override_reason': ''
        }

    def validate_trade_size(self, size: float) -> dict:
        """Validate proposed trade size"""
        max_size = self.account_balance * self.max_trade_size_pct

        if size < 1.0:
            return {'valid': False, 'error': 'Minimum trade size is $1.00'}

        if size > max_size:
            return {
                'valid': False,
                'error': f'Trade size ${size:.2f} exceeds 5% limit (${max_size:.2f})'
            }

        return {
            'valid': True,
            'risk_pct': (size / self.account_balance) * 100,
            'max_allowed': max_size
        }

    def check_daily_override_limit(self, proposed_size: float) -> dict:
        """Check if proposed override exceeds daily limit"""
        daily_limit = self.account_balance * self.daily_override_limit_pct

        if (self.daily_override_total + proposed_size) > daily_limit:
            return {
                'allowed': False,
                'used': self.daily_override_total,
                'limit': daily_limit,
                'remaining': daily_limit - self.daily_override_total
            }

        return {
            'allowed': True,
            'used': self.daily_override_total,
            'limit': daily_limit,
            'remaining': daily_limit - self.daily_override_total
        }

    def update_account_balance(self, new_balance: float):
        """Update account balance for risk calculations"""
        self.account_balance = new_balance

class LiveTradeManager:
    """Live trade execution and management"""

    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('LiveTradeManager')
        self.db = TradeDatabase(os.path.join(config.DATA_DIR, 'trades.db'))
        self.trade_queue = queue.Queue()
        self.positions: Dict[str, Position] = {}
        self.is_trading = False
        self.trade_lock = Lock()

        # Initialize commission calculator and manual trade controller
        self.commission_calc = CommissionCalculator()
        self.account_balance = 1000.0  # Starting balance
        self.manual_controller = ManualTradeController(self.account_balance)

        # Load trained model
        self.model = self.load_best_model()

        # Performance tracking
        self.daily_pnl = 0.0
        self.total_trades = 0
        self.winning_trades = 0

        # Balance tracking for equity curve
        self.balance_history = []
        self.starting_balance = self.account_balance

        self.logger.info("Live Trade Manager initialized with commission tracking and manual controls")

    def load_best_model(self):
        """Load the best trained model"""
        try:
            # In a real implementation, load the actual trained model
            # For now, return a dummy model that generates random predictions
            self.logger.info("Loading best trained model...")
            return {"type": "dummy", "accuracy": 0.607, "variant": "balanced"}
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            return None

    def get_model_prediction(self, symbol: str, market_data: dict) -> tuple:
        """Get model prediction for a symbol"""
        try:
            # Simulate model prediction
            prediction = np.random.random()  # 0-1 probability
            confidence = np.random.uniform(0.6, 0.95)  # Confidence level

            return prediction, confidence
        except Exception as e:
            self.logger.error(f"Model prediction failed: {e}")
            return 0.5, 0.0

    def start_trading(self):
        """Start live trading"""
        if self.is_trading:
            return False

        self.is_trading = True
        self.logger.info("Starting live trading...")

        # Start trading thread
        trading_thread = Thread(target=self._trading_loop, daemon=True)
        trading_thread.start()

        return True

    def stop_trading(self):
        """Stop live trading"""
        self.is_trading = False
        self.logger.info("Stopping live trading...")

    def _trading_loop(self):
        """Main trading loop"""
        while self.is_trading:
            try:
                # Process pending trades
                self._process_trade_queue()

                # Update positions
                self._update_positions()

                # Generate new signals (if model is available)
                if self.model:
                    self._generate_signals()

                time.sleep(1)  # 1 second interval

            except Exception as e:
                self.logger.error(f"Trading loop error: {e}")
                time.sleep(5)

    def _process_trade_queue(self):
        """Process trades in queue"""
        while not self.trade_queue.empty():
            try:
                trade = self.trade_queue.get_nowait()
                self._execute_trade(trade)
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"Trade processing error: {e}")

    def _execute_trade(self, trade: Trade):
        """Execute a single trade"""
        try:
            with self.trade_lock:
                # Simulate trade execution
                trade.status = 'FILLED'
                trade.timestamp = datetime.now().isoformat()

                # Update database
                self.db.add_trade(trade)

                # Update position
                self._update_position_from_trade(trade)

                self.total_trades += 1
                self.logger.info(f"Trade executed: {trade.symbol} {trade.side} {trade.quantity} @ {trade.price}")

        except Exception as e:
            trade.status = 'FAILED'
            self.db.add_trade(trade)
            self.logger.error(f"Trade execution failed: {e}")

    def _update_position_from_trade(self, trade: Trade):
        """Update position based on executed trade"""
        symbol = trade.symbol

        if symbol not in self.positions:
            # New position
            position = Position(
                symbol=symbol,
                side=trade.side,
                quantity=trade.quantity,
                entry_price=trade.price,
                current_price=trade.price,
                unrealized_pnl=0.0,
                timestamp=trade.timestamp
            )
            self.positions[symbol] = position
        else:
            # Update existing position
            position = self.positions[symbol]
            if position.side == trade.side:
                # Add to position
                total_quantity = position.quantity + trade.quantity
                weighted_price = ((position.entry_price * position.quantity) +
                                (trade.price * trade.quantity)) / total_quantity
                position.quantity = total_quantity
                position.entry_price = weighted_price
            else:
                # Reduce or close position
                if trade.quantity >= position.quantity:
                    # Close position
                    del self.positions[symbol]
                else:
                    # Reduce position
                    position.quantity -= trade.quantity

        # Update database
        if symbol in self.positions:
            self.db.update_position(self.positions[symbol])

    def _update_positions(self):
        """Update current prices and P&L for all positions"""
        for symbol, position in self.positions.items():
            try:
                # Simulate price update
                price_change = np.random.normal(0, self.config.COMMISSION_RATE)  # Small random change
                position.current_price *= (1 + price_change)

                # Calculate unrealized P&L
                if position.side == 'BUY':
                    position.unrealized_pnl = (position.current_price - position.entry_price) * position.quantity
                else:
                    position.unrealized_pnl = (position.entry_price - position.current_price) * position.quantity

                position.timestamp = datetime.now().isoformat()
                self.db.update_position(position)

            except Exception as e:
                self.logger.error(f"Position update error for {symbol}: {e}")

    def _generate_signals(self):
        """Generate trading signals based on model predictions"""
        try:
            # Simulate signal generation for major pairs
            symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT']

            for symbol in symbols:
                # Get model prediction
                prediction, confidence = self.get_model_prediction(symbol, {})

                # Generate signal if confidence is high enough
                if confidence > 0.8:
                    if prediction > 0.6:  # Buy signal
                        self._create_signal_trade(symbol, 'BUY', prediction, confidence)
                    elif prediction < 0.4:  # Sell signal
                        self._create_signal_trade(symbol, 'SELL', prediction, confidence)

        except Exception as e:
            self.logger.error(f"Signal generation error: {e}")

    def _create_signal_trade(self, symbol: str, side: str, prediction: float, confidence: float):
        """Create a trade based on model signal"""
        try:
            # Calculate position size based on risk management
            risk_amount = self.config.FIXED_RISK_AMOUNT

            # Simulate current price
            current_price = 50000 if 'BTC' in symbol else 3000 if 'ETH' in symbol else 1.0
            current_price *= (1 + np.random.normal(0, 0.01))

            quantity = risk_amount / current_price

            trade = Trade(
                id=f"SIGNAL_{int(time.time())}_{symbol}",
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=current_price,
                status='PENDING',
                timestamp=datetime.now().isoformat(),
                model_prediction=prediction,
                confidence=confidence,
                stop_loss=current_price * (0.99 if side == 'BUY' else 1.01),
                take_profit=current_price * (1.02 if side == 'BUY' else 0.98)
            )

            self.trade_queue.put(trade)
            self.logger.info(f"Signal trade created: {symbol} {side} (confidence: {confidence:.2f})")

        except Exception as e:
            self.logger.error(f"Signal trade creation error: {e}")

    def place_manual_trade(self, symbol: str, side: str, quantity: float, price: float) -> str:
        """Place a manual trade"""
        try:
            trade_id = f"MANUAL_{int(time.time())}_{symbol}"

            trade = Trade(
                id=trade_id,
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=price,
                status='PENDING',
                timestamp=datetime.now().isoformat()
            )

            self.trade_queue.put(trade)
            self.logger.info(f"Manual trade placed: {trade_id}")

            return trade_id

        except Exception as e:
            self.logger.error(f"Manual trade placement error: {e}")
            return ""

    def get_trading_status(self) -> dict:
        """Get current trading status"""
        return {
            'is_trading': self.is_trading,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'daily_pnl': self.daily_pnl,
            'win_rate': self.winning_trades / max(self.total_trades, 1) * 100,
            'queue_size': self.trade_queue.qsize(),
            'active_positions': len(self.positions),
            'model_loaded': self.model is not None
        }

    def get_recent_trades(self, limit: int = 50) -> List[dict]:
        """Get recent trades as dictionaries"""
        trades = self.db.get_trades(limit)
        return [asdict(trade) for trade in trades]

    def get_positions(self) -> List[dict]:
        """Get current positions as dictionaries"""
        positions = list(self.positions.values())
        return [asdict(position) for position in positions]

class TradeManagerWebApp:
    """Flask web application for trade management"""

    def __init__(self, trade_manager: LiveTradeManager):
        self.trade_manager = trade_manager
        self.app = Flask(__name__)
        self.app.secret_key = 'trading_vps4_secret_key'
        self.setup_routes()
        self.logger = logging.getLogger('TradeManagerWebApp')

    def setup_routes(self):
        """Setup Flask routes"""

        @self.app.route('/')
        def dashboard():
            """Main trading dashboard"""
            return render_template_string(self.get_dashboard_template())

        @self.app.route('/api/status')
        def api_status():
            """Get trading status"""
            status = self.trade_manager.get_trading_status()
            return jsonify(status)

        @self.app.route('/api/start_trading', methods=['POST'])
        def api_start_trading():
            """Start live trading"""
            try:
                success = self.trade_manager.start_trading()
                return jsonify({'success': success, 'message': 'Trading started' if success else 'Already trading'})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/stop_trading', methods=['POST'])
        def api_stop_trading():
            """Stop live trading"""
            try:
                self.trade_manager.stop_trading()
                return jsonify({'success': True, 'message': 'Trading stopped'})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/place_trade', methods=['POST'])
        def api_place_trade():
            """Place a manual trade"""
            try:
                data = request.get_json()
                trade_id = self.trade_manager.place_manual_trade(
                    symbol=data['symbol'],
                    side=data['side'],
                    quantity=float(data['quantity']),
                    price=float(data['price'])
                )
                return jsonify({'success': True, 'trade_id': trade_id})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/trades')
        def api_trades():
            """Get recent trades"""
            try:
                limit = request.args.get('limit', 50, type=int)
                trades = self.trade_manager.get_recent_trades(limit)
                return jsonify(trades)
            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/positions')
        def api_positions():
            """Get current positions"""
            try:
                positions = self.trade_manager.get_positions()
                return jsonify(positions)
            except Exception as e:
                return jsonify({'error': str(e)}), 500

    def get_dashboard_template(self):
        """HTML template for trading dashboard"""
        return '''
<!DOCTYPE html>
<html>
<head>
    <title>Live Trade Manager - VPS 4</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }

        .header {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 25px;
            font-weight: 600;
        }

        .status-trading { background: #d4edda; color: #155724; }
        .status-stopped { background: #f8d7da; color: #721c24; }

        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }

        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.2); }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #eee;
            padding-bottom: 8px;
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .metric {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .metric-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .table-container {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
        }

        .status-filled { color: #28a745; font-weight: 600; }
        .status-pending { color: #ffc107; font-weight: 600; }
        .status-failed { color: #dc3545; font-weight: 600; }

        .side-buy { color: #28a745; font-weight: 600; }
        .side-sell { color: #dc3545; font-weight: 600; }

        .pnl-positive { color: #28a745; font-weight: 600; }
        .pnl-negative { color: #dc3545; font-weight: 600; }

        .alert {
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid transparent;
        }

        .alert-success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .alert-warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .full-width { grid-column: 1 / -1; }

        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
            .status-bar { flex-direction: column; align-items: stretch; }
            .controls { justify-content: center; }
            .metrics { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Live Trade Manager - VPS 4</h1>
            <div class="status-bar">
                <div id="trading-status" class="status-indicator status-stopped">
                    <span id="status-dot">⭕</span>
                    <span id="status-text">Trading Stopped</span>
                </div>
                <div class="controls">
                    <button id="start-btn" class="btn btn-success" onclick="startTrading()">▶️ Start Trading</button>
                    <button id="stop-btn" class="btn btn-danger" onclick="stopTrading()">⏹️ Stop Trading</button>
                    <button class="btn btn-primary" onclick="refreshData()">🔄 Refresh</button>
                </div>
            </div>
        </div>

        <div id="alerts"></div>

        <div class="grid">
            <div class="card">
                <h3>📊 Trading Metrics</h3>
                <div class="metrics">
                    <div class="metric">
                        <div id="total-trades" class="metric-value">0</div>
                        <div class="metric-label">Total Trades</div>
                    </div>
                    <div class="metric">
                        <div id="win-rate" class="metric-value">0%</div>
                        <div class="metric-label">Win Rate</div>
                    </div>
                    <div class="metric">
                        <div id="daily-pnl" class="metric-value">$0.00</div>
                        <div class="metric-label">Daily P&L</div>
                    </div>
                    <div class="metric">
                        <div id="active-positions" class="metric-value">0</div>
                        <div class="metric-label">Active Positions</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>📝 Manual Trade</h3>
                <form id="trade-form" onsubmit="placeTrade(event)">
                    <div class="form-group">
                        <label for="symbol">Symbol</label>
                        <select id="symbol" class="form-control" required>
                            <option value="BTCUSDT">BTC/USDT</option>
                            <option value="ETHUSDT">ETH/USDT</option>
                            <option value="ADAUSDT">ADA/USDT</option>
                            <option value="DOTUSDT">DOT/USDT</option>
                            <option value="LINKUSDT">LINK/USDT</option>
                            <option value="BNBUSDT">BNB/USDT</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="side">Side</label>
                        <select id="side" class="form-control" required>
                            <option value="BUY">Buy</option>
                            <option value="SELL">Sell</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="quantity">Quantity</label>
                        <input type="number" id="quantity" class="form-control" step="0.00001" required>
                    </div>
                    <div class="form-group">
                        <label for="price">Price</label>
                        <input type="number" id="price" class="form-control" step="0.01" required>
                    </div>
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        📈 Place Trade
                    </button>
                </form>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>💼 Current Positions</h3>
                <div class="table-container">
                    <table id="positions-table">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Side</th>
                                <th>Quantity</th>
                                <th>Entry Price</th>
                                <th>Current Price</th>
                                <th>Unrealized P&L</th>
                            </tr>
                        </thead>
                        <tbody id="positions-tbody">
                            <tr>
                                <td colspan="6" style="text-align: center; color: #6c757d;">No open positions</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card">
                <h3>📋 Recent Trades</h3>
                <div class="table-container">
                    <table id="trades-table">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Symbol</th>
                                <th>Side</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>P&L</th>
                            </tr>
                        </thead>
                        <tbody id="trades-tbody">
                            <tr>
                                <td colspan="7" style="text-align: center; color: #6c757d;">No trades yet</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isTrading = false;
        let refreshInterval;

        function showAlert(message, type = 'success') {
            const alertsDiv = document.getElementById('alerts');
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'error' ? 'alert-danger' : 'alert-warning';

            const alertHtml = `
                <div class="alert ${alertClass}">
                    ${message}
                    <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
                </div>
            `;

            alertsDiv.innerHTML = alertHtml + alertsDiv.innerHTML;

            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alerts = alertsDiv.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, 5000);
        }

        function updateTradingStatus(status) {
            const statusDiv = document.getElementById('trading-status');
            const statusDot = document.getElementById('status-dot');
            const statusText = document.getElementById('status-text');
            const startBtn = document.getElementById('start-btn');
            const stopBtn = document.getElementById('stop-btn');

            isTrading = status.is_trading;

            if (isTrading) {
                statusDiv.className = 'status-indicator status-trading';
                statusDot.textContent = '🟢';
                statusText.textContent = 'Trading Active';
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else {
                statusDiv.className = 'status-indicator status-stopped';
                statusDot.textContent = '⭕';
                statusText.textContent = 'Trading Stopped';
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }

            // Update metrics
            document.getElementById('total-trades').textContent = status.total_trades || 0;
            document.getElementById('win-rate').textContent = (status.win_rate || 0).toFixed(1) + '%';
            document.getElementById('daily-pnl').textContent = '$' + (status.daily_pnl || 0).toFixed(2);
            document.getElementById('active-positions').textContent = status.active_positions || 0;
        }

        function startTrading() {
            fetch('/api/start_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('Trading started successfully!', 'success');
                        refreshData();
                    } else {
                        showAlert('Failed to start trading: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showAlert('Error starting trading: ' + error, 'error');
                });
        }

        function stopTrading() {
            fetch('/api/stop_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('Trading stopped successfully!', 'warning');
                        refreshData();
                    } else {
                        showAlert('Failed to stop trading: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showAlert('Error stopping trading: ' + error, 'error');
                });
        }

        function placeTrade(event) {
            event.preventDefault();

            const formData = {
                symbol: document.getElementById('symbol').value,
                side: document.getElementById('side').value,
                quantity: document.getElementById('quantity').value,
                price: document.getElementById('price').value
            };

            fetch('/api/place_trade', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert(`Trade placed successfully! ID: ${data.trade_id}`, 'success');
                        document.getElementById('trade-form').reset();
                        setTimeout(refreshData, 1000);
                    } else {
                        showAlert('Failed to place trade: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showAlert('Error placing trade: ' + error, 'error');
                });
        }

        function refreshData() {
            // Update status
            fetch('/api/status')
                .then(response => response.json())
                .then(data => updateTradingStatus(data))
                .catch(error => console.error('Status update error:', error));

            // Update positions
            fetch('/api/positions')
                .then(response => response.json())
                .then(data => updatePositionsTable(data))
                .catch(error => console.error('Positions update error:', error));

            // Update trades
            fetch('/api/trades?limit=20')
                .then(response => response.json())
                .then(data => updateTradesTable(data))
                .catch(error => console.error('Trades update error:', error));
        }

        function updatePositionsTable(positions) {
            const tbody = document.getElementById('positions-tbody');

            if (positions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #6c757d;">No open positions</td></tr>';
                return;
            }

            tbody.innerHTML = positions.map(pos => `
                <tr>
                    <td>${pos.symbol}</td>
                    <td class="side-${pos.side.toLowerCase()}">${pos.side}</td>
                    <td>${parseFloat(pos.quantity).toFixed(6)}</td>
                    <td>$${parseFloat(pos.entry_price).toFixed(2)}</td>
                    <td>$${parseFloat(pos.current_price).toFixed(2)}</td>
                    <td class="${pos.unrealized_pnl >= 0 ? 'pnl-positive' : 'pnl-negative'}">
                        $${parseFloat(pos.unrealized_pnl).toFixed(2)}
                    </td>
                </tr>
            `).join('');
        }

        function updateTradesTable(trades) {
            const tbody = document.getElementById('trades-tbody');

            if (trades.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #6c757d;">No trades yet</td></tr>';
                return;
            }

            tbody.innerHTML = trades.map(trade => {
                const time = new Date(trade.timestamp).toLocaleTimeString();
                return `
                    <tr>
                        <td>${time}</td>
                        <td>${trade.symbol}</td>
                        <td class="side-${trade.side.toLowerCase()}">${trade.side}</td>
                        <td>${parseFloat(trade.quantity).toFixed(6)}</td>
                        <td>$${parseFloat(trade.price).toFixed(2)}</td>
                        <td class="status-${trade.status.toLowerCase()}">${trade.status}</td>
                        <td class="${trade.profit_loss >= 0 ? 'pnl-positive' : 'pnl-negative'}">
                            $${parseFloat(trade.profit_loss).toFixed(2)}
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();

            // Auto-refresh every 5 seconds
            refreshInterval = setInterval(refreshData, 5000);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
        '''

    def run(self, host='127.0.0.1', port=8081, debug=True):
        """Start the web application"""
        self.logger.info(f"Starting Trade Manager Web App on {host}:{port}")
        self.app.run(host=host, port=port, debug=debug)

def main():
    """Main entry point for the trade manager"""
    import argparse

    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(TradingConfig.LOGS_DIR, 'trade_manager.log')),
            logging.StreamHandler()
        ]
    )

    logger = logging.getLogger('TradeManagerMain')

    try:
        # Ensure directories exist
        os.makedirs(TradingConfig.DATA_DIR, exist_ok=True)
        os.makedirs(TradingConfig.LOGS_DIR, exist_ok=True)

        # Initialize trade manager
        logger.info("Initializing Live Trade Manager...")
        trade_manager = LiveTradeManager(TradingConfig())

        # Initialize web application
        logger.info("Initializing Trade Manager Web Application...")
        web_app = TradeManagerWebApp(trade_manager)

        print(f"\n🚀 Live Trade Manager - VPS 4")
        print(f"📊 Web interface: http://localhost:self.config.WEB_PORT")
        print(f"🔧 Mode: Live Trading Management")
        print(f"📁 Database: {os.path.join(TradingConfig.DATA_DIR, 'trades.db')}")
        print(f"📁 Logs: {TradingConfig.LOGS_DIR}")
        print(f"\n✅ Trade Manager ready! Open your browser to access the dashboard.")
        print(f"⚠️  Note: System starts in SIMULATION mode for safety")
        print(f"🎯 Features:")
        print(f"   • Real-time trade execution and monitoring")
        print(f"   • Manual trade placement")
        print(f"   • Position management")
        print(f"   • Performance tracking")
        print(f"   • Model-based signal generation")

        # Start the web application
        web_app.run(host='127.0.0.1', port=8081, debug=False)

    except KeyboardInterrupt:
        logger.info("Trade Manager interrupted by user")
        print(f"\n⚠️  Trade Manager stopped by user")
    except Exception as e:
        logger.error(f"Trade Manager error: {e}")
        print(f"\n❌ Trade Manager error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
