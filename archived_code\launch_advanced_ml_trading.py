"""
Launch Script for Advanced ML Trading System
Complete TCN + CNN + PPO implementation with live Bitcoin trading
"""

import os
import sys
import logging
import argparse
from datetime import datetime

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

def setup_environment():
    """Setup environment and directories"""
    config = TradingConfig()
    
    # Create necessary directories
    directories = [
        config.MODELS_DIR,
        config.LOGS_DIR,
        config.REPORTS_DIR,
        config.DATA_DIR,
        'models',  # For individual model files
        'logs/ppo_tensorboard',
        'logs/ppo_eval'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("✅ Environment setup complete")
    return config

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'tensorflow',
        'torch', 
        'stable_baselines3',
        'gymnasium',
        'optuna',
        'sklearn',
        'pandas',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Please install missing packages:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All dependencies available")
    return True

def run_tests():
    """Run comprehensive tests"""
    print("\n🧪 Running comprehensive tests...")
    
    try:
        from test_advanced_ml_system import run_comprehensive_test
        test_results = run_comprehensive_test()
        
        total_passed = sum(test_results.values())
        total_tests = len(test_results)
        
        if total_passed == total_tests:
            print("✅ All tests passed!")
            return True
        else:
            print(f"⚠️ {total_tests - total_passed} tests failed")
            return False
            
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

def run_training(symbols=None, quick_mode=False):
    """Run the advanced ML training"""
    print("\n🚀 Starting Advanced ML Training...")
    
    try:
        from advanced_ml_training_system import AdvancedMLTradingSystem
        
        config = TradingConfig()
        
        # Override symbols if specified
        if symbols:
            config.TRADING_PAIRS = symbols
            print(f"Training symbols: {symbols}")
        
        # Initialize system
        ml_system = AdvancedMLTradingSystem(config)
        
        if quick_mode:
            print("⚡ Quick mode: Training with reduced parameters")
            # Override for quick testing
            original_pairs = config.TRADING_PAIRS
            config.TRADING_PAIRS = ['BTCUSDT']  # Only BTC for quick mode
        
        # Run training
        results = ml_system.train_all_symbols()
        
        # Print results
        ml_system.print_training_summary()
        
        return results
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_live_trading():
    """Launch live trading with trained models"""
    print("\n💰 Starting Live Trading System...")
    
    try:
        # Check if models exist
        config = TradingConfig()
        model_files = []
        
        for symbol in config.TRADING_PAIRS:
            model_path = os.path.join(config.MODELS_DIR, f"{symbol}_advanced_ensemble_config.joblib")
            if os.path.exists(model_path):
                model_files.append(symbol)
        
        if not model_files:
            print("❌ No trained models found. Please run training first.")
            return False
        
        print(f"✅ Found trained models for: {model_files}")
        
        # Launch integrated trading system
        from complete_trading_system import CompleteTradingSystem
        
        trading_system = CompleteTradingSystem()
        
        print("🚀 Launching live trading with advanced ML models...")
        trading_system.run()
        
        return True
        
    except Exception as e:
        print(f"❌ Live trading launch failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(description='Advanced ML Trading System Launcher')
    parser.add_argument('--mode', choices=['test', 'train', 'live', 'full'], 
                       default='full', help='Execution mode')
    parser.add_argument('--symbols', nargs='+', 
                       help='Specific symbols to train (e.g., BTCUSDT ETHUSDT)')
    parser.add_argument('--quick', action='store_true', 
                       help='Quick mode for testing')
    parser.add_argument('--skip-tests', action='store_true',
                       help='Skip dependency and system tests')
    
    args = parser.parse_args()
    
    print("🚀 ADVANCED ML TRADING SYSTEM LAUNCHER")
    print("="*60)
    print("🎯 TCN + CNN + PPO Ensemble")
    print("🔍 Grid Search Optimization") 
    print("📊 85% Composite Score Target")
    print("💰 Live Bitcoin Trading")
    print("="*60)
    
    # Setup environment
    config = setup_environment()
    
    # Check dependencies
    if not args.skip_tests:
        if not check_dependencies():
            print("❌ Dependency check failed. Exiting.")
            return 1
    
    success = True
    
    # Execute based on mode
    if args.mode in ['test', 'full']:
        if not args.skip_tests:
            print("\n📋 PHASE 1: SYSTEM TESTING")
            print("-" * 40)
            if not run_tests():
                print("⚠️ Tests failed. Continue anyway? (y/n)")
                if input().lower() != 'y':
                    return 1
    
    if args.mode in ['train', 'full']:
        print("\n🤖 PHASE 2: MODEL TRAINING")
        print("-" * 40)
        results = run_training(args.symbols, args.quick)
        if results is None:
            success = False
        else:
            # Check if any models meet threshold
            summary = results.get('summary', {})
            threshold_met = summary.get('threshold_met', 0)
            
            if threshold_met == 0:
                print("⚠️ No models met the 85% threshold requirement.")
                print("Continue to live trading anyway? (y/n)")
                if input().lower() != 'y':
                    return 1
    
    if args.mode in ['live', 'full']:
        print("\n💰 PHASE 3: LIVE TRADING")
        print("-" * 40)
        if not run_live_trading():
            success = False
    
    # Final status
    print("\n" + "="*60)
    if success:
        print("🎉 ADVANCED ML TRADING SYSTEM LAUNCHED SUCCESSFULLY!")
        print("📊 Monitor performance via web dashboard")
        print("📈 Check logs for detailed information")
    else:
        print("❌ LAUNCH COMPLETED WITH ERRORS")
        print("📋 Check logs for troubleshooting")
    print("="*60)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
