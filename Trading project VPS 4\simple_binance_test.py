#!/usr/bin/env python3
"""
SIMPLE BINANCE CONNECTION TEST
=============================
Test Binance API connection using requests library (no CCXT required).
"""

import os
import sys
import json
import time
import hmac
import hashlib
import requests
from datetime import datetime
from urllib.parse import urlencode

class SimpleBinanceConnector:
    """Simple Binance API connector using requests."""
    
    def __init__(self, api_key, secret_key, testnet=True):
        self.api_key = api_key
        self.secret_key = secret_key
        self.testnet = testnet
        
        if testnet:
            self.base_url = "https://testnet.binance.vision"
        else:
            self.base_url = "https://api.binance.com"
    
    def _generate_signature(self, query_string):
        """Generate HMAC SHA256 signature."""
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, endpoint, params=None, signed=False):
        """Make API request to Binance."""
        if params is None:
            params = {}
        
        headers = {
            'X-MBX-APIKEY': self.api_key
        }
        
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            query_string = urlencode(params)
            signature = self._generate_signature(query_string)
            params['signature'] = signature
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"API request failed: {e}")
    
    def test_connectivity(self):
        """Test basic connectivity."""
        try:
            result = self._make_request("/api/v3/ping")
            return True
        except Exception as e:
            print(f"Connectivity test failed: {e}")
            return False
    
    def get_server_time(self):
        """Get server time."""
        try:
            result = self._make_request("/api/v3/time")
            return result['serverTime']
        except Exception as e:
            print(f"Server time failed: {e}")
            return None
    
    def get_account_info(self):
        """Get account information."""
        try:
            result = self._make_request("/api/v3/account", signed=True)
            return result
        except Exception as e:
            print(f"Account info failed: {e}")
            return None
    
    def get_ticker_price(self, symbol="BTCUSDT"):
        """Get ticker price."""
        try:
            result = self._make_request("/api/v3/ticker/price", {"symbol": symbol})
            return float(result['price'])
        except Exception as e:
            print(f"Ticker price failed: {e}")
            return None

def load_api_keys():
    """Load API keys from BinanceAPI_2.txt file."""
    try:
        # Try multiple possible paths
        possible_paths = [
            os.path.join(os.path.dirname(os.path.dirname(__file__)), "BinanceAPI_2.txt"),
            r"C:\Users\<USER>\Documents\Trading system\Trading project VPS 5\BinanceAPI_2.txt",
            "BinanceAPI_2.txt",
            "../BinanceAPI_2.txt"
        ]

        for api_file_path in possible_paths:
            if os.path.exists(api_file_path):
                print(f"Found API keys at: {api_file_path}")
                with open(api_file_path, 'r') as f:
                    lines = [line.strip() for line in f.readlines() if line.strip()]
                    if len(lines) >= 2:
                        api_key = lines[0]
                        secret_key = lines[1]
                        print(f"API Key loaded: {api_key[:8]}...{api_key[-8:]}")
                        print(f"Secret Key loaded: {secret_key[:8]}...{secret_key[-8:]}")
                        return api_key, secret_key
        
        return None, None
        
    except Exception as e:
        print(f"Error loading API keys: {e}")
        return None, None

def test_binance_connection(testnet=True):
    """Test Binance connection."""
    mode = "TESTNET" if testnet else "LIVE"
    print(f"\n🔗 Testing Binance {mode} Connection")
    print("-" * 50)
    
    # Load API keys
    api_key, secret_key = load_api_keys()
    if not api_key or not secret_key:
        print("❌ API keys not found")
        return False
    
    print(f"✅ API Key: {api_key[:8]}...{api_key[-8:]}")
    print(f"✅ Secret Key: {secret_key[:8]}...{secret_key[-8:]}")
    
    # Create connector
    connector = SimpleBinanceConnector(api_key, secret_key, testnet)
    
    # Test 1: Basic connectivity
    print("\n📡 Testing basic connectivity...")
    if not connector.test_connectivity():
        print("❌ Basic connectivity failed")
        return False
    print("✅ Basic connectivity: OK")
    
    # Test 2: Server time
    print("\n⏰ Testing server time...")
    server_time = connector.get_server_time()
    if server_time:
        server_dt = datetime.fromtimestamp(server_time / 1000)
        print(f"✅ Server time: {server_dt}")
    else:
        print("❌ Server time failed")
        return False
    
    # Test 3: Market data
    print("\n📊 Testing market data...")
    btc_price = connector.get_ticker_price("BTCUSDT")
    if btc_price:
        print(f"✅ BTC/USDT Price: ${btc_price:,.2f}")
    else:
        print("❌ Market data failed")
        return False
    
    # Test 4: Account access
    print("\n🔐 Testing account access...")
    account_info = connector.get_account_info()
    if account_info:
        print("✅ Account access: OK")
        
        # Show account type
        account_type = account_info.get('accountType', 'Unknown')
        print(f"   Account Type: {account_type}")
        
        # Show permissions
        permissions = account_info.get('permissions', [])
        print(f"   Permissions: {', '.join(permissions)}")
        
        # Show balances
        balances = account_info.get('balances', [])
        non_zero_balances = [b for b in balances if float(b['free']) > 0 or float(b['locked']) > 0]
        
        if non_zero_balances:
            print("   Balances:")
            for balance in non_zero_balances[:5]:  # Show first 5
                asset = balance['asset']
                free = float(balance['free'])
                locked = float(balance['locked'])
                if free > 0 or locked > 0:
                    print(f"     {asset}: {free:.8f} (Free: {free:.8f}, Locked: {locked:.8f})")
        else:
            print("   Balances: No non-zero balances found")
        
        return True
    else:
        print("❌ Account access failed")
        return False

def main():
    """Main test function."""
    print("🔍 BINANCE CONNECTION TEST (Simple Version)")
    print("=" * 60)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test both testnet and live
    testnet_success = test_binance_connection(testnet=True)
    live_success = test_binance_connection(testnet=False)
    
    # Final report
    print("\n" + "=" * 60)
    print("📋 CONNECTION TEST RESULTS")
    print("=" * 60)
    
    if testnet_success:
        print("✅ TESTNET: Ready for safe testing")
    else:
        print("❌ TESTNET: Connection issues")
    
    if live_success:
        print("✅ LIVE ACCOUNT: Ready for real money trading")
    else:
        print("❌ LIVE ACCOUNT: Connection issues")
    
    if testnet_success or live_success:
        print("\n🎯 READY FOR LIVE TRADING!")
        print("   Your API keys are working correctly")
        print("   You can enable live mode in the webapp")
        print("\n📋 Next Steps:")
        print("   1. Go to http://localhost:5000")
        print("   2. Click 'Switch to Live Mode'")
        if testnet_success:
            print("   3. Choose option 1 (TESTNET) for safe testing")
        if live_success:
            print("   4. Choose option 2 (LIVE MONEY) for real trading")
        return True
    else:
        print("\n❌ CONNECTION ISSUES DETECTED")
        print("   Check your API keys and network connection")
        return False

if __name__ == "__main__":
    main()
