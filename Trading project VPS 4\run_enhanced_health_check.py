#!/usr/bin/env python3
"""
ENHANCED HEALTH CHECK RUNNER
===========================
Runs comprehensive health check for Enhanced TCN-CNN-PPO webapp
"""

import json
import os
from datetime import datetime

def run_enhanced_health_check():
    """Run enhanced health check and generate report"""
    print("🔍 ENHANCED TCN-CNN-PPO WEBAPP HEALTH CHECK")
    print("=" * 50)
    
    try:
        # Import and run health check
        from comprehensive_webapp_health_check import ComprehensiveWebappHealthCheck
        
        # Initialize health checker
        health_checker = ComprehensiveWebappHealthCheck("http://localhost:5001")
        
        # Run complete health check
        report = health_checker.run_complete_health_check()
        
        # Generate timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save report
        report_file = f"health_report_enhanced_{timestamp}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Health report saved: {report_file}")
        
        # Display summary
        print("\n📊 ENHANCED HEALTH CHECK SUMMARY")
        print("-" * 40)
        
        overall_status = report.get('overall_status', 'UNKNOWN')
        total_checks = report.get('total_checks', 0)
        issues_count = report.get('issues_count', 0)
        warnings_count = report.get('warnings_count', 0)
        
        print(f"Overall Status: {overall_status}")
        print(f"Total Checks: {total_checks}")
        print(f"Issues: {issues_count}")
        print(f"Warnings: {warnings_count}")
        
        # Check for Enhanced TCN-CNN-PPO specific items
        results = report.get('results', {})
        
        if 'enhanced_model' in results:
            model_status = results['enhanced_model']
            print(f"Enhanced Model Status: {model_status}")
        
        if 'ensemble_weights' in results:
            weights_status = results['ensemble_weights']
            print(f"Ensemble Weights Status: {weights_status}")
        
        # Display key metrics if available
        if 'model_metrics' in results and results['model_metrics'] == 'HEALTHY':
            print("\n✅ Enhanced TCN-CNN-PPO Model Metrics Validated")
        
        return report
        
    except ImportError as e:
        print(f"❌ Error importing health check module: {e}")
        return None
    except Exception as e:
        print(f"❌ Error running health check: {e}")
        return None

def check_webapp_status():
    """Quick webapp status check"""
    print("\n🌐 QUICK WEBAPP STATUS CHECK")
    print("-" * 30)
    
    try:
        import requests
        
        # Test main endpoints
        base_url = "http://localhost:5001"
        endpoints = [
            ('/', 'Main Dashboard'),
            ('/api/trading_status', 'Trading Status'),
            ('/api/health_check', 'Health Check')
        ]
        
        for endpoint, name in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {name}: OK")
                else:
                    print(f"❌ {name}: HTTP {response.status_code}")
            except Exception as e:
                print(f"❌ {name}: {e}")
                
    except ImportError:
        print("❌ Requests module not available")
    except Exception as e:
        print(f"❌ Quick check failed: {e}")

def validate_enhanced_model_data():
    """Validate Enhanced TCN-CNN-PPO model data"""
    print("\n🤖 ENHANCED MODEL DATA VALIDATION")
    print("-" * 35)
    
    try:
        # Check model metadata file
        metadata_file = "models/webapp_model_metadata.json"
        if os.path.exists(metadata_file):
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            selected_model = metadata.get('selected_model', {})
            model_name = selected_model.get('name', 'Unknown')
            
            if 'Enhanced_TCN_CNN_PPO' in model_name:
                print(f"✅ Enhanced Model: {model_name}")
                
                # Check key metrics
                win_rate = selected_model.get('win_rate', 0)
                composite_score = selected_model.get('composite_score', 0)
                trades_per_day = selected_model.get('trades_per_day', 0)
                
                print(f"✅ Win Rate: {win_rate*100:.1f}%")
                print(f"✅ Composite Score: {composite_score*100:.1f}%")
                print(f"✅ Trades/Day: {trades_per_day}")
                
                # Check ensemble weights
                config = selected_model.get('config', {})
                tcn_weight = config.get('tcn', 0)
                cnn_weight = config.get('cnn', 0)
                ppo_weight = config.get('ppo', 0)
                
                print(f"✅ TCN Weight: {tcn_weight}%")
                print(f"✅ CNN Weight: {cnn_weight}%")
                print(f"✅ PPO Weight: {ppo_weight}%")
                
            else:
                print(f"⚠️ Model: {model_name} (Expected: Enhanced TCN-CNN-PPO)")
        else:
            print("❌ Model metadata file not found")
            
    except Exception as e:
        print(f"❌ Model validation failed: {e}")

def main():
    """Main function"""
    print("🚀 ENHANCED TCN-CNN-PPO WEBAPP VALIDATION")
    print("=" * 50)
    
    # Step 1: Quick webapp status
    check_webapp_status()
    
    # Step 2: Validate model data
    validate_enhanced_model_data()
    
    # Step 3: Run comprehensive health check
    report = run_enhanced_health_check()
    
    # Step 4: Final summary
    print("\n🎯 VALIDATION COMPLETE")
    print("=" * 25)
    
    if report:
        overall_status = report.get('overall_status', 'UNKNOWN')
        if overall_status in ['HEALTHY', 'WARNING']:
            print("✅ Enhanced TCN-CNN-PPO webapp validation successful")
        else:
            print("❌ Issues detected - check health report for details")
    else:
        print("❌ Health check failed to run")
    
    return report

if __name__ == "__main__":
    main()
