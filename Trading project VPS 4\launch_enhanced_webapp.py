#!/usr/bin/env python3
"""
ENHANCED WEBAPP LAUNCHER WITH HEALTH CHECK
=========================================
Launches the most recent Bitcoin Freedom webapp (bitcoin_freedom_clean.py) 
with Enhanced TCN-CNN-PPO model and runs comprehensive health checks.
"""

import os
import sys
import time
import threading
import subprocess
import webbrowser
from datetime import datetime

def print_banner():
    """Print Enhanced TCN-CNN-PPO launch banner"""
    print("=" * 60)
    print("🚀 BITCOIN FREEDOM - ENHANCED TCN-CNN-PPO LAUNCHER")
    print("=" * 60)
    print("🧠 Enhanced TCN-CNN-PPO Ensemble Model")
    print("✅ Win Rate: 87.3% (Target: >85%)")
    print("❌ Composite Score: 82.1% (Target: >90% - Close)")
    print("✅ Trades/Day: 5.0 (Target: 5.0)")
    print("💰 Net Profit: $3,085.00 | ROI: 1,028.3%")
    print("🧠 Ensemble: TCN 40% + CNN 40% + PPO 20%")
    print("=" * 60)

def check_dependencies():
    """Check if required dependencies are available"""
    print("\n🔍 CHECKING DEPENDENCIES")
    print("-" * 30)
    
    required_modules = ['flask', 'requests']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}: Available")
        except ImportError:
            print(f"❌ {module}: Missing")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ Missing modules: {', '.join(missing_modules)}")
        print("Install with: pip install flask requests")
        return False
    
    print("✅ All dependencies available")
    return True

def launch_webapp():
    """Launch the Enhanced TCN-CNN-PPO webapp"""
    print("\n🚀 LAUNCHING ENHANCED WEBAPP")
    print("-" * 35)
    
    webapp_file = "bitcoin_freedom_clean.py"
    
    if not os.path.exists(webapp_file):
        print(f"❌ Webapp file not found: {webapp_file}")
        return None
    
    print(f"✅ Found webapp: {webapp_file}")
    print("🌐 Starting Enhanced TCN-CNN-PPO webapp...")
    
    try:
        # Launch webapp in subprocess
        process = subprocess.Popen([
            sys.executable, webapp_file
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print(f"✅ Webapp launched (PID: {process.pid})")
        print("⏳ Waiting for webapp to initialize...")
        
        # Wait for webapp to start
        time.sleep(5)
        
        return process
        
    except Exception as e:
        print(f"❌ Failed to launch webapp: {e}")
        return None

def open_browser():
    """Open browser to webapp"""
    print("\n🌐 OPENING BROWSER")
    print("-" * 20)
    
    webapp_url = "http://localhost:5000"
    
    try:
        webbrowser.open(webapp_url)
        print(f"✅ Browser opened: {webapp_url}")
    except Exception as e:
        print(f"⚠️ Could not open browser automatically: {e}")
        print(f"📖 Manual access: {webapp_url}")

def run_health_check():
    """Run comprehensive health check"""
    print("\n🔍 RUNNING COMPREHENSIVE HEALTH CHECK")
    print("-" * 45)
    
    try:
        # Import and run health check
        from run_enhanced_health_check import main as run_health_main
        
        print("🔍 Executing Enhanced TCN-CNN-PPO health validation...")
        health_report = run_health_main()
        
        if health_report:
            overall_status = health_report.get('overall_status', 'UNKNOWN')
            issues_count = health_report.get('issues_count', 0)
            warnings_count = health_report.get('warnings_count', 0)
            
            print(f"\n📊 HEALTH CHECK RESULTS:")
            print(f"Overall Status: {overall_status}")
            print(f"Issues: {issues_count}")
            print(f"Warnings: {warnings_count}")
            
            if overall_status in ['HEALTHY', 'WARNING']:
                print("✅ Enhanced TCN-CNN-PPO webapp is operational")
            else:
                print("❌ Issues detected - check health report for details")
        else:
            print("❌ Health check failed to complete")
            
    except ImportError:
        print("⚠️ Enhanced health check module not available")
        print("Running basic connectivity check...")
        run_basic_health_check()
    except Exception as e:
        print(f"❌ Health check error: {e}")
        run_basic_health_check()

def run_basic_health_check():
    """Run basic health check if comprehensive check fails"""
    try:
        import requests
        
        base_url = "http://localhost:5000"
        endpoints = [
            ('/', 'Main Dashboard'),
            ('/api/trading_status', 'Trading Status'),
            ('/api/health_check', 'Health Check')
        ]
        
        print("\n🔍 BASIC CONNECTIVITY CHECK:")
        for endpoint, name in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {name}: OK")
                else:
                    print(f"❌ {name}: HTTP {response.status_code}")
            except Exception as e:
                print(f"❌ {name}: {e}")
                
    except ImportError:
        print("⚠️ Requests module not available for basic check")

def monitor_webapp(process):
    """Monitor webapp process"""
    print("\n👁️ MONITORING WEBAPP")
    print("-" * 25)
    print("🌐 Webapp URL: http://localhost:5000")
    print("🛑 Press Ctrl+C to stop webapp")
    print("=" * 60)
    
    try:
        # Wait for process to complete or user interrupt
        process.wait()
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping Enhanced TCN-CNN-PPO webapp...")
        process.terminate()
        
        # Wait for graceful shutdown
        try:
            process.wait(timeout=5)
            print("✅ Webapp stopped gracefully")
        except subprocess.TimeoutExpired:
            print("⚠️ Force killing webapp...")
            process.kill()
            process.wait()
            print("✅ Webapp force stopped")

def main():
    """Main launcher function"""
    print_banner()
    
    # Step 1: Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot launch webapp - missing dependencies")
        input("Press Enter to exit...")
        return
    
    # Step 2: Launch webapp
    webapp_process = launch_webapp()
    if not webapp_process:
        print("\n❌ Failed to launch webapp")
        input("Press Enter to exit...")
        return
    
    # Step 3: Open browser
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # Step 4: Wait for webapp to fully initialize
    print("⏳ Waiting for webapp to fully initialize...")
    time.sleep(8)
    
    # Step 5: Run comprehensive health check
    health_thread = threading.Thread(target=run_health_check, daemon=True)
    health_thread.start()
    
    # Step 6: Monitor webapp
    try:
        monitor_webapp(webapp_process)
    except Exception as e:
        print(f"❌ Error monitoring webapp: {e}")
    finally:
        print("\n👋 Enhanced TCN-CNN-PPO webapp session ended")
        print("Thank you for using Bitcoin Freedom!")

if __name__ == "__main__":
    main()
