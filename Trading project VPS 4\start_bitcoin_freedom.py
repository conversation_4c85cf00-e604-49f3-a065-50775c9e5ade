#!/usr/bin/env python3
"""
BITCOIN FREEDOM DIRECT LAUNCHER
===============================
Direct launcher that bypasses Python path issues.
"""

import os
import sys
import time
import threading
import webbrowser
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """Main launcher function."""
    print("🚀 BITCOIN FREEDOM PRODUCTION LIVE TRADING APPLICATION")
    print("=" * 60)
    print("💰 Real-time BTC trading with auto signal generation")
    print("🤖 Conservative Elite model (93.2% Win Rate)")
    print("📊 79.1% composite score")
    print("⚡ Production-ready threading")
    print("🔒 LIVE TRADING MODE: ACTIVE")
    print("=" * 60)
    
    try:
        # Import the main trading app
        from live_trading_web_app import app, trading_engine, live_trading_loop
        
        print("✅ Trading system imported successfully")
        print(f"🤖 Model loaded: Conservative Elite")
        print(f"📊 Current Balance: $300.00")
        print(f"📈 Active Trades: {len(trading_engine.open_trades) if trading_engine else 0}")
        
        # Start auto trading if not already running
        if not trading_engine.is_running:
            print("\n🚀 STARTING AUTO TRADING...")
            trading_engine.is_running = True
            
            # Start the trading loop in background thread
            trading_thread = threading.Thread(
                target=live_trading_loop,
                daemon=True,
                name="AutoTradingLoop"
            )
            trading_thread.start()
            
            # Verify thread started
            time.sleep(2)
            if trading_thread.is_alive():
                print("✅ AUTO TRADING STARTED SUCCESSFULLY!")
                print("🎯 System will now automatically trade when conditions are met")
            else:
                print("❌ AUTO TRADING FAILED TO START")
        else:
            print("✅ Auto trading already running")
        
        # Open browser after a short delay
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://localhost:5000')
                print("🌐 Browser opened to http://localhost:5000")
            except:
                print("📖 Manual browser access: http://localhost:5000")
        
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # Start the web application
        print("\n🌐 Starting web server on http://localhost:5000")
        print("🎮 Dashboard will open automatically in your browser")
        print("🛑 Press Ctrl+C to stop the server")
        print("=" * 60)
        
        # Run the Flask app
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("🔧 Please ensure all dependencies are installed")
        print("💡 Run: pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ Error starting webapp: {e}")
        return False
    
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 Bitcoin Freedom stopped by user")
        print("👋 Thank you for using Bitcoin Freedom!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
