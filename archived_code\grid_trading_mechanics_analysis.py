"""
Grid Trading Mechanics Analysis
Answers the 5 critical questions about the trading system implementation
"""

import os
import sys
from datetime import datetime

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))

def analyze_trading_mechanics():
    """Analyze the current trading system mechanics"""
    print("🔍 GRID TRADING MECHANICS ANALYSIS")
    print("=" * 60)
    print("Analyzing the current implementation to answer critical questions")
    print("=" * 60)
    
    # Get current Bitcoin price for grid calculations
    try:
        import requests
        response = requests.get('https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT', timeout=5)
        current_price = float(response.json()['lastPrice'])
        print(f"📊 Current BTC Price: ${current_price:,.2f}")
    except:
        current_price = 67000.0  # Fallback price
        print(f"📊 Using Fallback BTC Price: ${current_price:,.2f}")
    
    print("\n" + "=" * 60)
    
    # Question 1: Order Types
    print("❓ QUESTION 1: Order Types for Entry")
    print("-" * 40)
    print("🔍 ANALYSIS:")
    print("   Current Implementation: MARKET ORDERS at grid touch")
    print("   • When price touches grid level → Immediate market order")
    print("   • No advance limit orders placed")
    print("   • No stop orders for entry")
    print()
    print("📋 CODE EVIDENCE:")
    print("   fully_automated_trading_system.py:330-337")
    print("   → create_market_buy_order() / create_market_sell_order()")
    print()
    print("✅ ANSWER 1:")
    print("   ❌ NO advance limit/stop orders")
    print("   ✅ Market orders placed when grid level touched")
    print("   ⚠️  This is NOT traditional grid trading!")
    
    print("\n" + "=" * 60)
    
    # Question 2: Stop Loss Placement
    print("❓ QUESTION 2: Stop Loss Placement Timing")
    print("-" * 40)
    print("🔍 ANALYSIS:")
    print("   Current Implementation: Stop loss calculated but not always placed")
    print("   • Stop loss calculated: entry_price ± grid_spacing")
    print("   • Some code places stop loss orders")
    print("   • Some code only monitors in software")
    print()
    print("📋 CODE EVIDENCE:")
    print("   live_binance_integration.py:260-267")
    print("   → create_stop_loss_order() after market order")
    print("   grid_trading_core.py:375-376")
    print("   → sl_price = entry_price * (1 - grid_spacing)")
    print()
    print("✅ ANSWER 2:")
    print("   ✅ Stop loss calculated when entry triggered")
    print("   ⚠️  Implementation varies (some place orders, some monitor)")
    print("   📍 Stop loss = 1 grid level away (0.25%)")
    
    print("\n" + "=" * 60)
    
    # Question 3: Fixed Grid Levels
    print("❓ QUESTION 3: Fixed Grid Levels")
    print("-" * 40)
    print("🔍 ANALYSIS:")
    print("   Grid Spacing: 0.25% (0.0025) - FIXED")
    print("   Grid Levels: 10 above + 10 below current price")
    print("   Reference Price: $50,000 for consistent level IDs")
    print()
    
    # Calculate actual grid levels
    grid_spacing = 0.0025
    levels_to_show = 2
    
    print("📊 CURRENT GRID LEVELS (2 above/below current price):")
    print(f"   Current Price: ${current_price:,.2f}")
    print()
    
    for i in range(levels_to_show, -levels_to_show-1, -1):
        level_price = current_price * (1 + (i * grid_spacing))
        if i > 0:
            direction = f"↑ +{i} level{'s' if i > 1 else ''}"
            color = "🟢"
        elif i < 0:
            direction = f"↓ {i} level{'s' if abs(i) > 1 else ''}"
            color = "🔴"
        else:
            direction = "→ Current"
            color = "🟡"
        
        spacing_pct = i * 0.25
        print(f"   {color} ${level_price:8,.2f} ({spacing_pct:+5.2f}%) {direction}")
    
    print()
    print("✅ ANSWER 3:")
    print("   ✅ Grid levels are FIXED at 0.25% spacing")
    print("   ✅ Levels calculated around current price")
    print("   ✅ Consistent level IDs using $50k reference")
    
    print("\n" + "=" * 60)
    
    # Question 4: Exit Conditions
    print("❓ QUESTION 4: Trade Exit Requirements")
    print("-" * 40)
    print("🔍 ANALYSIS:")
    print("   Exit Conditions: MUST hit either profit target OR stop loss")
    print("   • Profit Target: 1 grid level in profit direction")
    print("   • Stop Loss: 1 grid level against position")
    print("   • No manual exits or time-based exits")
    print()
    print("📋 CODE EVIDENCE:")
    print("   grid_trading_core.py:192-208")
    print("   → Check take profit AND stop loss conditions")
    print("   → Trade must hit one of these levels to exit")
    print()
    
    # Calculate exit levels for current price
    buy_profit = current_price * (1 + grid_spacing)
    buy_stop = current_price * (1 - grid_spacing)
    sell_profit = current_price * (1 - grid_spacing)
    sell_stop = current_price * (1 + grid_spacing)
    
    print("📊 EXIT LEVELS FOR CURRENT PRICE:")
    print(f"   If BUY at ${current_price:,.2f}:")
    print(f"   • Profit Target: ${buy_profit:,.2f} (+0.25%)")
    print(f"   • Stop Loss:    ${buy_stop:,.2f} (-0.25%)")
    print()
    print(f"   If SELL at ${current_price:,.2f}:")
    print(f"   • Profit Target: ${sell_profit:,.2f} (-0.25%)")
    print(f"   • Stop Loss:    ${sell_stop:,.2f} (+0.25%)")
    print()
    print("✅ ANSWER 4:")
    print("   ✅ Trade MUST exit at profit target OR stop loss")
    print("   ✅ No other exit conditions")
    print("   📍 Exit levels = 1 grid spacing (0.25%) away")
    
    print("\n" + "=" * 60)
    
    # Question 5: Training Considerations
    print("❓ QUESTION 5: Training Accounts for Trading Mechanics")
    print("-" * 40)
    print("🔍 ANALYSIS:")
    print("   Training Data: 60 days + 30 days out-of-sample")
    print("   Grid Simulation: Uses same 0.25% spacing")
    print("   Exit Logic: Same profit/stop loss rules")
    print("   Risk Management: Same $10 risk / $25 profit")
    print()
    print("📋 CODE EVIDENCE:")
    print("   grid_trading_core.py:296-397")
    print("   → Simulation uses same grid spacing and exit rules")
    print("   → Same position sizing and risk management")
    print()
    print("✅ ANSWER 5:")
    print("   ✅ Training DOES account for grid mechanics")
    print("   ✅ Same 0.25% spacing in training and live")
    print("   ✅ Same exit conditions (profit/stop loss)")
    print("   ✅ Same risk management ($10/$25)")
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY OF FINDINGS")
    print("=" * 60)
    
    print("1️⃣ ORDER TYPES:")
    print("   ❌ NO advance limit orders placed")
    print("   ✅ Market orders when grid touched")
    print("   ⚠️  Not traditional grid trading approach")
    print()
    
    print("2️⃣ STOP LOSS TIMING:")
    print("   ✅ Calculated when entry triggered")
    print("   ⚠️  Implementation varies (orders vs monitoring)")
    print("   📍 Set at 1 grid level away (0.25%)")
    print()
    
    print("3️⃣ GRID LEVELS:")
    print("   ✅ FIXED 0.25% spacing")
    print("   ✅ 10 levels above/below current price")
    print("   ✅ Consistent calculation method")
    print()
    
    print("4️⃣ EXIT CONDITIONS:")
    print("   ✅ MUST hit profit target OR stop loss")
    print("   ✅ No other exit methods")
    print("   📍 Exits at ±0.25% from entry")
    print()
    
    print("5️⃣ TRAINING ALIGNMENT:")
    print("   ✅ Training uses same mechanics")
    print("   ✅ Same grid spacing and exits")
    print("   ✅ Same risk management rules")
    print()
    
    print("🎯 OVERALL ASSESSMENT:")
    print("   ✅ Grid spacing and exits are properly implemented")
    print("   ✅ Training matches live trading mechanics")
    print("   ⚠️  Entry method differs from traditional grid trading")
    print("   ⚠️  Stop loss implementation needs consistency")
    
    return {
        'current_price': current_price,
        'grid_spacing': grid_spacing,
        'levels_above': buy_profit,
        'levels_below': buy_stop
    }

def show_detailed_grid_levels(current_price, num_levels=5):
    """Show detailed grid levels around current price"""
    print(f"\n📊 DETAILED GRID LEVELS (±{num_levels} levels)")
    print("=" * 70)
    
    grid_spacing = 0.0025
    
    print(f"{'Level':<8} {'Price':<12} {'Change':<10} {'Direction':<12} {'Action'}")
    print("-" * 70)
    
    for i in range(num_levels, -num_levels-1, -1):
        level_price = current_price * (1 + (i * grid_spacing))
        change_pct = i * 0.25
        
        if i > 0:
            direction = f"↑ Above (+{i})"
            action = "SELL signal"
            color = "🟢"
        elif i < 0:
            direction = f"↓ Below ({i})"
            action = "BUY signal"
            color = "🔴"
        else:
            direction = "→ Current"
            action = "Monitor"
            color = "🟡"
        
        print(f"{color} {i:+3d}    ${level_price:8,.2f}  {change_pct:+6.2f}%   {direction:<12} {action}")

if __name__ == "__main__":
    print("🎯 GRID TRADING SYSTEM MECHANICS ANALYSIS")
    print("Comprehensive analysis of trading implementation")
    print()
    
    # Run main analysis
    results = analyze_trading_mechanics()
    
    # Show detailed grid levels
    show_detailed_grid_levels(results['current_price'])
    
    print("\n" + "=" * 70)
    print("🎉 ANALYSIS COMPLETE")
    print("=" * 70)
    print("All 5 questions have been analyzed with code evidence.")
    print("The system implements a modified grid trading approach.")
    print("=" * 70)
