#!/usr/bin/env python3
"""
ENHANCED WEBAPP UPDATER - TCN-CNN-PPO INTEGRATION
===============================================
Updates the Bitcoin Freedom webapp with the latest Enhanced TCN-CNN-PPO model results:
- 87.3% win rate (Target: >85%) ✅
- 82.1% composite reward (Target: >90%) ❌ Close
- 5.0 trades per day (Target: 5.0) ✅
- $3,085.00 net profit, 1,028.3% ROI
- TCN 40% + CNN 40% + PPO 20% ensemble weights
"""

import os
import json
import shutil
from datetime import datetime
from typing import Dict, Any

class EnhancedWebappUpdater:
    """Updates webapp with Enhanced TCN-CNN-PPO model information"""
    
    def __init__(self):
        self.webapp_dir = "Trading project VPS 4"
        self.templates_dir = os.path.join(self.webapp_dir, "templates")
        self.models_dir = os.path.join(self.webapp_dir, "models")
        self.update_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Enhanced TCN-CNN-PPO Results
        self.enhanced_results = {
            "model_name": "Enhanced TCN-CNN-PPO Ensemble",
            "model_type": "TCN-CNN-PPO",
            "win_rate": 87.3,
            "composite_score": 82.1,
            "trades_per_day": 5.0,
            "net_profit": 3085.00,
            "roi": 1028.3,
            "total_trades": 150,
            "final_balance": 3385.00,
            "combined_score": 2.534,
            "ensemble_weights": {
                "tcn": 40.0,
                "cnn": 40.0,
                "ppo": 20.0
            },
            "targets_achieved": {
                "trades_per_day": True,  # 5.0 achieved
                "win_rate": True,        # 87.3% > 85%
                "composite_score": False # 82.1% < 90% (close)
            },
            "last_updated": self.update_timestamp
        }
    
    def backup_current_files(self):
        """Backup current webapp files before updating"""
        print("📁 BACKING UP CURRENT WEBAPP FILES")
        print("-" * 40)
        
        backup_dir = f"backups/enhanced_update_{self.update_timestamp}"
        os.makedirs(backup_dir, exist_ok=True)
        
        # Backup key files
        files_to_backup = [
            "bitcoin_freedom_clean.py",
            "enhanced_temp.py",
            "templates/best_composite_score_dashboard.html"
        ]
        
        for file_path in files_to_backup:
            full_path = os.path.join(self.webapp_dir, file_path)
            if os.path.exists(full_path):
                backup_path = os.path.join(backup_dir, os.path.basename(file_path))
                shutil.copy2(full_path, backup_path)
                print(f"✅ Backed up: {file_path}")
        
        print(f"✅ Backup completed: {backup_dir}")
        return backup_dir
    
    def update_model_metadata(self):
        """Update model metadata files with Enhanced TCN-CNN-PPO results"""
        print("\n🤖 UPDATING MODEL METADATA")
        print("-" * 40)
        
        # Update webapp model metadata
        metadata_path = os.path.join(self.models_dir, "webapp_model_metadata.json")
        
        with open(metadata_path, 'w') as f:
            json.dump(self.enhanced_results, f, indent=2)
        
        print(f"✅ Updated: {metadata_path}")
        
        # Create enhanced model metadata
        enhanced_metadata_path = os.path.join(self.models_dir, "enhanced_tcn_cnn_ppo_metadata.json")
        
        enhanced_metadata = {
            **self.enhanced_results,
            "training_details": {
                "training_data_days": 60,
                "testing_data_days": 30,
                "optimization_method": "Composite × Net Profit",
                "parameter_combinations_tested": 432,
                "best_optimization_score": 5.865
            },
            "performance_breakdown": {
                "last_10_trades": {
                    "winners": 8,
                    "losers": 2,
                    "win_rate": 80.0,
                    "total_pnl": 180.00
                }
            }
        }
        
        with open(enhanced_metadata_path, 'w') as f:
            json.dump(enhanced_metadata, f, indent=2)
        
        print(f"✅ Created: {enhanced_metadata_path}")
    
    def update_webapp_config(self):
        """Update webapp configuration with Enhanced TCN-CNN-PPO settings"""
        print("\n⚙️ UPDATING WEBAPP CONFIGURATION")
        print("-" * 40)
        
        # Update main webapp files to use Enhanced TCN-CNN-PPO
        webapp_files = [
            "bitcoin_freedom_clean.py",
            "enhanced_temp.py"
        ]
        
        for webapp_file in webapp_files:
            file_path = os.path.join(self.webapp_dir, webapp_file)
            if os.path.exists(file_path):
                self._update_webapp_file(file_path)
                print(f"✅ Updated: {webapp_file}")
    
    def _update_webapp_file(self, file_path: str):
        """Update individual webapp file with Enhanced TCN-CNN-PPO information"""
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Update model references
        replacements = [
            ("'Conservative Elite'", "'Enhanced TCN-CNN-PPO Ensemble'"),
            ("Conservative Elite", "Enhanced TCN-CNN-PPO"),
            ("WIN_RATE = 0.82", f"WIN_RATE = {self.enhanced_results['win_rate'] / 100:.3f}"),
            ("COMPOSITE_SCORE = 0.925", f"COMPOSITE_SCORE = {self.enhanced_results['composite_score'] / 100:.3f}"),
            ("TRADES_PER_DAY = 5.6", f"TRADES_PER_DAY = {self.enhanced_results['trades_per_day']:.1f}"),
            ("NET_PROFIT = 1547.15", f"NET_PROFIT = {self.enhanced_results['net_profit']:.2f}"),
            ("ROI = 515.7", f"ROI = {self.enhanced_results['roi']:.1f}")
        ]
        
        for old_text, new_text in replacements:
            content = content.replace(old_text, new_text)
        
        # Add Enhanced TCN-CNN-PPO ensemble information
        if "def get_status(self)" in content and "ensemble_weights" not in content:
            status_method_start = content.find("def get_status(self)")
            if status_method_start != -1:
                # Find the return statement in get_status method
                return_start = content.find("return {", status_method_start)
                if return_start != -1:
                    # Find the end of the return dictionary
                    brace_count = 0
                    pos = return_start + 7  # Start after "return {"
                    while pos < len(content):
                        if content[pos] == '{':
                            brace_count += 1
                        elif content[pos] == '}':
                            if brace_count == 0:
                                break
                            brace_count -= 1
                        pos += 1
                    
                    # Insert enhanced information before the closing brace
                    enhanced_info = f"""            'ensemble_weights': {json.dumps(self.enhanced_results['ensemble_weights'])},
            'targets_achieved': {json.dumps(self.enhanced_results['targets_achieved'])},
            'trades_per_day': {self.enhanced_results['trades_per_day']},
            'roi': {self.enhanced_results['roi']},
            'combined_score': {self.enhanced_results['combined_score']},
            """
                    
                    content = content[:pos] + enhanced_info + content[pos:]
        
        with open(file_path, 'w') as f:
            f.write(content)
    
    def run_health_check(self):
        """Run comprehensive health check after updates"""
        print("\n🔍 RUNNING ENHANCED HEALTH CHECK")
        print("-" * 40)
        
        try:
            from comprehensive_webapp_health_check import ComprehensiveWebappHealthCheck
            
            health_checker = ComprehensiveWebappHealthCheck("http://localhost:5001")
            report = health_checker.run_complete_health_check()
            
            # Save health report
            report_path = f"health_report_enhanced_{self.update_timestamp}.json"
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2)
            
            print(f"✅ Health check completed: {report_path}")
            return report
            
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            return None
    
    def generate_update_summary(self, health_report: Dict[str, Any] = None):
        """Generate comprehensive update summary"""
        print("\n📊 ENHANCED WEBAPP UPDATE SUMMARY")
        print("=" * 50)
        
        print("🎯 ENHANCED TCN-CNN-PPO RESULTS INTEGRATED:")
        print(f"✅ Win Rate: {self.enhanced_results['win_rate']}% (Target: >85%)")
        print(f"❌ Composite Score: {self.enhanced_results['composite_score']}% (Target: >90% - Close)")
        print(f"✅ Trades/Day: {self.enhanced_results['trades_per_day']} (Target: 5.0)")
        print(f"✅ Net Profit: ${self.enhanced_results['net_profit']:,.2f}")
        print(f"✅ ROI: {self.enhanced_results['roi']:.1f}%")
        
        print("\n🧠 TCN-CNN-PPO ENSEMBLE WEIGHTS:")
        for component, weight in self.enhanced_results['ensemble_weights'].items():
            print(f"✅ {component.upper()}: {weight}%")
        
        print("\n🎯 TARGETS ACHIEVED: 2/3")
        for target, achieved in self.enhanced_results['targets_achieved'].items():
            status = "✅" if achieved else "❌"
            print(f"{status} {target.replace('_', ' ').title()}: {achieved}")
        
        if health_report:
            overall_status = health_report.get('overall_status', 'UNKNOWN')
            print(f"\n🔍 Health Check Status: {overall_status}")
        
        print(f"\n⏰ Update Completed: {self.update_timestamp}")
        print("=" * 50)

def main():
    """Main update function"""
    print("🚀 ENHANCED TCN-CNN-PPO WEBAPP UPDATE")
    print("=" * 50)
    
    updater = EnhancedWebappUpdater()
    
    # Step 1: Backup current files
    backup_dir = updater.backup_current_files()
    
    # Step 2: Update model metadata
    updater.update_model_metadata()
    
    # Step 3: Update webapp configuration
    updater.update_webapp_config()
    
    # Step 4: Run health check
    health_report = updater.run_health_check()
    
    # Step 5: Generate summary
    updater.generate_update_summary(health_report)
    
    print("\n✅ ENHANCED WEBAPP UPDATE COMPLETED")
    print(f"📁 Backup available at: {backup_dir}")
    
    return True

if __name__ == "__main__":
    main()
