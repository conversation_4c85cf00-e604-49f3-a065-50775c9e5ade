#!/usr/bin/env python3
"""
Deep Dive Trading System Diagnostic Tool
"""

import sys
import time
from datetime import datetime, timedelta
sys.path.append('.')

def diagnose_trading_system():
    print('🔍 DEEP DIVE: TRADING SYSTEM DIAGNOSTIC')
    print('=' * 70)
    
    try:
        # Import the trading system
        from live_trading_web_app import trading_engine, AI_MONITOR_AVAILABLE, ai_monitor
        
        print('✅ Trading engine imported successfully')
        
        # 1. Check basic system status
        print(f'\n1️⃣ BASIC SYSTEM STATUS:')
        print(f'   Trading Engine Running: {trading_engine.is_running}')
        print(f'   Open Trades: {len(trading_engine.open_trades)}')
        print(f'   Max Open Positions: {trading_engine.max_open_positions}')
        print(f'   Total Closed Trades: {len(trading_engine.closed_trades)}')
        print(f'   Daily Trades: {trading_engine.daily_trades}')
        
        # 2. Check price data
        print(f'\n2️⃣ PRICE DATA STATUS:')
        current_price = trading_engine.get_current_btc_price()
        print(f'   Current BTC Price: ${current_price:,.2f}' if current_price else '❌ No price data')
        print(f'   Price History Length: {len(trading_engine.price_history)}')
        print(f'   Minimum Required: 24 (for AI) / 2 (for grid)')
        
        if len(trading_engine.price_history) >= 2:
            latest_prices = [entry['price'] for entry in trading_engine.price_history[-5:]]
            print(f'   Recent Prices: {[f"${p:,.0f}" for p in latest_prices]}')
            
            # Check price movement
            if len(trading_engine.price_history) >= 2:
                prev_price = trading_engine.price_history[-2]['price']
                price_change = current_price - prev_price
                price_change_percent = abs(price_change) / prev_price
                grid_threshold = trading_engine.model.grid_size_percent  # 0.25%
                
                print(f'   Price Change: ${price_change:+.2f} ({price_change_percent:.3%})')
                print(f'   Grid Threshold: {grid_threshold:.3%} (0.25%)')
                print(f'   Grid Triggered: {"✅ YES" if price_change_percent >= grid_threshold else "❌ NO"}')
        
        # 3. Check AI monitoring
        print(f'\n3️⃣ AI MONITORING STATUS:')
        print(f'   AI Monitor Available: {AI_MONITOR_AVAILABLE}')
        
        if AI_MONITOR_AVAILABLE and ai_monitor:
            print(f'   AI Monitor Running: {ai_monitor.monitoring}')
            
            if ai_monitor.monitoring:
                status = ai_monitor.get_current_status()
                print(f'   Current Confidence: {status["current_confidence"]:.1%}')
                print(f'   Above Threshold: {status.get("above_threshold", False)}')
                print(f'   Total Signals: {status.get("total_signals", 0)}')
                
                # Check recent signals
                recent_signals = ai_monitor.get_recent_signals(3)
                print(f'   Recent Signals: {len(recent_signals)}')
                
                if recent_signals:
                    latest_signal = recent_signals[-1]
                    signal_age = (datetime.now() - latest_signal['timestamp']).total_seconds()
                    print(f'   Latest Signal:')
                    print(f'     Action: {latest_signal["action"]}')
                    print(f'     Confidence: {latest_signal["confidence"]:.1%}')
                    print(f'     Action Prob: {latest_signal["action_probability"]:.1%}')
                    print(f'     Age: {signal_age:.1f}s (max: 30s)')
                    
                    # Check if signal would trigger
                    would_trigger = (
                        latest_signal["confidence"] >= 0.75 and
                        latest_signal["action_probability"] > 0.4 and
                        latest_signal["action"] in ['BUY', 'SELL'] and
                        signal_age <= 30
                    )
                    print(f'     Would Trigger: {"✅ YES" if would_trigger else "❌ NO"}')
            else:
                print('   ❌ AI Monitor not running - starting it...')
                ai_monitor.start_monitoring()
                time.sleep(2)
                print('   ✅ AI Monitor started')
        
        # 4. Check AI ensemble
        print(f'\n4️⃣ AI ENSEMBLE STATUS:')
        has_ai_ensemble = trading_engine.model.ai_ensemble is not None
        print(f'   AI Ensemble Available: {has_ai_ensemble}')
        
        if has_ai_ensemble:
            # Test AI input preparation
            ai_data = trading_engine.prepare_ai_input_data()
            print(f'   AI Input Data Ready: {"✅ YES" if ai_data is not None else "❌ NO"}')
            
            if ai_data is None:
                print(f'   ❌ AI disabled - insufficient price history')
                print(f'   Need: 24 data points, Have: {len(trading_engine.price_history)}')
        
        # 5. Test should_enter_trade logic
        print(f'\n5️⃣ TRADE ENTRY LOGIC TEST:')
        
        # Check risk management blocks
        open_trades_block = len(trading_engine.open_trades) >= trading_engine.max_open_positions
        print(f'   Open Trades Block: {"❌ BLOCKED" if open_trades_block else "✅ OK"} ({len(trading_engine.open_trades)}/{trading_engine.max_open_positions})')
        
        # Check daily loss limit
        today = datetime.now().date()
        daily_pnl = sum(t.pnl for t in trading_engine.closed_trades
                       if t.exit_timestamp and t.exit_timestamp.date() == today)
        daily_loss_block = daily_pnl <= -trading_engine.daily_loss_limit
        print(f'   Daily Loss Block: {"❌ BLOCKED" if daily_loss_block else "✅ OK"} (P&L: ${daily_pnl:.2f}, Limit: ${-trading_engine.daily_loss_limit:.2f})')
        
        # Test grid trading signals
        grid_signal_result = trading_engine.check_grid_trading_signals()
        print(f'   Grid Signal Result: {"✅ SIGNAL" if grid_signal_result else "❌ NO SIGNAL"}')
        
        # Overall should_enter_trade result
        should_enter = trading_engine.should_enter_trade()
        print(f'   Should Enter Trade: {"✅ YES" if should_enter else "❌ NO"}')
        
        # 6. Test signal generation
        print(f'\n6️⃣ SIGNAL GENERATION TEST:')
        if should_enter:
            direction, confidence = trading_engine.generate_trade_signal()
            print(f'   Signal Generated: {"✅ YES" if direction else "❌ NO"}')
            if direction:
                print(f'   Direction: {direction}')
                print(f'   Confidence: {confidence:.1%}')
                print(f'   Meets Threshold: {"✅ YES" if confidence > 0.1 else "❌ NO"}')
        else:
            print(f'   ❌ Cannot test - should_enter_trade returned False')
        
        # 7. Summary and recommendations
        print(f'\n7️⃣ DIAGNOSTIC SUMMARY:')
        
        issues = []
        if not trading_engine.is_running:
            issues.append("Trading engine not running")
        if open_trades_block:
            issues.append("Too many open trades")
        if daily_loss_block:
            issues.append("Daily loss limit exceeded")
        if len(trading_engine.price_history) < 2:
            issues.append("Insufficient price history for grid trading")
        if len(trading_engine.price_history) < 24:
            issues.append("Insufficient price history for AI trading")
        if AI_MONITOR_AVAILABLE and ai_monitor and not ai_monitor.monitoring:
            issues.append("AI monitor not running")
        
        if issues:
            print(f'   ❌ ISSUES FOUND:')
            for i, issue in enumerate(issues, 1):
                print(f'      {i}. {issue}')
        else:
            print(f'   ✅ NO CRITICAL ISSUES FOUND')
            print(f'   System should be trading when conditions are met')
        
        return len(issues) == 0
        
    except Exception as e:
        print(f'❌ DIAGNOSTIC ERROR: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = diagnose_trading_system()
    if success:
        print(f'\n🚀 SYSTEM APPEARS HEALTHY')
        print(f'   If no trades are happening, it may be due to:')
        print(f'   - Market conditions not meeting grid thresholds')
        print(f'   - AI signals not meeting confidence criteria')
        print(f'   - Normal conservative trading behavior')
    else:
        print(f'\n🔧 ISSUES DETECTED - FIX REQUIRED')
        print(f'   Address the issues above to restore trading')
