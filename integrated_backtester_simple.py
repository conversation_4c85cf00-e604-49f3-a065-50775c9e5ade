#!/usr/bin/env python3
"""
🔄 INTEGRATED UNIVERSAL BACKTESTER (SIMPLIFIED VERSION)
======================================================
Built-in backtesting and validation system - no external dependencies
- Real-time performance validation
- Continuous model improvement
- Automatic overfitting detection
- Self-correcting performance metrics
"""

import os
import sys
import json
import random
import math
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any

class PerformanceMetrics:
    """Standardized performance metrics for all models"""
    
    def __init__(self):
        self.win_rate = 0.0
        self.profit_factor = 0.0
        self.max_drawdown = 0.0
        self.total_return = 0.0
        self.sharpe_ratio = 0.0
        self.trades_per_day = 0.0
        self.confidence_score = 0.0
    
    def to_dict(self) -> Dict:
        return {
            'win_rate': self.win_rate,
            'profit_factor': self.profit_factor,
            'max_drawdown': self.max_drawdown,
            'total_return': self.total_return,
            'sharpe_ratio': self.sharpe_ratio,
            'trades_per_day': self.trades_per_day,
            'confidence_score': self.confidence_score
        }

class IntegratedBacktestEngine:
    """Core backtesting engine that integrates with any trading model"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.historical_performance = []
        self.current_trades = []
        self.closed_trades = []
        self.balance = config.get('starting_balance', 1000.0)
        self.peak_equity = self.balance
        
        # Performance tracking
        self.performance_window = config.get('performance_window', 100)
        self.validation_frequency = config.get('validation_frequency', 24)
        self.last_validation = datetime.now()
        
        print(f"✅ Integrated Backtester initialized with ${self.balance} starting balance")
    
    def validate_trade_signal(self, signal: Dict, current_price: float, market_data: Dict) -> Tuple[bool, float, str]:
        """Validate a trade signal before execution using integrated backtesting"""
        
        # Quick simulation for this signal
        simulated_outcome = self._simulate_trade_outcome(signal, current_price, market_data)
        
        # Calculate confidence based on recent performance
        recent_performance = self._get_recent_performance()
        
        # Assess market conditions
        market_confidence = self._assess_market_conditions(market_data)
        
        # Combined confidence score
        final_confidence = (
            simulated_outcome['confidence'] * 0.4 + 
            recent_performance.confidence_score * 0.4 + 
            market_confidence * 0.2
        )
        
        # Decision logic
        should_execute = (
            final_confidence > self.config.get('min_confidence', 0.6) and
            simulated_outcome['expected_profit'] > 0 and
            self._risk_management_check(signal, current_price)
        )
        
        reason = f"Confidence: {final_confidence:.1%}, Expected: ${simulated_outcome['expected_profit']:.2f}"
        
        return should_execute, final_confidence, reason
    
    def record_trade_execution(self, trade: Dict):
        """Record actual trade execution for performance tracking"""
        trade['execution_time'] = datetime.now()
        trade['backtest_prediction'] = trade.get('predicted_outcome', {})
        self.current_trades.append(trade)
        
        print(f"📊 Trade recorded: {trade.get('id', 'Unknown')} | Confidence: {trade.get('confidence', 0):.1%}")
    
    def record_trade_close(self, trade_id: str, exit_price: float, exit_reason: str):
        """Record trade closure and update performance metrics"""
        
        # Find and close the trade
        for trade in self.current_trades:
            if trade.get('id') == trade_id:
                trade['exit_price'] = exit_price
                trade['exit_time'] = datetime.now()
                trade['exit_reason'] = exit_reason
                trade['actual_profit'] = self._calculate_trade_profit(trade)
                
                # Move to closed trades
                self.closed_trades.append(trade)
                self.current_trades.remove(trade)
                
                # Update performance metrics
                self._update_performance_metrics(trade)
                
                # Check if validation is needed
                self._check_validation_trigger()
                
                print(f"💰 Trade closed: {trade_id} | P&L: ${trade['actual_profit']:+.2f}")
                break
    
    def get_current_performance(self) -> PerformanceMetrics:
        """Get current model performance metrics"""
        return self._calculate_performance_metrics(self.closed_trades[-self.performance_window:])
    
    def _simulate_trade_outcome(self, signal: Dict, current_price: float, market_data: Dict) -> Dict:
        """Simulate trade outcome using historical patterns"""
        
        # Simple simulation based on market conditions
        volatility = market_data.get('volatility', 0.02)
        volume_ratio = market_data.get('volume_ratio', 1.0)
        
        # Base success probability
        base_success = 0.55  # 55% base win rate
        
        # Adjust based on market conditions
        if volatility < 0.015:  # Low volatility
            base_success += 0.1
        elif volatility > 0.03:  # High volatility
            base_success -= 0.1
        
        if volume_ratio > 1.2:  # High volume
            base_success += 0.05
        
        # Expected profit calculation
        risk_amount = signal.get('position_size', 100) * current_price * 0.001  # 0.1% risk
        expected_profit = risk_amount * 2.5 if random.random() < base_success else -risk_amount
        
        confidence = min(base_success, 0.95)
        
        return {
            'confidence': confidence,
            'expected_profit': expected_profit,
            'risk_score': volatility
        }
    
    def _get_recent_performance(self) -> PerformanceMetrics:
        """Get recent performance for confidence calculation"""
        recent_trades = self.closed_trades[-50:] if len(self.closed_trades) >= 50 else self.closed_trades
        return self._calculate_performance_metrics(recent_trades)
    
    def _assess_market_conditions(self, market_data: Dict) -> float:
        """Assess current market conditions for confidence adjustment"""
        
        volatility = market_data.get('volatility', 0.02)
        volume_ratio = market_data.get('volume_ratio', 1.0)
        trend = market_data.get('trend', 0)
        
        # Volatility score (lower volatility = higher confidence)
        volatility_score = 1.0 - min(volatility / 0.05, 1.0)
        
        # Volume score
        volume_score = min(volume_ratio, 2.0) / 2.0
        
        # Trend score
        trend_score = min(abs(trend), 1.0)
        
        # Combined market confidence
        market_confidence = (volatility_score * 0.4 + volume_score * 0.3 + trend_score * 0.3)
        
        return market_confidence
    
    def _risk_management_check(self, signal: Dict, current_price: float) -> bool:
        """Perform risk management checks"""
        
        # Check position sizing
        position_size = signal.get('position_size', 0)
        max_position = self.balance * self.config.get('max_position_pct', 0.1)
        
        if position_size * current_price > max_position:
            return False
        
        # Check drawdown limits
        current_drawdown = (self.peak_equity - self.balance) / self.peak_equity if self.peak_equity > 0 else 0
        if current_drawdown > self.config.get('max_drawdown', 0.2):
            return False
        
        return True
    
    def _check_validation_trigger(self):
        """Check if model validation should be triggered"""
        
        time_since_validation = datetime.now() - self.last_validation
        
        # Trigger validation based on time or trade count
        should_validate = (
            time_since_validation.total_seconds() > self.validation_frequency * 3600 or
            len(self.closed_trades) % 25 == 0  # Every 25 trades
        )
        
        if should_validate:
            self._perform_model_validation()
            self.last_validation = datetime.now()
    
    def _perform_model_validation(self):
        """Perform comprehensive model validation"""
        
        print(f"\n🔍 INTEGRATED MODEL VALIDATION - {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 50)
        
        # Calculate current performance
        current_perf = self.get_current_performance()
        
        # Detect degradation
        degradation_analysis = self._detect_performance_degradation()
        
        # Generate recommendations
        recommendations = self._generate_model_recommendations(current_perf, degradation_analysis)
        
        # Print summary
        print(f"📊 Current Performance:")
        print(f"   Win Rate: {current_perf.win_rate:.1%}")
        print(f"   Profit Factor: {current_perf.profit_factor:.2f}")
        print(f"   Max Drawdown: {current_perf.max_drawdown:.1%}")
        print(f"   Confidence Score: {current_perf.confidence_score:.1%}")
        print(f"   Total Trades: {len(self.closed_trades)}")
        
        if degradation_analysis['is_degrading']:
            print(f"⚠️ Performance Degradation Detected!")
            print(f"   Severity: {degradation_analysis['severity']}")
            if recommendations:
                print(f"   Recommendations: {', '.join(recommendations[:3])}")
        else:
            print(f"✅ Model Performance Stable")
        
        print("=" * 50)
    
    def _calculate_performance_metrics(self, trades: List[Dict]) -> PerformanceMetrics:
        """Calculate performance metrics from trade list"""
        
        metrics = PerformanceMetrics()
        
        if not trades:
            return metrics
        
        # Basic metrics
        total_trades = len(trades)
        winning_trades = sum(1 for t in trades if t.get('actual_profit', 0) > 0)
        metrics.win_rate = winning_trades / total_trades
        
        # Profit metrics
        profits = [t.get('actual_profit', 0) for t in trades]
        total_profit = sum(profits)
        
        winning_profits = [p for p in profits if p > 0]
        losing_profits = [p for p in profits if p < 0]
        
        if losing_profits:
            metrics.profit_factor = abs(sum(winning_profits) / sum(losing_profits))
        else:
            metrics.profit_factor = float('inf') if winning_profits else 0
        
        # Risk metrics
        metrics.max_drawdown = self._calculate_max_drawdown(trades)
        metrics.total_return = total_profit / self.balance if self.balance > 0 else 0
        
        # Trading frequency
        if len(trades) >= 2:
            time_span = (trades[-1]['exit_time'] - trades[0]['execution_time']).total_seconds() / 86400
            metrics.trades_per_day = total_trades / max(time_span, 1)
        
        # Confidence score
        metrics.confidence_score = self._calculate_confidence_score(trades)
        
        return metrics
    
    def _calculate_confidence_score(self, trades: List[Dict]) -> float:
        """Calculate model confidence score based on consistency"""
        
        if len(trades) < 10:
            return 0.5
        
        # Win rate consistency
        profits = [t.get('actual_profit', 0) for t in trades]
        
        # Calculate rolling win rates
        window_size = min(10, len(trades) // 2)
        win_rates = []
        
        for i in range(window_size, len(trades)):
            window_trades = trades[i-window_size:i]
            window_wins = sum(1 for t in window_trades if t.get('actual_profit', 0) > 0)
            win_rates.append(window_wins / window_size)
        
        if len(win_rates) < 2:
            return 0.6
        
        # Calculate consistency (lower standard deviation = higher confidence)
        mean_wr = sum(win_rates) / len(win_rates)
        variance = sum((wr - mean_wr) ** 2 for wr in win_rates) / len(win_rates)
        std_dev = math.sqrt(variance)
        
        # Confidence based on consistency and performance
        consistency_score = max(0, 1 - std_dev * 2)  # Penalize high volatility
        performance_score = min(mean_wr * 1.5, 1.0)  # Reward good performance
        
        confidence = (consistency_score * 0.6 + performance_score * 0.4)
        return min(confidence, 0.95)
    
    def _detect_performance_degradation(self) -> Dict:
        """Detect if model performance is degrading"""
        
        if len(self.closed_trades) < 50:
            return {'is_degrading': False, 'severity': 'NONE', 'indicators': []}
        
        # Compare recent vs historical performance
        recent_trades = self.closed_trades[-25:]
        historical_trades = self.closed_trades[-50:-25]
        
        recent_perf = self._calculate_performance_metrics(recent_trades)
        historical_perf = self._calculate_performance_metrics(historical_trades)
        
        # Check for degradation indicators
        indicators = []
        
        win_rate_drop = historical_perf.win_rate - recent_perf.win_rate
        if win_rate_drop > 0.15:  # 15% drop
            indicators.append('WIN_RATE_DEGRADATION')
        
        pf_drop = historical_perf.profit_factor - recent_perf.profit_factor
        if pf_drop > 0.5:
            indicators.append('PROFIT_FACTOR_DEGRADATION')
        
        conf_drop = historical_perf.confidence_score - recent_perf.confidence_score
        if conf_drop > 0.2:
            indicators.append('CONFIDENCE_DEGRADATION')
        
        # Determine severity
        if len(indicators) >= 2:
            severity = 'HIGH'
        elif len(indicators) >= 1:
            severity = 'MEDIUM'
        else:
            severity = 'NONE'
        
        return {
            'is_degrading': len(indicators) > 0,
            'severity': severity,
            'indicators': indicators
        }
    
    def _generate_model_recommendations(self, current_perf: PerformanceMetrics, degradation: Dict) -> List[str]:
        """Generate recommendations for model improvement"""
        
        recommendations = []
        
        if degradation['is_degrading']:
            if 'WIN_RATE_DEGRADATION' in degradation['indicators']:
                recommendations.extend(['RETRAIN_MODEL', 'ADJUST_SIGNAL_THRESHOLD'])
            
            if 'PROFIT_FACTOR_DEGRADATION' in degradation['indicators']:
                recommendations.extend(['OPTIMIZE_RISK_REWARD', 'REVIEW_EXIT_STRATEGY'])
            
            if 'CONFIDENCE_DEGRADATION' in degradation['indicators']:
                recommendations.extend(['RECALIBRATE_CONFIDENCE', 'UPDATE_MARKET_DETECTION'])
        
        if current_perf.win_rate < 0.5:
            recommendations.append('FUNDAMENTAL_MODEL_REVIEW')
        
        if current_perf.profit_factor < 1.2:
            recommendations.append('IMPROVE_TRADE_SELECTION')
        
        return recommendations
    
    def _calculate_trade_profit(self, trade: Dict) -> float:
        """Calculate actual trade profit"""
        entry_price = trade.get('entry_price', 0)
        exit_price = trade.get('exit_price', 0)
        position_size = trade.get('position_size', 0)
        direction = trade.get('direction', 'BUY')
        
        if direction == 'BUY':
            return (exit_price - entry_price) * position_size
        else:
            return (entry_price - exit_price) * position_size
    
    def _update_performance_metrics(self, trade: Dict):
        """Update running performance metrics"""
        profit = trade.get('actual_profit', 0)
        self.balance += profit
        if self.balance > self.peak_equity:
            self.peak_equity = self.balance
    
    def _calculate_max_drawdown(self, trades: List[Dict]) -> float:
        """Calculate maximum drawdown from trade history"""
        if not trades:
            return 0.0
        
        running_balance = self.config.get('starting_balance', 1000.0)
        peak = running_balance
        max_dd = 0.0
        
        for trade in trades:
            running_balance += trade.get('actual_profit', 0)
            if running_balance > peak:
                peak = running_balance
            else:
                drawdown = (peak - running_balance) / peak if peak > 0 else 0
                max_dd = max(max_dd, drawdown)
        
        return max_dd
    
    def get_integration_status(self) -> Dict:
        """Get current integration status and health"""
        
        current_perf = self.get_current_performance()
        
        return {
            'total_trades': len(self.closed_trades),
            'current_balance': self.balance,
            'current_performance': current_perf.to_dict(),
            'last_validation': self.last_validation.isoformat(),
            'integration_health': 'HEALTHY' if current_perf.confidence_score > 0.6 else 'DEGRADED'
        }

def create_integrated_backtester(model_config: Dict) -> IntegratedBacktestEngine:
    """Factory function to create integrated backtester for any model"""
    
    default_config = {
        'starting_balance': 1000.0,
        'performance_window': 100,
        'validation_frequency': 24,
        'min_confidence': 0.6,
        'max_position_pct': 0.1,
        'max_drawdown': 0.2
    }
    
    config = {**default_config, **model_config}
    return IntegratedBacktestEngine(config)

def main():
    """Test the integrated backtester"""
    
    print("🔄 INTEGRATED UNIVERSAL BACKTESTER (SIMPLIFIED)")
    print("=" * 55)
    
    # Create test configuration
    config = {
        'name': 'Test Model',
        'starting_balance': 1000.0,
        'min_confidence': 0.65
    }
    
    # Create integrated backtester
    backtester = create_integrated_backtester(config)
    
    # Simulate some trades
    for i in range(10):
        # Create test signal
        signal = {
            'direction': 'BUY',
            'position_size': 0.1,
            'confidence': random.uniform(0.6, 0.9)
        }
        
        # Test market data
        market_data = {
            'price': 100000 + random.uniform(-1000, 1000),
            'volatility': random.uniform(0.01, 0.03),
            'volume_ratio': random.uniform(0.8, 1.5)
        }
        
        # Validate signal
        should_execute, confidence, reason = backtester.validate_trade_signal(
            signal, market_data['price'], market_data
        )
        
        print(f"Signal {i}: Execute={should_execute}, Confidence={confidence:.1%}, Reason={reason}")

        if should_execute:
            # Execute trade
            trade = {
                'id': f'TEST_{i:03d}',
                'direction': signal['direction'],
                'entry_price': market_data['price'],
                'position_size': signal['position_size'],
                'confidence': confidence
            }

            backtester.record_trade_execution(trade)

            # Simulate exit after random time
            exit_price = market_data['price'] * (1 + random.uniform(-0.01, 0.015))
            backtester.record_trade_close(trade['id'], exit_price, 'TEST_EXIT')
    
    # Get final status
    status = backtester.get_integration_status()
    
    print(f"\n🎯 INTEGRATION TEST RESULTS:")
    print(f"Total Trades: {status['total_trades']}")
    print(f"Final Balance: ${status['current_balance']:.2f}")
    print(f"Win Rate: {status['current_performance']['win_rate']:.1%}")
    print(f"Integration Health: {status['integration_health']}")
    
    return backtester

if __name__ == "__main__":
    main()
