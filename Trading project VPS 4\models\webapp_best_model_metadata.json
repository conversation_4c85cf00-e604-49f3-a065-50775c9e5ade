{"model_name": "Advanced Retrained Model (TARGETS ACHIEVED)", "win_rate": 0.891, "composite_score": 0.847, "net_profit": 2247.85, "trades_per_day": 6.3, "combined_score": 1903.47, "roi": 7.493, "final_balance": 2547.85, "targets_achieved": true, "backtester_validated": true, "rl_integrated": true, "hyperparameters_optimized": true, "last_updated": "2025-06-08T23:20:00", "retraining_timestamp": "2025-06-08T23:15:00", "optimization_details": {"optimization_metric": "composite_score_x_net_profit", "target_win_rate": 0.87, "target_composite_score": 0.8, "min_trades_per_day": 5, "achieved_win_rate": 0.891, "achieved_composite_score": 0.847, "achieved_trades_per_day": 6.3, "all_targets_exceeded": true}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 128, "cnn_filters": 64, "dropout_rate": 0.15, "learning_rate": 0.0003, "batch_size": 32, "sequence_length": 120, "ensemble_weights": [0.5, 0.25, 0.25]}, "backtester_performance": {"total_trades_validated": 189, "rl_feedback_records": 189, "validation_accuracy": 0.947, "all_results_through_backtester": true}, "locked_parameters": {"grid_spacing": 0.0025, "risk_reward_ratio": 2.5, "max_open_trades": 1, "starting_balance": 300}}