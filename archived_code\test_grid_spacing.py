#!/usr/bin/env python3
"""
GRID SPACING VALIDATION TEST
============================

This script tests that the 0.25% grid spacing is properly locked
and cannot be changed in the trading system.

Author: Bitcoin Freedom Trading System
Date: 2025-06-03
"""

import os
import sys
import json

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_grid_spacing_lock():
    """Test that grid spacing is locked at 0.25%."""
    print("🔒 TESTING GRID SPACING LOCK")
    print("=" * 50)
    
    try:
        # Test 1: Check metadata file
        print("📋 Test 1: Checking metadata file...")
        metadata_path = os.path.join("models", "webapp_focused_model_metadata.json")
        
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            grid_settings = metadata.get('grid_trading_settings', {})
            grid_spacing = grid_settings.get('grid_size_percent', 0)
            is_locked = grid_settings.get('grid_spacing_locked', False)
            
            print(f"   Grid Spacing: {grid_spacing}")
            print(f"   Is Locked: {is_locked}")
            
            if grid_spacing == 0.0025 and is_locked:
                print("   ✅ PASS: Grid spacing is locked at 0.25%")
            else:
                print("   ❌ FAIL: Grid spacing not properly locked")
                return False
        else:
            print("   ❌ FAIL: Metadata file not found")
            return False
        
        # Test 2: Test model loading
        print("\n🤖 Test 2: Testing model loading...")
        try:
            # Import the model classes
            from live_trading_web_app import BestCompositeModel, CrossMarginCalculator
            
            # Create model instance
            model = BestCompositeModel()
            print(f"   Model loaded: {model.model_id}")
            print(f"   Grid spacing: {model.grid_size_percent}")
            
            if abs(model.grid_size_percent - 0.0025) < 0.0001:
                print("   ✅ PASS: Model has correct grid spacing")
            else:
                print(f"   ❌ FAIL: Model grid spacing is {model.grid_size_percent}, expected 0.0025")
                return False
            
            # Test 3: Test calculator validation
            print("\n🧮 Test 3: Testing calculator validation...")
            calculator = CrossMarginCalculator(model)
            
            # Verify grid spacing is still locked after calculator initialization
            if abs(model.grid_size_percent - 0.0025) < 0.0001:
                print("   ✅ PASS: Calculator maintains locked grid spacing")
            else:
                print(f"   ❌ FAIL: Calculator changed grid spacing to {model.grid_size_percent}")
                return False
            
            # Test 4: Test grid calculation
            print("\n📊 Test 4: Testing grid calculations...")
            test_price = 100000.0  # $100k BTC
            
            # Test position size calculation
            position_calc = calculator.calculate_position_size(test_price, 10.0)
            expected_grid_size = test_price * 0.0025  # $250
            actual_grid_size = position_calc['grid_size_usd']
            
            print(f"   Test price: ${test_price:,.2f}")
            print(f"   Expected grid size: ${expected_grid_size:,.2f}")
            print(f"   Actual grid size: ${actual_grid_size:,.2f}")
            
            if abs(actual_grid_size - expected_grid_size) < 0.01:
                print("   ✅ PASS: Grid size calculation correct")
            else:
                print("   ❌ FAIL: Grid size calculation incorrect")
                return False
            
            # Test grid levels calculation
            grid_levels = calculator.calculate_grid_levels(test_price, 'BUY')
            grid_size_from_levels = grid_levels['grid_size_usd']
            
            print(f"   Grid size from levels: ${grid_size_from_levels:,.2f}")
            
            if abs(grid_size_from_levels - expected_grid_size) < 0.01:
                print("   ✅ PASS: Grid levels calculation correct")
            else:
                print("   ❌ FAIL: Grid levels calculation incorrect")
                return False
            
            # Test 5: Attempt to modify grid spacing (should be prevented)
            print("\n🛡️ Test 5: Testing grid spacing protection...")
            original_spacing = model.grid_size_percent
            
            # Try to change grid spacing
            model.grid_size_percent = 0.005  # Try to change to 0.5%
            
            # Create new calculator to trigger validation
            new_calculator = CrossMarginCalculator(model)
            
            # Check if it was reset back to 0.25%
            if abs(model.grid_size_percent - 0.0025) < 0.0001:
                print("   ✅ PASS: Grid spacing protection working - value reset to 0.25%")
            else:
                print(f"   ❌ FAIL: Grid spacing protection failed - value is {model.grid_size_percent}")
                return False
            
            print("\n🎉 ALL TESTS PASSED!")
            print("🔒 Grid spacing is properly locked at 0.25%")
            print("🛡️ Protection mechanisms are working correctly")
            return True
            
        except Exception as e:
            print(f"   ❌ FAIL: Error testing model: {e}")
            return False
    
    except Exception as e:
        print(f"❌ FAIL: Error in grid spacing test: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 GRID SPACING LOCK VALIDATION")
    print("=" * 60)
    print("Testing that 0.25% grid spacing is locked and cannot be changed")
    print("=" * 60)
    
    success = test_grid_spacing_lock()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ GRID SPACING LOCK TEST: PASSED")
        print("🔒 0.25% grid spacing is properly locked and protected")
    else:
        print("❌ GRID SPACING LOCK TEST: FAILED")
        print("⚠️ Grid spacing lock needs to be fixed")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
