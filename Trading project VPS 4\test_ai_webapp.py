#!/usr/bin/env python3
"""
Test AI Monitoring Integration in Webapp
"""

import urllib.request
import json
import time

def test_ai_monitoring():
    print('🧪 TESTING AI MONITORING INTEGRATION')
    print('=' * 60)

    # Wait for webapp to start
    time.sleep(5)

    try:
        # Test main webapp
        with urllib.request.urlopen('http://localhost:5000', timeout=10) as response:
            if response.getcode() == 200:
                print('✅ Webapp is running')
            else:
                print(f'❌ Webapp HTTP error: {response.getcode()}')
                
        # Test AI monitoring endpoint
        try:
            with urllib.request.urlopen('http://localhost:5000/api/ai_status', timeout=10) as response:
                if response.getcode() == 200:
                    data = json.loads(response.read().decode('utf-8'))
                    print('✅ AI monitoring endpoint working!')
                    
                    if data.get('status') == 'success':
                        ai = data.get('ai_monitoring', {})
                        signals = data.get('signal_activity', {})
                        
                        print(f'   AI Active: {ai.get("active", False)}')
                        print(f'   Current Confidence: {ai.get("current_confidence", 0)*100:.1f}%')
                        print(f'   Threshold: {ai.get("confidence_threshold", 0)*100:.0f}%')
                        print(f'   Above Threshold: {ai.get("above_threshold", False)}')
                        print(f'   Signals (1h): {signals.get("signals_last_hour", 0)}')
                        print(f'   Total Signals: {signals.get("total_signals", 0)}')
                        
                        print('\n🎯 AI MONITORING STATUS:')
                        if ai.get('active', False):
                            confidence = ai.get('current_confidence', 0) * 100
                            threshold = ai.get('confidence_threshold', 0) * 100
                            
                            if confidence >= threshold:
                                print(f'   🟢 SIGNAL READY - Confidence {confidence:.1f}% > {threshold:.0f}%')
                            else:
                                print(f'   🟡 WAITING - Confidence {confidence:.1f}% < {threshold:.0f}%')
                        else:
                            print('   🔴 AI monitoring not active')
                            
                    else:
                        print(f'   ❌ AI monitoring error: {data.get("error", "Unknown")}')
                        
                else:
                    print(f'❌ AI endpoint HTTP error: {response.getcode()}')
                    
        except Exception as e:
            print(f'❌ AI monitoring endpoint failed: {e}')
            
        print('\n🎨 WEBAPP ACCESS:')
        print('   🌐 Main Interface: http://localhost:5000')
        print('   🤖 AI Status: Click the cog icon (⚙️) at bottom left')
        print('   📊 Real-time confidence monitoring in status popup')
        
        print('\n📋 HOW TO VIEW AI CONFIDENCE:')
        print('   1. Open http://localhost:5000 in browser')
        print('   2. Look for small cog icon at bottom left corner')
        print('   3. Click the cog to open status popup')
        print('   4. See "AI Confidence Monitor" section')
        print('   5. Watch confidence percentage update every 5 seconds')
        print('   6. Green = Ready (>75%), Yellow = Waiting (<75%)')
        
    except Exception as e:
        print(f'❌ Webapp test failed: {e}')

if __name__ == "__main__":
    test_ai_monitoring()
