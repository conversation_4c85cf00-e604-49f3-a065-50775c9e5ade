#!/usr/bin/env python3
"""
Simple script to check if the template content has been updated correctly.
"""

def check_template_content():
    """Check the template file for Conservative Elite content."""
    template_path = "templates/bitcoin_freedom_dashboard.html"
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔍 CHECKING TEMPLATE CONTENT")
        print("=" * 40)
        
        checks = [
            ("Conservative Elite Trading System", "Page title"),
            ("Conservative Elite System", "Header subtitle"),
            ("Conservative Elite Model - 93.2% Performance", "Model badge"),
            ("93.2%", "Performance score"),
            ("🔒", "Lock indicator"),
            ("20250105_CONSERVATIVE_ELITE_932_UPDATE", "Cache buster"),
            ("Conservative Elite Score", "Metric label"),
            ("System Name", "Status field"),
            ("Dynamic Compounding", "Risk scaling")
        ]
        
        found_count = 0
        total_count = len(checks)
        
        for text, description in checks:
            if text in content:
                print(f"✅ {description}: FOUND")
                found_count += 1
            else:
                print(f"❌ {description}: NOT FOUND")
        
        print("\n📊 SUMMARY")
        print("-" * 20)
        print(f"Found: {found_count}/{total_count} items")
        
        if found_count == total_count:
            print("🎉 ALL CONSERVATIVE ELITE CONTENT FOUND!")
            print("✅ Template has been updated correctly")
            return True
        else:
            print("❌ SOME CONTENT MISSING")
            print("❌ Template needs more updates")
            return False
            
    except Exception as e:
        print(f"❌ Error reading template: {e}")
        return False

def check_backend_model():
    """Check if the backend model is configured correctly."""
    try:
        print("\n🔍 CHECKING BACKEND MODEL")
        print("=" * 30)
        
        from live_trading_web_app import BestCompositeModel
        model = BestCompositeModel()
        
        checks = [
            (model.model_type, "Conservative Elite", "Model type"),
            (model.composite_score * 100, 93.2, "Composite score"),
            (model.win_rate * 100, 93.2, "Win rate"),
            (model.trades_per_day, 5.8, "Trades per day")
        ]
        
        found_count = 0
        total_count = len(checks)
        
        for actual, expected, description in checks:
            if isinstance(expected, str):
                if expected in str(actual):
                    print(f"✅ {description}: {actual}")
                    found_count += 1
                else:
                    print(f"❌ {description}: {actual} (expected to contain '{expected}')")
            else:
                if abs(float(actual) - float(expected)) < 0.1:
                    print(f"✅ {description}: {actual}")
                    found_count += 1
                else:
                    print(f"❌ {description}: {actual} (expected {expected})")
        
        print(f"\nBackend checks: {found_count}/{total_count}")
        return found_count == total_count
        
    except Exception as e:
        print(f"❌ Error checking backend: {e}")
        return False

def main():
    """Main check function."""
    print("🏆 CONSERVATIVE ELITE INTEGRATION CHECK")
    print("=" * 50)
    
    template_ok = check_template_content()
    backend_ok = check_backend_model()
    
    print("\n🎯 FINAL RESULT")
    print("=" * 20)
    
    if template_ok and backend_ok:
        print("🎉 CONSERVATIVE ELITE INTEGRATION COMPLETE!")
        print("✅ Template content updated")
        print("✅ Backend model configured")
        print("✅ Ready for deployment")
        
        print("\n📋 NEXT STEPS:")
        print("1. Clear browser cache completely")
        print("2. Use Ctrl+F5 to force refresh")
        print("3. Try the /conservative_elite route")
        print("4. Check developer tools for any cached resources")
        
        return True
    else:
        print("❌ INTEGRATION INCOMPLETE")
        if not template_ok:
            print("❌ Template needs updates")
        if not backend_ok:
            print("❌ Backend needs configuration")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
