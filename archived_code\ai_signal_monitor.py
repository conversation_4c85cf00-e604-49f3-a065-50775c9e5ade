#!/usr/bin/env python3
"""
AI Signal Monitoring System
Real-time monitoring of Conservative Elite AI confidence levels and signal generation
"""

import time
import json
from datetime import datetime, timedelta
from collections import deque
import threading
import sys
import os

class AISignalMonitor:
    def __init__(self):
        self.confidence_history = deque(maxlen=1000)  # Store last 1000 readings
        self.signal_history = deque(maxlen=100)       # Store last 100 signals
        self.monitoring = False
        self.monitor_thread = None
        self.confidence_threshold = 0.75  # 75% threshold for Conservative Elite
        self.update_interval = 5  # Check every 5 seconds
        
    def start_monitoring(self):
        """Start the AI signal monitoring"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            print("🤖 AI Signal Monitor started")
    
    def stop_monitoring(self):
        """Stop the AI signal monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        print("🛑 AI Signal Monitor stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                # Get current AI confidence
                confidence_data = self._get_current_ai_confidence()
                
                if confidence_data:
                    # Store confidence reading
                    self.confidence_history.append(confidence_data)
                    
                    # Check if signal should be generated
                    if confidence_data['confidence'] >= self.confidence_threshold:
                        signal = self._generate_signal_from_confidence(confidence_data)
                        if signal:
                            self.signal_history.append(signal)
                            print(f"🎯 AI SIGNAL GENERATED: {signal['action']} (Confidence: {signal['confidence']:.1%})")
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                print(f"❌ AI Monitor error: {e}")
                time.sleep(10)  # Wait longer on error
    
    def _get_current_ai_confidence(self):
        """Get current AI model confidence"""
        try:
            sys.path.append('.')
            from live_trading_web_app import trading_engine
            
            # Get current price
            current_price = trading_engine.get_current_btc_price()
            if not current_price:
                return None
            
            # Simulate AI confidence calculation (based on actual system)
            # In real system, this would call the actual AI model
            confidence_data = self._calculate_ai_confidence(current_price)
            
            return {
                'timestamp': datetime.now(),
                'price': current_price,
                'confidence': confidence_data['confidence'],
                'action_probabilities': confidence_data['probabilities'],
                'technical_indicators': confidence_data['indicators'],
                'grid_level': confidence_data['grid_level'],
                'market_conditions': confidence_data['market_conditions']
            }
            
        except Exception as e:
            print(f"❌ Error getting AI confidence: {e}")
            return None
    
    def _calculate_ai_confidence(self, current_price):
        """Calculate AI confidence based on Conservative Elite model logic"""
        import random
        import math
        
        # Simulate technical indicators (in real system, these would be calculated)
        vwap_signal = random.uniform(-1, 1)
        bb_position = random.uniform(0, 1)
        eth_btc_ratio = random.uniform(-0.05, 0.05)
        flow_strength = random.uniform(30, 90)
        
        # Grid level calculation
        grid_spacing = 0.0025
        base_price = 105000
        grid_level = round((current_price - base_price) / (base_price * grid_spacing))
        
        # Conservative Elite confidence calculation (weighted)
        confidence_components = {
            'vwap_strength': abs(vwap_signal) * 0.30,      # 30% weight
            'bb_position': (1 - abs(bb_position - 0.5)) * 0.25,  # 25% weight
            'eth_btc_signal': abs(eth_btc_ratio) * 0.25,   # 25% weight
            'flow_strength': (flow_strength / 100) * 0.20  # 20% weight
        }
        
        # Calculate overall confidence
        base_confidence = sum(confidence_components.values())
        
        # Add market condition modifiers
        market_volatility = random.uniform(0.8, 1.2)
        time_of_day_factor = 1.0 + 0.1 * math.sin(datetime.now().hour * math.pi / 12)
        
        final_confidence = base_confidence * market_volatility * time_of_day_factor
        final_confidence = max(0.0, min(1.0, final_confidence))  # Clamp to 0-1
        
        # Determine action probabilities
        if vwap_signal > 0.3 and bb_position < 0.3:
            buy_prob = 0.4 + (final_confidence * 0.4)
            sell_prob = 0.1
        elif vwap_signal < -0.3 and bb_position > 0.7:
            buy_prob = 0.1
            sell_prob = 0.4 + (final_confidence * 0.4)
        else:
            buy_prob = 0.2
            sell_prob = 0.2
        
        hold_prob = 1.0 - buy_prob - sell_prob
        
        return {
            'confidence': final_confidence,
            'probabilities': {
                'BUY': buy_prob,
                'SELL': sell_prob,
                'HOLD': hold_prob
            },
            'indicators': {
                'vwap_signal': vwap_signal,
                'bb_position': bb_position,
                'eth_btc_ratio': eth_btc_ratio,
                'flow_strength': flow_strength
            },
            'grid_level': grid_level,
            'market_conditions': {
                'volatility': market_volatility,
                'time_factor': time_of_day_factor
            }
        }
    
    def _generate_signal_from_confidence(self, confidence_data):
        """Generate trading signal when confidence exceeds threshold"""
        probabilities = confidence_data['action_probabilities']
        
        # Determine strongest action
        max_action = max(probabilities, key=probabilities.get)
        max_prob = probabilities[max_action]
        
        # Only generate signal for BUY/SELL (not HOLD)
        if max_action in ['BUY', 'SELL'] and max_prob > 0.4:
            return {
                'signal_id': f"AI_{int(time.time())}",
                'timestamp': confidence_data['timestamp'],
                'action': max_action,
                'confidence': confidence_data['confidence'],
                'action_probability': max_prob,
                'price': confidence_data['price'],
                'grid_level': confidence_data['grid_level'],
                'reason': f"AI confidence {confidence_data['confidence']:.1%} > {self.confidence_threshold:.1%}",
                'technical_indicators': confidence_data['technical_indicators']
            }
        
        return None
    
    def get_current_status(self):
        """Get current AI monitoring status"""
        if not self.confidence_history:
            return {
                'status': 'No data',
                'current_confidence': 0.0,
                'signals_generated': 0,
                'monitoring': self.monitoring
            }
        
        latest = self.confidence_history[-1]
        
        # Calculate recent averages
        recent_confidences = [reading['confidence'] for reading in list(self.confidence_history)[-20:]]
        avg_confidence = sum(recent_confidences) / len(recent_confidences)
        
        # Count recent signals
        recent_signals = [s for s in self.signal_history if s['timestamp'] > datetime.now() - timedelta(hours=1)]
        
        return {
            'status': 'Active' if self.monitoring else 'Stopped',
            'current_confidence': latest['confidence'],
            'average_confidence_20min': avg_confidence,
            'confidence_threshold': self.confidence_threshold,
            'above_threshold': latest['confidence'] >= self.confidence_threshold,
            'signals_last_hour': len(recent_signals),
            'total_signals': len(self.signal_history),
            'last_update': latest['timestamp'],
            'current_price': latest['price'],
            'grid_level': latest['grid_level'],
            'monitoring': self.monitoring,
            'readings_stored': len(self.confidence_history)
        }
    
    def get_confidence_trend(self, minutes=60):
        """Get confidence trend over specified minutes"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        recent_readings = [r for r in self.confidence_history if r['timestamp'] > cutoff_time]
        
        if len(recent_readings) < 2:
            return {'trend': 'insufficient_data', 'readings': len(recent_readings)}
        
        # Calculate trend
        confidences = [r['confidence'] for r in recent_readings]
        first_half = confidences[:len(confidences)//2]
        second_half = confidences[len(confidences)//2:]
        
        avg_first = sum(first_half) / len(first_half)
        avg_second = sum(second_half) / len(second_half)
        
        trend_direction = 'rising' if avg_second > avg_first else 'falling'
        trend_strength = abs(avg_second - avg_first)
        
        return {
            'trend': trend_direction,
            'strength': trend_strength,
            'current_avg': avg_second,
            'previous_avg': avg_first,
            'readings': len(recent_readings),
            'time_period': f"{minutes} minutes"
        }
    
    def get_recent_signals(self, limit=10):
        """Get recent signals"""
        return list(self.signal_history)[-limit:]

# Global monitor instance
ai_monitor = AISignalMonitor()

def start_ai_monitoring():
    """Start AI signal monitoring"""
    ai_monitor.start_monitoring()
    return ai_monitor

def get_ai_status():
    """Get current AI monitoring status"""
    return ai_monitor.get_current_status()

def get_ai_trend(minutes=60):
    """Get AI confidence trend"""
    return ai_monitor.get_confidence_trend(minutes)

def get_recent_ai_signals(limit=10):
    """Get recent AI signals"""
    return ai_monitor.get_recent_signals(limit)

if __name__ == "__main__":
    print("🤖 AI SIGNAL MONITORING SYSTEM")
    print("=" * 60)
    
    # Start monitoring
    monitor = start_ai_monitoring()
    
    try:
        # Monitor for 60 seconds
        for i in range(12):
            time.sleep(5)
            status = get_ai_status()
            
            print(f"\n📊 AI STATUS UPDATE #{i+1}:")
            print(f"   Confidence: {status['current_confidence']:.1%}")
            print(f"   Above Threshold: {'✅' if status['above_threshold'] else '❌'}")
            print(f"   Signals (1h): {status['signals_last_hour']}")
            print(f"   Price: ${status['current_price']:,.2f}")
            
            if status['above_threshold']:
                print(f"   🎯 SIGNAL READY - Confidence above {status['confidence_threshold']:.1%}!")
    
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")
    
    finally:
        ai_monitor.stop_monitoring()
