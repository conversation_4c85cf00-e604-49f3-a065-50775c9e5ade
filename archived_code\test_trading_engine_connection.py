"""
Test Trading Engine Connection
Tests if the webapp can connect to and start the actual trading engines
"""

import os
import sys
import asyncio
from datetime import datetime

def test_enhanced_test_engine():
    """Test the enhanced test trading engine"""
    print("🔍 TESTING ENHANCED TEST TRADING ENGINE")
    print("=" * 50)
    
    try:
        # Add config to path
        sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
        
        # Import components
        from enhanced_test_trading_engine import EnhancedTestTradingEngine
        from trading_config import TradingConfig
        
        print("✅ Successfully imported EnhancedTestTradingEngine")
        print("✅ Successfully imported TradingConfig")
        
        # Initialize config
        config = TradingConfig()
        print(f"✅ Config initialized - Grid spacing: {config.GRID_SPACING}")
        
        # Initialize test engine
        test_engine = EnhancedTestTradingEngine(config)
        print("✅ Test engine initialized")
        
        # Test starting the engine
        async def test_start():
            success = await test_engine.start_test_trading()
            return success
        
        # Run async test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        success = loop.run_until_complete(test_start())
        
        if success:
            print("✅ Test trading engine started successfully!")
            
            # Test processing a cycle
            async def test_cycle():
                cycle_result = await test_engine.process_test_trading_cycle()
                return cycle_result
            
            cycle_result = loop.run_until_complete(test_cycle())
            
            if cycle_result['success']:
                print("✅ Trading cycle processed successfully!")
                print(f"   Current Price: ${cycle_result['current_price']:,.2f}")
                print(f"   Open Trades: {cycle_result['open_trades']}")
                print(f"   Closed Trades: {cycle_result['closed_trades']}")
                print(f"   Current Balance: ${cycle_result['current_balance']:,.2f}")
            else:
                print(f"❌ Trading cycle failed: {cycle_result.get('error', 'Unknown error')}")
            
            # Stop the engine
            async def test_stop():
                await test_engine.stop_test_trading()
            
            loop.run_until_complete(test_stop())
            print("✅ Test trading engine stopped successfully!")
            
        else:
            print("❌ Failed to start test trading engine")
            return False
        
        loop.close()
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_fully_automated_system():
    """Test the fully automated trading system"""
    print("\n🔍 TESTING FULLY AUTOMATED TRADING SYSTEM")
    print("=" * 50)
    
    try:
        # Import components
        from fully_automated_trading_system import FullyAutomatedTradingSystem
        from trading_config import TradingConfig
        
        print("✅ Successfully imported FullyAutomatedTradingSystem")
        
        # Initialize config
        config = TradingConfig()
        
        # Initialize system (without connecting to Binance)
        system = FullyAutomatedTradingSystem(config, connect_binance=False)
        print("✅ Fully automated system initialized (offline mode)")
        
        # Test system components
        if hasattr(system, 'grid_calculator'):
            print("✅ Grid calculator available")
        if hasattr(system, 'trade_manager'):
            print("✅ Trade manager available")
        if hasattr(system, 'metrics_calculator'):
            print("✅ Metrics calculator available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_webapp_integration():
    """Test webapp integration with trading engines"""
    print("\n🔍 TESTING WEBAPP INTEGRATION")
    print("=" * 50)
    
    try:
        # Test if webapp can import the engines
        from enhanced_test_trading_engine import EnhancedTestTradingEngine
        from trading_config import TradingConfig
        
        print("✅ Webapp can import trading engines")
        
        # Test session state simulation
        class MockSessionState:
            def __init__(self):
                self.data = {}
            
            def __contains__(self, key):
                return key in self.data
            
            def __getitem__(self, key):
                return self.data[key]
            
            def __setitem__(self, key, value):
                self.data[key] = value
        
        # Simulate webapp session state
        session_state = MockSessionState()
        
        # Test engine initialization
        config = TradingConfig()
        session_state['test_engine'] = EnhancedTestTradingEngine(config)
        
        print("✅ Engine can be stored in session state")
        print("✅ Webapp integration test passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Webapp integration error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 TRADING ENGINE CONNECTION TEST")
    print("Testing if webapp can connect to actual trading engines")
    print("=" * 60)
    
    # Test enhanced test engine
    test1_passed = test_enhanced_test_engine()
    
    # Test fully automated system
    test2_passed = test_fully_automated_system()
    
    # Test webapp integration
    test3_passed = test_webapp_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Enhanced Test Engine", test1_passed),
        ("Fully Automated System", test2_passed),
        ("Webapp Integration", test3_passed)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name:<25} {status}")
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Webapp can connect to trading engines")
        print("✅ Real trade execution should work")
        print("\n🚀 NEXT STEPS:")
        print("   1. Start your webapp: streamlit run simple_enhanced_webapp.py --server.port 8502")
        print("   2. Click 'Start Test' to begin real test trading")
        print("   3. Monitor the Signal Monitoring section for real trades")
        print("   4. Check that trades actually execute and balance updates")
        
        return True
    else:
        print("⚠️ SOME TESTS FAILED!")
        print("🔧 Issues to resolve:")
        
        if not test1_passed:
            print("   ❌ Enhanced test engine not working")
            print("   💡 Check dependencies and imports")
        if not test2_passed:
            print("   ❌ Fully automated system not working")
            print("   💡 Check system components")
        if not test3_passed:
            print("   ❌ Webapp integration not working")
            print("   💡 Check webapp imports and session state")
        
        print("\n⚠️ Webapp will fall back to simulation mode")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TRADING ENGINE CONNECTION TEST PASSED!")
    else:
        print("⚠️ TRADING ENGINE CONNECTION TEST FAILED!")
    print("=" * 60)
    
    input("\nPress Enter to exit...")
