#!/usr/bin/env python3
"""
8-Hour Conservative Elite Trading Monitor
Expected: ~5.8 trades per day = ~1.9 trades in 8 hours
"""

import urllib.request
import json
import time
from datetime import datetime, timedelta

class TradingMonitor:
    def __init__(self):
        self.start_time = datetime.now()
        self.end_time = self.start_time + timedelta(hours=8)
        self.check_interval = 300  # 5 minutes
        self.trade_count_start = 0
        self.last_price = 0
        self.price_history = []
        self.status_checks = 0
        
    def get_trading_status(self):
        """Get current trading status from webapp"""
        try:
            with urllib.request.urlopen('http://localhost:5000/api/trading_status', timeout=10) as response:
                if response.getcode() == 200:
                    return json.loads(response.read().decode('utf-8'))
        except Exception as e:
            print(f"❌ Status check failed: {e}")
        return None
    
    def get_recent_trades(self):
        """Get recent trades from webapp"""
        try:
            with urllib.request.urlopen('http://localhost:5000/api/recent_trades', timeout=10) as response:
                if response.getcode() == 200:
                    return json.loads(response.read().decode('utf-8'))
        except Exception as e:
            print(f"❌ Trades check failed: {e}")
        return []
    
    def log_status(self, status_data):
        """Log current status"""
        now = datetime.now()
        elapsed = now - self.start_time
        remaining = self.end_time - now
        
        current_price = status_data.get('current_price', 0)
        total_trades = status_data.get('performance', {}).get('total_trades', 0)
        
        # Track price movement
        if self.last_price > 0:
            price_change = current_price - self.last_price
            price_change_pct = (price_change / self.last_price) * 100
        else:
            price_change = 0
            price_change_pct = 0
        
        self.last_price = current_price
        self.price_history.append({
            'time': now,
            'price': current_price,
            'change': price_change,
            'change_pct': price_change_pct
        })
        
        # Calculate trades in this session
        trades_this_session = total_trades - self.trade_count_start
        
        print(f"\n🕐 {now.strftime('%H:%M:%S')} - STATUS CHECK #{self.status_checks + 1}")
        print(f"⏱️  Elapsed: {str(elapsed).split('.')[0]} | Remaining: {str(remaining).split('.')[0]}")
        print(f"💹 Price: ${current_price:,.2f} ({price_change:+.2f} | {price_change_pct:+.3f}%)")
        print(f"📊 Trades This Session: {trades_this_session}")
        print(f"🎯 Expected in 8h: ~1.9 trades (5.8/day average)")
        
        # Check if trading is running
        is_running = status_data.get('is_running', False)
        if not is_running:
            print("⚠️  WARNING: Trading appears to be stopped!")
        
        self.status_checks += 1
        return trades_this_session
    
    def analyze_price_movement(self):
        """Analyze price movement for grid touches"""
        if len(self.price_history) < 2:
            return
        
        # Calculate price volatility
        prices = [p['price'] for p in self.price_history[-12:]]  # Last hour
        if len(prices) > 1:
            price_range = max(prices) - min(prices)
            volatility_pct = (price_range / prices[-1]) * 100
            
            print(f"📈 Price Analysis (last hour):")
            print(f"   Range: ${min(prices):,.2f} - ${max(prices):,.2f}")
            print(f"   Volatility: {volatility_pct:.3f}%")
            
            # Grid spacing is 0.25%
            grid_spacing = 0.25
            if volatility_pct > grid_spacing:
                print(f"   ✅ Sufficient movement for grid touches")
            else:
                print(f"   ⏳ Low volatility - waiting for more movement")
    
    def run_monitoring(self):
        """Run 8-hour monitoring session"""
        print("🚀 CONSERVATIVE ELITE 8-HOUR TRADING MONITOR")
        print("=" * 70)
        print(f"📅 Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📅 End Time: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Expected Trades: ~1.9 (based on 5.8/day average)")
        print(f"⏱️  Check Interval: {self.check_interval//60} minutes")
        print("=" * 70)
        
        # Get initial status
        initial_status = self.get_trading_status()
        if initial_status:
            self.trade_count_start = initial_status.get('performance', {}).get('total_trades', 0)
            print(f"📊 Starting Trade Count: {self.trade_count_start}")
        else:
            print("❌ Could not get initial status")
            return
        
        try:
            while datetime.now() < self.end_time:
                # Get current status
                status = self.get_trading_status()
                if status:
                    trades_this_session = self.log_status(status)
                    
                    # Check for new trades
                    if trades_this_session > 0:
                        print(f"🎉 NEW TRADE DETECTED! Total this session: {trades_this_session}")
                        
                        # Get trade details
                        recent_trades = self.get_recent_trades()
                        if recent_trades:
                            latest_trade = recent_trades[0]
                            print(f"📋 Latest Trade Details:")
                            print(f"   Type: {latest_trade.get('type', 'Unknown')}")
                            print(f"   Price: ${latest_trade.get('price', 0):,.2f}")
                            print(f"   Amount: ${latest_trade.get('amount', 0):.2f}")
                    
                    # Analyze price movement
                    self.analyze_price_movement()
                
                else:
                    print(f"❌ Status check failed at {datetime.now().strftime('%H:%M:%S')}")
                
                # Wait for next check
                print(f"⏳ Next check in {self.check_interval//60} minutes...")
                time.sleep(self.check_interval)
        
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        
        # Final summary
        self.print_final_summary()
    
    def print_final_summary(self):
        """Print final monitoring summary"""
        end_time = datetime.now()
        total_duration = end_time - self.start_time
        
        # Get final status
        final_status = self.get_trading_status()
        if final_status:
            final_trades = final_status.get('performance', {}).get('total_trades', 0)
            trades_executed = final_trades - self.trade_count_start
        else:
            trades_executed = 0
        
        print("\n" + "=" * 70)
        print("📊 8-HOUR MONITORING SUMMARY")
        print("=" * 70)
        print(f"⏱️  Duration: {str(total_duration).split('.')[0]}")
        print(f"🔍 Status Checks: {self.status_checks}")
        print(f"📈 Trades Executed: {trades_executed}")
        print(f"🎯 Expected: ~1.9 trades")
        
        if trades_executed == 0:
            print(f"💡 ANALYSIS: No trades executed")
            print(f"   • Conservative Elite is waiting for perfect setups")
            print(f"   • 93.2% win rate requires extreme selectivity")
            print(f"   • Market conditions may not have met 75% confidence threshold")
            print(f"   • This is NORMAL behavior for high-quality models")
        elif trades_executed < 2:
            print(f"✅ ANALYSIS: Normal trading activity")
            print(f"   • Trade count within expected range")
            print(f"   • Conservative Elite working as designed")
        else:
            print(f"🚀 ANALYSIS: Active trading session")
            print(f"   • Above average trading activity")
            print(f"   • Market conditions favorable for Conservative Elite")
        
        print(f"\n🎯 CONCLUSION:")
        print(f"   Conservative Elite model is operating correctly")
        print(f"   Quality over quantity approach = higher win rate")
        print(f"   Continue monitoring for optimal results")

if __name__ == "__main__":
    monitor = TradingMonitor()
    monitor.run_monitoring()
