#!/usr/bin/env python3
"""
FOCUSED MODEL RETRAINING WITH 4 KEY INDICATORS
==============================================

This script retrains the trading model using only the 4 most effective indicators:
1. VWAP (Volume Weighted Average Price)
2. Bollinger Bands
3. ETH/BTC Ratio  
4. Flow Strength

Features:
- Hyperparameter optimization using Optuna
- Out-of-sample validation
- Performance metrics calculation
- Model deployment ready output

Author: Bitcoin Freedom Trading System
Date: 2025-06-03
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add project paths
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

try:
    import optuna
    OPTUNA_AVAILABLE = True
    print("✅ Optuna available for hyperparameter optimization")
except ImportError:
    OPTUNA_AVAILABLE = False
    print("⚠️ Optuna not available - using grid search")

try:
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.preprocessing import StandardScaler
    import joblib
    SKLEARN_AVAILABLE = True
    print("✅ Scikit-learn available")
except ImportError:
    SKLEARN_AVAILABLE = False
    print("❌ Scikit-learn not available")

# Import our indicators
try:
    from indicators.technical_indicators import (
        calculate_vwap,
        calculate_bollinger_bands, 
        calculate_eth_btc_ratio,
        calculate_flow_strength
    )
    INDICATORS_AVAILABLE = True
    print("✅ Technical indicators available")
except ImportError:
    INDICATORS_AVAILABLE = False
    print("❌ Technical indicators not available")

class FocusedModelTrainer:
    """Focused model trainer using only 4 key indicators."""
    
    def __init__(self):
        self.indicators = ['vwap', 'bollinger_bands', 'eth_btc_ratio', 'flow_strength']
        self.best_params = {}
        self.best_score = 0.0
        self.model = None
        self.scaler = StandardScaler()
        
        # Hyperparameter search spaces
        self.param_spaces = {
            'vwap': {
                'period': [12, 24, 48, 72],  # Hours for VWAP calculation
            },
            'bollinger_bands': {
                'window': [14, 20, 26, 30],  # Period for moving average
                'std_dev': [1.5, 2.0, 2.5, 3.0],  # Standard deviations
            },
            'eth_btc_ratio': {
                'threshold': [0.03, 0.05, 0.07, 0.10],  # Change threshold
                'lookback': [6, 12, 24, 48],  # Hours to look back
            },
            'flow_strength': {
                'volume_window': [10, 14, 20, 28],  # Volume calculation window
                'price_window': [10, 14, 20, 28],   # Price calculation window
                'threshold': [40, 50, 60, 70],      # Strength threshold
            }
        }
        
        # Model hyperparameters
        self.model_params = {
            'random_forest': {
                'n_estimators': [50, 100, 200, 300],
                'max_depth': [5, 10, 15, 20, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
            },
            'gradient_boosting': {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7, 10],
            },
            'logistic_regression': {
                'C': [0.1, 1.0, 10.0, 100.0],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear', 'saga'],
            }
        }

    def load_market_data(self, hours_back=2000):
        """Load market data for training."""
        print(f"📊 Loading market data ({hours_back} hours)...")
        
        # Simulate loading real market data
        # In production, this would load from your data source
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours_back)
        
        # Generate sample data structure (replace with real data loading)
        timestamps = pd.date_range(start=start_time, end=end_time, freq='H')
        n_samples = len(timestamps)
        
        # Simulate realistic BTC price data
        np.random.seed(42)  # For reproducible results
        base_price = 100000
        price_changes = np.random.normal(0, 0.02, n_samples)  # 2% hourly volatility
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1000))  # Minimum price floor
        
        # Simulate volume data
        volumes = np.random.lognormal(mean=8, sigma=1, size=n_samples)
        
        # Create DataFrame
        data = pd.DataFrame({
            'timestamp': timestamps,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': volumes
        })
        
        print(f"✅ Loaded {len(data)} data points")
        print(f"   Date range: {data['timestamp'].min()} to {data['timestamp'].max()}")
        print(f"   Price range: ${data['close'].min():,.2f} to ${data['close'].max():,.2f}")
        
        return data

    def calculate_indicators(self, data, params):
        """Calculate the 4 focused indicators with given parameters."""
        print("🔧 Calculating focused indicators...")
        
        features = pd.DataFrame(index=data.index)
        
        try:
            # 1. VWAP
            if INDICATORS_AVAILABLE:
                vwap_values = calculate_vwap(
                    data['close'].values, 
                    data['volume'].values, 
                    period=params['vwap']['period']
                )
            else:
                # Fallback calculation
                vwap_values = (data['close'] * data['volume']).rolling(
                    window=params['vwap']['period']
                ).sum() / data['volume'].rolling(window=params['vwap']['period']).sum()
            
            features['vwap_signal'] = (data['close'] < vwap_values).astype(int)
            features['vwap_distance'] = (data['close'] - vwap_values) / vwap_values
            
            # 2. Bollinger Bands
            if INDICATORS_AVAILABLE:
                bb_upper, bb_middle, bb_lower = calculate_bollinger_bands(
                    data['close'].values,
                    window=params['bollinger_bands']['window'],
                    std_dev=params['bollinger_bands']['std_dev']
                )
            else:
                # Fallback calculation
                bb_middle = data['close'].rolling(window=params['bollinger_bands']['window']).mean()
                bb_std = data['close'].rolling(window=params['bollinger_bands']['window']).std()
                bb_upper = bb_middle + (bb_std * params['bollinger_bands']['std_dev'])
                bb_lower = bb_middle - (bb_std * params['bollinger_bands']['std_dev'])
            
            bb_position = (data['close'] - bb_lower) / (bb_upper - bb_lower)
            features['bb_buy_signal'] = (bb_position < 0.4).astype(int)
            features['bb_sell_signal'] = (bb_position > 0.6).astype(int)
            features['bb_position'] = bb_position
            
            # 3. ETH/BTC Ratio (simulated)
            if INDICATORS_AVAILABLE:
                eth_btc_ratio = calculate_eth_btc_ratio(
                    threshold=params['eth_btc_ratio']['threshold'],
                    lookback=params['eth_btc_ratio']['lookback']
                )
            else:
                # Simulate ETH/BTC ratio changes
                ratio_changes = np.random.normal(0, 0.01, len(data))
                eth_btc_signal = np.abs(ratio_changes) > params['eth_btc_ratio']['threshold']
                eth_btc_ratio = ratio_changes
            
            features['eth_btc_signal'] = (eth_btc_ratio > params['eth_btc_ratio']['threshold']).astype(int)
            features['eth_btc_strength'] = eth_btc_ratio
            
            # 4. Flow Strength
            if INDICATORS_AVAILABLE:
                flow_strength = calculate_flow_strength(
                    data['close'].values,
                    data['volume'].values,
                    volume_window=params['flow_strength']['volume_window'],
                    price_window=params['flow_strength']['price_window']
                )
            else:
                # Fallback calculation
                price_change = data['close'].pct_change(params['flow_strength']['price_window'])
                volume_avg = data['volume'].rolling(window=params['flow_strength']['volume_window']).mean()
                flow_strength = (price_change * volume_avg).rolling(window=14).mean()
                flow_strength = (flow_strength - flow_strength.mean()) / flow_strength.std()
                flow_strength = flow_strength * 50 + 50  # Normalize to 0-100
            
            features['flow_buy_signal'] = (flow_strength > params['flow_strength']['threshold']).astype(int)
            features['flow_sell_signal'] = (flow_strength < (100 - params['flow_strength']['threshold'])).astype(int)
            features['flow_strength'] = flow_strength
            
            # Remove NaN values
            features = features.fillna(0)
            
            print(f"✅ Calculated {len(features.columns)} features")
            print(f"   Features: {list(features.columns)}")
            
            return features
            
        except Exception as e:
            print(f"❌ Error calculating indicators: {e}")
            return pd.DataFrame()

    def create_labels(self, data, lookahead_hours=4, profit_threshold=0.02):
        """Create trading labels based on future price movements."""
        print(f"🎯 Creating labels (lookahead: {lookahead_hours}h, threshold: {profit_threshold:.1%})...")
        
        labels = []
        prices = data['close'].values
        
        for i in range(len(prices)):
            if i + lookahead_hours >= len(prices):
                labels.append(0)  # No trade
                continue
            
            current_price = prices[i]
            future_prices = prices[i+1:i+lookahead_hours+1]
            
            # Check for profitable BUY opportunity
            max_future_price = np.max(future_prices)
            if (max_future_price - current_price) / current_price >= profit_threshold:
                labels.append(1)  # BUY signal
                continue
            
            # Check for profitable SELL opportunity  
            min_future_price = np.min(future_prices)
            if (current_price - min_future_price) / current_price >= profit_threshold:
                labels.append(2)  # SELL signal
                continue
            
            labels.append(0)  # No trade
        
        labels = np.array(labels)
        print(f"✅ Created {len(labels)} labels")
        print(f"   BUY signals: {np.sum(labels == 1)} ({np.sum(labels == 1)/len(labels):.1%})")
        print(f"   SELL signals: {np.sum(labels == 2)} ({np.sum(labels == 2)/len(labels):.1%})")
        print(f"   No trade: {np.sum(labels == 0)} ({np.sum(labels == 0)/len(labels):.1%})")
        
        return labels

    def optimize_hyperparameters(self, data, max_trials=50):
        """Optimize hyperparameters using Optuna or grid search."""
        print(f"🔍 Starting hyperparameter optimization ({max_trials} trials)...")

        if OPTUNA_AVAILABLE:
            return self._optimize_with_optuna(data, max_trials)
        else:
            return self._optimize_with_grid_search(data)

    def _optimize_with_optuna(self, data, max_trials):
        """Optimize using Optuna."""
        print("🎯 Using Optuna for optimization...")

        def objective(trial):
            # Sample hyperparameters
            params = {}

            # VWAP parameters
            params['vwap'] = {
                'period': trial.suggest_categorical('vwap_period', self.param_spaces['vwap']['period'])
            }

            # Bollinger Bands parameters
            params['bollinger_bands'] = {
                'window': trial.suggest_categorical('bb_window', self.param_spaces['bollinger_bands']['window']),
                'std_dev': trial.suggest_categorical('bb_std_dev', self.param_spaces['bollinger_bands']['std_dev'])
            }

            # ETH/BTC Ratio parameters
            params['eth_btc_ratio'] = {
                'threshold': trial.suggest_categorical('eth_btc_threshold', self.param_spaces['eth_btc_ratio']['threshold']),
                'lookback': trial.suggest_categorical('eth_btc_lookback', self.param_spaces['eth_btc_ratio']['lookback'])
            }

            # Flow Strength parameters
            params['flow_strength'] = {
                'volume_window': trial.suggest_categorical('flow_vol_window', self.param_spaces['flow_strength']['volume_window']),
                'price_window': trial.suggest_categorical('flow_price_window', self.param_spaces['flow_strength']['price_window']),
                'threshold': trial.suggest_categorical('flow_threshold', self.param_spaces['flow_strength']['threshold'])
            }

            # Model parameters
            model_type = trial.suggest_categorical('model_type', ['random_forest', 'gradient_boosting', 'logistic_regression'])

            if model_type == 'random_forest':
                model_params = {
                    'n_estimators': trial.suggest_categorical('rf_n_estimators', self.model_params['random_forest']['n_estimators']),
                    'max_depth': trial.suggest_categorical('rf_max_depth', self.model_params['random_forest']['max_depth']),
                    'min_samples_split': trial.suggest_categorical('rf_min_samples_split', self.model_params['random_forest']['min_samples_split']),
                    'min_samples_leaf': trial.suggest_categorical('rf_min_samples_leaf', self.model_params['random_forest']['min_samples_leaf'])
                }
                model = RandomForestClassifier(**model_params, random_state=42)
            elif model_type == 'gradient_boosting':
                model_params = {
                    'n_estimators': trial.suggest_categorical('gb_n_estimators', self.model_params['gradient_boosting']['n_estimators']),
                    'learning_rate': trial.suggest_categorical('gb_learning_rate', self.model_params['gradient_boosting']['learning_rate']),
                    'max_depth': trial.suggest_categorical('gb_max_depth', self.model_params['gradient_boosting']['max_depth'])
                }
                model = GradientBoostingClassifier(**model_params, random_state=42)
            else:  # logistic_regression
                model_params = {
                    'C': trial.suggest_categorical('lr_C', self.model_params['logistic_regression']['C']),
                    'penalty': trial.suggest_categorical('lr_penalty', self.model_params['logistic_regression']['penalty']),
                    'solver': trial.suggest_categorical('lr_solver', self.model_params['logistic_regression']['solver'])
                }
                model = LogisticRegression(**model_params, random_state=42, max_iter=1000)

            # Calculate features and labels
            features = self.calculate_indicators(data, params)
            labels = self.create_labels(data)

            if len(features) == 0 or len(labels) == 0:
                return 0.0

            # Align features and labels
            min_len = min(len(features), len(labels))
            features = features.iloc[:min_len]
            labels = labels[:min_len]

            # Remove rows with NaN
            valid_mask = ~features.isnull().any(axis=1)
            features = features[valid_mask]
            labels = labels[valid_mask]

            if len(features) < 100:  # Need minimum samples
                return 0.0

            # Split data for validation
            X_train, X_val, y_train, y_val = train_test_split(
                features, labels, test_size=0.3, random_state=42, stratify=labels
            )

            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)

            # Train and evaluate
            try:
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_val_scaled)

                # Calculate composite score
                accuracy = accuracy_score(y_val, y_pred)
                f1 = f1_score(y_val, y_pred, average='weighted')

                # Bonus for generating trading signals
                signal_ratio = np.sum(y_pred != 0) / len(y_pred)
                signal_bonus = min(signal_ratio * 0.1, 0.05)  # Up to 5% bonus

                composite_score = (accuracy * 0.6 + f1 * 0.4) + signal_bonus

                return composite_score

            except Exception as e:
                print(f"⚠️ Trial failed: {e}")
                return 0.0

        # Run optimization
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=max_trials)

        print(f"✅ Optimization complete!")
        print(f"   Best score: {study.best_value:.3f}")
        print(f"   Best params: {study.best_params}")

        return study.best_params, study.best_value

    def _optimize_with_grid_search(self, data):
        """Optimize using grid search (fallback)."""
        print("🔍 Using grid search optimization...")

        best_score = 0.0
        best_params = {}

        # Simplified grid search with fewer combinations
        vwap_periods = [24, 48]
        bb_windows = [20, 26]
        bb_stds = [2.0, 2.5]
        eth_thresholds = [0.05, 0.07]
        flow_thresholds = [50, 60]

        total_combinations = len(vwap_periods) * len(bb_windows) * len(bb_stds) * len(eth_thresholds) * len(flow_thresholds)
        print(f"   Testing {total_combinations} parameter combinations...")

        combination = 0
        for vwap_period in vwap_periods:
            for bb_window in bb_windows:
                for bb_std in bb_stds:
                    for eth_threshold in eth_thresholds:
                        for flow_threshold in flow_thresholds:
                            combination += 1

                            params = {
                                'vwap': {'period': vwap_period},
                                'bollinger_bands': {'window': bb_window, 'std_dev': bb_std},
                                'eth_btc_ratio': {'threshold': eth_threshold, 'lookback': 24},
                                'flow_strength': {'volume_window': 14, 'price_window': 14, 'threshold': flow_threshold}
                            }

                            try:
                                # Calculate features and labels
                                features = self.calculate_indicators(data, params)
                                labels = self.create_labels(data)

                                if len(features) == 0 or len(labels) == 0:
                                    continue

                                # Align and clean data
                                min_len = min(len(features), len(labels))
                                features = features.iloc[:min_len]
                                labels = labels[:min_len]

                                valid_mask = ~features.isnull().any(axis=1)
                                features = features[valid_mask]
                                labels = labels[valid_mask]

                                if len(features) < 100:
                                    continue

                                # Quick evaluation with Random Forest
                                X_train, X_val, y_train, y_val = train_test_split(
                                    features, labels, test_size=0.3, random_state=42, stratify=labels
                                )

                                scaler = StandardScaler()
                                X_train_scaled = scaler.fit_transform(X_train)
                                X_val_scaled = scaler.transform(X_val)

                                model = RandomForestClassifier(n_estimators=100, random_state=42)
                                model.fit(X_train_scaled, y_train)
                                y_pred = model.predict(X_val_scaled)

                                accuracy = accuracy_score(y_val, y_pred)
                                f1 = f1_score(y_val, y_pred, average='weighted')
                                signal_ratio = np.sum(y_pred != 0) / len(y_pred)

                                score = (accuracy * 0.6 + f1 * 0.4) + min(signal_ratio * 0.1, 0.05)

                                if score > best_score:
                                    best_score = score
                                    best_params = params.copy()
                                    print(f"   🎯 New best score: {score:.3f} (combination {combination}/{total_combinations})")

                            except Exception as e:
                                continue

        print(f"✅ Grid search complete!")
        print(f"   Best score: {best_score:.3f}")

        return best_params, best_score

    def train_final_model(self, data, best_params):
        """Train the final model with best parameters."""
        print("🎯 Training final model with optimized parameters...")

        # Calculate features with best parameters
        features = self.calculate_indicators(data, best_params)
        labels = self.create_labels(data)

        if len(features) == 0 or len(labels) == 0:
            raise ValueError("Failed to generate features or labels")

        # Align data
        min_len = min(len(features), len(labels))
        features = features.iloc[:min_len]
        labels = labels[:min_len]

        # Clean data
        valid_mask = ~features.isnull().any(axis=1)
        features = features[valid_mask]
        labels = labels[valid_mask]

        print(f"   Training samples: {len(features)}")
        print(f"   Features: {list(features.columns)}")

        # Split for out-of-sample testing
        X_train, X_test, y_train, y_test = train_test_split(
            features, labels, test_size=0.2, random_state=42, stratify=labels
        )

        # Scale features
        self.scaler.fit(X_train)
        X_train_scaled = self.scaler.transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)

        # Train best model (Random Forest as default)
        self.model = RandomForestClassifier(
            n_estimators=200,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42
        )

        self.model.fit(X_train_scaled, y_train)

        # Evaluate on test set
        y_pred = self.model.predict(X_test_scaled)
        y_pred_proba = self.model.predict_proba(X_test_scaled)

        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
        recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
        f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)

        # Trading-specific metrics
        buy_signals = np.sum(y_pred == 1)
        sell_signals = np.sum(y_pred == 2)
        total_signals = buy_signals + sell_signals
        signal_rate = total_signals / len(y_pred)

        # Estimate trades per day
        hours_per_sample = 1  # Assuming hourly data
        trades_per_day = signal_rate * 24 / hours_per_sample

        # Calculate composite score
        composite_score = (accuracy * 0.4 + precision * 0.2 + recall * 0.2 + f1 * 0.2)

        # Store results
        self.best_params = best_params
        self.best_score = composite_score

        results = {
            'model_id': f"focused_4indicators_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'composite_score': composite_score,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'trades_per_day': trades_per_day,
            'signal_rate': signal_rate,
            'buy_signals': int(buy_signals),
            'sell_signals': int(sell_signals),
            'total_signals': int(total_signals),
            'test_samples': len(y_test),
            'feature_count': len(features.columns),
            'parameters': best_params,
            'training_date': datetime.now().isoformat()
        }

        print(f"✅ Model training complete!")
        print(f"   Composite Score: {composite_score:.1%}")
        print(f"   Accuracy: {accuracy:.1%}")
        print(f"   F1 Score: {f1:.1%}")
        print(f"   Estimated Trades/Day: {trades_per_day:.1f}")
        print(f"   Signal Rate: {signal_rate:.1%}")

        return results

    def save_model(self, results, model_dir="models"):
        """Save the trained model and metadata."""
        print("💾 Saving model and metadata...")

        os.makedirs(model_dir, exist_ok=True)

        model_id = results['model_id']

        # Save model
        model_path = os.path.join(model_dir, f"{model_id}_model.joblib")
        joblib.dump(self.model, model_path)

        # Save scaler
        scaler_path = os.path.join(model_dir, f"{model_id}_scaler.joblib")
        joblib.dump(self.scaler, scaler_path)

        # Save metadata
        metadata_path = os.path.join(model_dir, f"{model_id}_metadata.json")
        with open(metadata_path, 'w') as f:
            json.dump(results, f, indent=2)

        # Update webapp metadata for deployment
        webapp_metadata = {
            'model_id': model_id,
            'composite_score': results['composite_score'],
            'trades_per_day': results['trades_per_day'],
            'accuracy': results['accuracy'],
            'f1_score': results['f1_score'],
            'signal_rate': results['signal_rate'],
            'live_trading_ready': results['composite_score'] >= 0.85,
            'parameters': results['parameters'],
            'last_updated': datetime.now().isoformat(),
            'indicators_used': ['vwap', 'bollinger_bands', 'eth_btc_ratio', 'flow_strength']
        }

        webapp_metadata_path = os.path.join(model_dir, "webapp_focused_model_metadata.json")
        with open(webapp_metadata_path, 'w') as f:
            json.dump(webapp_metadata, f, indent=2)

        print(f"✅ Model saved successfully!")
        print(f"   Model: {model_path}")
        print(f"   Scaler: {scaler_path}")
        print(f"   Metadata: {metadata_path}")
        print(f"   Webapp metadata: {webapp_metadata_path}")

        return model_path, scaler_path, metadata_path

def main():
    """Main training pipeline."""
    print("🚀 FOCUSED MODEL RETRAINING SYSTEM")
    print("=" * 60)
    print("📊 Using 4 Key Indicators:")
    print("   1. VWAP (Volume Weighted Average Price)")
    print("   2. Bollinger Bands")
    print("   3. ETH/BTC Ratio")
    print("   4. Flow Strength")
    print("=" * 60)

    if not SKLEARN_AVAILABLE:
        print("❌ Scikit-learn not available. Please install: pip install scikit-learn")
        return

    try:
        # Initialize trainer
        trainer = FocusedModelTrainer()

        # Load data
        print("\n📊 STEP 1: Loading market data...")
        data = trainer.load_market_data(hours_back=2000)

        # Optimize hyperparameters
        print("\n🔍 STEP 2: Optimizing hyperparameters...")
        best_params, best_score = trainer.optimize_hyperparameters(data, max_trials=30)

        if best_score < 0.7:
            print(f"⚠️ Warning: Best score ({best_score:.1%}) is below 70%. Consider more data or different parameters.")

        # Train final model
        print("\n🎯 STEP 3: Training final model...")
        results = trainer.train_final_model(data, best_params)

        # Save model
        print("\n💾 STEP 4: Saving model...")
        model_path, scaler_path, metadata_path = trainer.save_model(results)

        # Final summary
        print("\n🎉 RETRAINING COMPLETE!")
        print("=" * 60)
        print(f"✅ Model ID: {results['model_id']}")
        print(f"✅ Composite Score: {results['composite_score']:.1%}")
        print(f"✅ Estimated Trades/Day: {results['trades_per_day']:.1f}")
        print(f"✅ Live Trading Ready: {'YES' if results['composite_score'] >= 0.85 else 'NO'}")
        print("=" * 60)

        if results['composite_score'] >= 0.85:
            print("🚀 Model meets deployment criteria!")
            print("   You can now use this model for live trading.")
        else:
            print("⚠️ Model below deployment threshold (85%).")
            print("   Consider retraining with more data or different parameters.")

        return results

    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
