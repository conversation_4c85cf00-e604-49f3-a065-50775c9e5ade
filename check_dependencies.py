#!/usr/bin/env python3
"""
🔍 BITCOIN FREEDOM DEPENDENCY CHECKER
====================================
Checks and installs all required dependencies for the trading system
"""

import os
import sys
import subprocess
import importlib.util

def check_python_version():
    """Check Python version compatibility"""
    print("🐍 PYTHON VERSION CHECK")
    print("=" * 40)
    
    version = sys.version_info
    print(f"Current Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print("✅ Python version compatible")
        return True

def check_package(package_name, import_name=None):
    """Check if a package is installed"""
    if import_name is None:
        import_name = package_name
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            module = importlib.import_module(import_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"✅ {package_name}: {version}")
            return True
        else:
            print(f"❌ {package_name}: NOT FOUND")
            return False
    except Exception as e:
        print(f"❌ {package_name}: ERROR - {e}")
        return False

def install_package(package_name):
    """Install a package using pip"""
    print(f"📦 Installing {package_name}...")
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package_name} installed successfully")
            return True
        else:
            print(f"❌ Failed to install {package_name}")
            print(f"Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ Timeout installing {package_name}")
        return False
    except Exception as e:
        print(f"❌ Error installing {package_name}: {e}")
        return False

def check_and_install_dependencies():
    """Check and install all required dependencies"""
    print("\n📦 DEPENDENCY CHECK & INSTALLATION")
    print("=" * 50)
    
    # Core dependencies for Bitcoin Freedom
    dependencies = {
        # Web Framework
        'Flask': 'flask',
        'Requests': 'requests',
        
        # Trading & Market Data
        'CCXT': 'ccxt',
        'Pandas': 'pandas', 
        'NumPy': 'numpy',
        
        # Optional but recommended
        'Waitress': 'waitress',  # Production WSGI server
    }
    
    missing_packages = []
    installed_packages = []
    
    # Check each dependency
    for display_name, package_name in dependencies.items():
        if check_package(display_name, package_name):
            installed_packages.append(package_name)
        else:
            missing_packages.append(package_name)
    
    print(f"\n📊 SUMMARY:")
    print(f"✅ Installed: {len(installed_packages)}")
    print(f"❌ Missing: {len(missing_packages)}")
    
    if missing_packages:
        print(f"\n🔧 INSTALLING MISSING PACKAGES...")
        print("-" * 30)
        
        success_count = 0
        for package in missing_packages:
            if install_package(package):
                success_count += 1
        
        print(f"\n📈 INSTALLATION RESULTS:")
        print(f"✅ Successfully installed: {success_count}/{len(missing_packages)}")
        
        if success_count == len(missing_packages):
            print("🎉 All dependencies installed successfully!")
            return True
        else:
            print("⚠️ Some packages failed to install")
            return False
    else:
        print("🎉 All dependencies already installed!")
        return True

def check_trading_system_files():
    """Check if key trading system files exist"""
    print("\n📁 TRADING SYSTEM FILES CHECK")
    print("=" * 40)
    
    key_files = [
        'bitcoin_freedom_clean.py',
        'enhanced_temp.py',
        'conservative_elite_backtester.py',
        'templates/bitcoin_freedom_dashboard.html',
        'templates/bitcoin_freedom_enhanced_dashboard.html',
        'BinanceAPI_2.txt'
    ]
    
    missing_files = []
    found_files = []
    
    for file_path in key_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
            found_files.append(file_path)
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    print(f"\n📊 FILE SUMMARY:")
    print(f"✅ Found: {len(found_files)}")
    print(f"❌ Missing: {len(missing_files)}")
    
    if missing_files:
        print(f"\n⚠️ Missing files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
    
    return len(missing_files) == 0

def test_imports():
    """Test critical imports for the trading system"""
    print("\n🧪 IMPORT TESTING")
    print("=" * 30)
    
    test_imports = [
        ('Flask web framework', 'from flask import Flask, render_template, jsonify'),
        ('HTTP requests', 'import requests'),
        ('Market data (if available)', 'import ccxt'),
        ('Data processing (if available)', 'import pandas as pd'),
        ('Numerical computing (if available)', 'import numpy as np'),
    ]
    
    success_count = 0
    for description, import_statement in test_imports:
        try:
            exec(import_statement)
            print(f"✅ {description}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {description}: {e}")
        except Exception as e:
            print(f"⚠️ {description}: {e}")
    
    print(f"\n📊 IMPORT RESULTS: {success_count}/{len(test_imports)} successful")
    return success_count >= 2  # At least Flask and requests should work

def check_api_keys():
    """Check if API keys file exists"""
    print("\n🔑 API KEYS CHECK")
    print("=" * 25)
    
    api_key_paths = [
        'BinanceAPI_2.txt',
        r'C:\Users\<USER>\Documents\Trading system\Trading project VPS 5\BinanceAPI_2.txt'
    ]
    
    for path in api_key_paths:
        if os.path.exists(path):
            print(f"✅ API keys found: {path}")
            return True
    
    print("❌ API keys file not found")
    print("   Expected: BinanceAPI_2.txt")
    print("   Note: Required for live trading only")
    return False

def generate_system_report():
    """Generate comprehensive system readiness report"""
    print("\n" + "=" * 60)
    print("🎯 BITCOIN FREEDOM SYSTEM READINESS REPORT")
    print("=" * 60)
    
    checks = {
        'Python Version': check_python_version(),
        'Dependencies': check_and_install_dependencies(),
        'System Files': check_trading_system_files(),
        'Import Tests': test_imports(),
        'API Keys': check_api_keys()
    }
    
    print(f"\n📊 OVERALL SYSTEM STATUS:")
    print("-" * 30)
    
    passed = 0
    total = len(checks)
    
    for check_name, result in checks.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{check_name:20} {status}")
        if result:
            passed += 1
    
    print("-" * 30)
    print(f"TOTAL: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 SYSTEM READY FOR TRADING!")
        print("   All dependencies installed and configured")
        print("   Ready to launch Bitcoin Freedom")
    elif passed >= 3:
        print("\n⚠️ SYSTEM PARTIALLY READY")
        print("   Core functionality available")
        print("   Some optional features may not work")
    else:
        print("\n❌ SYSTEM NOT READY")
        print("   Critical dependencies missing")
        print("   Please resolve issues before trading")
    
    return passed >= 3

def main():
    """Main dependency checker"""
    print("🔍 BITCOIN FREEDOM DEPENDENCY CHECKER")
    print("=" * 50)
    print("Checking system readiness for Conservative Elite trading...")
    
    try:
        system_ready = generate_system_report()
        
        if system_ready:
            print("\n🚀 NEXT STEPS:")
            print("   1. Launch Clean System: py bitcoin_freedom_clean.py")
            print("   2. Launch Enhanced System: py enhanced_temp.py")
            print("   3. Run Backtester: py conservative_elite_backtester.py")
        else:
            print("\n🔧 REQUIRED ACTIONS:")
            print("   1. Install missing dependencies")
            print("   2. Ensure system files are present")
            print("   3. Re-run this checker")
        
        return system_ready
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Dependency check cancelled by user")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    main()
