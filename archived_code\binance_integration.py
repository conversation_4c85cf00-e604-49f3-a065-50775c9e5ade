#!/usr/bin/env python3
"""
Binance Integration Module for TCN-CNN-PPO Live Trading
======================================================

This module provides integration with Binance API for live trading.
Ready to connect when you're prepared for real money trading.

IMPORTANT: Test thoroughly in simulation mode before enabling live trading!

Author: Trading System VPS 4
Date: 2025-01-27
"""

import os
import json
import time
import hmac
import hashlib
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

@dataclass
class BinanceConfig:
    """Binance API configuration."""
    
    # API credentials (set these when ready for live trading)
    api_key: str = ""  # Your Binance API key
    api_secret: str = ""  # Your Binance API secret
    
    # Trading configuration
    base_url: str = "https://api.binance.com"  # Use testnet for testing: https://testnet.binance.vision
    symbol: str = "BTCUSDT"
    
    # Risk management
    max_position_size: float = 0.001  # Maximum BTC position size
    min_order_value: float = 10.0  # Minimum order value in USDT
    
    # Order settings
    order_type: str = "MARKET"  # MARKET or LIMIT
    time_in_force: str = "GTC"  # Good Till Cancelled

class BinanceClient:
    """Binance API client for live trading integration."""
    
    def __init__(self, config: BinanceConfig):
        self.config = config
        self.session = requests.Session()
        
        # Set API key in headers
        if config.api_key:
            self.session.headers.update({
                'X-MBX-APIKEY': config.api_key
            })
        
        print(f"🔗 Binance Client Initialized")
        print(f"   Base URL: {config.base_url}")
        print(f"   Symbol: {config.symbol}")
        print(f"   API Key: {'Set' if config.api_key else 'Not Set'}")
    
    def _generate_signature(self, params: Dict[str, Any]) -> str:
        """Generate HMAC SHA256 signature for authenticated requests."""
        query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        return hmac.new(
            self.config.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, method: str, endpoint: str, params: Dict[str, Any] = None, signed: bool = False) -> Dict[str, Any]:
        """Make authenticated request to Binance API."""
        if params is None:
            params = {}
        
        url = f"{self.config.base_url}{endpoint}"
        
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            params['signature'] = self._generate_signature(params)
        
        try:
            if method == 'GET':
                response = self.session.get(url, params=params)
            elif method == 'POST':
                response = self.session.post(url, params=params)
            elif method == 'DELETE':
                response = self.session.delete(url, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Binance API Error: {e}")
            if hasattr(e.response, 'text'):
                print(f"   Response: {e.response.text}")
            raise
    
    def get_account_info(self) -> Dict[str, Any]:
        """Get account information including balances."""
        return self._make_request('GET', '/api/v3/account', signed=True)
    
    def get_symbol_price(self, symbol: str = None) -> float:
        """Get current price for symbol."""
        symbol = symbol or self.config.symbol
        
        response = self._make_request('GET', '/api/v3/ticker/price', {
            'symbol': symbol
        })
        
        return float(response['price'])
    
    def get_order_book(self, symbol: str = None, limit: int = 10) -> Dict[str, Any]:
        """Get order book for symbol."""
        symbol = symbol or self.config.symbol
        
        return self._make_request('GET', '/api/v3/depth', {
            'symbol': symbol,
            'limit': limit
        })
    
    def place_market_order(self, side: str, quantity: float, symbol: str = None) -> Dict[str, Any]:
        """Place a market order."""
        symbol = symbol or self.config.symbol
        
        # Validate order
        if quantity < 0.00001:  # Minimum BTC quantity
            raise ValueError(f"Quantity too small: {quantity}")
        
        params = {
            'symbol': symbol,
            'side': side.upper(),  # BUY or SELL
            'type': 'MARKET',
            'quantity': f"{quantity:.6f}",
        }
        
        print(f"📤 Placing {side} order: {quantity:.6f} {symbol}")
        
        return self._make_request('POST', '/api/v3/order', params, signed=True)
    
    def place_limit_order(self, side: str, quantity: float, price: float, symbol: str = None) -> Dict[str, Any]:
        """Place a limit order."""
        symbol = symbol or self.config.symbol
        
        params = {
            'symbol': symbol,
            'side': side.upper(),
            'type': 'LIMIT',
            'timeInForce': self.config.time_in_force,
            'quantity': f"{quantity:.6f}",
            'price': f"{price:.2f}",
        }
        
        print(f"📤 Placing {side} limit order: {quantity:.6f} {symbol} @ ${price:.2f}")
        
        return self._make_request('POST', '/api/v3/order', params, signed=True)
    
    def cancel_order(self, order_id: str, symbol: str = None) -> Dict[str, Any]:
        """Cancel an existing order."""
        symbol = symbol or self.config.symbol
        
        params = {
            'symbol': symbol,
            'orderId': order_id,
        }
        
        print(f"❌ Cancelling order: {order_id}")
        
        return self._make_request('DELETE', '/api/v3/order', params, signed=True)
    
    def get_order_status(self, order_id: str, symbol: str = None) -> Dict[str, Any]:
        """Get status of an order."""
        symbol = symbol or self.config.symbol
        
        params = {
            'symbol': symbol,
            'orderId': order_id,
        }
        
        return self._make_request('GET', '/api/v3/order', params, signed=True)
    
    def get_open_orders(self, symbol: str = None) -> List[Dict[str, Any]]:
        """Get all open orders."""
        symbol = symbol or self.config.symbol
        
        params = {
            'symbol': symbol,
        }
        
        return self._make_request('GET', '/api/v3/openOrders', params, signed=True)
    
    def get_balance(self, asset: str = 'USDT') -> float:
        """Get balance for specific asset."""
        account_info = self.get_account_info()
        
        for balance in account_info['balances']:
            if balance['asset'] == asset:
                return float(balance['free'])
        
        return 0.0
    
    def calculate_quantity(self, usdt_amount: float, price: float = None) -> float:
        """Calculate BTC quantity for given USDT amount."""
        if price is None:
            price = self.get_symbol_price()
        
        quantity = usdt_amount / price
        
        # Apply maximum position size limit
        quantity = min(quantity, self.config.max_position_size)
        
        return quantity

class LiveTradingIntegration:
    """Integration layer between trading engine and Binance."""
    
    def __init__(self, config: BinanceConfig):
        self.config = config
        self.client = BinanceClient(config)
        self.is_connected = False
        
        # Test connection if API keys are provided
        if config.api_key and config.api_secret:
            self.test_connection()
    
    def test_connection(self) -> bool:
        """Test connection to Binance API."""
        try:
            account_info = self.client.get_account_info()
            self.is_connected = True
            
            print("✅ Binance Connection Successful")
            print(f"   Account Type: {account_info.get('accountType', 'Unknown')}")
            print(f"   Can Trade: {account_info.get('canTrade', False)}")
            
            # Show balances
            usdt_balance = self.client.get_balance('USDT')
            btc_balance = self.client.get_balance('BTC')
            
            print(f"   USDT Balance: ${usdt_balance:.2f}")
            print(f"   BTC Balance: {btc_balance:.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ Binance Connection Failed: {e}")
            self.is_connected = False
            return False
    
    def place_trade(self, direction: str, risk_amount: float) -> Optional[Dict[str, Any]]:
        """Place a trade on Binance."""
        if not self.is_connected:
            print("❌ Not connected to Binance")
            return None
        
        try:
            # Get current price
            current_price = self.client.get_symbol_price()
            
            # Calculate quantity based on risk amount
            quantity = self.client.calculate_quantity(risk_amount, current_price)
            
            # Validate minimum order value
            order_value = quantity * current_price
            if order_value < self.config.min_order_value:
                print(f"❌ Order value too small: ${order_value:.2f} (min: ${self.config.min_order_value})")
                return None
            
            # Place market order
            order = self.client.place_market_order(direction, quantity)
            
            print(f"✅ Order placed successfully:")
            print(f"   Order ID: {order['orderId']}")
            print(f"   Symbol: {order['symbol']}")
            print(f"   Side: {order['side']}")
            print(f"   Quantity: {order['origQty']}")
            print(f"   Status: {order['status']}")
            
            return order
            
        except Exception as e:
            print(f"❌ Failed to place trade: {e}")
            return None
    
    def close_trade(self, order_id: str, original_side: str) -> Optional[Dict[str, Any]]:
        """Close a trade by placing opposite order."""
        if not self.is_connected:
            print("❌ Not connected to Binance")
            return None
        
        try:
            # Get original order info
            original_order = self.client.get_order_status(order_id)
            
            if original_order['status'] != 'FILLED':
                print(f"❌ Original order not filled: {original_order['status']}")
                return None
            
            # Calculate opposite side and quantity
            opposite_side = 'SELL' if original_side == 'BUY' else 'BUY'
            quantity = float(original_order['executedQty'])
            
            # Place closing order
            closing_order = self.client.place_market_order(opposite_side, quantity)
            
            print(f"✅ Closing order placed:")
            print(f"   Closing Order ID: {closing_order['orderId']}")
            print(f"   Original Order ID: {order_id}")
            print(f"   Side: {closing_order['side']}")
            print(f"   Quantity: {closing_order['origQty']}")
            
            return closing_order
            
        except Exception as e:
            print(f"❌ Failed to close trade: {e}")
            return None

def create_binance_integration(api_key: str = "", api_secret: str = "", testnet: bool = True) -> LiveTradingIntegration:
    """Create Binance integration with configuration."""
    
    config = BinanceConfig(
        api_key=api_key,
        api_secret=api_secret,
        base_url="https://testnet.binance.vision" if testnet else "https://api.binance.com"
    )
    
    return LiveTradingIntegration(config)

# Example usage and setup instructions
if __name__ == '__main__':
    print("🔗 BINANCE INTEGRATION SETUP")
    print("=" * 50)
    print("To enable live trading with Binance:")
    print("1. Create Binance API keys at https://www.binance.com/en/my/settings/api-management")
    print("2. Enable 'Spot & Margin Trading' permission")
    print("3. Set API key and secret in this module")
    print("4. Test on Binance Testnet first: https://testnet.binance.vision/")
    print("5. Start with small amounts")
    print("=" * 50)
    
    # Test with empty credentials (will show connection error)
    integration = create_binance_integration()
    
    if not integration.is_connected:
        print("⚠️  Set your API credentials to enable live trading")
        print("   Edit binance_integration.py and add your API key/secret")
    
    print("\n🚀 Ready to integrate with live trading web app!")
