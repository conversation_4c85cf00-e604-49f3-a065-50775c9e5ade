"""
Simple Trading Executor
A lightweight trading system that actually executes trades for the webapp
"""

import time
import random
import csv
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import requests

class SimpleTrade:
    """Simple trade record"""
    def __init__(self, trade_id: str, action: str, entry_price: float, 
                 risk_amount: float, profit_target: float):
        self.trade_id = trade_id
        self.action = action  # BUY/SELL
        self.entry_price = entry_price
        self.risk_amount = risk_amount
        self.profit_target = profit_target
        self.entry_time = datetime.now()
        self.exit_price = None
        self.exit_time = None
        self.pnl = 0.0
        self.status = "OPEN"
        self.exit_reason = None
        
        # Calculate exit levels with TRUE 2.5:1 risk-to-reward ratio
        # Risk: 0.1% price movement, Reward: 0.25% price movement (2.5:1)
        if action == "BUY":
            self.take_profit_price = entry_price * 1.0025  # +0.25% (reward)
            self.stop_loss_price = entry_price * 0.999     # -0.1% (risk - 2.5x smaller)
        else:  # SELL
            self.take_profit_price = entry_price * 0.9975  # -0.25% (reward)
            self.stop_loss_price = entry_price * 1.001     # +0.1% (risk - 2.5x smaller)

class TradingSignal:
    """Trading signal with lifecycle management"""
    def __init__(self, signal_id: str, action: str, price: float, confidence: float,
                 grid_level: int, created_time: datetime):
        self.signal_id = signal_id
        self.action = action
        self.price = price
        self.confidence = confidence
        self.grid_level = grid_level
        self.created_time = created_time
        self.status = "PENDING"  # PENDING, EXECUTED, CANCELLED, EXPIRED
        self.expiry_time = created_time + timedelta(minutes=5)  # 5-minute expiry
        self.cancel_reason = None

class SimpleTradingExecutor:
    """Simple trading executor that actually executes trades"""

    def __init__(self, initial_balance: float = 300.0):
        self.balance = initial_balance
        self.initial_balance = initial_balance
        self.open_trades: Dict[str, SimpleTrade] = {}
        self.closed_trades: List[SimpleTrade] = []
        self.trade_counter = 0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0

        # Signal management
        self.pending_signals: Dict[str, TradingSignal] = {}
        self.signal_counter = 0
        self.signal_history: List[TradingSignal] = []
        
    def get_current_price(self) -> float:
        """Get current Bitcoin price with realistic simulation"""
        try:
            # Get real price as base
            response = requests.get('https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT', timeout=5)
            data = response.json()
            base_price = float(data['lastPrice'])
        except:
            base_price = 67000.0

        # Add realistic price movement simulation
        if not hasattr(self, 'simulated_price'):
            self.simulated_price = base_price
            self.last_update = datetime.now()

        # Simulate price movement every few seconds
        now = datetime.now()
        if (now - self.last_update).total_seconds() > 2:  # Update every 2 seconds
            # Increased movement range to ensure exits trigger (was ±0.1%, now ±0.5%)
            change_percent = random.uniform(-0.005, 0.005)  # ±0.5%
            self.simulated_price *= (1 + change_percent)
            self.last_update = now
            print(f"💹 PRICE UPDATE: ${self.simulated_price:.2f} ({change_percent:+.3%})")

        return self.simulated_price

    def log_trade_to_csv(self, trade):
        """Log completed trade to CSV file with enhanced information"""
        csv_file = 'trade_logs.csv'
        file_exists = os.path.exists(csv_file)

        with open(csv_file, 'a', newline='') as file:
            writer = csv.writer(file)

            # Write header if file is new
            if not file_exists:
                writer.writerow([
                    'Trade ID', 'Side', 'Entry Price', 'Exit Price',
                    'Entry Time', 'Exit Time', 'Duration (s)', 'P&L',
                    'Risk Amount', 'Profit Target', 'Take Profit Price', 'Stop Loss Price',
                    'Exit Reason', 'Status', 'Balance After'
                ])

            # Calculate duration
            duration = (trade.exit_time - trade.entry_time).total_seconds() if trade.exit_time else 0

            # Write trade data with enhanced information
            writer.writerow([
                trade.trade_id,
                trade.action,
                f"{trade.entry_price:.2f}",
                f"{trade.exit_price:.2f}" if trade.exit_price else "OPEN",
                trade.entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                trade.exit_time.strftime('%Y-%m-%d %H:%M:%S') if trade.exit_time else "OPEN",
                f"{duration:.0f}",
                f"{trade.pnl:.2f}",
                f"{trade.risk_amount:.2f}",
                f"{trade.profit_target:.2f}",
                f"{trade.take_profit_price:.2f}",
                f"{trade.stop_loss_price:.2f}",
                trade.exit_reason or "N/A",
                trade.status,
                f"{self.balance:.2f}"
            ])

        print(f"📝 TRADE LOGGED TO CSV: {trade.trade_id} | P&L: ${trade.pnl:.2f} | Balance: ${self.balance:.2f}")

    def check_grid_levels(self, current_price: float, testing_mode: bool = True) -> List[Dict]:
        """Check if price is near grid levels and generate signals"""
        signals = []

        # Initialize cycle counter
        if not hasattr(self, 'cycle_count'):
            self.cycle_count = 0
        self.cycle_count += 1

        print(f"🔍 GRID CHECK CYCLE {self.cycle_count}: Price ${current_price:.2f} | Mode: {'TESTING' if testing_mode else 'ML_PRODUCTION'}")

        # CONDITIONAL forced signals - only in testing mode (ONE TRADE AT A TIME - PROPER GRID TRADING)
        print(f"🔍 FORCED SIGNAL CHECK: testing_mode={testing_mode}, cycle_count={self.cycle_count}, open_trades={len(self.open_trades)}")

        if testing_mode and self.cycle_count >= 1 and len(self.open_trades) == 0:  # FIXED: Only when NO open trades
            # Generate a forced signal for testing
            action = "BUY" if self.cycle_count % 2 == 0 else "SELL"

            signals.append({
                'action': action,
                'price': current_price,
                'confidence': 0.85,
                'grid_level': 0,
                'reason': f'TESTING_FORCED_CYCLE_{self.cycle_count}',
                'is_forced': True,  # Mark as forced for tracking
                'source': 'TESTING_SYSTEM'
            })

            print(f"🧪 TESTING FORCED SIGNAL: {action} @ ${current_price:.2f} (Cycle {self.cycle_count})")
            print(f"⚠️  WARNING: This is a testing signal, not ML-driven")
        else:
            print(f"❌ FORCED SIGNAL BLOCKED: testing_mode={testing_mode}, cycle_count={self.cycle_count}, open_trades={len(self.open_trades)}")

        # Real grid level detection with ML decision placeholder
        grid_signals = self._check_real_grid_levels(current_price, testing_mode)
        signals.extend(grid_signals)

        print(f"📡 TOTAL SIGNALS GENERATED: {len(signals)} ({'Testing' if any(s.get('is_forced') for s in signals) else 'Grid'} signals)")
        return signals

    def _check_real_grid_levels(self, current_price: float, testing_mode: bool) -> List[Dict]:
        """Check real grid levels with ML decision logic"""
        signals = []
        grid_spacing = 0.0025  # 0.25%

        # Use fixed base price for consistent grid levels
        if not hasattr(self, 'grid_base_price'):
            self.grid_base_price = current_price
            print(f"📍 GRID BASE PRICE SET: ${self.grid_base_price:.2f}")

        # Check wider range of grid levels
        for i in range(-5, 6):  # Check 11 levels around base price
            level_price = self.grid_base_price * (1 + (i * grid_spacing))
            price_diff = abs(current_price - level_price) / level_price

            # Much more sensitive detection - within 0.1% of grid level
            if price_diff < 0.001:  # Within 0.1% of grid level
                # Get ML decision for this grid level
                ml_decision = self._get_ml_decision(i, level_price, current_price, testing_mode)

                if ml_decision['action'] != 'HOLD':
                    signals.append({
                        'action': ml_decision['action'],
                        'price': level_price,
                        'confidence': ml_decision['confidence'],
                        'grid_level': i,
                        'reason': f'GRID_LEVEL_{i}',
                        'is_forced': False,
                        'source': 'ML_GRID_SYSTEM',
                        'ml_features': ml_decision.get('features', {})
                    })

                    print(f"📊 GRID SIGNAL: {ml_decision['action']} @ ${level_price:.2f} (Level {i}, Conf: {ml_decision['confidence']:.1%})")
                else:
                    print(f"⏸️  GRID HOLD: Level {i} @ ${level_price:.2f} (ML decided HOLD)")

        return signals

    def _get_ml_decision(self, grid_level: int, level_price: float, current_price: float, testing_mode: bool) -> Dict:
        """Get ML model decision for grid level (placeholder for real ML integration)"""
        try:
            # TODO: Replace with real ML model integration
            # This is where TCN/CNN/PPO ensemble would make decisions

            if testing_mode:
                # In testing mode, use simplified logic for demonstration
                confidence = random.uniform(0.7, 0.9)

                if confidence > 0.75:  # Lower threshold for more signals
                    if grid_level < 0:  # Below base price - BUY signal
                        action = "BUY"
                    elif grid_level > 0:  # Above base price - SELL signal
                        action = "SELL"
                    else:
                        action = "HOLD"  # At base price - no action
                else:
                    action = "HOLD"  # Low confidence - no action

                return {
                    'action': action,
                    'confidence': confidence,
                    'features': {'testing_mode': True, 'grid_level': grid_level}
                }

            else:
                # Production mode - would use real ML models
                # features = self._calculate_ml_features(current_price)
                # prediction = self.ml_model.predict_ensemble(features)
                # return self._convert_ml_prediction_to_action(prediction)

                # For now, return HOLD to prevent trading without ML
                return {
                    'action': 'HOLD',
                    'confidence': 0.0,
                    'features': {'ml_model_not_loaded': True}
                }

        except Exception as e:
            print(f"❌ ML decision failed: {e}")
            return {'action': 'HOLD', 'confidence': 0.0, 'features': {'error': str(e)}}

    def manage_signals(self, current_price: float, testing_mode: bool = True) -> Dict:
        """Manage signal lifecycle: create, expire, cancel superseded signals"""
        current_time = datetime.now()

        # 1. Generate new signals
        new_signals = self.check_grid_levels(current_price, testing_mode)
        signals_created = 0
        signals_cancelled = 0

        # 2. Create TradingSignal objects for new signals
        for signal_data in new_signals:
            self.signal_counter += 1
            signal_id = f"SIG_{self.signal_counter:03d}"

            trading_signal = TradingSignal(
                signal_id=signal_id,
                action=signal_data['action'],
                price=signal_data['price'],
                confidence=signal_data['confidence'],
                grid_level=signal_data['grid_level'],
                created_time=current_time
            )

            # 3. Check for superseding logic
            superseded_signals = self._check_superseding(trading_signal)
            for superseded_id in superseded_signals:
                if superseded_id in self.pending_signals:
                    self.pending_signals[superseded_id].status = "CANCELLED"
                    self.pending_signals[superseded_id].cancel_reason = f"Superseded by {signal_id}"
                    self.signal_history.append(self.pending_signals[superseded_id])
                    del self.pending_signals[superseded_id]
                    signals_cancelled += 1
                    print(f"🚫 SIGNAL CANCELLED: {superseded_id} (superseded by {signal_id})")

            # 4. Add new signal to pending queue
            self.pending_signals[signal_id] = trading_signal
            signals_created += 1
            print(f"📡 SIGNAL CREATED: {signal_id} - {trading_signal.action} @ ${trading_signal.price:.2f}")

        # 5. Expire old signals
        expired_signals = []
        for signal_id, signal in list(self.pending_signals.items()):
            if current_time > signal.expiry_time:
                signal.status = "EXPIRED"
                signal.cancel_reason = "5-minute expiry reached"
                self.signal_history.append(signal)
                expired_signals.append(signal_id)
                del self.pending_signals[signal_id]
                print(f"⏰ SIGNAL EXPIRED: {signal_id} (5-minute timeout)")

        return {
            'signals_created': signals_created,
            'signals_cancelled': signals_cancelled,
            'signals_expired': len(expired_signals),
            'pending_signals': len(self.pending_signals)
        }

    def _check_superseding(self, new_signal: TradingSignal) -> List[str]:
        """Check which existing signals should be cancelled by the new signal"""
        superseded_ids = []

        for signal_id, existing_signal in self.pending_signals.items():
            # Same grid level superseding
            if existing_signal.grid_level == new_signal.grid_level:
                superseded_ids.append(signal_id)

            # Opposite action on nearby levels (within 1 level)
            elif (abs(existing_signal.grid_level - new_signal.grid_level) <= 1 and
                  existing_signal.action != new_signal.action):
                superseded_ids.append(signal_id)

        return superseded_ids

    def execute_pending_signals(self, risk_mode: str = 'fixed') -> List[SimpleTrade]:
        """Execute pending signals that are ready"""
        executed_trades = []
        executed_signal_ids = []

        # Sort signals by confidence (highest first)
        sorted_signals = sorted(
            self.pending_signals.items(),
            key=lambda x: x[1].confidence,
            reverse=True
        )

        for signal_id, signal in sorted_signals:
            if len(self.open_trades) >= 1:  # FIXED: Max 1 concurrent trade (proper grid trading)
                print(f"🚫 SIGNAL BLOCKED: {signal_id} - Already have {len(self.open_trades)} open trade(s)")
                break

            # Execute the signal
            trade = self.execute_trade_from_signal(signal, risk_mode)
            if trade:
                executed_trades.append(trade)
                signal.status = "EXECUTED"
                self.signal_history.append(signal)
                executed_signal_ids.append(signal_id)
                print(f"✅ SIGNAL EXECUTED: {signal_id} -> Trade {trade.trade_id}")

                # CRITICAL: Stop executing more signals after first trade
                print(f"🛑 STOPPING SIGNAL EXECUTION: One trade limit reached")
                break

        # Remove executed signals from pending queue
        for signal_id in executed_signal_ids:
            del self.pending_signals[signal_id]

        return executed_trades

    def execute_trade_from_signal(self, signal: TradingSignal, risk_mode: str = 'fixed') -> Optional[SimpleTrade]:
        """Execute a trade from a TradingSignal object"""
        # Convert TradingSignal to dict format for compatibility
        signal_dict = {
            'action': signal.action,
            'price': signal.price,
            'confidence': signal.confidence,
            'grid_level': signal.grid_level
        }

        return self.execute_trade(signal_dict, risk_mode, self.balance)

    def execute_trade(self, signal: Dict, risk_mode: str = 'fixed',
                     current_balance: float = None) -> Optional[SimpleTrade]:
        """Execute a trade based on signal"""
        if len(self.open_trades) >= 1:  # FIXED: Max 1 concurrent trade (proper grid trading)
            return None
        
        current_price = signal['price']
        action = signal['action']
        
        # Calculate risk amount
        if risk_mode == 'percentage' and current_balance:
            risk_amount = current_balance * 0.05  # 5% of balance
        else:
            risk_amount = 10.0  # Fixed $10
        
        # Calculate profit target (2.5:1 ratio)
        profit_target = risk_amount * 2.5
        
        # Create trade
        self.trade_counter += 1
        trade_id = f"TRADE_{self.trade_counter:03d}"
        
        trade = SimpleTrade(trade_id, action, current_price, risk_amount, profit_target)
        self.open_trades[trade_id] = trade
        
        print(f"🎯 TRADE EXECUTED: {action} @ ${current_price:.2f} | Risk: ${risk_amount:.2f} | Target: ${profit_target:.2f}")
        
        return trade
    
    def check_exits(self, current_price: float) -> List[SimpleTrade]:
        """Check for trade exits with enhanced debugging and proper exit logic"""
        closed_trades = []
        trades_to_remove = []

        if self.open_trades:
            print(f"🔍 CHECKING EXITS: Current Price ${current_price:.2f} | Open Trades: {len(self.open_trades)}")

        for trade_id, trade in self.open_trades.items():
            exit_triggered = False

            # Add minimum trade duration (prevent instant exits)
            trade_age = (datetime.now() - trade.entry_time).total_seconds()
            if trade_age < 5:  # Minimum 5 seconds before exit
                print(f"  {trade_id}: Too young ({trade_age:.0f}s < 5s)")
                continue

            print(f"  {trade_id}: {trade.action} @ ${trade.entry_price:.2f} (Age: {trade_age:.0f}s)")
            print(f"    TP: ${trade.take_profit_price:.2f} | SL: ${trade.stop_loss_price:.2f}")

            if trade.action == "BUY":
                print(f"    Current ${current_price:.2f} vs TP ${trade.take_profit_price:.2f} (need >=)")
                print(f"    Current ${current_price:.2f} vs SL ${trade.stop_loss_price:.2f} (need <=)")

                # Check take profit - use actual current price for realistic exit
                if current_price >= trade.take_profit_price:
                    trade.exit_price = current_price  # Use actual current price
                    # Calculate actual P&L based on price difference
                    price_diff = current_price - trade.entry_price
                    trade.pnl = (price_diff / trade.entry_price) * (trade.risk_amount * 2.5)  # Scale to risk amount
                    trade.exit_reason = "TAKE_PROFIT"
                    exit_triggered = True
                    print(f"    ✅ TAKE PROFIT TRIGGERED! Price moved from ${trade.entry_price:.2f} to ${current_price:.2f}")
                # Check stop loss
                elif current_price <= trade.stop_loss_price:
                    trade.exit_price = current_price  # Use actual current price
                    # Calculate actual loss based on price difference
                    price_diff = trade.entry_price - current_price
                    trade.pnl = -(price_diff / trade.entry_price) * trade.risk_amount  # Actual loss
                    trade.exit_reason = "STOP_LOSS"
                    exit_triggered = True
                    print(f"    🔴 STOP LOSS TRIGGERED! Price moved from ${trade.entry_price:.2f} to ${current_price:.2f}")
                else:
                    print(f"    ⏳ NO EXIT CONDITIONS MET")

            else:  # SELL
                print(f"    Current ${current_price:.2f} vs TP ${trade.take_profit_price:.2f} (need <=)")
                print(f"    Current ${current_price:.2f} vs SL ${trade.stop_loss_price:.2f} (need >=)")

                # Check take profit - use actual current price for realistic exit
                if current_price <= trade.take_profit_price:
                    trade.exit_price = current_price  # Use actual current price
                    # Calculate actual P&L based on price difference (SELL profits when price goes down)
                    price_diff = trade.entry_price - current_price
                    trade.pnl = (price_diff / trade.entry_price) * (trade.risk_amount * 2.5)  # Scale to risk amount
                    trade.exit_reason = "TAKE_PROFIT"
                    exit_triggered = True
                    print(f"    ✅ TAKE PROFIT TRIGGERED! Price moved from ${trade.entry_price:.2f} to ${current_price:.2f}")
                # Check stop loss
                elif current_price >= trade.stop_loss_price:
                    trade.exit_price = current_price  # Use actual current price
                    # Calculate actual loss based on price difference
                    price_diff = current_price - trade.entry_price
                    trade.pnl = -(price_diff / trade.entry_price) * trade.risk_amount  # Actual loss
                    trade.exit_reason = "STOP_LOSS"
                    exit_triggered = True
                    print(f"    🔴 STOP LOSS TRIGGERED! Price moved from ${trade.entry_price:.2f} to ${current_price:.2f}")
                else:
                    print(f"    ⏳ NO EXIT CONDITIONS MET")

            if exit_triggered:
                # Ensure each trade gets unique exit time
                trade.exit_time = datetime.now()
                trade.status = "CLOSED"

                # Update balance with actual P&L
                self.balance += trade.pnl

                # Update statistics
                self.total_trades += 1
                if trade.pnl > 0:
                    self.winning_trades += 1
                else:
                    self.losing_trades += 1

                # Log to CSV with complete trade information
                self.log_trade_to_csv(trade)

                closed_trades.append(trade)
                trades_to_remove.append(trade_id)

                # Calculate trade duration
                duration = (trade.exit_time - trade.entry_time).total_seconds()
                print(f"✅ TRADE CLOSED: {trade.action} @ ${trade.exit_price:.2f} | P&L: ${trade.pnl:.2f} | Duration: {duration:.0f}s | {trade.exit_reason}")

        # Remove closed trades from open trades
        for trade_id in trades_to_remove:
            self.closed_trades.append(self.open_trades[trade_id])
            del self.open_trades[trade_id]

        return closed_trades
    
    def process_trading_cycle(self, risk_mode: str = 'fixed', testing_mode: bool = True) -> Dict:
        """Process one complete trading cycle with proper signal management"""
        try:
            print(f"\n🔄 === TRADING CYCLE START ===")
            print(f"🎯 MODE: {'TESTING (Forced Signals Enabled)' if testing_mode else 'ML PRODUCTION (ML Models Only)'}")

            # Get current price
            current_price = self.get_current_price()
            print(f"💹 Current Price: ${current_price:.2f}")
            print(f"📊 Current State: Open={len(self.open_trades)}, Pending={len(self.pending_signals)}, Balance=${self.balance:.2f}")

            # 1. Check for exits first
            print(f"\n1️⃣ CHECKING EXITS...")
            closed_trades = self.check_exits(current_price)
            print(f"✅ Exit Check Complete: {len(closed_trades)} trades closed")

            # 2. Manage signals (create, expire, cancel superseded)
            print(f"\n2️⃣ MANAGING SIGNALS...")
            signal_management = self.manage_signals(current_price, testing_mode)
            print(f"📡 Signal Management Complete:")
            print(f"   Created: {signal_management['signals_created']}")
            print(f"   Cancelled: {signal_management['signals_cancelled']}")
            print(f"   Expired: {signal_management['signals_expired']}")
            print(f"   Pending: {signal_management['pending_signals']}")

            # 3. Execute pending signals
            print(f"\n3️⃣ EXECUTING SIGNALS...")
            executed_trades = self.execute_pending_signals(risk_mode)
            print(f"🎯 Signal Execution Complete: {len(executed_trades)} trades executed")

            # Performance tracking by signal type
            forced_trades = sum(1 for trade in executed_trades if hasattr(trade, 'signal_source') and 'TESTING' in trade.signal_source)
            ml_trades = len(executed_trades) - forced_trades

            # Final summary
            print(f"\n🔄 === TRADING CYCLE COMPLETE ===")
            print(f"📊 Final State: Open={len(self.open_trades)}, Pending={len(self.pending_signals)}, Balance=${self.balance:.2f}")
            print(f"🎯 Cycle Results: Executed={len(executed_trades)} (Forced: {forced_trades}, ML: {ml_trades}), Closed={len(closed_trades)}")
            print(f"💰 Total P&L: ${self.balance - self.initial_balance:.2f}")

            if testing_mode and forced_trades > 0:
                print(f"⚠️  WARNING: {forced_trades} forced testing signals executed - not ML-driven")

            return {
                'success': True,
                'current_price': current_price,
                'executed_trades': len(executed_trades),
                'closed_trades': len(closed_trades),
                'open_trades': len(self.open_trades),
                'current_balance': self.balance,
                'total_pnl': self.balance - self.initial_balance,
                'total_trades': self.total_trades,
                'win_rate': self.winning_trades / max(self.total_trades, 1),
                'new_trades': executed_trades,
                'closed_today': closed_trades,
                # Signal management info
                'signal_management': signal_management,
                'pending_signals': len(self.pending_signals),
                'signal_history_count': len(self.signal_history)
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'current_price': 67000.0,
                'executed_trades': 0,
                'closed_trades': 0,
                'open_trades': len(self.open_trades),
                'current_balance': self.balance,
                'pending_signals': len(self.pending_signals)
            }
    
    def get_recent_trades(self, limit: int = 10) -> List[Dict]:
        """Get recent trades with complete timing information"""
        recent_trades = self.closed_trades[-limit:] if self.closed_trades else []

        trade_data = []
        for trade in recent_trades:
            # Calculate trade duration
            if trade.exit_time and trade.entry_time:
                duration = trade.exit_time - trade.entry_time
                duration_str = f"{duration.total_seconds():.0f}s"
            else:
                duration_str = "N/A"

            trade_data.append({
                'Trade ID': trade.trade_id,
                'Side': trade.action,
                'Entry Price': f"${trade.entry_price:.2f}",
                'Exit Price': f"${trade.exit_price:.2f}" if trade.exit_price else "OPEN",
                'Entry Time': trade.entry_time.strftime('%H:%M:%S'),
                'Exit Time': trade.exit_time.strftime('%H:%M:%S') if trade.exit_time else "OPEN",
                'Duration': duration_str,
                'P&L': f"${trade.pnl:.2f}",
                'Status': trade.status,
                'Exit Reason': trade.exit_reason or "N/A"
            })

        return trade_data
    
    def get_pending_signals(self) -> List[Dict]:
        """Get pending signals for display"""
        signal_data = []
        for signal_id, signal in self.pending_signals.items():
            time_remaining = (signal.expiry_time - datetime.now()).total_seconds()
            signal_data.append({
                'Signal ID': signal_id,
                'Action': signal.action,
                'Price': f"${signal.price:,.2f}",
                'Confidence': f"{signal.confidence:.1%}",
                'Grid Level': signal.grid_level,
                'Status': signal.status,
                'Created': signal.created_time.strftime('%H:%M:%S'),
                'Expires In': f"{max(0, int(time_remaining))}s",
                'Source': 'Grid Trading Engine'
            })
        return signal_data

    def get_signal_history(self, limit: int = 10) -> List[Dict]:
        """Get recent signal history for display"""
        recent_signals = self.signal_history[-limit:] if self.signal_history else []
        signal_data = []

        for signal in recent_signals:
            signal_data.append({
                'Signal ID': signal.signal_id,
                'Action': signal.action,
                'Price': f"${signal.price:,.2f}",
                'Confidence': f"{signal.confidence:.1%}",
                'Grid Level': signal.grid_level,
                'Status': signal.status,
                'Created': signal.created_time.strftime('%H:%M:%S'),
                'Cancel Reason': signal.cancel_reason or 'N/A'
            })

        return signal_data

    def reset(self):
        """Reset the trading executor"""
        self.balance = self.initial_balance
        self.open_trades.clear()
        self.closed_trades.clear()
        self.trade_counter = 0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0

        # Reset signal management
        self.pending_signals.clear()
        self.signal_counter = 0
        self.signal_history.clear()

        # DON'T reset cycle_count - let it continue incrementing for forced signals
        # self.cycle_count = 0  # Keep this commented to preserve cycle progression

        print("🔄 Trading executor reset to initial state")

# Global instance for webapp
trading_executor = None

def get_trading_executor(initial_balance: float = 300.0) -> SimpleTradingExecutor:
    """Get or create trading executor instance"""
    global trading_executor
    if trading_executor is None:
        trading_executor = SimpleTradingExecutor(initial_balance)
    return trading_executor

def test_complete_process():
    """Test the complete signal generation to execution process"""
    print('🔍 TESTING COMPLETE SIGNAL TO EXECUTION PROCESS')
    print('=' * 60)

    executor = get_trading_executor()
    executor.reset()

    print('\n📊 INITIAL STATE:')
    print(f'Balance: ${executor.balance:.2f}')
    print(f'Open Trades: {len(executor.open_trades)}')
    print(f'Pending Signals: {len(executor.pending_signals)}')

    print('\n🔄 PROCESSING TRADING CYCLE...')
    cycle_result = executor.process_trading_cycle('fixed')

    print('\n📡 SIGNAL MANAGEMENT RESULTS:')
    sm = cycle_result.get('signal_management', {})
    print(f'Signals Created: {sm.get("signals_created", 0)}')
    print(f'Signals Cancelled: {sm.get("signals_cancelled", 0)}')
    print(f'Signals Expired: {sm.get("signals_expired", 0)}')
    print(f'Pending Signals: {sm.get("pending_signals", 0)}')

    print('\n🎯 TRADE EXECUTION RESULTS:')
    print(f'Trades Executed: {cycle_result["executed_trades"]}')
    print(f'Current Balance: ${cycle_result["current_balance"]:.2f}')
    print(f'Total P&L: ${cycle_result["total_pnl"]:.2f}')

    print('\n📋 PENDING SIGNALS DETAILS:')
    pending = executor.get_pending_signals()
    for signal in pending:
        print(f'  {signal["Signal ID"]}: {signal["Action"]} @ {signal["Price"]} (Grid {signal["Grid Level"]}, {signal["Confidence"]})')

    print('\n📈 OPEN TRADES DETAILS:')
    for trade_id, trade in executor.open_trades.items():
        print(f'  {trade_id}: {trade.action} @ ${trade.entry_price:.2f}')
        print(f'    Take Profit: ${trade.take_profit_price:.2f}')
        print(f'    Stop Loss: ${trade.stop_loss_price:.2f}')
        print(f'    Risk: ${trade.risk_amount:.2f} | Target: ${trade.profit_target:.2f}')

    print('\n✅ TRADE LOGIC VERIFICATION COMPLETE')
    return cycle_result

if __name__ == "__main__":
    test_complete_process()
