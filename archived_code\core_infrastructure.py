#!/usr/bin/env python3
"""
Core Infrastructure - Phase 1 Implementation
Auto-fix system, health monitoring, and error recovery
"""

import os
import sys
import logging
import time
import json
import sqlite3
import requests
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import traceback

# Configuration
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

@dataclass
class HealthCheck:
    """Health check result structure"""
    component: str
    status: str  # 'HEALTHY', 'WARNING', 'CRITICAL', 'FAILED'
    message: str
    timestamp: str
    auto_fix_attempted: bool = False
    auto_fix_successful: bool = False

class AutoFixSystem:
    """Automated system repair and recovery"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('AutoFixSystem')
        self.fix_attempts = {}
        self.max_fix_attempts = 3
        self.fix_cooldown = 300  # 5 minutes
        
    def attempt_fix(self, component: str, error_type: str, error_details: str) -> bool:
        """Attempt to automatically fix a system issue"""
        fix_key = f"{component}_{error_type}"
        
        # Check if we've already tried fixing this recently
        if self._is_in_cooldown(fix_key):
            self.logger.warning(f"Fix attempt for {fix_key} is in cooldown")
            return False
        
        # Check max attempts
        if self._exceeded_max_attempts(fix_key):
            self.logger.error(f"Max fix attempts exceeded for {fix_key}")
            return False
        
        self.logger.info(f"Attempting auto-fix for {component}: {error_type}")
        
        try:
            if component == "database":
                return self._fix_database_issues(error_type, error_details)
            elif component == "api_connection":
                return self._fix_api_connection(error_type, error_details)
            elif component == "file_system":
                return self._fix_file_system(error_type, error_details)
            elif component == "memory":
                return self._fix_memory_issues(error_type, error_details)
            else:
                self.logger.warning(f"No auto-fix available for component: {component}")
                return False
                
        except Exception as e:
            self.logger.error(f"Auto-fix failed for {component}: {e}")
            return False
        finally:
            self._record_fix_attempt(fix_key)
    
    def _fix_database_issues(self, error_type: str, error_details: str) -> bool:
        """Fix database-related issues"""
        if "database is locked" in error_details.lower():
            self.logger.info("Attempting to fix database lock")
            time.sleep(2)  # Wait for lock to release
            return True
            
        elif "no such table" in error_details.lower():
            self.logger.info("Recreating missing database tables")
            return self._recreate_database_tables()
            
        elif "disk full" in error_details.lower():
            self.logger.info("Attempting to free disk space")
            return self._cleanup_old_files()
            
        return False
    
    def _fix_api_connection(self, error_type: str, error_details: str) -> bool:
        """Fix API connection issues"""
        if "connection timeout" in error_details.lower():
            self.logger.info("Retrying API connection with backoff")
            time.sleep(5)
            return True
            
        elif "rate limit" in error_details.lower():
            self.logger.info("Waiting for rate limit reset")
            time.sleep(60)  # Wait 1 minute for rate limit
            return True
            
        elif "invalid api key" in error_details.lower():
            self.logger.error("Invalid API key - manual intervention required")
            return False
            
        return False
    
    def _fix_file_system(self, error_type: str, error_details: str) -> bool:
        """Fix file system issues"""
        if "permission denied" in error_details.lower():
            self.logger.info("Attempting to fix file permissions")
            return self._fix_file_permissions()
            
        elif "no space left" in error_details.lower():
            self.logger.info("Cleaning up old files to free space")
            return self._cleanup_old_files()
            
        return False
    
    def _fix_memory_issues(self, error_type: str, error_details: str) -> bool:
        """Fix memory-related issues"""
        if "out of memory" in error_details.lower():
            self.logger.info("Attempting memory cleanup")
            import gc
            gc.collect()
            return True
            
        return False
    
    def _recreate_database_tables(self) -> bool:
        """Recreate missing database tables"""
        try:
            from trade_manager_webapp import TradeDatabase
            db = TradeDatabase(os.path.join(self.config.DATA_DIR, 'trades.db'))
            db.init_database()
            self.logger.info("Database tables recreated successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to recreate database tables: {e}")
            return False
    
    def _cleanup_old_files(self) -> bool:
        """Clean up old log and temporary files"""
        try:
            cleaned_size = 0
            
            # Clean old log files (keep last 7 days)
            logs_dir = self.config.LOGS_DIR
            if os.path.exists(logs_dir):
                cutoff_date = datetime.now() - timedelta(days=7)
                for filename in os.listdir(logs_dir):
                    filepath = os.path.join(logs_dir, filename)
                    if os.path.isfile(filepath):
                        file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                        if file_time < cutoff_date:
                            file_size = os.path.getsize(filepath)
                            os.remove(filepath)
                            cleaned_size += file_size
            
            self.logger.info(f"Cleaned up {cleaned_size / 1024 / 1024:.2f} MB of old files")
            return cleaned_size > 0
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old files: {e}")
            return False
    
    def _fix_file_permissions(self) -> bool:
        """Fix file permission issues"""
        try:
            # Ensure data directories have correct permissions
            for directory in [self.config.DATA_DIR, self.config.LOGS_DIR, 
                            self.config.MODELS_DIR, self.config.REPORTS_DIR]:
                if os.path.exists(directory):
                    os.chmod(directory, 0o755)
            
            self.logger.info("File permissions fixed")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to fix file permissions: {e}")
            return False
    
    def _is_in_cooldown(self, fix_key: str) -> bool:
        """Check if fix attempt is in cooldown period"""
        if fix_key not in self.fix_attempts:
            return False
        
        last_attempt = self.fix_attempts[fix_key]['last_attempt']
        return (datetime.now() - last_attempt).seconds < self.fix_cooldown
    
    def _exceeded_max_attempts(self, fix_key: str) -> bool:
        """Check if max fix attempts exceeded"""
        if fix_key not in self.fix_attempts:
            return False
        
        return self.fix_attempts[fix_key]['count'] >= self.max_fix_attempts
    
    def _record_fix_attempt(self, fix_key: str):
        """Record fix attempt for tracking"""
        if fix_key not in self.fix_attempts:
            self.fix_attempts[fix_key] = {'count': 0, 'last_attempt': None}
        
        self.fix_attempts[fix_key]['count'] += 1
        self.fix_attempts[fix_key]['last_attempt'] = datetime.now()

class HealthMonitor:
    """System health monitoring and alerting"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('HealthMonitor')
        self.auto_fix = AutoFixSystem(config)
        self.health_history = []
        self.monitoring = False
        self.monitor_thread = None
        
    def start_monitoring(self):
        """Start continuous health monitoring"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("Health monitoring started")
    
    def stop_monitoring(self):
        """Stop health monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Health monitoring stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                health_results = self.run_health_checks()
                self._process_health_results(health_results)
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Health monitoring error: {e}")
                time.sleep(60)  # Wait longer on error
    
    def run_health_checks(self) -> List[HealthCheck]:
        """Run comprehensive health checks"""
        checks = []
        
        # Database health
        checks.append(self._check_database_health())
        
        # File system health
        checks.append(self._check_filesystem_health())
        
        # Memory health
        checks.append(self._check_memory_health())
        
        # API connectivity
        checks.append(self._check_api_connectivity())
        
        # Process health
        checks.append(self._check_process_health())
        
        return checks
    
    def _check_database_health(self) -> HealthCheck:
        """Check database connectivity and integrity"""
        try:
            db_path = os.path.join(self.config.DATA_DIR, 'trades.db')
            
            if not os.path.exists(db_path):
                return HealthCheck(
                    component="database",
                    status="CRITICAL",
                    message="Database file does not exist",
                    timestamp=datetime.now().isoformat()
                )
            
            # Test database connection
            conn = sqlite3.connect(db_path, timeout=5)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            conn.close()
            
            if table_count < 3:  # Should have trades, positions, performance tables
                return HealthCheck(
                    component="database",
                    status="WARNING",
                    message=f"Only {table_count} tables found, expected 3+",
                    timestamp=datetime.now().isoformat()
                )
            
            return HealthCheck(
                component="database",
                status="HEALTHY",
                message=f"Database operational with {table_count} tables",
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            return HealthCheck(
                component="database",
                status="FAILED",
                message=f"Database check failed: {str(e)}",
                timestamp=datetime.now().isoformat()
            )
    
    def _check_filesystem_health(self) -> HealthCheck:
        """Check file system space and permissions"""
        try:
            # Check disk space
            statvfs = os.statvfs(self.config.BASE_DIR)
            free_space = statvfs.f_frsize * statvfs.f_bavail
            free_space_gb = free_space / (1024**3)
            
            if free_space_gb < 0.1:  # Less than 100MB
                return HealthCheck(
                    component="file_system",
                    status="CRITICAL",
                    message=f"Low disk space: {free_space_gb:.2f} GB free",
                    timestamp=datetime.now().isoformat()
                )
            elif free_space_gb < 1.0:  # Less than 1GB
                return HealthCheck(
                    component="file_system",
                    status="WARNING",
                    message=f"Low disk space: {free_space_gb:.2f} GB free",
                    timestamp=datetime.now().isoformat()
                )
            
            # Check directory permissions
            for directory in [self.config.DATA_DIR, self.config.LOGS_DIR]:
                if not os.access(directory, os.W_OK):
                    return HealthCheck(
                        component="file_system",
                        status="CRITICAL",
                        message=f"No write permission for {directory}",
                        timestamp=datetime.now().isoformat()
                    )
            
            return HealthCheck(
                component="file_system",
                status="HEALTHY",
                message=f"File system healthy, {free_space_gb:.2f} GB free",
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            return HealthCheck(
                component="file_system",
                status="FAILED",
                message=f"File system check failed: {str(e)}",
                timestamp=datetime.now().isoformat()
            )
    
    def _check_memory_health(self) -> HealthCheck:
        """Check memory usage"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            if memory_percent > 90:
                return HealthCheck(
                    component="memory",
                    status="CRITICAL",
                    message=f"High memory usage: {memory_percent:.1f}%",
                    timestamp=datetime.now().isoformat()
                )
            elif memory_percent > 80:
                return HealthCheck(
                    component="memory",
                    status="WARNING",
                    message=f"Elevated memory usage: {memory_percent:.1f}%",
                    timestamp=datetime.now().isoformat()
                )
            
            return HealthCheck(
                component="memory",
                status="HEALTHY",
                message=f"Memory usage: {memory_percent:.1f}%",
                timestamp=datetime.now().isoformat()
            )
            
        except ImportError:
            return HealthCheck(
                component="memory",
                status="WARNING",
                message="psutil not available for memory monitoring",
                timestamp=datetime.now().isoformat()
            )
        except Exception as e:
            return HealthCheck(
                component="memory",
                status="FAILED",
                message=f"Memory check failed: {str(e)}",
                timestamp=datetime.now().isoformat()
            )
    
    def _check_api_connectivity(self) -> HealthCheck:
        """Check Binance API connectivity"""
        try:
            # Test basic connectivity to Binance
            response = requests.get("https://api.binance.com/api/v3/ping", timeout=10)
            
            if response.status_code == 200:
                return HealthCheck(
                    component="api_connection",
                    status="HEALTHY",
                    message="Binance API connectivity confirmed",
                    timestamp=datetime.now().isoformat()
                )
            else:
                return HealthCheck(
                    component="api_connection",
                    status="WARNING",
                    message=f"Binance API returned status {response.status_code}",
                    timestamp=datetime.now().isoformat()
                )
                
        except requests.exceptions.Timeout:
            return HealthCheck(
                component="api_connection",
                status="WARNING",
                message="Binance API connection timeout",
                timestamp=datetime.now().isoformat()
            )
        except Exception as e:
            return HealthCheck(
                component="api_connection",
                status="FAILED",
                message=f"API connectivity check failed: {str(e)}",
                timestamp=datetime.now().isoformat()
            )
    
    def _check_process_health(self) -> HealthCheck:
        """Check process health and resource usage"""
        try:
            import psutil
            process = psutil.Process()
            
            # Check CPU usage
            cpu_percent = process.cpu_percent()
            
            # Check memory usage
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # Check file descriptors
            num_fds = process.num_fds() if hasattr(process, 'num_fds') else 0
            
            status = "HEALTHY"
            message = f"CPU: {cpu_percent:.1f}%, Memory: {memory_mb:.1f}MB, FDs: {num_fds}"
            
            if cpu_percent > 80:
                status = "WARNING"
                message += " (High CPU)"
            elif memory_mb > 500:
                status = "WARNING"
                message += " (High Memory)"
            
            return HealthCheck(
                component="process",
                status=status,
                message=message,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            return HealthCheck(
                component="process",
                status="FAILED",
                message=f"Process health check failed: {str(e)}",
                timestamp=datetime.now().isoformat()
            )
    
    def _process_health_results(self, health_results: List[HealthCheck]):
        """Process health check results and trigger auto-fixes"""
        critical_issues = []
        warning_issues = []
        
        for check in health_results:
            self.health_history.append(check)
            
            if check.status == "CRITICAL" or check.status == "FAILED":
                critical_issues.append(check)
                
                # Attempt auto-fix for critical issues
                if self.auto_fix.attempt_fix(check.component, check.status, check.message):
                    check.auto_fix_attempted = True
                    check.auto_fix_successful = True
                    self.logger.info(f"Auto-fix successful for {check.component}")
                else:
                    check.auto_fix_attempted = True
                    check.auto_fix_successful = False
                    self.logger.error(f"Auto-fix failed for {check.component}")
                    
            elif check.status == "WARNING":
                warning_issues.append(check)
        
        # Log summary
        if critical_issues:
            self.logger.error(f"Critical issues detected: {len(critical_issues)}")
        elif warning_issues:
            self.logger.warning(f"Warning issues detected: {len(warning_issues)}")
        else:
            self.logger.debug("All health checks passed")
        
        # Keep only last 1000 health records
        if len(self.health_history) > 1000:
            self.health_history = self.health_history[-1000:]
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get current health summary"""
        if not self.health_history:
            return {"status": "UNKNOWN", "message": "No health data available"}
        
        latest_checks = {}
        for check in reversed(self.health_history):
            if check.component not in latest_checks:
                latest_checks[check.component] = check
        
        overall_status = "HEALTHY"
        critical_count = 0
        warning_count = 0
        
        for check in latest_checks.values():
            if check.status in ["CRITICAL", "FAILED"]:
                critical_count += 1
                overall_status = "CRITICAL"
            elif check.status == "WARNING":
                warning_count += 1
                if overall_status == "HEALTHY":
                    overall_status = "WARNING"
        
        return {
            "overall_status": overall_status,
            "critical_issues": critical_count,
            "warning_issues": warning_count,
            "components": {comp: asdict(check) for comp, check in latest_checks.items()},
            "monitoring_active": self.monitoring,
            "last_check": max(check.timestamp for check in latest_checks.values()) if latest_checks else None
        }

def main():
    """Test the health monitoring and auto-fix system"""
    config = TradingConfig()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create health monitor
    monitor = HealthMonitor(config)
    
    print("🔍 Running health checks...")
    health_results = monitor.run_health_checks()
    
    for check in health_results:
        status_emoji = {
            "HEALTHY": "✅",
            "WARNING": "⚠️",
            "CRITICAL": "🚨",
            "FAILED": "❌"
        }.get(check.status, "❓")
        
        print(f"{status_emoji} {check.component}: {check.status} - {check.message}")
    
    print("\n📊 Health Summary:")
    summary = monitor.get_health_summary()
    print(f"Overall Status: {summary['overall_status']}")
    print(f"Critical Issues: {summary['critical_issues']}")
    print(f"Warning Issues: {summary['warning_issues']}")
    
    print("\n🔧 Starting continuous monitoring...")
    monitor.start_monitoring()
    
    try:
        time.sleep(60)  # Monitor for 1 minute
    except KeyboardInterrupt:
        print("\n⏹️ Stopping monitoring...")
    finally:
        monitor.stop_monitoring()

if __name__ == "__main__":
    main()
