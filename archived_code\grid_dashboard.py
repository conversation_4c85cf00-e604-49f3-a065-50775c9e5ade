"""
Grid Trading Dashboard
Real-time monitoring and control interface for grid trading system
"""

import os
import sys
import logging
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import threading
import time

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import grid trading components
from grid_trading_core import GridLevelCalculator, GridTradeManager, GridAction
from grid_feature_engineering import GridFeatureEngineering
from grid_composite_metrics import GridCompositeMetrics
from grid_trade_manager import GridBinanceTradeManager
from advanced_ml_training_system import AdvancedMLTradingSystem

try:
    import streamlit as st
    import plotly.graph_objects as go
    import plotly.express as px
    import pandas as pd
    STREAMLIT_AVAILABLE = True
except ImportError:
    STREAMLIT_AVAILABLE = False
    print("⚠️ Streamlit not available - using console dashboard")

# Web Dashboard (Streamlit)
# This module provides a real-time dashboard for equity, drawdown, grid status, trade log, composite score, and controls
# Uses live_binance_integration, grid_composite_metrics, and other modules

from live_binance_integration import LiveTradingEngine

st.set_page_config(page_title="Grid Trading System Dashboard", layout="wide")

# Sidebar controls
st.sidebar.title("Grid Trading Controls")
mode = st.sidebar.selectbox("Mode", ["Live", "Test"])
start_button = st.sidebar.button("Start Bot")
stop_button = st.sidebar.button("Stop Bot")
emergency_button = st.sidebar.button("Emergency Stop")

# Main dashboard
st.title("Grid Trading System Dashboard")

class GridTradingDashboard:
    """Grid Trading Dashboard for monitoring and control"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('GridDashboard')
        
        # Initialize components
        self.ml_system = AdvancedMLTradingSystem(config)
        self.trade_manager = GridBinanceTradeManager(config)
        self.metrics_calculator = GridCompositeMetrics(config)
        
        # Dashboard state
        self.trading_active = False
        self.current_price = 0.0
        self.session_data = {
            'start_time': None,
            'trades': [],
            'performance': {},
            'grid_levels': {},
            'ml_predictions': []
        }
        
        # Performance tracking
        self.performance_history = []
        self.price_history = []
        
    def run_streamlit_dashboard(self):
        """Run Streamlit web dashboard"""
        if not STREAMLIT_AVAILABLE:
            self.logger.error("Streamlit not available")
            return
        
        st.set_page_config(
            page_title="Grid Trading Dashboard",
            page_icon="📊",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Main dashboard
        self._render_dashboard()
    
    def _render_dashboard(self):
        """Render the main dashboard interface"""
        
        # Header
        st.title("🎯 Grid Trading Dashboard")
        st.markdown("**Real-time monitoring and control for ML-enhanced grid trading**")
        
        # Sidebar controls
        self._render_sidebar()
        
        # Main content
        col1, col2, col3 = st.columns([2, 2, 1])
        
        with col1:
            self._render_trading_status()
            self._render_grid_levels()
        
        with col2:
            self._render_performance_charts()
            self._render_recent_trades()
        
        with col3:
            self._render_ml_status()
            self._render_risk_metrics()
        
        # Bottom section
        self._render_trade_history()
    
    def _render_sidebar(self):
        """Render sidebar controls"""
        
        st.sidebar.header("🎮 Trading Controls")
        
        # Trading status
        if self.trading_active:
            st.sidebar.success("🟢 Trading ACTIVE")
            if st.sidebar.button("🛑 STOP Trading", type="primary"):
                self._stop_trading()
        else:
            st.sidebar.error("🔴 Trading STOPPED")
            if st.sidebar.button("🚀 START Trading", type="primary"):
                self._start_trading()
        
        st.sidebar.divider()
        
        # Configuration
        st.sidebar.header("⚙️ Configuration")
        st.sidebar.metric("Starting Capital", f"${self.config.INITIAL_CAPITAL}")
        st.sidebar.metric("Risk per Trade", f"${self.config.FIXED_RISK_AMOUNT}")
        st.sidebar.metric("Profit Target", f"${self.config.TARGET_PROFIT}")
        st.sidebar.metric("Grid Spacing", f"{self.config.GRID_SPACING*100:.2f}%")
        st.sidebar.metric("Max Trades", self.config.MAX_CONCURRENT_TRADES)
        
        st.sidebar.divider()
        
        # Emergency controls
        st.sidebar.header("🚨 Emergency")
        if st.sidebar.button("⚠️ EMERGENCY STOP", type="secondary"):
            self._emergency_stop()
        
        if st.sidebar.button("💾 Save Session", type="secondary"):
            self._save_session()
    
    def _render_trading_status(self):
        """Render current trading status"""
        
        st.subheader("📊 Trading Status")
        
        # Current metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Current Price", f"${self.current_price:,.2f}")
        
        with col2:
            active_trades = len(self.session_data.get('trades', []))
            st.metric("Active Trades", active_trades)
        
        with col3:
            session_pnl = sum(trade.get('profit_loss', 0) for trade in self.session_data.get('trades', []))
            st.metric("Session P&L", f"${session_pnl:.2f}")
        
        with col4:
            if self.session_data.get('start_time'):
                duration = datetime.now() - self.session_data['start_time']
                hours = duration.total_seconds() / 3600
                st.metric("Session Time", f"{hours:.1f}h")
            else:
                st.metric("Session Time", "0.0h")
    
    def _render_grid_levels(self):
        """Render grid levels visualization"""
        
        st.subheader("🎯 Grid Levels")
        
        if not self.session_data.get('grid_levels'):
            st.info("No grid levels active")
            return
        
        # Create grid levels chart
        grid_data = []
        for level_id, level in self.session_data['grid_levels'].items():
            grid_data.append({
                'Level': level_id,
                'Price': level.price,
                'Touches': level.touches,
                'Last Action': level.last_action.name if level.last_action else 'NONE'
            })
        
        if grid_data:
            df = pd.DataFrame(grid_data)
            
            # Grid levels chart
            fig = go.Figure()
            
            # Add current price line
            fig.add_hline(y=self.current_price, line_dash="dash", 
                         line_color="blue", annotation_text="Current Price")
            
            # Add grid levels
            for _, row in df.iterrows():
                color = 'green' if row['Last Action'] == 'BUY' else 'red' if row['Last Action'] == 'SELL' else 'gray'
                fig.add_hline(y=row['Price'], line_color=color, opacity=0.5,
                             annotation_text=f"L{row['Level']}")
            
            fig.update_layout(
                title="Grid Levels vs Current Price",
                yaxis_title="Price ($)",
                height=300
            )
            
            st.plotly_chart(fig, use_container_width=True)
    
    def _render_performance_charts(self):
        """Render performance charts"""
        
        st.subheader("📈 Performance")
        
        # Composite score over time
        if self.performance_history:
            df = pd.DataFrame(self.performance_history)
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=df['timestamp'],
                y=df['composite_score'],
                mode='lines+markers',
                name='Composite Score',
                line=dict(color='blue')
            ))
            
            # Add threshold line
            fig.add_hline(y=self.config.COMPOSITE_SCORE_THRESHOLD, 
                         line_dash="dash", line_color="red",
                         annotation_text="85% Threshold")
            
            fig.update_layout(
                title="Composite Score Over Time",
                yaxis_title="Score",
                height=250
            )
            
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No performance data yet")
        
        # Equity curve
        if self.session_data.get('trades'):
            trades = self.session_data['trades']
            equity_curve = [self.config.INITIAL_CAPITAL]
            
            for trade in trades:
                equity_curve.append(equity_curve[-1] + trade.get('profit_loss', 0))
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                y=equity_curve,
                mode='lines',
                name='Equity',
                line=dict(color='green')
            ))
            
            fig.update_layout(
                title="Equity Curve",
                yaxis_title="Balance ($)",
                height=250
            )
            
            st.plotly_chart(fig, use_container_width=True)
    
    def _render_recent_trades(self):
        """Render recent trades table"""
        
        st.subheader("📋 Recent Trades")
        
        trades = self.session_data.get('trades', [])
        if not trades:
            st.info("No trades yet")
            return
        
        # Show last 10 trades
        recent_trades = trades[-10:]
        
        trade_data = []
        for trade in recent_trades:
            trade_data.append({
                'Time': trade.get('timestamp', '')[:19],
                'Action': trade.get('action', ''),
                'Price': f"${trade.get('entry_price', 0):.2f}",
                'Size': f"{trade.get('position_size', 0):.6f}",
                'P&L': f"${trade.get('profit_loss', 0):.2f}",
                'Status': trade.get('status', '')
            })
        
        if trade_data:
            df = pd.DataFrame(trade_data)
            st.dataframe(df, use_container_width=True)
    
    def _render_ml_status(self):
        """Render ML model status"""
        
        st.subheader("🤖 ML Status")
        
        # Model info
        deployment_model = self.ml_system.get_deployment_model()
        
        if deployment_model.get('model'):
            st.success("✅ Model Loaded")
            st.metric("Composite Score", f"{deployment_model.get('score', 0):.3f}")
            st.metric("Net Profit", f"${deployment_model.get('net_profit', 0):.2f}")
            
            # Recent predictions
            predictions = self.session_data.get('ml_predictions', [])
            if predictions:
                latest = predictions[-1]
                action_color = {'BUY': 'green', 'SELL': 'red', 'HOLD': 'gray'}
                action = latest.get('action', 'HOLD')
                st.markdown(f"**Latest Prediction:** :{action_color.get(action, 'gray')}[{action}]")
                st.metric("Confidence", f"{latest.get('confidence', 0):.2f}")
        else:
            st.error("❌ No Model")
    
    def _render_risk_metrics(self):
        """Render risk metrics"""
        
        st.subheader("⚠️ Risk Metrics")
        
        if self.session_data.get('trades'):
            trades = self.session_data['trades']
            
            # Calculate current metrics
            total_trades = len(trades)
            winning_trades = sum(1 for t in trades if t.get('profit_loss', 0) > 0)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            total_pnl = sum(t.get('profit_loss', 0) for t in trades)
            
            # Display metrics
            st.metric("Win Rate", f"{win_rate:.1%}")
            st.metric("Total P&L", f"${total_pnl:.2f}")
            
            # Risk warnings
            if win_rate < 0.4:
                st.error("⚠️ Low Win Rate")
            
            if total_pnl < -50:
                st.error("⚠️ High Losses")
        else:
            st.info("No risk data yet")
    
    def _render_trade_history(self):
        """Render complete trade history"""
        
        st.subheader("📊 Complete Trade History")
        
        trades = self.session_data.get('trades', [])
        if not trades:
            st.info("No trade history")
            return
        
        # Create detailed trade table
        trade_data = []
        for i, trade in enumerate(trades):
            trade_data.append({
                '#': i + 1,
                'Timestamp': trade.get('timestamp', '')[:19],
                'Grid Level': trade.get('grid_level_id', ''),
                'Action': trade.get('action', ''),
                'Entry Price': f"${trade.get('entry_price', 0):.2f}",
                'Exit Price': f"${trade.get('exit_price_actual', trade.get('exit_price', 0)):.2f}",
                'Position Size': f"{trade.get('position_size', 0):.6f}",
                'P&L': f"${trade.get('profit_loss', 0):.2f}",
                'Exit Reason': trade.get('exit_reason', ''),
                'Status': trade.get('status', '')
            })
        
        if trade_data:
            df = pd.DataFrame(trade_data)
            st.dataframe(df, use_container_width=True)
            
            # Download button
            csv = df.to_csv(index=False)
            st.download_button(
                label="📥 Download Trade History",
                data=csv,
                file_name=f"grid_trades_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
    
    def _start_trading(self):
        """Start grid trading"""
        try:
            # Initialize trading session
            self.session_data['start_time'] = datetime.now()
            self.session_data['trades'] = []
            self.trading_active = True
            
            # Start trading manager
            asyncio.run(self.trade_manager.start_grid_trading())
            
            st.success("🚀 Trading started successfully!")
            self.logger.info("Grid trading started from dashboard")
            
        except Exception as e:
            st.error(f"Failed to start trading: {e}")
            self.logger.error(f"Failed to start trading: {e}")
    
    def _stop_trading(self):
        """Stop grid trading"""
        try:
            self.trade_manager.stop_grid_trading()
            self.trading_active = False
            
            st.success("🛑 Trading stopped successfully!")
            self.logger.info("Grid trading stopped from dashboard")
            
        except Exception as e:
            st.error(f"Failed to stop trading: {e}")
            self.logger.error(f"Failed to stop trading: {e}")
    
    def _emergency_stop(self):
        """Emergency stop all trading"""
        try:
            self.trade_manager.stop_grid_trading()
            self.trading_active = False
            
            st.error("🚨 EMERGENCY STOP ACTIVATED!")
            self.logger.warning("Emergency stop activated from dashboard")
            
        except Exception as e:
            st.error(f"Emergency stop failed: {e}")
            self.logger.error(f"Emergency stop failed: {e}")
    
    def _save_session(self):
        """Save current session data"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"session_{timestamp}.json"
            
            with open(f"reports/{filename}", 'w') as f:
                json.dump(self.session_data, f, indent=2, default=str)
            
            st.success(f"💾 Session saved: {filename}")
            self.logger.info(f"Session saved: {filename}")
            
        except Exception as e:
            st.error(f"Failed to save session: {e}")
            self.logger.error(f"Failed to save session: {e}")

def run_console_dashboard(config: TradingConfig):
    """Run console-based dashboard if Streamlit not available"""
    
    print("🎯 GRID TRADING CONSOLE DASHBOARD")
    print("=" * 50)
    
    dashboard = GridTradingDashboard(config)
    
    while True:
        print("\n📊 DASHBOARD MENU:")
        print("1. 🚀 Start Trading")
        print("2. 🛑 Stop Trading")
        print("3. 📈 View Status")
        print("4. 📋 View Trades")
        print("5. 💾 Save Session")
        print("6. 🚪 Exit")
        
        choice = input("\nSelect option (1-6): ").strip()
        
        if choice == '1':
            dashboard._start_trading()
        elif choice == '2':
            dashboard._stop_trading()
        elif choice == '3':
            status = dashboard.trade_manager.get_trading_status()
            print(f"\n📊 Trading Status: {status}")
        elif choice == '4':
            trades = dashboard.session_data.get('trades', [])
            print(f"\n📋 Total Trades: {len(trades)}")
            for i, trade in enumerate(trades[-5:]):  # Show last 5
                print(f"  {i+1}. {trade.get('action', '')} @ ${trade.get('entry_price', 0):.2f} - P&L: ${trade.get('profit_loss', 0):.2f}")
        elif choice == '5':
            dashboard._save_session()
        elif choice == '6':
            break
        else:
            print("❌ Invalid option")

def main():
    """Main dashboard function"""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Initialize config
    config = TradingConfig()
    
    if STREAMLIT_AVAILABLE:
        # Run Streamlit dashboard
        dashboard = GridTradingDashboard(config)
        dashboard.run_streamlit_dashboard()
    else:
        # Run console dashboard
        run_console_dashboard(config)

if __name__ == "__main__":
    main()

# Example integration
# config = ... # Load config
# engine = LiveTradingEngine(config, test_mode=(mode=="Test"))
# if start_button:
#     engine.start()
# if stop_button:
#     engine.stop()
# if emergency_button:
#     engine.emergency_stop()
# ...display engine.monitor() output...
