#!/usr/bin/env python3
"""
COMPREHENSIVE WEBAPP HEALTH CHECK SYSTEM
=======================================
Complete health check system for Bitcoin Freedom webapp that validates:
- Webapp connectivity and responsiveness
- Binance API integration and data flow
- Database connectivity and data persistence
- Trading engine functionality
- UI/UX functionality and data display
- Real-time data updates
- Error handling and recovery
"""

import requests
import json
import time
import sqlite3
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

class ComprehensiveWebappHealthCheck:
    """Complete health check system for Bitcoin Freedom webapp"""
    
    def __init__(self, base_url: str = "http://localhost:5001"):
        self.base_url = base_url
        self.results = {}
        self.issues = []
        self.warnings = []
        self.start_time = datetime.now()
        
    def run_complete_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check covering all webapp aspects"""
        print("🔍 COMPREHENSIVE BITCOIN FREEDOM WEBAPP HEALTH CHECK")
        print("=" * 70)
        print(f"🌐 Target URL: {self.base_url}")
        print(f"⏰ Check Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
        # Core connectivity checks
        self.check_webapp_connectivity()
        self.check_api_endpoints()
        
        # Data integration checks
        self.check_binance_integration()
        self.check_database_connectivity()
        self.check_data_persistence()
        
        # Trading functionality checks
        self.check_trading_engine_status()
        self.check_model_integration()
        self.check_risk_management()
        
        # UI/UX functionality checks
        self.check_dashboard_data_display()
        self.check_real_time_updates()
        self.check_user_interactions()
        
        # Performance and reliability checks
        self.check_response_times()
        self.check_error_handling()
        self.check_data_accuracy()
        
        # Generate comprehensive report
        return self.generate_health_report()
    
    def check_webapp_connectivity(self):
        """Check if webapp is accessible and responding"""
        print("\n🌐 WEBAPP CONNECTIVITY CHECK")
        print("-" * 40)
        
        try:
            # Test main dashboard
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                print("✅ Main dashboard accessible")
                self.results['webapp_connectivity'] = 'HEALTHY'
                
                # Check if correct template is being used
                if 'Bitcoin Freedom' in response.text and 'Enhanced Elite' not in response.text:
                    print("✅ Correct Bitcoin Freedom template in use")
                    self.results['template_correct'] = 'HEALTHY'
                else:
                    print("❌ Wrong template - still showing Enhanced Elite")
                    self.results['template_correct'] = 'ERROR'
                    self.issues.append("Webapp using wrong template - Enhanced Elite instead of Bitcoin Freedom")
                    
            else:
                print(f"❌ Dashboard returned status code: {response.status_code}")
                self.results['webapp_connectivity'] = 'ERROR'
                self.issues.append(f"HTTP Status Code: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to webapp - server not running")
            self.results['webapp_connectivity'] = 'FAILED'
            self.issues.append("Webapp not accessible - check if server is running")
        except Exception as e:
            print(f"❌ Connection error: {e}")
            self.results['webapp_connectivity'] = 'ERROR'
            self.issues.append(f"Connection error: {str(e)}")
    
    def check_api_endpoints(self):
        """Check all API endpoints are responding correctly"""
        print("\n🔌 API ENDPOINTS CHECK")
        print("-" * 40)
        
        endpoints = [
            '/api/trading_status',
            '/api/health_check',
            '/api/preflight_check',
            '/api/recent_trades'
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ {endpoint} - OK")
                    self.results[f'api_{endpoint.replace("/", "_")}'] = 'HEALTHY'
                else:
                    print(f"❌ {endpoint} - Status: {response.status_code}")
                    self.results[f'api_{endpoint.replace("/", "_")}'] = 'ERROR'
                    self.issues.append(f"API endpoint {endpoint} returned {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint} - Error: {e}")
                self.results[f'api_{endpoint.replace("/", "_")}'] = 'FAILED'
                self.issues.append(f"API endpoint {endpoint} failed: {str(e)}")
    
    def check_binance_integration(self):
        """Check Binance API integration and data flow"""
        print("\n💰 BINANCE INTEGRATION CHECK")
        print("-" * 40)
        
        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                # Check Binance connection
                if data.get('binance_connected', False):
                    print("✅ Binance API connected")
                    self.results['binance_connection'] = 'HEALTHY'
                else:
                    print("❌ Binance API not connected")
                    self.results['binance_connection'] = 'ERROR'
                    self.issues.append("Binance API connection failed")
                
                # Check account balance
                if 'account_balance' in data and data['account_balance'] > 0:
                    print(f"✅ Account balance: ${data['account_balance']:.2f}")
                    self.results['account_balance'] = 'HEALTHY'
                else:
                    print("❌ No account balance data")
                    self.results['account_balance'] = 'ERROR'
                    self.issues.append("Account balance not available")
                
                # Check current price data
                if 'current_price' in data and data['current_price'] > 0:
                    print(f"✅ BTC price data: ${data['current_price']:,.2f}")
                    self.results['price_data'] = 'HEALTHY'
                else:
                    print("❌ No price data available")
                    self.results['price_data'] = 'ERROR'
                    self.issues.append("BTC price data not available")
                    
        except Exception as e:
            print(f"❌ Binance integration check failed: {e}")
            self.results['binance_integration'] = 'FAILED'
            self.issues.append(f"Binance integration check failed: {str(e)}")
    
    def check_database_connectivity(self):
        """Check database connectivity and data persistence"""
        print("\n🗄️ DATABASE CONNECTIVITY CHECK")
        print("-" * 40)
        
        db_path = "bitcoin_freedom_trades.db"
        
        try:
            # Check if database file exists
            if os.path.exists(db_path):
                print("✅ Database file exists")
                self.results['database_file'] = 'HEALTHY'
                
                # Test database connection
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Check if trades table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='trades'")
                if cursor.fetchone():
                    print("✅ Trades table exists")
                    self.results['trades_table'] = 'HEALTHY'
                    
                    # Check recent trades
                    cursor.execute("SELECT COUNT(*) FROM trades WHERE created_at > datetime('now', '-24 hours')")
                    recent_count = cursor.fetchone()[0]
                    print(f"✅ Recent trades (24h): {recent_count}")
                    self.results['recent_trades_count'] = recent_count
                    
                else:
                    print("❌ Trades table missing")
                    self.results['trades_table'] = 'ERROR'
                    self.issues.append("Trades table not found in database")
                
                conn.close()
                
            else:
                print("❌ Database file not found")
                self.results['database_file'] = 'ERROR'
                self.issues.append("Database file not found")
                
        except Exception as e:
            print(f"❌ Database check failed: {e}")
            self.results['database_connectivity'] = 'FAILED'
            self.issues.append(f"Database connectivity check failed: {str(e)}")
    
    def check_trading_engine_status(self):
        """Check trading engine functionality"""
        print("\n⚙️ TRADING ENGINE STATUS CHECK")
        print("-" * 40)

        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=10)
            if response.status_code == 200:
                data = response.json()

                # Check if trading engine is running
                if data.get('is_running', False):
                    print("✅ Trading engine is running")
                    self.results['trading_engine_running'] = 'HEALTHY'
                else:
                    print("⚠️ Trading engine is stopped")
                    self.results['trading_engine_running'] = 'WARNING'
                    self.warnings.append("Trading engine is currently stopped")

                # Check model configuration
                model_name = data.get('model_name', 'Unknown')
                win_rate = data.get('win_rate', 0)

                if 'Conservative Elite' in model_name or win_rate >= 0.8:
                    print(f"✅ Model: {model_name} ({win_rate*100:.1f}% win rate)")
                    self.results['model_configuration'] = 'HEALTHY'
                else:
                    print(f"❌ Unexpected model: {model_name}")
                    self.results['model_configuration'] = 'ERROR'
                    self.issues.append(f"Unexpected model configuration: {model_name}")

        except Exception as e:
            print(f"❌ Trading engine check failed: {e}")
            self.results['trading_engine_status'] = 'FAILED'
            self.issues.append(f"Trading engine status check failed: {str(e)}")

    def check_data_persistence(self):
        """Check data persistence and recovery"""
        print("\n💾 DATA PERSISTENCE CHECK")
        print("-" * 40)

        try:
            # Check recent trades API
            response = requests.get(f"{self.base_url}/api/recent_trades", timeout=10)
            if response.status_code == 200:
                trades = response.json()
                if isinstance(trades, list):
                    print(f"✅ Recent trades API working ({len(trades)} trades)")
                    self.results['recent_trades_api'] = 'HEALTHY'
                else:
                    print("❌ Recent trades API returned invalid data")
                    self.results['recent_trades_api'] = 'ERROR'
                    self.issues.append("Recent trades API returned invalid data format")
            else:
                print(f"❌ Recent trades API failed: {response.status_code}")
                self.results['recent_trades_api'] = 'ERROR'
                self.issues.append(f"Recent trades API failed with status {response.status_code}")

        except Exception as e:
            print(f"❌ Data persistence check failed: {e}")
            self.results['data_persistence'] = 'FAILED'
            self.issues.append(f"Data persistence check failed: {str(e)}")

    def check_model_integration(self):
        """Check model integration and AI functionality"""
        print("\n🤖 ENHANCED TCN-CNN-PPO MODEL INTEGRATION CHECK")
        print("-" * 40)

        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=10)
            if response.status_code == 200:
                data = response.json()

                # Check for Enhanced TCN-CNN-PPO model
                model_name = data.get('model_name', 'Unknown')
                if 'TCN-CNN-PPO' in model_name or 'Enhanced' in model_name:
                    print(f"✅ Enhanced TCN-CNN-PPO Model: {model_name}")
                    self.results['enhanced_model'] = 'HEALTHY'
                else:
                    print(f"⚠️ Model: {model_name} (Expected: Enhanced TCN-CNN-PPO)")
                    self.results['enhanced_model'] = 'WARNING'
                    self.warnings.append(f"Expected Enhanced TCN-CNN-PPO model, found: {model_name}")

                # Check enhanced model metrics
                expected_metrics = {
                    'win_rate': 87.3,  # Target: >85%
                    'composite_score': 82.1,  # Target: >90% (close)
                    'trades_per_day': 5.0,  # Target: 5.0
                    'roi': 1028.3,  # Enhanced ROI
                    'net_profit': 3085.00  # Enhanced profit
                }

                print("\n📊 ENHANCED MODEL PERFORMANCE VALIDATION:")
                for metric, expected_value in expected_metrics.items():
                    actual_value = data.get(metric, 0)
                    if metric == 'win_rate':
                        if actual_value >= 85.0:
                            print(f"✅ Win Rate: {actual_value}% (Target: >85%)")
                        else:
                            print(f"❌ Win Rate: {actual_value}% (Below target)")
                            self.issues.append(f"Win rate {actual_value}% below 85% target")
                    elif metric == 'composite_score':
                        if actual_value >= 80.0:
                            print(f"✅ Composite Score: {actual_value}% (Good)")
                        else:
                            print(f"❌ Composite Score: {actual_value}% (Below 80%)")
                            self.issues.append(f"Composite score {actual_value}% below 80%")
                    elif metric == 'trades_per_day':
                        if actual_value == 5.0:
                            print(f"✅ Trades/Day: {actual_value} (Target achieved)")
                        else:
                            print(f"⚠️ Trades/Day: {actual_value} (Target: 5.0)")
                            self.warnings.append(f"Trades per day {actual_value} vs target 5.0")
                    else:
                        print(f"✅ {metric}: {actual_value}")

                # Check TCN-CNN-PPO ensemble weights
                ensemble_weights = data.get('ensemble_weights', {})
                if ensemble_weights:
                    tcn_weight = ensemble_weights.get('tcn', 0)
                    cnn_weight = ensemble_weights.get('cnn', 0)
                    ppo_weight = ensemble_weights.get('ppo', 0)

                    if tcn_weight == 40 and cnn_weight == 40 and ppo_weight == 20:
                        print(f"✅ Ensemble Weights: TCN {tcn_weight}%, CNN {cnn_weight}%, PPO {ppo_weight}%")
                        self.results['ensemble_weights'] = 'HEALTHY'
                    else:
                        print(f"⚠️ Ensemble Weights: TCN {tcn_weight}%, CNN {cnn_weight}%, PPO {ppo_weight}%")
                        self.warnings.append("Ensemble weights not at expected values (TCN 40%, CNN 40%, PPO 20%)")
                else:
                    print("⚠️ Ensemble weights not available")
                    self.warnings.append("Ensemble weights not reported")

                self.results['model_metrics'] = 'HEALTHY'

        except Exception as e:
            print(f"❌ Enhanced model integration check failed: {e}")
            self.results['model_integration'] = 'FAILED'
            self.issues.append(f"Enhanced model integration check failed: {str(e)}")

    def check_risk_management(self):
        """Check risk management settings"""
        print("\n⚖️ RISK MANAGEMENT CHECK")
        print("-" * 40)

        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=10)
            if response.status_code == 200:
                data = response.json()

                # Check risk parameters
                risk_params = ['max_open_trades', 'risk_per_trade', 'leverage']
                for param in risk_params:
                    if param in data:
                        value = data[param]
                        print(f"✅ {param}: {value}")

                        # Validate specific risk parameters
                        if param == 'max_open_trades' and value != 1:
                            self.warnings.append(f"Max open trades is {value}, expected 1")
                        elif param == 'leverage' and value != 3:
                            self.warnings.append(f"Leverage is {value}, expected 3")
                    else:
                        print(f"⚠️ Missing {param}")
                        self.warnings.append(f"Missing risk parameter: {param}")

                self.results['risk_management'] = 'HEALTHY'

        except Exception as e:
            print(f"❌ Risk management check failed: {e}")
            self.results['risk_management'] = 'FAILED'
            self.issues.append(f"Risk management check failed: {str(e)}")

    def check_dashboard_data_display(self):
        """Check dashboard data display functionality"""
        print("\n📊 DASHBOARD DATA DISPLAY CHECK")
        print("-" * 40)

        try:
            # Check main dashboard
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                content = response.text

                # Check for key UI elements
                ui_elements = [
                    ('Bitcoin Freedom', 'Main title'),
                    ('Trading Status', 'Status section'),
                    ('Account Balance', 'Balance section'),
                    ('Recent Trades', 'Trades section')
                ]

                missing_elements = []
                for element, description in ui_elements:
                    if element in content:
                        print(f"✅ {description} present")
                    else:
                        missing_elements.append(description)
                        print(f"❌ {description} missing")

                if missing_elements:
                    self.results['dashboard_ui'] = 'ERROR'
                    self.issues.append(f"Missing UI elements: {', '.join(missing_elements)}")
                else:
                    self.results['dashboard_ui'] = 'HEALTHY'

        except Exception as e:
            print(f"❌ Dashboard display check failed: {e}")
            self.results['dashboard_display'] = 'FAILED'
            self.issues.append(f"Dashboard display check failed: {str(e)}")

    def check_real_time_updates(self):
        """Check real-time data updates"""
        print("\n🔄 REAL-TIME UPDATES CHECK")
        print("-" * 40)

        try:
            # Get initial data
            response1 = requests.get(f"{self.base_url}/api/trading_status", timeout=10)
            if response1.status_code == 200:
                data1 = response1.json()
                initial_timestamp = data1.get('timestamp', '')

                # Wait and get data again
                time.sleep(2)
                response2 = requests.get(f"{self.base_url}/api/trading_status", timeout=10)
                if response2.status_code == 200:
                    data2 = response2.json()
                    second_timestamp = data2.get('timestamp', '')

                    if initial_timestamp != second_timestamp:
                        print("✅ Real-time updates working")
                        self.results['real_time_updates'] = 'HEALTHY'
                    else:
                        print("⚠️ Timestamps not updating")
                        self.results['real_time_updates'] = 'WARNING'
                        self.warnings.append("Real-time timestamps not updating")
                else:
                    print("❌ Second API call failed")
                    self.results['real_time_updates'] = 'ERROR'
                    self.issues.append("Real-time updates check failed on second call")
            else:
                print("❌ Initial API call failed")
                self.results['real_time_updates'] = 'ERROR'
                self.issues.append("Real-time updates check failed on initial call")

        except Exception as e:
            print(f"❌ Real-time updates check failed: {e}")
            self.results['real_time_updates'] = 'FAILED'
            self.issues.append(f"Real-time updates check failed: {str(e)}")

    def check_user_interactions(self):
        """Check user interaction functionality"""
        print("\n👆 USER INTERACTIONS CHECK")
        print("-" * 40)

        # Note: We'll only test GET endpoints to avoid affecting live trading
        try:
            # Test health check endpoint (safe to call)
            response = requests.get(f"{self.base_url}/api/health_check", timeout=10)
            if response.status_code == 200:
                print("✅ Health check interaction working")
                self.results['health_check_interaction'] = 'HEALTHY'
            else:
                print(f"❌ Health check failed: {response.status_code}")
                self.results['health_check_interaction'] = 'ERROR'
                self.issues.append(f"Health check interaction failed: {response.status_code}")

            # Test preflight check endpoint
            response = requests.get(f"{self.base_url}/api/preflight_check", timeout=10)
            if response.status_code == 200:
                print("✅ Preflight check interaction working")
                self.results['preflight_interaction'] = 'HEALTHY'
            else:
                print(f"❌ Preflight check failed: {response.status_code}")
                self.results['preflight_interaction'] = 'ERROR'
                self.issues.append(f"Preflight check interaction failed: {response.status_code}")

        except Exception as e:
            print(f"❌ User interactions check failed: {e}")
            self.results['user_interactions'] = 'FAILED'
            self.issues.append(f"User interactions check failed: {str(e)}")

    def check_response_times(self):
        """Check webapp response times"""
        print("\n⚡ RESPONSE TIMES CHECK")
        print("-" * 40)

        endpoints = [
            ('/', 'Main dashboard'),
            ('/api/trading_status', 'Trading status'),
            ('/api/health_check', 'Health check')
        ]

        for endpoint, name in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                end_time = time.time()
                response_time = (end_time - start_time) * 1000  # Convert to milliseconds

                if response.status_code == 200:
                    if response_time < 1000:  # Less than 1 second
                        print(f"✅ {name}: {response_time:.0f}ms")
                        self.results[f'response_time_{endpoint.replace("/", "_")}'] = response_time
                    else:
                        print(f"⚠️ {name}: {response_time:.0f}ms (slow)")
                        self.results[f'response_time_{endpoint.replace("/", "_")}'] = response_time
                        self.warnings.append(f"{name} response time is slow: {response_time:.0f}ms")
                else:
                    print(f"❌ {name}: HTTP {response.status_code}")
                    self.issues.append(f"{name} returned HTTP {response.status_code}")

            except Exception as e:
                print(f"❌ {name}: Error - {e}")
                self.issues.append(f"{name} response time check failed: {str(e)}")

    def check_error_handling(self):
        """Check error handling and recovery"""
        print("\n🛡️ ERROR HANDLING CHECK")
        print("-" * 40)

        try:
            # Test invalid endpoint
            response = requests.get(f"{self.base_url}/api/invalid_endpoint", timeout=10)
            if response.status_code == 404:
                print("✅ 404 errors handled correctly")
                self.results['error_handling_404'] = 'HEALTHY'
            else:
                print(f"⚠️ Unexpected response for invalid endpoint: {response.status_code}")
                self.results['error_handling_404'] = 'WARNING'
                self.warnings.append(f"Invalid endpoint returned {response.status_code} instead of 404")

        except Exception as e:
            print(f"❌ Error handling check failed: {e}")
            self.results['error_handling'] = 'FAILED'
            self.issues.append(f"Error handling check failed: {str(e)}")

    def check_data_accuracy(self):
        """Check data accuracy and consistency"""
        print("\n🎯 DATA ACCURACY CHECK")
        print("-" * 40)

        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=10)
            if response.status_code == 200:
                data = response.json()

                # Check data types and ranges
                checks = [
                    ('current_price', lambda x: isinstance(x, (int, float)) and x > 0, 'BTC price should be positive number'),
                    ('win_rate', lambda x: isinstance(x, (int, float)) and 0 <= x <= 1, 'Win rate should be between 0 and 1'),
                    ('account_balance', lambda x: isinstance(x, (int, float)) and x >= 0, 'Account balance should be non-negative'),
                    ('trades_today', lambda x: isinstance(x, int) and x >= 0, 'Trades today should be non-negative integer')
                ]

                accuracy_issues = []
                for field, validator, description in checks:
                    if field in data:
                        if validator(data[field]):
                            print(f"✅ {field}: {data[field]} (valid)")
                        else:
                            print(f"❌ {field}: {data[field]} (invalid)")
                            accuracy_issues.append(description)
                    else:
                        print(f"⚠️ {field}: missing")
                        self.warnings.append(f"Missing data field: {field}")

                if accuracy_issues:
                    self.results['data_accuracy'] = 'ERROR'
                    self.issues.extend(accuracy_issues)
                else:
                    self.results['data_accuracy'] = 'HEALTHY'

        except Exception as e:
            print(f"❌ Data accuracy check failed: {e}")
            self.results['data_accuracy'] = 'FAILED'
            self.issues.append(f"Data accuracy check failed: {str(e)}")
    
    def generate_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive health report"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        # Determine overall health status
        if self.issues:
            overall_status = 'CRITICAL' if len(self.issues) > 3 else 'UNHEALTHY'
        elif self.warnings:
            overall_status = 'WARNING'
        else:
            overall_status = 'HEALTHY'
        
        report = {
            'timestamp': self.start_time.isoformat(),
            'duration_seconds': duration,
            'overall_status': overall_status,
            'total_checks': len(self.results),
            'issues_count': len(self.issues),
            'warnings_count': len(self.warnings),
            'results': self.results,
            'issues': self.issues,
            'warnings': self.warnings,
            'recommendations': self.generate_recommendations()
        }
        
        # Print summary
        print("\n" + "=" * 70)
        print("📊 HEALTH CHECK SUMMARY")
        print("=" * 70)
        print(f"🎯 Overall Status: {overall_status}")
        print(f"⏱️ Check Duration: {duration:.2f} seconds")
        print(f"✅ Total Checks: {len(self.results)}")
        print(f"❌ Issues Found: {len(self.issues)}")
        print(f"⚠️ Warnings: {len(self.warnings)}")
        
        if self.issues:
            print("\n🚨 CRITICAL ISSUES:")
            for i, issue in enumerate(self.issues, 1):
                print(f"   {i}. {issue}")
        
        if self.warnings:
            print("\n⚠️ WARNINGS:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"   {i}. {warning}")
        
        # Save report to file
        report_filename = f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"\n📄 Full report saved to: {report_filename}")
        
        return report
    
    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on health check results"""
        recommendations = []
        
        if self.results.get('template_correct') == 'ERROR':
            recommendations.append("Fix webapp template - change from enhanced_elite_dashboard.html to bitcoin_freedom_dashboard.html")
        
        if self.results.get('binance_connection') == 'ERROR':
            recommendations.append("Check Binance API credentials and network connectivity")
        
        if self.results.get('database_file') == 'ERROR':
            recommendations.append("Initialize database and create required tables")
        
        if self.results.get('trading_engine_running') == 'WARNING':
            recommendations.append("Start trading engine if live trading is desired")
        
        return recommendations

def main():
    """Run comprehensive webapp health check"""
    health_checker = ComprehensiveWebappHealthCheck()
    report = health_checker.run_complete_health_check()
    
    # Return exit code based on health status
    if report['overall_status'] == 'CRITICAL':
        exit(2)
    elif report['overall_status'] == 'UNHEALTHY':
        exit(1)
    else:
        exit(0)

if __name__ == '__main__':
    main()
