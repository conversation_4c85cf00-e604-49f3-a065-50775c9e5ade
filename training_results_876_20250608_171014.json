{"success": true, "target_score": 0.876, "best_score": 0.8990891017807802, "iterations_completed": 5, "max_iterations": 25, "training_iterations": [{"iteration": 1, "composite_score": 0.6915309379315475, "backtest_results": {"total_trades": 36, "winning_trades": 26, "win_rate": 0.726612375172619, "total_profit": 697.1325780420145, "final_balance": 997.1325780420145, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 3, "tcn_filters": 64, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:10:11.494747"}, {"iteration": 2, "composite_score": 0.7229946592658525, "backtest_results": {"total_trades": 35, "winning_trades": 25, "win_rate": 0.739197863706341, "total_profit": 547.2845367959478, "final_balance": 847.2845367959478, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:10:11.997510"}, {"iteration": 3, "composite_score": 0.7759753257352576, "backtest_results": {"total_trades": 37, "winning_trades": 28, "win_rate": 0.7603901302941031, "total_profit": 777.8929870737518, "final_balance": 1077.8929870737518, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:10:12.502285"}, {"iteration": 4, "composite_score": 0.8133001991969537, "backtest_results": {"total_trades": 31, "winning_trades": 24, "win_rate": 0.7753200796787816, "total_profit": 786.5453577516523, "final_balance": 1086.5453577516523, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:10:13.027031"}, {"iteration": 5, "composite_score": 0.8990891017807802, "backtest_results": {"total_trades": 35, "winning_trades": 28, "win_rate": 0.8096356407123121, "total_profit": 861.4231390283659, "final_balance": 1161.4231390283658, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:10:13.620909"}], "best_model": {"iteration": 5, "composite_score": 0.8990891017807802, "backtest_results": {"total_trades": 35, "winning_trades": 28, "win_rate": 0.8096356407123121, "total_profit": 861.4231390283659, "final_balance": 1161.4231390283658, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:10:13.620909"}, "locked_parameters": {"grid_spacing": 0.0025, "risk_reward_ratio": 2.5, "training_days": 60, "testing_days": 30, "tcn_weight": 0.4, "cnn_weight": 0.4, "ppo_weight": 0.2}, "final_hyperparameters": {"tcn_layers": 4, "tcn_filters": 96, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "results_source": "PURE_OUT_OF_SAMPLE_BACKTEST_ONLY", "reinforcement_learning_applied": true, "hyperparameter_optimization_applied": true}