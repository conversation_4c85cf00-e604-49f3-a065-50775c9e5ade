#!/usr/bin/env python3
"""
Quick Trading Readiness Check for 8-Hour Test
Focuses on the most critical components for trading.
"""

import sys
import time
from datetime import datetime

def print_header(title):
    print(f"\n{'='*50}")
    print(f"🔍 {title}")
    print('='*50)

def check_conservative_elite_system():
    """Check if Conservative Elite system is properly configured."""
    print_header("CONSERVATIVE ELITE SYSTEM CHECK")
    
    try:
        from live_trading_web_app import BestCompositeModel, LiveTradingEngine
        
        # Check model
        model = BestCompositeModel()
        print(f"✅ Model Type: {model.model_type}")
        print(f"✅ Composite Score: {model.composite_score*100:.1f}%")
        print(f"✅ Win Rate: {model.win_rate*100:.1f}%")
        print(f"✅ Risk per Trade: ${model.risk_per_trade:.2f}")
        print(f"✅ Profit Target: ${model.profit_target:.2f}")
        
        # Check trading engine
        engine = LiveTradingEngine(model)
        print(f"✅ Trading Engine: INITIALIZED")
        print(f"✅ Account Balance: ${engine.balance:.2f}")
        print(f"✅ Open Trades: {len(engine.open_trades)}")
        
        # Test signal generation
        signal = engine.generate_trade_signal()
        print(f"✅ Signal Generation: {signal if signal else 'HOLD'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Conservative Elite check failed: {e}")
        return False

def check_trading_loop():
    """Check if the main trading loop can run."""
    print_header("TRADING LOOP CHECK")
    
    try:
        from live_trading_web_app import LiveTradingEngine, BestCompositeModel
        
        model = BestCompositeModel()
        engine = LiveTradingEngine(model)
        
        # Test one iteration of trading loop
        print("🔄 Testing trading loop iteration...")
        
        # Check if should trade
        should_trade = engine.should_trade()
        print(f"📊 Should Trade: {should_trade}")
        
        # Check grid signals
        grid_signals = engine.check_grid_trading_signals()
        print(f"📊 Grid Signals: {grid_signals}")
        
        # Check AI signals
        try:
            ai_signals = engine.check_ai_monitor_signals()
            print(f"📊 AI Signals: {ai_signals}")
        except:
            print(f"⚠️ AI Signals: NOT AVAILABLE")
        
        print(f"✅ Trading Loop: FUNCTIONAL")
        return True
        
    except Exception as e:
        print(f"❌ Trading loop check failed: {e}")
        return False

def check_price_data():
    """Check if price data is accessible."""
    print_header("PRICE DATA CHECK")
    
    try:
        import ccxt
        
        # Test public price data
        exchange = ccxt.binance()
        ticker = exchange.fetch_ticker('BTC/USDT')
        current_price = ticker['last']
        
        print(f"✅ Current BTC Price: ${current_price:,.2f}")
        print(f"✅ 24h Change: {ticker['percentage']:.2f}%")
        print(f"✅ Volume: {ticker['baseVolume']:,.0f} BTC")
        
        # Test price history
        from live_trading_web_app import LiveTradingEngine, BestCompositeModel
        model = BestCompositeModel()
        engine = LiveTradingEngine(model)
        
        if len(engine.price_history) > 0:
            print(f"✅ Price History: {len(engine.price_history)} points")
        else:
            print(f"⚠️ Price History: EMPTY (will populate)")
        
        return True
        
    except Exception as e:
        print(f"❌ Price data check failed: {e}")
        return False

def check_trade_execution():
    """Check if trade execution components work."""
    print_header("TRADE EXECUTION CHECK")
    
    try:
        from live_trading_web_app import LiveTradingEngine, BestCompositeModel
        
        model = BestCompositeModel()
        engine = LiveTradingEngine(model)
        
        # Check trade management methods
        methods_to_check = [
            'should_trade',
            'generate_trade_signal',
            'check_grid_trading_signals',
            'update_performance_metrics'
        ]
        
        for method_name in methods_to_check:
            if hasattr(engine, method_name):
                print(f"✅ Method {method_name}: EXISTS")
            else:
                print(f"❌ Method {method_name}: MISSING")
                return False
        
        # Test position management
        max_positions = getattr(model, 'max_positions', 1)
        current_positions = len(engine.open_trades)
        
        print(f"✅ Max Positions: {max_positions}")
        print(f"✅ Current Positions: {current_positions}")
        
        if current_positions < max_positions:
            print(f"✅ Can Open New Trades: YES")
        else:
            print(f"⚠️ Can Open New Trades: NO (at limit)")
        
        return True
        
    except Exception as e:
        print(f"❌ Trade execution check failed: {e}")
        return False

def check_data_logging():
    """Check if trade logging works."""
    print_header("DATA LOGGING CHECK")
    
    try:
        # Check CSV logging
        from trade_csv_logger import TradeCSVLogger
        csv_logger = TradeCSVLogger("test_check.csv")
        print(f"✅ CSV Logger: WORKING")
        
        # Check database
        from trade_database import TradingDatabase
        db = TradingDatabase("test_check.db")
        print(f"✅ Database: WORKING")
        
        # Test if existing trade history exists
        import os
        if os.path.exists("trade_history.csv"):
            print(f"✅ Trade History: EXISTS")
        else:
            print(f"⚠️ Trade History: WILL BE CREATED")
        
        return True
        
    except Exception as e:
        print(f"❌ Data logging check failed: {e}")
        return False

def test_webapp_startup():
    """Test if webapp can start."""
    print_header("WEBAPP STARTUP CHECK")
    
    try:
        from live_trading_web_app import app
        
        # Check if app can be created
        print(f"✅ Flask App: CREATED")
        
        # Check critical routes
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        critical_routes = ['/', '/api/trading_status', '/api/start_trading', '/api/stop_trading']
        
        for route in critical_routes:
            if route in routes:
                print(f"✅ Route {route}: EXISTS")
            else:
                print(f"❌ Route {route}: MISSING")
                return False
        
        print(f"✅ Webapp: READY TO START")
        return True
        
    except Exception as e:
        print(f"❌ Webapp startup check failed: {e}")
        return False

def main():
    """Main readiness check."""
    print("🚀 QUICK TRADING READINESS CHECK")
    print("=" * 60)
    print(f"⏰ Check Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    checks = [
        ("Conservative Elite System", check_conservative_elite_system),
        ("Trading Loop", check_trading_loop),
        ("Price Data", check_price_data),
        ("Trade Execution", check_trade_execution),
        ("Data Logging", check_data_logging),
        ("Webapp Startup", test_webapp_startup)
    ]
    
    results = {}
    passed = 0
    
    for name, check_func in checks:
        try:
            result = check_func()
            results[name] = result
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {name}: EXCEPTION - {e}")
            results[name] = False
    
    # Summary
    print_header("READINESS SUMMARY")
    
    for name, result in results.items():
        status = "✅ READY" if result else "❌ NOT READY"
        print(f"{status} {name}")
    
    total = len(checks)
    print(f"\n📊 OVERALL: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 SYSTEM IS READY FOR 8-HOUR TEST!")
        print("\n📋 START TRADING:")
        print("1. Run: python live_trading_web_app.py")
        print("2. Open: http://localhost:5000/conservative_elite")
        print("3. Click: 'Start Trading'")
        print("4. Monitor: Check for trades every 30 minutes")
        print("5. Expected: 5-6 trades over 8 hours")
        return True
    else:
        print(f"\n❌ {total - passed} ISSUES NEED FIXING")
        print("❌ System is NOT ready for trading")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
