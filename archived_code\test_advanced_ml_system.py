"""
Test Script for Advanced ML Trading System
Tests TCN, CNN, and PPO models with live Bitcoin data
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime
import traceback

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import the advanced ML system
from advanced_ml_training_system import AdvancedMLTradingSystem

def setup_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_advanced_ml.log'),
            logging.StreamHandler()
        ]
    )

def test_individual_models():
    """Test individual model components"""
    print("\n🧪 Testing Individual Model Components...")
    print("="*60)
    
    # Test TCN Model
    print("📈 Testing TCN Model...")
    try:
        from models.tcn_model import create_tcn_model
        
        # Create sample data
        sample_data = pd.DataFrame({
            'feature_1': np.random.randn(1000),
            'feature_2': np.random.randn(1000),
            'feature_3': np.random.randn(1000),
            'label': np.random.randint(0, 2, 1000)
        })
        
        tcn = create_tcn_model(sequence_length=50, num_features=3)
        X, y = tcn.prepare_sequences(sample_data)
        
        if len(X) > 0:
            print(f"✅ TCN: Created {len(X)} sequences")
        else:
            print("❌ TCN: Failed to create sequences")
            
    except Exception as e:
        print(f"❌ TCN Test Failed: {e}")
    
    # Test CNN Model
    print("🔍 Testing CNN Model...")
    try:
        from models.cnn_model import create_cnn_model
        
        cnn = create_cnn_model(window_size=40, num_features=3)
        X, y = cnn.prepare_windows(sample_data)
        
        if len(X) > 0:
            print(f"✅ CNN: Created {len(X)} windows")
        else:
            print("❌ CNN: Failed to create windows")
            
    except Exception as e:
        print(f"❌ CNN Test Failed: {e}")
    
    # Test PPO Agent
    print("🎯 Testing PPO Agent...")
    try:
        from models.ppo_agent import create_ppo_agent, TradingEnvironment
        
        # Create trading environment
        env_data = pd.DataFrame({
            'close': np.cumsum(np.random.randn(500)) + 100,
            'feature_1': np.random.randn(500),
            'feature_2': np.random.randn(500),
            'label': np.random.randint(0, 2, 500)
        })
        
        env = TradingEnvironment(env_data)
        obs, _ = env.reset()
        
        if obs is not None and len(obs) > 0:
            print(f"✅ PPO: Environment created with observation shape {obs.shape}")
        else:
            print("❌ PPO: Failed to create environment")
            
    except Exception as e:
        print(f"❌ PPO Test Failed: {e}")

def test_ensemble_system():
    """Test ensemble model system"""
    print("\n🎯 Testing Ensemble System...")
    print("="*60)
    
    try:
        from models.ensemble_model import create_ensemble_trader
        
        # Create sample training data
        np.random.seed(42)
        n_samples = 1000
        
        # Generate realistic price data
        price_data = np.cumsum(np.random.randn(n_samples) * 0.01) + 100
        
        # Generate features
        features = {
            'close': price_data,
            'open': price_data + np.random.randn(n_samples) * 0.1,
            'high': price_data + np.abs(np.random.randn(n_samples)) * 0.2,
            'low': price_data - np.abs(np.random.randn(n_samples)) * 0.2,
            'volume': np.random.exponential(1000, n_samples),
            'rsi': np.random.uniform(20, 80, n_samples),
            'macd': np.random.randn(n_samples),
            'bb_upper': price_data + 2,
            'bb_lower': price_data - 2,
            'sma_20': price_data + np.random.randn(n_samples) * 0.1,
            'ema_12': price_data + np.random.randn(n_samples) * 0.05,
            'label': np.random.randint(0, 2, n_samples)
        }
        
        test_data = pd.DataFrame(features)
        
        # Create ensemble
        ensemble = create_ensemble_trader()
        ensemble.initialize_models(sequence_length=50, window_size=40, num_features=8)
        
        print("✅ Ensemble models initialized successfully")
        
        # Test prediction (without training)
        try:
            prediction = ensemble.predict_ensemble(test_data.iloc[-100:])
            print(f"✅ Ensemble prediction: {prediction['ensemble_prediction']} (confidence: {prediction['confidence']:.3f})")
        except Exception as e:
            print(f"⚠️ Ensemble prediction failed (expected without training): {e}")
        
    except Exception as e:
        print(f"❌ Ensemble Test Failed: {e}")
        traceback.print_exc()

def test_data_collection():
    """Test data collection and feature engineering"""
    print("\n📊 Testing Data Collection...")
    print("="*60)
    
    try:
        config = TradingConfig()
        
        # Test with a single symbol
        test_symbol = 'BTCUSDT'
        
        from ml_training_system import DataCollector, FeatureEngineering
        
        data_collector = DataCollector(config)
        feature_engineer = FeatureEngineering()
        
        print(f"Collecting data for {test_symbol}...")
        
        # Collect small amount of data for testing
        df = data_collector.collect_historical_data(test_symbol, '1h', 5)  # 5 days
        
        if len(df) > 0:
            print(f"✅ Collected {len(df)} data points for {test_symbol}")
            
            # Test feature engineering
            features_df = feature_engineer.create_features(df)
            print(f"✅ Created {len(features_df.columns)} features")
            
            # Show sample features
            feature_cols = [col for col in features_df.columns if col not in ['open', 'high', 'low', 'close', 'volume']]
            print(f"Sample features: {feature_cols[:5]}...")
            
        else:
            print(f"❌ No data collected for {test_symbol}")
            
    except Exception as e:
        print(f"❌ Data Collection Test Failed: {e}")
        traceback.print_exc()

def test_quick_training():
    """Test quick training with minimal data"""
    print("\n🚀 Testing Quick Training...")
    print("="*60)
    
    try:
        config = TradingConfig()
        
        # Create minimal test system
        ml_system = AdvancedMLTradingSystem(config)
        
        # Test with BTCUSDT only
        test_symbol = 'BTCUSDT'
        
        print(f"Testing training pipeline for {test_symbol}...")
        
        # Collect minimal data
        train_data, test_data = ml_system.collect_and_prepare_data(test_symbol)
        
        print(f"✅ Data prepared: {len(train_data)} train, {len(test_data)} test")
        
        # Test ensemble creation and quick training
        from models.ensemble_model import create_ensemble_trader
        
        ensemble = create_ensemble_trader()
        
        # Get number of features
        num_features = len([col for col in train_data.columns 
                           if col not in ['label', 'future_return', 'open', 'high', 'low', 'close', 'volume']])
        
        ensemble.initialize_models(
            sequence_length=30,  # Reduced for testing
            window_size=25,      # Reduced for testing
            num_features=num_features
        )
        
        print(f"✅ Ensemble initialized with {num_features} features")
        
        # Quick training test (very reduced parameters)
        print("Starting quick training test...")
        results = ensemble.train_ensemble(
            train_data.iloc[-200:],  # Use only last 200 samples
            test_data.iloc[-50:],    # Use only last 50 samples for validation
            tcn_epochs=2,            # Minimal epochs
            cnn_epochs=2,
            ppo_timesteps=1000       # Minimal timesteps
        )
        
        print("✅ Quick training completed!")
        
        # Test prediction
        prediction = ensemble.predict_ensemble(test_data.iloc[-10:])
        print(f"✅ Test prediction: {prediction['ensemble_prediction']} (confidence: {prediction['confidence']:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Quick Training Test Failed: {e}")
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🧪 ADVANCED ML TRADING SYSTEM - COMPREHENSIVE TEST")
    print("="*80)
    print("🎯 Testing TCN + CNN + PPO Ensemble with Live Bitcoin Data")
    print("🔍 Grid Search Optimization")
    print("📊 85% Composite Score Target")
    print("="*80)
    
    # Setup
    setup_logging()
    
    # Create directories
    config = TradingConfig()
    os.makedirs(config.MODELS_DIR, exist_ok=True)
    os.makedirs(config.LOGS_DIR, exist_ok=True)
    os.makedirs(config.REPORTS_DIR, exist_ok=True)
    
    test_results = {
        'individual_models': False,
        'ensemble_system': False,
        'data_collection': False,
        'quick_training': False
    }
    
    # Test individual components
    try:
        test_individual_models()
        test_results['individual_models'] = True
    except Exception as e:
        print(f"Individual models test failed: {e}")
    
    # Test ensemble system
    try:
        test_ensemble_system()
        test_results['ensemble_system'] = True
    except Exception as e:
        print(f"Ensemble system test failed: {e}")
    
    # Test data collection
    try:
        test_data_collection()
        test_results['data_collection'] = True
    except Exception as e:
        print(f"Data collection test failed: {e}")
    
    # Test quick training
    try:
        if test_quick_training():
            test_results['quick_training'] = True
    except Exception as e:
        print(f"Quick training test failed: {e}")
    
    # Print final results
    print("\n" + "="*80)
    print("🏁 TEST RESULTS SUMMARY")
    print("="*80)
    
    for test_name, passed in test_results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    total_passed = sum(test_results.values())
    total_tests = len(test_results)
    
    print(f"\nOverall: {total_passed}/{total_tests} tests passed ({(total_passed/total_tests)*100:.1f}%)")
    
    if total_passed == total_tests:
        print("\n🎉 All tests passed! System ready for full training.")
        print("💡 Next step: Run 'python advanced_ml_training_system.py' for full training")
    else:
        print("\n⚠️ Some tests failed. Please check the logs and fix issues before proceeding.")
    
    print("="*80)
    
    return test_results

if __name__ == "__main__":
    run_comprehensive_test()
