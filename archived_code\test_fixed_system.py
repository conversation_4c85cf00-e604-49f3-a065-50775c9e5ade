"""
Fixed Test Script for Advanced ML Trading System
Tests with all issues resolved
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime
import traceback

def setup_logging():
    """Setup logging without Unicode issues"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_fixed.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def test_tensorflow():
    """Test TensorFlow functionality"""
    print("Testing TensorFlow...")
    
    try:
        import tensorflow as tf
        print(f"TensorFlow version: {tf.__version__}")
        
        # Test basic operations
        x = tf.constant([[1.0, 2.0], [3.0, 4.0]])
        y = tf.constant([[1.0, 1.0], [0.0, 1.0]])
        result = tf.matmul(x, y)
        
        print("TensorFlow basic operations working")
        return True
        
    except Exception as e:
        print(f"TensorFlow test failed: {e}")
        return False

def test_stable_baselines():
    """Test stable-baselines3"""
    print("\nTesting stable-baselines3...")
    
    try:
        from stable_baselines3 import PPO
        import gymnasium as gym
        
        # Create simple environment
        env = gym.make('CartPole-v1')
        
        # Test PPO creation (don't train)
        model = PPO('MlpPolicy', env, verbose=0)
        
        print("stable-baselines3 working")
        env.close()
        return True
        
    except Exception as e:
        print(f"stable-baselines3 test failed: {e}")
        return False

def test_advanced_models():
    """Test advanced model creation"""
    print("\nTesting Advanced Models...")
    
    try:
        # Test TCN
        from models.tcn_model import create_tcn_model
        tcn = create_tcn_model(sequence_length=30, num_features=10)
        print("TCN model created successfully")
        
        # Test CNN  
        from models.cnn_model import create_cnn_model
        cnn = create_cnn_model(window_size=25, num_features=10)
        print("CNN model created successfully")
        
        # Test PPO
        from models.ppo_agent import create_ppo_agent
        ppo = create_ppo_agent()
        print("PPO agent created successfully")
        
        # Test Ensemble
        from models.ensemble_model import create_ensemble_trader
        ensemble = create_ensemble_trader()
        ensemble.initialize_models(sequence_length=30, window_size=25, num_features=10)
        print("Ensemble model created successfully")
        
        return True
        
    except Exception as e:
        print(f"Advanced models test failed: {e}")
        traceback.print_exc()
        return False

def test_data_preparation():
    """Test data preparation with real data"""
    print("\nTesting Data Preparation...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
        from trading_config import TradingConfig
        from ml_training_system import DataCollector, FeatureEngineering
        
        config = TradingConfig()
        data_collector = DataCollector(config)
        feature_engineer = FeatureEngineering()
        
        # Collect small amount of data
        df = data_collector.collect_historical_data('BTCUSDT', '1h', 2)  # 2 days
        
        if len(df) > 0:
            print(f"Collected {len(df)} data points")
            
            # Create features
            features_df = feature_engineer.create_features(df)
            print(f"Created {len(features_df.columns)} features")
            
            # Test model data preparation
            from models.tcn_model import create_tcn_model
            from models.cnn_model import create_cnn_model
            
            tcn = create_tcn_model(sequence_length=20, num_features=10)
            cnn = create_cnn_model(window_size=15, num_features=10)
            
            # Test sequence preparation
            X_tcn, y_tcn = tcn.prepare_sequences(features_df)
            print(f"TCN sequences: {X_tcn.shape if len(X_tcn) > 0 else 'None'}")
            
            # Test window preparation  
            X_cnn, y_cnn = cnn.prepare_windows(features_df)
            print(f"CNN windows: {X_cnn.shape if len(X_cnn) > 0 else 'None'}")
            
            return True
        else:
            print("No data collected")
            return False
            
    except Exception as e:
        print(f"Data preparation test failed: {e}")
        traceback.print_exc()
        return False

def test_quick_training():
    """Test quick training with minimal parameters"""
    print("\nTesting Quick Training...")
    
    try:
        from advanced_ml_training_system import AdvancedMLTradingSystem
        sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
        from trading_config import TradingConfig
        
        config = TradingConfig()
        ml_system = AdvancedMLTradingSystem(config)
        
        # Collect minimal data
        train_data, test_data = ml_system.collect_and_prepare_data('BTCUSDT')
        print(f"Data prepared: {len(train_data)} train, {len(test_data)} test")
        
        # Create ensemble with dynamic feature count
        from models.ensemble_model import create_ensemble_trader
        ensemble = create_ensemble_trader()
        
        # Get actual feature count
        feature_cols = [col for col in train_data.columns 
                       if col not in ['label', 'future_return', 'open', 'high', 'low', 'close', 'volume']]
        actual_features = len(feature_cols)
        
        print(f"Actual features: {actual_features}")
        
        # Initialize with correct feature count
        ensemble.initialize_models(
            sequence_length=20,  # Reduced
            window_size=15,      # Reduced
            num_features=actual_features
        )
        
        # Quick training with minimal data and epochs
        print("Starting minimal training...")
        results = ensemble.train_ensemble(
            train_data.iloc[-50:],   # Only last 50 samples
            test_data.iloc[-20:],    # Only last 20 samples for validation
            tcn_epochs=1,            # Minimal epochs
            cnn_epochs=1,
            ppo_timesteps=100        # Minimal timesteps
        )
        
        print("Quick training completed!")
        
        # Test prediction
        prediction = ensemble.predict_ensemble(test_data.iloc[-5:])
        print(f"Test prediction: {prediction['ensemble_prediction']} (confidence: {prediction['confidence']:.3f})")
        
        return True
        
    except Exception as e:
        print(f"Quick training test failed: {e}")
        traceback.print_exc()
        return False

def run_fixed_tests():
    """Run all fixed tests"""
    print("FIXED TEST SUITE - ADVANCED ML TRADING SYSTEM")
    print("="*70)
    print("Testing with all issues resolved")
    print("="*70)
    
    setup_logging()
    
    tests = [
        ("TensorFlow", test_tensorflow),
        ("stable-baselines3", test_stable_baselines),
        ("Advanced Models", test_advanced_models),
        ("Data Preparation", test_data_preparation),
        ("Quick Training", test_quick_training)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"{test_name} failed with exception: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # Summary
    print("\n" + "="*70)
    print("FIXED TEST RESULTS SUMMARY")
    print("="*70)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed >= total * 0.8:  # 80% pass rate
        print("\nSUCCESS: Fixed system is working!")
        print("Ready for full training.")
    else:
        print("\nSome tests still failing. Check errors above.")
    
    print("="*70)
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = run_fixed_tests()
    sys.exit(0 if success else 1)
