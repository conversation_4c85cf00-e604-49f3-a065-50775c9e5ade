#!/usr/bin/env python3
"""
Cycle 1 Model Integration Test
Comprehensive validation of the TCN-CNN-PPO Conservative Elite Model
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, Any

class Cycle1ModelTest:
    """Test suite for Cycle 1 model integration."""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.test_results = {}
        
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run complete Cycle 1 model integration test."""
        print("🤖 CYCLE 1 MODEL INTEGRATION TEST")
        print("=" * 50)
        print("Testing TCN-CNN-PPO Conservative Elite Model")
        print("Model ID: tcn_cnn_ppo_conservative_v3_20250601_234301")
        print("Target Composite Score: 91.4%")
        print("=" * 50)
        
        # Run all tests
        self.test_model_configuration()
        self.test_component_weights()
        self.test_performance_metrics()
        self.test_trading_parameters()
        self.test_cross_margin_integration()
        self.test_signal_generation()
        self.test_risk_management()
        self.test_real_time_integration()
        
        return self.generate_test_report()
    
    def test_model_configuration(self):
        """Test basic model configuration."""
        print("\n🔧 Testing Model Configuration...")
        
        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            data = response.json()
            model_info = data.get('model_info', {})
            
            tests = {
                'model_id': model_info.get('model_id') == "tcn_cnn_ppo_conservative_v3_20250601_234301",
                'model_type': 'TCN-CNN-PPO' in str(model_info.get('model_type', '')),
                'cycle': model_info.get('cycle') == 1,
                'composite_score': abs(model_info.get('composite_score', 0) - 91.4) < 0.1,
                'live_ready': model_info.get('live_trading_ready', False)
            }
            
            self.test_results['model_configuration'] = {
                'status': 'PASS' if all(tests.values()) else 'FAIL',
                'tests': tests,
                'model_info': model_info
            }
            
            for test_name, passed in tests.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {test_name}")
                
        except Exception as e:
            self.test_results['model_configuration'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Error: {e}")
    
    def test_component_weights(self):
        """Test TCN-CNN-PPO component weights."""
        print("\n⚖️ Testing Component Weights...")
        
        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            data = response.json()
            model_info = data.get('model_info', {})
            
            # Expected weights from Cycle 1 results
            expected_weights = {
                'tcn_weight': 39.8,
                'cnn_weight': 43.9,
                'ppo_weight': 16.4
            }
            
            tests = {}
            for component, expected in expected_weights.items():
                actual = model_info.get(component, 0)
                tests[component] = abs(actual - expected) < 0.5  # Allow 0.5% tolerance
                
            # Test weight sum (should be ~100%)
            total_weight = sum(model_info.get(k, 0) for k in expected_weights.keys())
            tests['weight_sum'] = abs(total_weight - 100.0) < 1.0
            
            self.test_results['component_weights'] = {
                'status': 'PASS' if all(tests.values()) else 'FAIL',
                'tests': tests,
                'weights': {k: model_info.get(k) for k in expected_weights.keys()},
                'total_weight': total_weight
            }
            
            for test_name, passed in tests.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {test_name}")
                
        except Exception as e:
            self.test_results['component_weights'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Error: {e}")
    
    def test_performance_metrics(self):
        """Test expected performance metrics."""
        print("\n📊 Testing Performance Metrics...")
        
        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            data = response.json()
            model_info = data.get('model_info', {})
            
            # Expected Cycle 1 performance
            expected_metrics = {
                'net_profit_target': 4476.37,
                'win_rate_target': 98.0,
                'trades_per_day_target': 6.1,
                'composite_score': 91.4
            }
            
            tests = {}
            for metric, expected in expected_metrics.items():
                actual = model_info.get(metric, 0)
                if metric == 'composite_score':
                    tests[metric] = abs(actual - expected) < 0.1
                else:
                    tests[metric] = actual == expected
            
            self.test_results['performance_metrics'] = {
                'status': 'PASS' if all(tests.values()) else 'FAIL',
                'tests': tests,
                'metrics': {k: model_info.get(k) for k in expected_metrics.keys()}
            }
            
            for test_name, passed in tests.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {test_name}")
                
        except Exception as e:
            self.test_results['performance_metrics'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Error: {e}")
    
    def test_trading_parameters(self):
        """Test trading parameters configuration."""
        print("\n💰 Testing Trading Parameters...")
        
        try:
            response = requests.get(f"{self.base_url}/api/cross_margin_analysis", timeout=5)
            data = response.json()
            account_config = data.get('account_config', {})
            
            # Expected trading parameters
            expected_params = {
                'account_size': 300.0,
                'risk_per_trade': 10.0,
                'profit_target': 25.0,
                'margin_type': 'CROSS',
                'grid_size_percent': 0.25
            }
            
            tests = {}
            for param, expected in expected_params.items():
                if param == 'grid_size_percent':
                    actual = data.get('grid_size_percent', 0)
                else:
                    actual = account_config.get(param, 0)
                tests[param] = actual == expected
            
            self.test_results['trading_parameters'] = {
                'status': 'PASS' if all(tests.values()) else 'FAIL',
                'tests': tests,
                'parameters': account_config
            }
            
            for test_name, passed in tests.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {test_name}")
                
        except Exception as e:
            self.test_results['trading_parameters'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Error: {e}")
    
    def test_cross_margin_integration(self):
        """Test cross margin calculations."""
        print("\n📈 Testing Cross Margin Integration...")
        
        try:
            response = requests.get(f"{self.base_url}/api/cross_margin_analysis", timeout=5)
            data = response.json()
            accuracy_check = data.get('accuracy_check', {})
            
            tests = {
                'risk_reward_buy': abs(accuracy_check.get('risk_reward_ratio_buy', 0) - 2.5) < 0.1,
                'risk_reward_sell': abs(accuracy_check.get('risk_reward_ratio_sell', 0) - 2.5) < 0.1,
                'position_sizing': data.get('buy_position', {}).get('position_size_btc', 0) > 0,
                'grid_levels': len(data.get('grid_analysis', {}).get('grid_levels', [])) > 0
            }
            
            self.test_results['cross_margin_integration'] = {
                'status': 'PASS' if all(tests.values()) else 'FAIL',
                'tests': tests,
                'accuracy_check': accuracy_check
            }
            
            for test_name, passed in tests.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {test_name}")
                
        except Exception as e:
            self.test_results['cross_margin_integration'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Error: {e}")
    
    def test_signal_generation(self):
        """Test trading signal generation."""
        print("\n🎯 Testing Signal Generation...")
        
        try:
            # Test force trade functionality
            response = requests.post(f"{self.base_url}/api/force_buy_test", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                tests = {
                    'signal_generated': data.get('success', False),
                    'confidence_present': 'confidence' in data,
                    'entry_price_present': 'entry_price' in data,
                    'position_size_present': 'position_size' in data
                }
                
                self.test_results['signal_generation'] = {
                    'status': 'PASS' if all(tests.values()) else 'FAIL',
                    'tests': tests,
                    'signal_data': data
                }
                
                for test_name, passed in tests.items():
                    status = "✅" if passed else "❌"
                    print(f"   {status} {test_name}")
            else:
                self.test_results['signal_generation'] = {
                    'status': 'FAIL',
                    'error': f"HTTP {response.status_code}"
                }
                print(f"   ❌ Signal generation failed: HTTP {response.status_code}")
                
        except Exception as e:
            self.test_results['signal_generation'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Error: {e}")
    
    def test_risk_management(self):
        """Test risk management systems."""
        print("\n🛡️ Testing Risk Management...")
        
        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            data = response.json()
            risk_mgmt = data.get('risk_management', {})
            
            tests = {
                'max_risk_per_trade': risk_mgmt.get('max_risk_per_trade') == 10.0,
                'max_open_positions': 'max_open_positions' in risk_mgmt,
                'stop_loss_enabled': risk_mgmt.get('stop_loss_enabled', False),
                'take_profit_enabled': risk_mgmt.get('take_profit_enabled', False)
            }
            
            self.test_results['risk_management'] = {
                'status': 'PASS' if all(tests.values()) else 'FAIL',
                'tests': tests,
                'risk_config': risk_mgmt
            }
            
            for test_name, passed in tests.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {test_name}")
                
        except Exception as e:
            self.test_results['risk_management'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Error: {e}")
    
    def test_real_time_integration(self):
        """Test real-time data integration."""
        print("\n⏱️ Testing Real-Time Integration...")
        
        try:
            # Get initial data
            response1 = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            data1 = response1.json()
            price1 = data1.get('current_price', 0)
            
            # Wait and get updated data
            time.sleep(2)
            response2 = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            data2 = response2.json()
            price2 = data2.get('current_price', 0)
            
            tests = {
                'price_in_range': 50000 <= price1 <= 200000,
                'price_consistent': abs(price1 - price2) < 1000,  # Allow reasonable variation
                'timestamp_updated': data1.get('last_update') != data2.get('last_update'),
                'model_active': data1.get('model_info', {}).get('model_id') is not None
            }
            
            self.test_results['real_time_integration'] = {
                'status': 'PASS' if all(tests.values()) else 'FAIL',
                'tests': tests,
                'price_data': {
                    'initial_price': price1,
                    'updated_price': price2,
                    'price_change': abs(price2 - price1)
                }
            }
            
            for test_name, passed in tests.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {test_name}")
                
        except Exception as e:
            self.test_results['real_time_integration'] = {
                'status': 'ERROR',
                'error': str(e)
            }
            print(f"   ❌ Error: {e}")
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        print("\n" + "=" * 50)
        print("📋 CYCLE 1 MODEL TEST REPORT")
        print("=" * 50)
        
        # Count results
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r.get('status') == 'PASS')
        failed_tests = sum(1 for r in self.test_results.values() if r.get('status') == 'FAIL')
        error_tests = sum(1 for r in self.test_results.values() if r.get('status') == 'ERROR')
        
        # Overall status
        if error_tests > 0:
            overall_status = 'ERROR'
        elif failed_tests > 0:
            overall_status = 'FAIL'
        else:
            overall_status = 'PASS'
        
        print(f"Overall Status: {overall_status}")
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"🚨 Errors: {error_tests}")
        
        # Detailed results
        print("\nDetailed Results:")
        for test_name, result in self.test_results.items():
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌',
                'ERROR': '🚨'
            }.get(result.get('status'), '❓')
            
            print(f"{status_icon} {test_name.upper()}: {result.get('status')}")
            if result.get('error'):
                print(f"   Error: {result['error']}")
        
        # Final assessment
        print("\n" + "-" * 50)
        if overall_status == 'PASS':
            print("🎉 CYCLE 1 MODEL INTEGRATION: COMPLETE")
            print("✅ TCN-CNN-PPO Conservative Elite Model is fully integrated")
            print("✅ All components are functioning correctly")
            print("✅ Ready for live trading deployment")
        else:
            print("⚠️ CYCLE 1 MODEL INTEGRATION: ISSUES DETECTED")
            print("❌ Some components need attention")
            print("📋 Review failed tests before proceeding")
        
        # Generate report
        report = {
            'timestamp': datetime.now().isoformat(),
            'model_id': 'tcn_cnn_ppo_conservative_v3_20250601_234301',
            'overall_status': overall_status,
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'errors': error_tests
            },
            'detailed_results': self.test_results
        }
        
        return report

def main():
    """Run the Cycle 1 model integration test."""
    tester = Cycle1ModelTest()
    report = tester.run_comprehensive_test()
    
    # Save report
    report_file = f"cycle1_model_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Test report saved to: {report_file}")
    
    # Exit with appropriate code
    if report['overall_status'] == 'PASS':
        return 0
    else:
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
