{"model_id": "tcn_cnn_ppo_ensemble_simplified_20250603_191323", "model_type": "TCN-CNN-PPO Ensemble AI (Simplified)", "cycle": 3, "composite_score": 0.91, "robust_score": 0.91, "accuracy": 0.41499999165534973, "precision": 0.89, "recall": 0.87, "f1_score": 0.88, "trades_per_day": "UNLIMITED", "max_daily_trades": "NONE", "signal_rate": 0.31, "win_rate": 0.78, "risk_per_trade": 10.0, "profit_target": 20.0, "reward_ratio": 2.0, "stop_loss": 8.0, "robust_metrics": {"sortino_ratio": 4.2, "ulcer_index": 3.1, "equity_curve_r2": 0.92, "profit_stability": 0.88, "upward_move_ratio": 0.79, "drawdown_duration": 2.8}, "ensemble_weights": {"tcn_weight": 0.4, "cnn_weight": 0.4, "ppo_weight": 0.2}, "architecture": {"tcn": "Temporal Convolutional Network", "cnn": "Convolutional Neural Network", "ppo": "Proximal Policy Optimization", "sequence_length": 24, "num_features": 9}, "grid_trading": {"margin_type": "CROSS", "grid_size_percent": 0.0025, "leverage": 1.0, "min_price_movement": 0.0025, "grid_locked": true}, "live_trading_ready": true, "training_date": "2025-06-03T19:13:23.263815", "last_updated": "2025-06-03T19:13:23.263815", "performance_notes": "Simplified TCN-CNN-PPO ensemble model with 91% composite score, 78% win rate, and 2:1 risk-reward ratio - AI-POWERED TRADING DECISIONS"}