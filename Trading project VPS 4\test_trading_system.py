#!/usr/bin/env python3
"""
🧪 TRADING SYSTEM TEST SCRIPT
============================
Test the Advanced Retrained Model trading system
"""

import requests
import time
import json

def test_trading_system():
    """Test the trading system functionality"""
    
    print("🧪 TESTING ADVANCED RETRAINED MODEL TRADING SYSTEM")
    print("=" * 60)
    
    base_url = "http://localhost:5001"
    
    try:
        # 1. Test webapp connectivity
        print("1️⃣ Testing webapp connectivity...")
        response = requests.get(f"{base_url}/api/trading_status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Webapp accessible")
            print(f"   📊 Model: {data.get('model_name', 'Unknown')}")
            print(f"   🎯 Targets Achieved: {data.get('targets_achieved', False)}")
            print(f"   🔄 Backtester Active: {data.get('backtester_active', False)}")
            print(f"   🧠 RL Active: {data.get('rl_active', False)}")
            print(f"   🏃 Trading Running: {data.get('is_running', False)}")
        else:
            print(f"   ❌ Webapp not accessible (Status: {response.status_code})")
            return False
        
        # 2. Start trading if not running
        print("\n2️⃣ Starting trading system...")
        if not data.get('is_running', False):
            response = requests.post(f"{base_url}/api/start_trading", timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Trading started: {result.get('message', 'Success')}")
            else:
                print(f"   ❌ Failed to start trading (Status: {response.status_code})")
                return False
        else:
            print("   ✅ Trading already running")
        
        # 3. Monitor for trade activity
        print("\n3️⃣ Monitoring for trade activity...")
        print("   ⏱️ Waiting for trades (will check for 2 minutes)...")
        
        initial_trades = data.get('trades_today', 0)
        start_time = time.time()
        
        while time.time() - start_time < 120:  # Monitor for 2 minutes
            try:
                response = requests.get(f"{base_url}/api/trading_status", timeout=5)
                if response.status_code == 200:
                    current_data = response.json()
                    current_trades = current_data.get('trades_today', 0)
                    
                    if current_trades > initial_trades:
                        print(f"   🎉 NEW TRADE DETECTED! Total today: {current_trades}")
                        
                        # Get recent trades
                        recent_trades = current_data.get('recent_trades', [])
                        if recent_trades:
                            latest_trade = recent_trades[0]
                            print(f"   📊 Latest Trade: {latest_trade.get('direction', 'Unknown')} @ ${latest_trade.get('entry_price', 0):,.2f}")
                            print(f"   💰 P&L: ${latest_trade.get('pnl', 0):.2f}")
                            print(f"   📈 Status: {latest_trade.get('status', 'Unknown')}")
                        
                        return True
                    
                    # Show current status
                    print(f"   🔄 Monitoring... Trades today: {current_trades} | Price: ${current_data.get('current_price', 0):,.2f}")
                
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                print(f"   ⚠️ Error during monitoring: {e}")
                time.sleep(5)
        
        print("   ⏰ Monitoring timeout - no new trades detected in 2 minutes")
        print("   💡 This may be normal if signal conditions aren't met")
        
        # 4. Check system health
        print("\n4️⃣ Checking system health...")
        try:
            response = requests.get(f"{base_url}/api/comprehensive_health_check", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                print(f"   🎯 Overall Status: {health_data.get('overall_status', 'Unknown')}")
                
                checks = health_data.get('checks', {})
                passed = sum(1 for status in checks.values() if status == 'PASS')
                total = len(checks)
                print(f"   ✅ Health Checks: {passed}/{total} passed")
                
                issues = health_data.get('issues', [])
                if issues:
                    print(f"   ⚠️ Issues found: {len(issues)}")
                    for issue in issues[:3]:  # Show first 3 issues
                        print(f"      - {issue}")
                else:
                    print("   ✅ No issues found")
                    
            else:
                print(f"   ❌ Health check failed (Status: {response.status_code})")
        except Exception as e:
            print(f"   ❌ Health check error: {e}")
        
        # 5. Final status
        print("\n5️⃣ Final system status...")
        response = requests.get(f"{base_url}/api/trading_status", timeout=5)
        if response.status_code == 200:
            final_data = response.json()
            print(f"   🏃 Trading Running: {final_data.get('is_running', False)}")
            print(f"   📊 Trades Today: {final_data.get('trades_today', 0)}")
            print(f"   💰 Open Positions: {final_data.get('open_trades', 0)}")
            print(f"   🔄 Backtester Active: {final_data.get('backtester_active', False)}")
            print(f"   🧠 RL System Active: {final_data.get('rl_active', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Main test function"""
    
    success = test_trading_system()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TRADING SYSTEM TEST COMPLETED")
        print("✅ System is operational and ready for trading")
        print("🔄 Advanced Retrained Model with Backtester & RL is active")
    else:
        print("❌ TRADING SYSTEM TEST FAILED")
        print("🔧 Please check system configuration and try again")
    print("=" * 60)

if __name__ == "__main__":
    main()
