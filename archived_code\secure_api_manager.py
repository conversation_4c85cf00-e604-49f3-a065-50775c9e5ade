#!/usr/bin/env python3
"""
Secure API Key Management System
Handles encryption, storage, and validation of API keys for live trading
"""

import os
import json
import base64
import getpass
import hashlib
from pathlib import Path
from typing import Dict, Optional, Tuple
from cryptography.fernet import Fe<PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import requests

class SecureAPIManager:
    """Secure API key management with encryption and validation"""

    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)

        self.encrypted_file = self.config_dir / "api_keys.enc"
        self.salt_file = self.config_dir / "key.salt"

        # Set restrictive permissions on config directory
        os.chmod(self.config_dir, 0o700)

        self._cipher = None
        self._api_keys = {}

    def _derive_key(self, password: str, salt: bytes) -> bytes:
        """Derive encryption key from password"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key

    def _get_or_create_salt(self) -> bytes:
        """Get existing salt or create new one"""
        if self.salt_file.exists():
            with open(self.salt_file, 'rb') as f:
                return f.read()
        else:
            salt = os.urandom(16)
            with open(self.salt_file, 'wb') as f:
                f.write(salt)
            os.chmod(self.salt_file, 0o600)
            return salt

    def setup_encryption(self, password: Optional[str] = None) -> bool:
        """Setup encryption with password"""
        if password is None:
            password = getpass.getpass("Enter password for API key encryption: ")

        salt = self._get_or_create_salt()
        key = self._derive_key(password, salt)
        self._cipher = Fernet(key)

        # Test encryption/decryption
        try:
            test_data = "test"
            encrypted = self._cipher.encrypt(test_data.encode())
            decrypted = self._cipher.decrypt(encrypted).decode()
            return decrypted == test_data
        except Exception:
            return False

    def store_api_keys(self, api_key: str, secret_key: str, testnet: bool = True) -> bool:
        """Store API keys securely"""
        if not self._cipher:
            print("❌ Encryption not setup. Call setup_encryption() first.")
            return False

        # Validate API keys first
        if not self._validate_api_keys(api_key, secret_key, testnet):
            print("❌ API key validation failed")
            return False

        api_data = {
            'api_key': api_key,
            'secret_key': secret_key,
            'testnet': testnet,
            'created_at': str(Path().cwd()),
            'hash': hashlib.sha256(f"{api_key}{secret_key}".encode()).hexdigest()[:16]
        }

        try:
            # Encrypt and store
            json_data = json.dumps(api_data)
            encrypted_data = self._cipher.encrypt(json_data.encode())

            with open(self.encrypted_file, 'wb') as f:
                f.write(encrypted_data)

            os.chmod(self.encrypted_file, 0o600)
            self._api_keys = api_data

            print("✅ API keys stored securely")
            return True

        except Exception as e:
            print(f"❌ Failed to store API keys: {e}")
            return False

    def load_api_keys(self, password: Optional[str] = None) -> bool:
        """Load and decrypt API keys"""
        if not self.encrypted_file.exists():
            print("❌ No encrypted API keys found")
            return False

        if not self._cipher:
            if not self.setup_encryption(password):
                print("❌ Failed to setup encryption")
                return False

        try:
            with open(self.encrypted_file, 'rb') as f:
                encrypted_data = f.read()

            decrypted_data = self._cipher.decrypt(encrypted_data)
            self._api_keys = json.loads(decrypted_data.decode())

            print("✅ API keys loaded successfully")
            return True

        except Exception as e:
            print(f"❌ Failed to load API keys: {e}")
            return False

    def get_api_keys(self) -> Dict[str, str]:
        """Get decrypted API keys"""
        if not self._api_keys:
            return {'api_key': '', 'secret_key': '', 'testnet': True}

        return {
            'api_key': self._api_keys.get('api_key', ''),
            'secret_key': self._api_keys.get('secret_key', ''),
            'testnet': self._api_keys.get('testnet', True)
        }

    def _validate_api_keys(self, api_key: str, secret_key: str, testnet: bool) -> bool:
        """Validate API keys with Binance"""
        try:
            import hmac
            import time

            # Prepare request
            base_url = "https://testnet.binance.vision" if testnet else "https://api.binance.com"
            endpoint = "/api/v3/account"
            timestamp = int(time.time() * 1000)

            # Create signature
            query_string = f"timestamp={timestamp}"
            signature = hmac.new(
                secret_key.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # Make request
            headers = {'X-MBX-APIKEY': api_key}
            url = f"{base_url}{endpoint}?{query_string}&signature={signature}"

            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                print("✅ API keys validated successfully")
                return True
            else:
                print(f"❌ API validation failed: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ API validation error: {e}")
            return False

    def update_api_keys(self, api_key: str, secret_key: str, testnet: bool = True) -> bool:
        """Update existing API keys"""
        return self.store_api_keys(api_key, secret_key, testnet)

    def delete_api_keys(self) -> bool:
        """Delete stored API keys"""
        try:
            if self.encrypted_file.exists():
                os.remove(self.encrypted_file)

            self._api_keys = {}
            print("✅ API keys deleted")
            return True

        except Exception as e:
            print(f"❌ Failed to delete API keys: {e}")
            return False

    def is_configured(self) -> bool:
        """Check if API keys are configured"""
        return bool(self._api_keys and self._api_keys.get('api_key'))

    def get_key_info(self) -> Dict[str, str]:
        """Get non-sensitive key information"""
        if not self._api_keys:
            return {'status': 'Not configured'}

        api_key = self._api_keys.get('api_key', '')
        return {
            'status': 'Configured',
            'api_key_preview': f"{api_key[:8]}...{api_key[-4:]}" if api_key else 'None',
            'testnet': str(self._api_keys.get('testnet', True)),
            'key_hash': self._api_keys.get('hash', 'Unknown')
        }

    def get_webapp_credentials(self) -> Dict[str, Optional[str]]:
        """Get credentials for webapp display (masked)"""
        if not self._api_keys:
            return {"api_key": None, "secret": None, "mode": "testnet"}

        api_key = self._api_keys.get("api_key", "")
        masked_key = f"{api_key[:8]}...{api_key[-8:]}" if len(api_key) > 16 else "Not Set"

        return {
            "api_key": masked_key,
            "secret": "●●●●●●●●" if self._api_keys.get("secret") else "Not Set",
            "mode": self._api_keys.get("mode", "testnet")
        }

    def get_exchange_client(self):
        """Get configured CCXT exchange client"""
        if not self.is_configured():
            return None

        try:
            import ccxt

            # Configure exchange based on mode
            if self._api_keys.get("mode") == "live":
                exchange = ccxt.binance({
                    'apiKey': self._api_keys["api_key"],
                    'secret': self._api_keys["secret"],
                    'sandbox': False,
                    'enableRateLimit': True,
                })
            else:  # testnet
                exchange = ccxt.binance({
                    'apiKey': self._api_keys["api_key"],
                    'secret': self._api_keys["secret"],
                    'sandbox': True,
                    'enableRateLimit': True,
                })

            return exchange

        except Exception as e:
            print(f"❌ Error creating exchange client: {e}")
            return None

    def update_credentials_webapp(self, api_key: str, secret: str, mode: str = "testnet") -> bool:
        """Update credentials via webapp interface"""
        try:
            # Validate inputs
            if not api_key or not secret:
                return False

            # Update credentials
            self._api_keys = {
                "api_key": api_key.strip(),
                "secret": secret.strip(),
                "mode": mode
            }

            # Save to encrypted file
            if self._cipher:
                return self._save_encrypted_keys()

            return True

        except Exception as e:
            print(f"❌ Error updating credentials: {e}")
            return False

class APIKeySetup:
    """Interactive API key setup utility"""

    def __init__(self):
        self.manager = SecureAPIManager()

    def interactive_setup(self) -> bool:
        """Interactive API key setup"""
        print("🔐 Secure API Key Setup")
        print("=" * 40)

        # Check if keys already exist
        if self.manager.encrypted_file.exists():
            print("⚠️ Encrypted API keys already exist.")
            choice = input("Do you want to update them? (y/N): ").lower()
            if choice != 'y':
                return False

        # Get trading mode
        print("\n📊 Trading Mode Selection:")
        print("1. Testnet (Paper Trading) - RECOMMENDED for testing")
        print("2. Live Trading - REAL MONEY")

        while True:
            mode_choice = input("\nSelect mode (1 or 2): ").strip()
            if mode_choice == '1':
                testnet = True
                print("✅ Testnet mode selected (Paper trading)")
                break
            elif mode_choice == '2':
                testnet = False
                print("⚠️ LIVE TRADING mode selected - REAL MONEY AT RISK")
                confirm = input("Are you sure? Type 'YES' to confirm: ")
                if confirm == 'YES':
                    break
                else:
                    print("Returning to mode selection...")
            else:
                print("❌ Invalid choice. Please enter 1 or 2.")

        # Get API credentials
        print(f"\n🔑 Enter your Binance {'Testnet' if testnet else 'Live'} API credentials:")
        print("You can get these from:")
        if testnet:
            print("- Testnet: https://testnet.binance.vision/")
        else:
            print("- Live: https://www.binance.com/en/my/settings/api-management")

        api_key = input("\nAPI Key: ").strip()
        if not api_key:
            print("❌ API key cannot be empty")
            return False

        secret_key = getpass.getpass("Secret Key: ").strip()
        if not secret_key:
            print("❌ Secret key cannot be empty")
            return False

        # Setup encryption
        print("\n🔒 Setting up encryption...")
        if not self.manager.setup_encryption():
            print("❌ Failed to setup encryption")
            return False

        # Store and validate keys
        print("\n✅ Validating API keys...")
        if self.manager.store_api_keys(api_key, secret_key, testnet):
            print("\n🎉 API keys configured successfully!")
            print(f"Mode: {'Testnet (Paper Trading)' if testnet else 'Live Trading (REAL MONEY)'}")
            return True
        else:
            print("\n❌ Failed to configure API keys")
            return False

    def quick_setup(self, api_key: str, secret_key: str, password: str, testnet: bool = True) -> bool:
        """Quick setup for automated deployment"""
        if not self.manager.setup_encryption(password):
            return False

        return self.manager.store_api_keys(api_key, secret_key, testnet)

def main():
    """Main API key management interface"""
    import argparse

    parser = argparse.ArgumentParser(description='Secure API Key Management')
    parser.add_argument('--setup', action='store_true', help='Interactive setup')
    parser.add_argument('--info', action='store_true', help='Show key information')
    parser.add_argument('--delete', action='store_true', help='Delete stored keys')
    parser.add_argument('--validate', action='store_true', help='Validate stored keys')

    args = parser.parse_args()

    if args.setup:
        setup = APIKeySetup()
        success = setup.interactive_setup()
        return success

    elif args.info:
        manager = SecureAPIManager()
        if manager.load_api_keys():
            info = manager.get_key_info()
            print("\n📊 API Key Information:")
            for key, value in info.items():
                print(f"  {key}: {value}")
        return True

    elif args.delete:
        manager = SecureAPIManager()
        confirm = input("⚠️ Delete all stored API keys? Type 'DELETE' to confirm: ")
        if confirm == 'DELETE':
            return manager.delete_api_keys()
        else:
            print("❌ Deletion cancelled")
            return False

    elif args.validate:
        manager = SecureAPIManager()
        if manager.load_api_keys():
            keys = manager.get_api_keys()
            return manager._validate_api_keys(
                keys['api_key'],
                keys['secret_key'],
                keys['testnet']
            )
        return False

    else:
        # Show help
        parser.print_help()
        return True




if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
