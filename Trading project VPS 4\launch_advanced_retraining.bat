@echo off
echo 🚀 LAUNCHING ADVANCED RETRAINING SYSTEM WITH BACKTESTER & RL
echo ================================================================
echo 🎯 Optimization Metric: Composite Score × Net Profit
echo 📊 Targets: >87%% Win Rate, >80%% Composite Score, 5+ Trades/Day
echo 🔄 ALL RESULTS GO THROUGH BACKTESTER WITH RL FEEDBACK
echo ================================================================
echo.

cd /d "%~dp0"

echo 📋 Checking Python environment...
py --version
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

echo.
echo 🔄 Starting Advanced Retraining System...
echo.

py advanced_retraining_with_backtester.py

echo.
echo 📊 Retraining completed. Check the generated HTML report for detailed results.
echo.
pause
