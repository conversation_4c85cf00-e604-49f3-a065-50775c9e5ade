"""
Test Binance Live Price Connection
Verifies that the website can fetch real Bitcoin prices from Binance
"""

import os
import sys
from datetime import datetime

def test_binance_connection():
    """Test Binance connection and live price fetching"""
    print("🔍 TESTING BINANCE LIVE PRICE CONNECTION")
    print("=" * 50)
    
    try:
        # Test 1: Import ccxt
        print("📦 Testing ccxt import...")
        import ccxt
        print("   ✅ ccxt imported successfully")
        
        # Test 2: Create Binance exchange
        print("\n🏦 Testing Binance exchange creation...")
        exchange = ccxt.binance({
            'enableRateLimit': True,
            'options': {'defaultType': 'spot'}
        })
        print("   ✅ Binance exchange created successfully")
        
        # Test 3: Fetch live Bitcoin price
        print("\n💰 Testing live Bitcoin price fetch...")
        ticker = exchange.fetch_ticker('BTC/USDT')
        
        price = ticker['last']
        change_24h = ticker['percentage']
        volume_24h = ticker['quoteVolume']
        high_24h = ticker['high']
        low_24h = ticker['low']
        
        print(f"   ✅ Live price fetched successfully!")
        print(f"   🟠 BTC/USDT: ${price:,.2f}")
        print(f"   📈 24h Change: {change_24h:+.2f}%")
        print(f"   📊 24h High: ${high_24h:,.2f}")
        print(f"   📉 24h Low: ${low_24h:,.2f}")
        print(f"   💰 24h Volume: ${volume_24h:,.0f}")
        print(f"   🕒 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Test 4: Verify data is recent
        print("\n🔍 Testing data freshness...")
        if price > 0 and change_24h is not None:
            print("   ✅ Data appears valid and fresh")
        else:
            print("   ❌ Data appears invalid")
            return False
        
        # Test 5: Test multiple calls (rate limiting)
        print("\n⏱️ Testing rate limiting...")
        for i in range(3):
            ticker2 = exchange.fetch_ticker('BTC/USDT')
            price2 = ticker2['last']
            print(f"   Call {i+1}: ${price2:,.2f}")
        print("   ✅ Rate limiting working properly")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        print("   💡 Install required packages: pip install ccxt")
        return False
    except Exception as e:
        print(f"   ❌ Connection error: {e}")
        print("   💡 Check internet connection and try again")
        return False

def test_webapp_integration():
    """Test the webapp's live price function"""
    print("\n🌐 TESTING WEBAPP INTEGRATION")
    print("=" * 50)
    
    try:
        # Add current directory to path
        sys.path.append(os.path.dirname(__file__))
        
        # Import the webapp function
        from simple_enhanced_webapp import get_live_bitcoin_price
        
        print("📦 Testing webapp price function...")
        btc_data = get_live_bitcoin_price()
        
        print(f"   ✅ Function executed successfully!")
        print(f"   🟠 Price: ${btc_data['price']:,.2f}")
        print(f"   📈 Change: {btc_data['change_24h']:+.2f}%")
        print(f"   🔗 Source: {btc_data['source']}")
        print(f"   🕒 Timestamp: {btc_data['timestamp'].strftime('%H:%M:%S')}")
        
        # Verify data source
        if btc_data['source'] == 'Binance Live API':
            print("   ✅ CONFIRMED: Using real Binance data!")
            return True
        elif 'Error' in btc_data['source']:
            print(f"   ❌ Error in data source: {btc_data['source']}")
            return False
        else:
            print(f"   ⚠️ Using fallback data: {btc_data['source']}")
            return False
            
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Function error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 BINANCE LIVE PRICE VERIFICATION")
    print("=" * 60)
    print("Testing real-time Bitcoin price integration for the website")
    print("=" * 60)
    
    # Test direct Binance connection
    binance_ok = test_binance_connection()
    
    # Test webapp integration
    webapp_ok = test_webapp_integration()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if binance_ok and webapp_ok:
        print("✅ ALL TESTS PASSED!")
        print("🎉 Website will show REAL Bitcoin prices from Binance!")
        print("\n🌐 Integration Status:")
        print("   ✅ Binance API connection: WORKING")
        print("   ✅ Live price fetching: WORKING") 
        print("   ✅ Webapp integration: WORKING")
        print("   ✅ Data source verification: CONFIRMED")
        
        print("\n🚀 Next Steps:")
        print("   1. Start your webapp: streamlit run simple_enhanced_webapp.py --server.port 8502")
        print("   2. Navigate to: http://localhost:8502")
        print("   3. See live Bitcoin prices at the top of the page")
        print("   4. Verify 'Binance Live API' appears as data source")
        print("   5. Prices will auto-refresh every 10 seconds")
        
        return True
        
    else:
        print("❌ SOME TESTS FAILED!")
        print("\n🔧 Issues Found:")
        if not binance_ok:
            print("   ❌ Binance connection failed")
            print("   💡 Check internet connection and install: pip install ccxt")
        if not webapp_ok:
            print("   ❌ Webapp integration failed")
            print("   💡 Check webapp file and dependencies")
        
        print("\n⚠️ Website will use fallback/simulated data")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 VERIFICATION COMPLETE - REAL BINANCE DATA CONFIRMED!")
    else:
        print("⚠️ VERIFICATION FAILED - CHECK ISSUES ABOVE")
    print("=" * 60)
    
    input("\nPress Enter to exit...")
