"""
Grid Trade Manager for Binance
Executes grid trading decisions with cross margin and proper risk management
"""

import os
import sys
import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import json
import pandas as pd

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import grid trading components
from grid_trading_core import GridLevelCalculator, GridTradeManager, GridAction, GridLevel
from grid_feature_engineering import GridFeatureEngineering
from grid_composite_metrics import GridCompositeMetrics

try:
    import ccxt
    CCXT_AVAILABLE = True
except ImportError:
    CCXT_AVAILABLE = False
    logging.warning("CCXT not available - live trading disabled")

class GridBinanceTradeManager:
    """Manages grid trading execution on Binance with cross margin"""

    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('GridBinanceTradeManager')

        # Initialize grid components
        self.grid_calculator = GridLevelCalculator(config)
        self.trade_manager = GridTradeManager(config)
        self.feature_engineer = GridFeatureEngineering(config)
        self.metrics_calculator = GridCompositeMetrics(config)

        # Binance connection
        self.exchange = None
        self.is_connected = False

        # Grid state
        self.current_grid_levels = {}
        self.last_price = 0.0
        self.trading_active = False

        # Performance tracking
        self.session_trades = []
        self.session_start_time = datetime.now()

        if CCXT_AVAILABLE:
            self._initialize_binance()

    def _initialize_binance(self):
        """Initialize Binance connection for cross margin trading"""
        try:
            if not self.config.BINANCE_API_KEY or not self.config.BINANCE_SECRET_KEY:
                raise ValueError("Binance API keys not configured")

            self.exchange = ccxt.binance({
                'apiKey': self.config.BINANCE_API_KEY,
                'secret': self.config.BINANCE_SECRET_KEY,
                'sandbox': getattr(self.config, 'BINANCE_TESTNET', True),
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'margin',  # Cross margin
                    'adjustForTimeDifference': True,
                }
            })

            self.logger.info("Binance connection initialized for cross margin trading")

        except Exception as e:
            self.logger.error(f"Failed to initialize Binance: {e}")
            self.exchange = None

    async def start_grid_trading(self, ml_model=None) -> bool:
        """Start grid trading with ML model"""
        try:
            if not CCXT_AVAILABLE or not self.exchange:
                self.logger.error("Binance connection not available")
                return False

            # Test connection
            if not await self._test_connection():
                return False

            # Get initial price and setup grid
            current_price = await self._get_current_price('BTC/USDT')
            if current_price <= 0:
                self.logger.error("Could not get current BTC price")
                return False

            # Initialize grid levels
            self.current_grid_levels = self.grid_calculator.calculate_grid_levels(current_price)
            self.last_price = current_price

            # Start trading
            self.trading_active = True
            self.session_start_time = datetime.now()

            self.logger.info(f"🟢 Grid trading STARTED at ${current_price:,.2f}")
            self.logger.info(f"Grid levels: {len(self.current_grid_levels)} levels")
            self.logger.info(f"Capital: ${self.config.INITIAL_CAPITAL}")
            self.logger.info(f"Risk per trade: ${self.config.FIXED_RISK_AMOUNT}")

            return True

        except Exception as e:
            self.logger.error(f"Failed to start grid trading: {e}")
            return False

    def stop_grid_trading(self) -> bool:
        """Stop grid trading and close positions"""
        try:
            self.trading_active = False

            # Close all active positions
            closed_trades = self._close_all_positions()

            # Calculate session performance
            session_metrics = self._calculate_session_performance()

            self.logger.info(f"🔴 Grid trading STOPPED")
            self.logger.info(f"Session trades: {len(self.session_trades)}")
            self.logger.info(f"Session P&L: ${session_metrics.get('total_pnl', 0):.2f}")

            return True

        except Exception as e:
            self.logger.error(f"Failed to stop grid trading: {e}")
            return False

    async def process_price_update(self, btc_price: float, eth_price: float, ml_model=None) -> List[Dict]:
        """Process price update and execute grid trading decisions"""

        if not self.trading_active:
            return []

        executed_trades = []

        try:
            # Check for grid level touches
            touched_levels = self.grid_calculator.check_grid_touch(
                btc_price, self.last_price, self.current_grid_levels
            )

            # Process each touched level
            for level in touched_levels:
                # Get ML decision if model available
                if ml_model:
                    action = await self._get_ml_decision(level, btc_price, eth_price, ml_model)
                else:
                    action = self._get_default_action(level, btc_price)

                # Execute trade if action is not HOLD
                if action != GridAction.HOLD:
                    trade_result = await self._execute_grid_trade(level, action, btc_price)
                    if trade_result['success']:
                        executed_trades.append(trade_result)

            # Check for position exits
            exit_trades = await self._check_position_exits(btc_price)
            executed_trades.extend(exit_trades)

            # Update last price
            self.last_price = btc_price

            return executed_trades

        except Exception as e:
            self.logger.error(f"Error processing price update: {e}")
            return []

    async def _get_ml_decision(self, level: GridLevel, btc_price: float,
                              eth_price: float, ml_model) -> GridAction:
        """Get ML model decision for grid level"""
        try:
            # Get recent market data for features
            btc_data = await self._get_recent_ohlcv('BTC/USDT')
            eth_data = await self._get_recent_ohlcv('ETH/USDT')

            if len(btc_data) < 50 or len(eth_data) < 50:
                self.logger.warning("Insufficient data for ML prediction")
                return GridAction.HOLD

            # Get current features
            current_features = self.feature_engineer.get_current_features(btc_data, eth_data)

            # Get ML prediction
            prediction = ml_model.predict(current_features)

            # Convert prediction to GridAction
            if hasattr(prediction, 'item'):
                pred_value = prediction.item()
            else:
                pred_value = prediction[0] if isinstance(prediction, (list, tuple)) else prediction

            if pred_value > 0.5:
                return GridAction.BUY
            elif pred_value < -0.5:
                return GridAction.SELL
            else:
                return GridAction.HOLD

        except Exception as e:
            self.logger.warning(f"ML decision failed, using default: {e}")
            return self._get_default_action(level, btc_price)

    def _get_default_action(self, level: GridLevel, current_price: float) -> GridAction:
        """Get default action when ML model not available"""
        # Simple grid strategy: buy below current price, sell above
        if level.price < current_price * 0.999:  # Buy if level is below current price
            return GridAction.BUY
        elif level.price > current_price * 1.001:  # Sell if level is above current price
            return GridAction.SELL
        else:
            return GridAction.HOLD

    async def _execute_grid_trade(self, level: GridLevel, action: GridAction,
                                 current_price: float) -> Dict:
        """Execute a grid trade on Binance"""
        try:
            # Check if we can open new trade
            if not self.trade_manager.can_open_new_trade():
                return {'success': False, 'error': 'Maximum concurrent trades reached'}

            # Calculate position size
            position_size = self.trade_manager.calculate_position_size(current_price)

            if position_size <= 0:
                return {'success': False, 'error': 'Invalid position size'}

            # Determine order side
            side = 'buy' if action == GridAction.BUY else 'sell'

            # Place market order
            order_result = await self._place_market_order('BTC/USDT', side, position_size)

            if not order_result['success']:
                return order_result

            # Create trade record
            trade_record = self.trade_manager.create_trade_record(
                level, action, position_size, current_price
            )

            # Add to active trades
            self.trade_manager.active_trades[trade_record['trade_id']] = trade_record

            # Log trade
            self.logger.info(f"🎯 Grid trade executed: {action.name} {position_size:.6f} BTC @ ${current_price:.2f}")

            return {
                'success': True,
                'trade_record': trade_record,
                'order_result': order_result
            }

        except Exception as e:
            self.logger.error(f"Failed to execute grid trade: {e}")
            return {'success': False, 'error': str(e)}

    async def _place_market_order(self, symbol: str, side: str, quantity: float) -> Dict:
        """Place market order on Binance"""
        try:
            if not self.exchange:
                return {'success': False, 'error': 'Exchange not connected'}

            # Place order
            order = self.exchange.create_market_order(symbol, side, quantity)

            self.logger.info(f"✅ Order placed: {side} {quantity:.6f} {symbol}")

            return {
                'success': True,
                'order_id': order['id'],
                'filled_quantity': order.get('filled', quantity),
                'average_price': order.get('average', 0),
                'fees': order.get('fees', [])
            }

        except Exception as e:
            self.logger.error(f"Failed to place order: {e}")
            return {'success': False, 'error': str(e)}

    async def _check_position_exits(self, current_price: float) -> List[Dict]:
        """Check and execute position exits"""
        exit_trades = []

        # Check each active trade for exit conditions
        trades_to_close = self.trade_manager.check_exit_conditions(current_price)

        for trade in trades_to_close:
            try:
                # Close position
                close_result = await self._close_position(trade)

                if close_result['success']:
                    # Update trade record
                    trade.update(close_result)
                    self.session_trades.append(trade)
                    exit_trades.append(trade)

                    self.logger.info(f"✅ Position closed: {trade['trade_id']} - "
                                   f"P&L: ${trade['profit_loss']:.2f}")

            except Exception as e:
                self.logger.error(f"Failed to close position {trade['trade_id']}: {e}")

        return exit_trades

    async def _close_position(self, trade: Dict) -> Dict:
        """Close a specific position"""
        try:
            # Determine close side
            close_side = 'sell' if trade['action'] == 'BUY' else 'buy'

            # Place closing order
            close_result = await self._place_market_order(
                'BTC/USDT', close_side, trade['position_size']
            )

            return close_result

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _close_all_positions(self) -> List[Dict]:
        """Close all active positions"""
        closed_trades = []

        for trade_id, trade in list(self.trade_manager.active_trades.items()):
            try:
                # Force close at current market price
                trade['exit_reason'] = 'FORCE_CLOSE'
                trade['status'] = 'CLOSED'

                # Add to session trades
                self.session_trades.append(trade)
                closed_trades.append(trade)

                # Remove from active trades
                del self.trade_manager.active_trades[trade_id]

            except Exception as e:
                self.logger.error(f"Failed to close trade {trade_id}: {e}")

        return closed_trades

    def _calculate_session_performance(self) -> Dict:
        """Calculate performance metrics for current session"""
        if not self.session_trades:
            return {'total_pnl': 0, 'trade_count': 0}

        # Calculate basic metrics
        total_pnl = sum(trade.get('profit_loss', 0) for trade in self.session_trades)
        trade_count = len(self.session_trades)

        # Calculate composite metrics
        composite_metrics = self.metrics_calculator.calculate_composite_score(
            self.session_trades, self.config.INITIAL_CAPITAL
        )

        return {
            'total_pnl': total_pnl,
            'trade_count': trade_count,
            'composite_score': composite_metrics.get('composite_score', 0),
            'win_rate': composite_metrics.get('win_rate', 0),
            'session_duration': (datetime.now() - self.session_start_time).total_seconds() / 3600
        }

    async def _test_connection(self) -> bool:
        """Test Binance connection"""
        try:
            balance = self.exchange.fetch_balance()
            self.logger.info("✅ Binance connection test successful")
            return True
        except Exception as e:
            self.logger.error(f"❌ Binance connection test failed: {e}")
            return False

    async def _get_current_price(self, symbol: str) -> float:
        """Get current market price"""
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            return ticker['last']
        except Exception as e:
            self.logger.error(f"Failed to get price for {symbol}: {e}")
            return 0.0

    async def _get_recent_ohlcv(self, symbol: str, timeframe: str = '1m', limit: int = 100) -> pd.DataFrame:
        """Get recent OHLCV data"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            return df

        except Exception as e:
            self.logger.error(f"Failed to get OHLCV for {symbol}: {e}")
            return pd.DataFrame()

    def get_trading_status(self) -> Dict:
        """Get current trading status"""
        return {
            'trading_active': self.trading_active,
            'connected': self.is_connected,
            'current_price': self.last_price,
            'active_trades': len(self.trade_manager.active_trades),
            'session_trades': len(self.session_trades),
            'grid_levels': len(self.current_grid_levels),
            'session_performance': self._calculate_session_performance()
        }

# Reporting & Export
# This module provides trade/export/reporting pipeline for the grid trading system
# Uses grid_composite_metrics, grid_trading_core, and trade logs

import pandas as pd
from grid_composite_metrics import GridCompositeMetrics
from grid_trading_core import GridTrader

class GridTradingReport:
    def __init__(self, trade_log, equity_curve, metrics):
        self.trade_log = trade_log
        self.equity_curve = equity_curve
        self.metrics = metrics

    def export_csv(self, filename):
        self.trade_log.to_csv(filename)

    def export_json(self, filename):
        self.trade_log.to_json(filename, orient='records')

    def generate_report(self):
        # Generate summary report with composite score, metrics, and trade stats
        report = {
            'composite_score': self.metrics['composite_score'],
            'metrics': self.metrics,
            'trade_count': len(self.trade_log),
            'final_equity': self.equity_curve[-1] if len(self.equity_curve) > 0 else None
        }
        return report
