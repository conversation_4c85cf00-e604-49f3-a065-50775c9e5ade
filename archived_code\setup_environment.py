"""
Environment Setup Script for Grid Trading System
Ensures all dependencies and directories are properly configured
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

def setup_logging():
    """Setup logging for environment setup"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('EnvironmentSetup')

def create_directories():
    """Create necessary directories"""
    logger = logging.getLogger('EnvironmentSetup')
    
    directories = [
        'logs',
        'models', 
        'reports',
        'data',
        'config',
        'audit_backup_20250531_093751'  # Backup directory
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"✅ Directory created/verified: {directory}")

def install_dependencies():
    """Install required dependencies"""
    logger = logging.getLogger('EnvironmentSetup')
    
    try:
        logger.info("📦 Installing dependencies from requirements.txt...")
        
        # Install requirements
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Dependencies installed successfully")
        else:
            logger.error(f"❌ Failed to install dependencies: {result.stderr}")
            return False
            
        # Install additional grid trading specific packages
        additional_packages = [
            'streamlit>=1.15.0',
            'python-dotenv>=0.19.0'
        ]
        
        for package in additional_packages:
            logger.info(f"📦 Installing {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"✅ {package} installed")
            else:
                logger.warning(f"⚠️ Failed to install {package}: {result.stderr}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error installing dependencies: {e}")
        return False

def create_env_file():
    """Create .env file template if it doesn't exist"""
    logger = logging.getLogger('EnvironmentSetup')
    
    env_file = Path('.env')
    
    if not env_file.exists():
        env_template = """# Grid Trading System Environment Variables

# Binance API Configuration
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=True

# Trading Configuration
INITIAL_CAPITAL=300
FIXED_RISK_AMOUNT=10
TARGET_PROFIT=20
GRID_SPACING=0.0025
MAX_CONCURRENT_TRADES=15

# ML Configuration
COMPOSITE_SCORE_THRESHOLD=0.85
TRAINING_DAYS=60
TESTING_DAYS=30

# Logging
LOG_LEVEL=INFO
"""
        
        with open('.env', 'w') as f:
            f.write(env_template)
        
        logger.info("✅ .env file template created")
        logger.warning("⚠️ Please update .env file with your actual API keys!")
    else:
        logger.info("✅ .env file already exists")

def verify_imports():
    """Verify that all critical imports work"""
    logger = logging.getLogger('EnvironmentSetup')
    
    critical_imports = [
        ('pandas', 'pd'),
        ('numpy', 'np'),
        ('sklearn', None),
        ('tensorflow', 'tf'),
        ('torch', None),
        ('ccxt', None),
        ('streamlit', 'st'),
        ('plotly', None),
        ('optuna', None),
        ('joblib', None)
    ]
    
    failed_imports = []
    
    for module, alias in critical_imports:
        try:
            if alias:
                exec(f"import {module} as {alias}")
            else:
                exec(f"import {module}")
            logger.info(f"✅ {module} import successful")
        except ImportError as e:
            logger.error(f"❌ {module} import failed: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        logger.error(f"❌ Failed imports: {failed_imports}")
        logger.error("Please install missing dependencies manually")
        return False
    else:
        logger.info("✅ All critical imports successful")
        return True

def check_system_requirements():
    """Check system requirements"""
    logger = logging.getLogger('EnvironmentSetup')
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        logger.error(f"❌ Python 3.8+ required, found {python_version.major}.{python_version.minor}")
        return False
    else:
        logger.info(f"✅ Python version: {python_version.major}.{python_version.minor}")
    
    # Check available disk space (basic check)
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        free_gb = free // (1024**3)
        
        if free_gb < 2:
            logger.warning(f"⚠️ Low disk space: {free_gb}GB free")
        else:
            logger.info(f"✅ Disk space: {free_gb}GB free")
    except Exception as e:
        logger.warning(f"⚠️ Could not check disk space: {e}")
    
    return True

def run_basic_tests():
    """Run basic functionality tests"""
    logger = logging.getLogger('EnvironmentSetup')
    
    try:
        # Test configuration loading
        sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
        from trading_config import TradingConfig
        
        config = TradingConfig()
        logger.info(f"✅ Configuration loaded: ${config.INITIAL_CAPITAL} capital")
        
        # Test grid components
        from grid_trading_core import GridLevelCalculator
        from grid_feature_engineering import GridFeatureEngineering
        from grid_composite_metrics import GridCompositeMetrics
        
        grid_calc = GridLevelCalculator(config)
        feature_eng = GridFeatureEngineering(config)
        metrics_calc = GridCompositeMetrics(config)
        
        logger.info("✅ Grid trading components loaded successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Basic tests failed: {e}")
        return False

def main():
    """Main setup function"""
    logger = setup_logging()
    
    print("🚀 GRID TRADING SYSTEM ENVIRONMENT SETUP")
    print("=" * 50)
    
    success = True
    
    # Step 1: Check system requirements
    logger.info("🔍 Step 1: Checking system requirements...")
    if not check_system_requirements():
        success = False
    
    # Step 2: Create directories
    logger.info("📁 Step 2: Creating directories...")
    create_directories()
    
    # Step 3: Create environment file
    logger.info("⚙️ Step 3: Setting up environment file...")
    create_env_file()
    
    # Step 4: Install dependencies
    logger.info("📦 Step 4: Installing dependencies...")
    if not install_dependencies():
        success = False
    
    # Step 5: Verify imports
    logger.info("🔍 Step 5: Verifying imports...")
    if not verify_imports():
        success = False
    
    # Step 6: Run basic tests
    logger.info("🧪 Step 6: Running basic tests...")
    if not run_basic_tests():
        success = False
    
    # Summary
    print("\n" + "=" * 50)
    if success:
        print("✅ ENVIRONMENT SETUP COMPLETED SUCCESSFULLY!")
        print("\n🎯 Next Steps:")
        print("1. Update .env file with your Binance API keys")
        print("2. Run: python test_grid_integration.py")
        print("3. Run: python launch_grid_training.py")
        print("4. Run: python deploy_live_trading.py")
    else:
        print("❌ ENVIRONMENT SETUP FAILED!")
        print("\n🔧 Please fix the errors above and run again")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
