#!/usr/bin/env python3
"""
BITCOIN FREEDOM PRODUCTION WEBAPP DEPLOYMENT SCRIPT
==================================================
Deploy the clean, production-ready trading webapp with all issues resolved.
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def check_dependencies():
    """Check and install required dependencies."""
    print("📦 CHECKING DEPENDENCIES...")
    
    dependencies = [
        'flask',
        'requests', 
        'waitress'
    ]
    
    missing = []
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            missing.append(dep)
            print(f"❌ {dep} - missing")
    
    if missing:
        print(f"\n📥 Installing missing dependencies: {', '.join(missing)}")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install'] + missing, 
                         check=True, capture_output=True)
            print("✅ All dependencies installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    
    return True

def verify_files():
    """Verify all required files exist."""
    print("\n🔍 VERIFYING FILES...")
    
    required_files = [
        'live_trading_web_app.py',
        'production_live_trading_app.py',
        'templates/production_live_trading_dashboard.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path} - missing")
    
    if missing_files:
        print(f"\n❌ Missing required files: {missing_files}")
        return False
    
    return True

def create_templates_directory():
    """Ensure templates directory exists."""
    templates_dir = Path('templates')
    templates_dir.mkdir(exist_ok=True)
    print(f"✅ Templates directory: {templates_dir.absolute()}")

def start_production_server():
    """Start the production trading server."""
    print("\n🚀 STARTING PRODUCTION TRADING SERVER...")
    print("=" * 60)
    
    try:
        # Import and start the production server
        from production_live_trading_app import ProductionTradingServer
        
        print("🌐 Production server starting...")
        print("📊 Auto trading will start automatically")
        print("🎯 All issues resolved - ready for live trading")
        print("=" * 60)
        
        server = ProductionTradingServer()
        
        # Open browser after a short delay
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://localhost:5000')
                print("🌐 Browser opened to http://localhost:5000")
            except:
                print("📖 Manual browser access: http://localhost:5000")
        
        import threading
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # Start the server (this will block)
        server.start_server()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("🔧 Falling back to development server...")
        start_development_server()
    except Exception as e:
        print(f"❌ Server error: {e}")
        return False
    
    return True

def start_development_server():
    """Fallback to development server if production fails."""
    print("\n🔧 STARTING DEVELOPMENT SERVER (FALLBACK)...")
    
    try:
        from live_trading_web_app import app, trading_engine, live_trading_loop
        import threading
        
        # Start trading thread manually for development server
        if not trading_engine.is_running:
            trading_engine.is_running = True
            thread = threading.Thread(target=live_trading_loop, daemon=True)
            thread.start()
            time.sleep(1)
            
            if thread.is_alive():
                print("✅ Auto trading started")
            else:
                print("❌ Auto trading failed to start")
        
        print("🌐 Development server starting on http://localhost:5000")
        print("⚠️ Note: Use production server for best performance")
        
        # Start Flask app
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
        
    except Exception as e:
        print(f"❌ Development server failed: {e}")
        return False
    
    return True

def display_deployment_info():
    """Display deployment information."""
    print("\n" + "=" * 60)
    print("🚀 BITCOIN FREEDOM WEBAPP DEPLOYMENT")
    print("=" * 60)
    print("💰 Bitcoin Freedom Trading System")
    print("🤖 TCN-CNN-PPO Ensemble Model (91.4% Score)")
    print("⚡ Production-Ready Threading")
    print("🎨 Clean, Modern UI")
    print("🔧 All Issues Resolved")
    print("=" * 60)

def display_features():
    """Display resolved features."""
    print("\n✅ RESOLVED ISSUES & FEATURES:")
    print("   🔄 Auto trading loop working")
    print("   💰 Correct P&L calculations ($25 profit targets)")
    print("   📊 Proper position sizing (0.038 BTC)")
    print("   🎯 Accurate exit conditions")
    print("   🌐 Production WSGI server")
    print("   🎨 Modern, responsive UI")
    print("   📱 Mobile-friendly design")
    print("   ⚡ Real-time updates")
    print("   🔒 Risk management controls")
    print("   🧪 Test trading functions")

def display_usage_instructions():
    """Display usage instructions."""
    print("\n📋 USAGE INSTRUCTIONS:")
    print("   1. 🌐 Open http://localhost:5000 in your browser")
    print("   2. 🚀 Click 'Start Trading' to begin auto trading")
    print("   3. 🧪 Use test functions to verify system")
    print("   4. ⚠️ Switch to Live Mode only when ready")
    print("   5. 📊 Monitor performance in real-time")
    print("   6. 🛑 Use 'Stop Trading' to halt system")

def main():
    """Main deployment function."""
    display_deployment_info()
    
    # Check system requirements
    if not check_dependencies():
        print("\n❌ DEPLOYMENT FAILED - Dependencies missing")
        return False
    
    # Verify files
    if not verify_files():
        print("\n❌ DEPLOYMENT FAILED - Required files missing")
        return False
    
    # Create directories
    create_templates_directory()
    
    # Display features
    display_features()
    display_usage_instructions()
    
    print("\n🚀 STARTING DEPLOYMENT...")
    time.sleep(2)
    
    # Start the server
    success = start_production_server()
    
    if success:
        print("\n✅ DEPLOYMENT SUCCESSFUL")
    else:
        print("\n❌ DEPLOYMENT FAILED")
    
    return success

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Deployment cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ DEPLOYMENT ERROR: {e}")
        sys.exit(1)
