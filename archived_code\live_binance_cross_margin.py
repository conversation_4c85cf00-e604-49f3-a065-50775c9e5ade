"""
Live Binance Cross Margin Trading Implementation
Direct integration with Binance API for real money cross margin trading
"""

import os
import sys
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional
import ccxt
import pandas as pd

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

class LiveBinanceCrossMarginTrader:
    """Live Binance cross margin trading implementation"""

    def __init__(self, api_key: str, secret_key: str, config: TradingConfig):
        self.config = config
        self.api_key = api_key
        self.secret_key = secret_key
        self.logger = logging.getLogger('LiveBinanceCrossMarginTrader')

        # Initialize Binance exchange
        self.exchange = ccxt.binance({
            'apiKey': api_key,
            'secret': secret_key,
            'sandbox': False,  # LIVE TRADING
            'enableRateLimit': True,
            'options': {
                'defaultType': 'margin',  # Cross margin trading
                'adjustForTimeDifference': True,
            }
        })

        self.is_connected_flag = False
        self.account_info = None

    def test_connection(self) -> bool:
        """Test Binance API connection"""
        try:
            # Test API connection
            self.account_info = self.exchange.fetch_balance()

            # Enable cross margin trading
            self.exchange.sapi_post_margin_account()

            self.is_connected_flag = True
            self.logger.info("✅ Binance cross margin connection successful")
            return True

        except Exception as e:
            self.logger.error(f"❌ Binance connection failed: {e}")
            self.is_connected_flag = False
            return False

    def is_connected(self) -> bool:
        """Check if connected to Binance"""
        return self.is_connected_flag

    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price"""
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            return float(ticker['last'])
        except Exception as e:
            self.logger.error(f"Failed to get price for {symbol}: {e}")
            return None

    def get_cross_margin_balance(self) -> Optional[Dict]:
        """Get cross margin account balance"""
        try:
            balance = self.exchange.sapi_get_margin_account()
            return balance
        except Exception as e:
            self.logger.error(f"Failed to get margin balance: {e}")
            return None

    def get_cross_margin_positions(self) -> Dict:
        """Get current cross margin positions"""
        try:
            positions = self.exchange.sapi_get_margin_account()
            user_assets = positions.get('userAssets', [])

            active_positions = {}
            for asset in user_assets:
                if float(asset.get('netAsset', 0)) != 0:
                    active_positions[asset['asset']] = {
                        'free': float(asset.get('free', 0)),
                        'locked': float(asset.get('locked', 0)),
                        'borrowed': float(asset.get('borrowed', 0)),
                        'interest': float(asset.get('interest', 0)),
                        'netAsset': float(asset.get('netAsset', 0))
                    }

            return active_positions

        except Exception as e:
            self.logger.error(f"Failed to get positions: {e}")
            return {}

    def place_cross_margin_buy_order(self, symbol: str, quantity: float) -> Optional[Dict]:
        """Place cross margin buy order"""
        try:
            # Ensure minimum order size
            if quantity < 0.00001:  # Minimum BTC order size
                self.logger.warning(f"Order quantity too small: {quantity}")
                return None

            # Place market buy order
            order = self.exchange.create_market_buy_order(
                symbol=symbol,
                amount=quantity,
                params={'type': 'margin'}
            )

            self.logger.info(f"✅ BUY order placed: {quantity:.6f} {symbol} - Order ID: {order.get('id')}")
            return order

        except Exception as e:
            self.logger.error(f"Failed to place buy order: {e}")
            return None

    def place_cross_margin_sell_order(self, symbol: str, quantity: float) -> Optional[Dict]:
        """Place cross margin sell order"""
        try:
            # Ensure minimum order size
            if quantity < 0.00001:  # Minimum BTC order size
                self.logger.warning(f"Order quantity too small: {quantity}")
                return None

            # Place market sell order
            order = self.exchange.create_market_sell_order(
                symbol=symbol,
                amount=quantity,
                params={'type': 'margin'}
            )

            self.logger.info(f"✅ SELL order placed: {quantity:.6f} {symbol} - Order ID: {order.get('id')}")
            return order

        except Exception as e:
            self.logger.error(f"Failed to place sell order: {e}")
            return None

    def get_order_status(self, symbol: str, order_id: str) -> Optional[Dict]:
        """Get order status"""
        try:
            order = self.exchange.fetch_order(order_id, symbol)
            return order
        except Exception as e:
            self.logger.error(f"Failed to get order status: {e}")
            return None

    def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel an order"""
        try:
            self.exchange.cancel_order(order_id, symbol)
            self.logger.info(f"✅ Order cancelled: {order_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to cancel order: {e}")
            return False

    def get_trade_history(self, symbol: str, limit: int = 100) -> List[Dict]:
        """Get recent trade history"""
        try:
            trades = self.exchange.fetch_my_trades(symbol, limit=limit)
            return trades
        except Exception as e:
            self.logger.error(f"Failed to get trade history: {e}")
            return []

    def get_market_features(self) -> Dict:
        """Get current market features for ML model"""
        try:
            # Get OHLCV data for features
            ohlcv = self.exchange.fetch_ohlcv('BTC/USDT', '1m', limit=100)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

            # Calculate technical indicators
            df['rsi'] = self._calculate_rsi(df['close'], 5)  # 5-period RSI as specified
            df['sma_20'] = df['close'].rolling(20).mean()
            df['bb_upper'], df['bb_lower'] = self._calculate_bollinger_bands(df['close'], 20)

            # Calculate VWAP
            df['vwap'] = (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()

            # Get ETH/BTC ratio
            eth_price = self.get_current_price('ETH/USDT')
            btc_price = self.get_current_price('BTC/USDT')
            eth_btc_ratio = eth_price / btc_price if eth_price and btc_price else 0

            # Return latest features (using the 4 specified indicators)
            latest = df.iloc[-1]
            return {
                'price': latest['close'],
                'volume': latest['volume'],
                'vwap': latest['vwap'],
                'rsi': latest['rsi'],
                'bb_upper': latest['bb_upper'],
                'bb_lower': latest['bb_lower'],
                'eth_btc_ratio': eth_btc_ratio,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Failed to get market features: {e}")
            return {
                'price': 50000,
                'volume': 1000,
                'vwap': 50000,
                'rsi': 50,
                'bb_upper': 51000,
                'bb_lower': 49000,
                'eth_btc_ratio': 0.06,
                'timestamp': datetime.now().isoformat()
            }

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2):
        """Calculate Bollinger Bands"""
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, lower_band

    def get_account_summary(self) -> Dict:
        """Get comprehensive account summary"""
        try:
            balance = self.get_cross_margin_balance()
            positions = self.get_cross_margin_positions()

            if not balance:
                return {}

            return {
                'total_asset_btc': float(balance.get('totalAssetOfBtc', 0)),
                'total_liability_btc': float(balance.get('totalLiabilityOfBtc', 0)),
                'total_net_asset_btc': float(balance.get('totalNetAssetOfBtc', 0)),
                'margin_level': float(balance.get('marginLevel', 0)),
                'index_price': float(balance.get('indexPrice', 0)),
                'positions': positions,
                'can_trade': balance.get('tradeEnabled', False),
                'can_transfer': balance.get('transferEnabled', False),
                'can_borrow': balance.get('borrowEnabled', False),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Failed to get account summary: {e}")
            return {}

    def emergency_close_all_positions(self) -> bool:
        """Emergency close all open positions"""
        try:
            positions = self.get_cross_margin_positions()

            for asset, position in positions.items():
                if position['netAsset'] > 0:
                    # Close long position
                    symbol = f"{asset}/USDT"
                    self.place_cross_margin_sell_order(symbol, abs(position['netAsset']))
                elif position['netAsset'] < 0:
                    # Close short position
                    symbol = f"{asset}/USDT"
                    self.place_cross_margin_buy_order(symbol, abs(position['netAsset']))

            self.logger.info("🚨 Emergency close all positions executed")
            return True

        except Exception as e:
            self.logger.error(f"Failed to emergency close positions: {e}")
            return False

    def validate_trading_permissions(self) -> Dict:
        """Validate account trading permissions"""
        try:
            account = self.get_cross_margin_balance()
            if not account:
                return {'valid': False, 'error': 'Cannot access account'}

            return {
                'valid': True,
                'can_trade': account.get('tradeEnabled', False),
                'can_borrow': account.get('borrowEnabled', False),
                'can_transfer': account.get('transferEnabled', False),
                'margin_level': float(account.get('marginLevel', 0)),
                'account_type': 'Cross Margin'
            }

        except Exception as e:
            return {'valid': False, 'error': str(e)}
