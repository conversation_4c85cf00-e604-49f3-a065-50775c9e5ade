"""
Final System Test - Comprehensive validation of complete trading system
Tests all components integration and readiness for live trading
"""

import os
import sys
import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Tuple

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import test components
from test_framework import TradingSystemTests
from ml_training_system import TradingMLSystem
from complete_trading_system import CompleteTradingSystem

class FinalSystemValidator:
    """Comprehensive system validation for live trading readiness"""
    
    def __init__(self):
        self.config = TradingConfig()
        self.logger = logging.getLogger('FinalSystemValidator')
        self.test_results = {}
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('final_system_test.log'),
                logging.StreamHandler()
            ]
        )
    
    def run_framework_tests(self) -> bool:
        """Run comprehensive framework tests"""
        try:
            self.logger.info("🧪 Running Framework Tests...")
            
            # Initialize test suite
            test_suite = TradingSystemTests()
            
            # Run all tests
            test_results = test_suite.run_all_tests()
            
            # Analyze results
            total_tests = test_results['total_tests']
            passed_tests = test_results['passed_tests']
            success_rate = (passed_tests / total_tests) * 100
            
            self.test_results['framework'] = {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'success_rate': success_rate,
                'status': 'PASS' if success_rate >= 95 else 'FAIL'
            }
            
            self.logger.info(f"✅ Framework Tests: {passed_tests}/{total_tests} passed ({success_rate:.1f}%)")
            return success_rate >= 95
            
        except Exception as e:
            self.logger.error(f"❌ Framework tests failed: {e}")
            self.test_results['framework'] = {'status': 'ERROR', 'error': str(e)}
            return False
    
    def test_ml_training(self) -> bool:
        """Test ML training system"""
        try:
            self.logger.info("🤖 Testing ML Training System...")
            
            # Initialize ML system
            ml_system = TradingMLSystem(self.config)
            
            # Test data collection
            data_collector = ml_system.data_collector
            test_data = data_collector.collect_historical_data('BTCUSDT', '1h', 10)
            
            if len(test_data) < 5:
                raise Exception("Insufficient test data collected")
            
            # Test feature engineering
            feature_engineer = ml_system.feature_engineer
            features = feature_engineer.create_features(test_data)
            
            if len(features.columns) < 10:
                raise Exception("Insufficient features created")
            
            # Test model training (quick test)
            model_trainer = ml_system.model_trainer
            X, y = model_trainer.prepare_training_data(features)
            
            if len(X) < 5:
                raise Exception("Insufficient training data prepared")
            
            self.test_results['ml_training'] = {
                'data_points': len(test_data),
                'features': len(features.columns),
                'training_samples': len(X),
                'status': 'PASS'
            }
            
            self.logger.info(f"✅ ML Training: {len(test_data)} data points, {len(features.columns)} features")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ ML training test failed: {e}")
            self.test_results['ml_training'] = {'status': 'ERROR', 'error': str(e)}
            return False
    
    def test_api_configuration(self) -> bool:
        """Test API configuration (without requiring actual keys)"""
        try:
            self.logger.info("🔑 Testing API Configuration...")
            
            # Check if API key attributes exist
            has_api_key = hasattr(self.config, 'BINANCE_API_KEY')
            has_secret_key = hasattr(self.config, 'BINANCE_SECRET_KEY')
            has_testnet_setting = hasattr(self.config, 'BINANCE_TESTNET')
            
            # Check secure API manager
            from secure_api_manager import SecureAPIManager
            api_manager = SecureAPIManager()
            
            # Test encryption/decryption capability
            test_data = "test_api_key"
            test_password = "test_password"
            
            encrypted = api_manager._encrypt_data(test_data, test_password)
            decrypted = api_manager._decrypt_data(encrypted, test_password)
            
            encryption_works = (decrypted == test_data)
            
            self.test_results['api_configuration'] = {
                'has_api_key_attr': has_api_key,
                'has_secret_key_attr': has_secret_key,
                'has_testnet_setting': has_testnet_setting,
                'encryption_works': encryption_works,
                'status': 'PASS' if all([has_api_key, has_secret_key, has_testnet_setting, encryption_works]) else 'FAIL'
            }
            
            self.logger.info(f"✅ API Configuration: Encryption works, attributes present")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ API configuration test failed: {e}")
            self.test_results['api_configuration'] = {'status': 'ERROR', 'error': str(e)}
            return False
    
    def test_web_interface(self) -> bool:
        """Test web interface components"""
        try:
            self.logger.info("🌐 Testing Web Interface...")
            
            # Import web app
            from trade_manager_webapp import TradingWebApp
            
            # Initialize web app
            web_app = TradingWebApp(self.config)
            
            # Test database initialization
            if not web_app.trade_db:
                raise Exception("Trade database not initialized")
            
            # Test route registration
            app_routes = [rule.rule for rule in web_app.app.url_map.iter_rules()]
            required_routes = ['/', '/api/status', '/api/trades', '/api/balance']
            
            missing_routes = [route for route in required_routes if route not in app_routes]
            if missing_routes:
                raise Exception(f"Missing routes: {missing_routes}")
            
            self.test_results['web_interface'] = {
                'database_initialized': True,
                'routes_registered': len(app_routes),
                'required_routes_present': len(missing_routes) == 0,
                'status': 'PASS'
            }
            
            self.logger.info(f"✅ Web Interface: {len(app_routes)} routes registered")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Web interface test failed: {e}")
            self.test_results['web_interface'] = {'status': 'ERROR', 'error': str(e)}
            return False
    
    def test_complete_system_integration(self) -> bool:
        """Test complete system integration"""
        try:
            self.logger.info("🔗 Testing Complete System Integration...")
            
            # Test system initialization (without API keys)
            system = CompleteTradingSystem()
            
            # Test component initialization
            if not hasattr(system, 'config'):
                raise Exception("Configuration not loaded")
            
            if not hasattr(system, 'logger'):
                raise Exception("Logging not initialized")
            
            # Test status method
            status = system.get_system_status()
            
            required_status_keys = ['system_initialized', 'trading_active', 'total_trades']
            missing_keys = [key for key in required_status_keys if key not in status]
            
            if missing_keys:
                raise Exception(f"Missing status keys: {missing_keys}")
            
            self.test_results['system_integration'] = {
                'components_initialized': True,
                'status_method_works': True,
                'status_keys_present': len(missing_keys) == 0,
                'status': 'PASS'
            }
            
            self.logger.info(f"✅ System Integration: All components integrated")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ System integration test failed: {e}")
            self.test_results['system_integration'] = {'status': 'ERROR', 'error': str(e)}
            return False
    
    def test_file_structure(self) -> bool:
        """Test required file structure"""
        try:
            self.logger.info("📁 Testing File Structure...")
            
            required_files = [
                'complete_trading_system.py',
                'ml_training_system.py',
                'live_binance_integration.py',
                'trade_manager_webapp.py',
                'secure_api_manager.py',
                'test_framework.py',
                'config/trading_config.py'
            ]
            
            missing_files = []
            for file_path in required_files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)
            
            required_dirs = ['logs', 'models', 'data']
            missing_dirs = []
            for dir_path in required_dirs:
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path, exist_ok=True)
            
            self.test_results['file_structure'] = {
                'required_files_present': len(missing_files) == 0,
                'missing_files': missing_files,
                'directories_created': True,
                'status': 'PASS' if len(missing_files) == 0 else 'FAIL'
            }
            
            if missing_files:
                self.logger.warning(f"⚠️ Missing files: {missing_files}")
            else:
                self.logger.info(f"✅ File Structure: All required files present")
            
            return len(missing_files) == 0
            
        except Exception as e:
            self.logger.error(f"❌ File structure test failed: {e}")
            self.test_results['file_structure'] = {'status': 'ERROR', 'error': str(e)}
            return False
    
    def generate_final_report(self) -> Dict:
        """Generate comprehensive final report"""
        try:
            # Calculate overall status
            test_categories = ['framework', 'ml_training', 'api_configuration', 
                             'web_interface', 'system_integration', 'file_structure']
            
            passed_tests = sum(1 for cat in test_categories 
                             if self.test_results.get(cat, {}).get('status') == 'PASS')
            
            total_tests = len(test_categories)
            overall_success_rate = (passed_tests / total_tests) * 100
            
            # Determine readiness status
            if overall_success_rate >= 95:
                readiness_status = "READY FOR LIVE TRADING"
                readiness_color = "🟢"
            elif overall_success_rate >= 80:
                readiness_status = "MOSTLY READY - MINOR ISSUES"
                readiness_color = "🟡"
            else:
                readiness_status = "NOT READY - MAJOR ISSUES"
                readiness_color = "🔴"
            
            final_report = {
                'timestamp': datetime.now().isoformat(),
                'overall_success_rate': overall_success_rate,
                'passed_tests': passed_tests,
                'total_tests': total_tests,
                'readiness_status': readiness_status,
                'readiness_color': readiness_color,
                'test_results': self.test_results,
                'recommendations': self._generate_recommendations()
            }
            
            return final_report
            
        except Exception as e:
            self.logger.error(f"Failed to generate final report: {e}")
            return {'error': str(e)}
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Check each test category
        for category, results in self.test_results.items():
            if results.get('status') == 'FAIL':
                if category == 'framework':
                    recommendations.append("Fix framework test failures before proceeding")
                elif category == 'ml_training':
                    recommendations.append("Resolve ML training issues")
                elif category == 'api_configuration':
                    recommendations.append("Configure Binance API keys: python secure_api_manager.py --setup")
                elif category == 'web_interface':
                    recommendations.append("Fix web interface initialization issues")
                elif category == 'system_integration':
                    recommendations.append("Resolve system integration problems")
                elif category == 'file_structure':
                    recommendations.append("Ensure all required files are present")
            
            elif results.get('status') == 'ERROR':
                recommendations.append(f"Investigate {category} errors: {results.get('error', 'Unknown error')}")
        
        # General recommendations
        if not recommendations:
            recommendations.extend([
                "System appears ready for live trading",
                "Configure API keys if not already done",
                "Start with small position sizes",
                "Monitor performance closely",
                "Review logs regularly"
            ])
        
        return recommendations

def main():
    """Run final system validation"""
    print("🔍 FINAL SYSTEM VALIDATION")
    print("=" * 60)
    print("Testing complete trading system readiness for live trading...")
    print()
    
    # Initialize validator
    validator = FinalSystemValidator()
    
    # Run all tests
    test_results = []
    
    print("📋 Running Comprehensive Tests...")
    print("-" * 40)
    
    # Test 1: Framework Tests
    result1 = validator.run_framework_tests()
    test_results.append(result1)
    
    # Test 2: ML Training
    result2 = validator.test_ml_training()
    test_results.append(result2)
    
    # Test 3: API Configuration
    result3 = validator.test_api_configuration()
    test_results.append(result3)
    
    # Test 4: Web Interface
    result4 = validator.test_web_interface()
    test_results.append(result4)
    
    # Test 5: System Integration
    result5 = validator.test_complete_system_integration()
    test_results.append(result5)
    
    # Test 6: File Structure
    result6 = validator.test_file_structure()
    test_results.append(result6)
    
    # Generate final report
    print("\n📊 GENERATING FINAL REPORT...")
    print("-" * 40)
    
    final_report = validator.generate_final_report()
    
    # Display results
    print(f"\n{final_report['readiness_color']} FINAL VALIDATION RESULTS")
    print("=" * 60)
    print(f"Overall Success Rate: {final_report['overall_success_rate']:.1f}%")
    print(f"Tests Passed: {final_report['passed_tests']}/{final_report['total_tests']}")
    print(f"System Status: {final_report['readiness_status']}")
    
    print(f"\n📋 DETAILED RESULTS:")
    print("-" * 30)
    for category, results in final_report['test_results'].items():
        status = results.get('status', 'UNKNOWN')
        emoji = "✅" if status == 'PASS' else "❌" if status == 'FAIL' else "⚠️"
        print(f"{emoji} {category.replace('_', ' ').title()}: {status}")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print("-" * 30)
    for i, rec in enumerate(final_report['recommendations'], 1):
        print(f"{i}. {rec}")
    
    # Final status
    if final_report['overall_success_rate'] >= 95:
        print(f"\n🎉 SYSTEM READY FOR LIVE TRADING!")
        print("Next step: python complete_trading_system.py")
    elif final_report['overall_success_rate'] >= 80:
        print(f"\n⚠️ SYSTEM MOSTLY READY - Address minor issues first")
    else:
        print(f"\n❌ SYSTEM NOT READY - Address major issues before proceeding")
    
    return final_report['overall_success_rate'] >= 80

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
