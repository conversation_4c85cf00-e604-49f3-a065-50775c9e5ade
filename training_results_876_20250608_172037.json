{"success": true, "target_score": 0.876, "best_score": 0.9109962876847033, "best_profit": 1450.353851570256, "best_combined_score": 1.3212669746097145, "iterations_completed": 5, "max_iterations": 25, "training_iterations": [{"iteration": 1, "composite_score": 0.6760614257361734, "backtest_results": {"total_trades": 105, "winning_trades": 75, "win_rate": 0.7204245702944694, "total_profit": 680.8014192732838, "final_balance": 980.8014192732838, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 3, "tcn_filters": 64, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:20:34.993011"}, {"iteration": 2, "composite_score": 0.7898622021759577, "backtest_results": {"total_trades": 112, "winning_trades": 85, "win_rate": 0.765944880870383, "total_profit": 866.9539320256056, "final_balance": 1166.9539320256056, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["OPTIMIZE_ENTRY_TIMING", "ENHANCE_RISK_MANAGEMENT"], "timestamp": "2025-06-08T17:20:35.541103"}, {"iteration": 3, "composite_score": 0.8073483248201365, "backtest_results": {"total_trades": 118, "winning_trades": 91, "win_rate": 0.7729393299280547, "total_profit": 785.8898000124752, "final_balance": 1085.8898000124752, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:20:36.157870"}, {"iteration": 4, "composite_score": 0.8660331698020627, "backtest_results": {"total_trades": 96, "winning_trades": 76, "win_rate": 0.7964132679208251, "total_profit": 1195.0626425258465, "final_balance": 1495.0626425258465, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:20:36.662168"}, {"iteration": 5, "composite_score": 0.9109962876847033, "backtest_results": {"total_trades": 110, "winning_trades": 89, "win_rate": 0.8143985150738813, "total_profit": 1450.353851570256, "final_balance": 1750.353851570256, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:20:37.166283"}], "best_model": {"iteration": 5, "composite_score": 0.9109962876847033, "backtest_results": {"total_trades": 110, "winning_trades": 89, "win_rate": 0.8143985150738813, "total_profit": 1450.353851570256, "final_balance": 1750.353851570256, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:20:37.166283"}, "best_profit_model": {"iteration": 5, "composite_score": 0.9109962876847033, "backtest_results": {"total_trades": 110, "winning_trades": 89, "win_rate": 0.8143985150738813, "total_profit": 1450.353851570256, "final_balance": 1750.353851570256, "source": "PURE_OUT_OF_SAMPLE_BACKTEST"}, "hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "rl_improvements": ["MAINTAIN_PERFORMANCE"], "timestamp": "2025-06-08T17:20:37.166283"}, "locked_parameters": {"grid_spacing": 0.0025, "risk_reward_ratio": 2.5, "training_days": 60, "testing_days": 30, "tcn_weight": 0.4, "cnn_weight": 0.4, "ppo_weight": 0.2}, "final_hyperparameters": {"tcn_layers": 4, "tcn_filters": 80, "dropout_rate": 0.2, "learning_rate": 0.0003, "batch_size": 32}, "results_source": "PURE_OUT_OF_SAMPLE_BACKTEST_ONLY", "reinforcement_learning_applied": true, "hyperparameter_optimization_applied": true, "min_trades_per_day": 3, "min_total_trades": 90, "optimization_criteria": "HIGHEST_COMPOSITE_REWARD_AND_HIGHEST_NET_PROFIT"}