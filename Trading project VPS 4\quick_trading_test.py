#!/usr/bin/env python3
"""
Quick Trading Logic Test
"""

import sys
from datetime import datetime
sys.path.append('.')

def test_trading_logic():
    print('🔍 DEEP DIVE: TRADING LOGIC ANALYSIS')
    print('=' * 50)
    
    try:
        from live_trading_web_app import trading_engine
        
        print(f'📊 CURRENT STATUS:')
        print(f'   Trading Running: {trading_engine.is_running}')
        print(f'   Open Trades: {len(trading_engine.open_trades)}')
        print(f'   Max Positions: {trading_engine.max_open_positions}')
        print(f'   Price History: {len(trading_engine.price_history)}')

        # Get current price
        current_price = trading_engine.get_current_btc_price()
        print(f'   Current Price: ${current_price:,.2f}')

        # Check price movement for grid signals
        if len(trading_engine.price_history) >= 2:
            prev_price = trading_engine.price_history[-2]['price']
            price_change = current_price - prev_price
            price_change_percent = abs(price_change) / prev_price
            grid_threshold = 0.0025  # 0.25%
            
            print(f'\n📈 PRICE MOVEMENT ANALYSIS:')
            print(f'   Previous Price: ${prev_price:,.2f}')
            print(f'   Current Price: ${current_price:,.2f}')
            print(f'   Price Change: ${price_change:+.2f}')
            print(f'   Change Percent: {price_change_percent:.4%}')
            print(f'   Grid Threshold: {grid_threshold:.4%} (0.25%)')
            grid_triggered = price_change_percent >= grid_threshold
            print(f'   Grid Triggered: {"✅ YES" if grid_triggered else "❌ NO"} ({price_change_percent:.4%} vs {grid_threshold:.4%})')

        # Test should_enter_trade
        print(f'\n🎯 TRADE ENTRY TEST:')
        should_enter = trading_engine.should_enter_trade()
        print(f'   Should Enter Trade: {"✅ YES" if should_enter else "❌ NO"}')

        if should_enter:
            direction, confidence = trading_engine.generate_trade_signal()
            print(f'   Signal Direction: {direction}')
            print(f'   Signal Confidence: {confidence:.1%}')
        else:
            print(f'   ❌ No trade signal - checking why...')
            
            # Check individual conditions
            open_trades_ok = len(trading_engine.open_trades) < trading_engine.max_open_positions
            print(f'   Open Trades OK: {"✅" if open_trades_ok else "❌"} ({len(trading_engine.open_trades)}/{trading_engine.max_open_positions})')
            
            # Check daily loss limit
            today = datetime.now().date()
            daily_pnl = sum(t.pnl for t in trading_engine.closed_trades
                           if t.exit_timestamp and t.exit_timestamp.date() == today)
            daily_loss_ok = daily_pnl > -trading_engine.daily_loss_limit
            print(f'   Daily Loss OK: {"✅" if daily_loss_ok else "❌"} (P&L: ${daily_pnl:.2f}, Limit: ${-trading_engine.daily_loss_limit:.2f})')
            
            # Check grid signals
            grid_signal = trading_engine.check_grid_trading_signals()
            print(f'   Grid Signal: {"✅" if grid_signal else "❌"} (Price movement: {price_change_percent:.4%})')

        print(f'\n🔍 ISSUE ANALYSIS:')
        if len(trading_engine.price_history) < 2:
            print(f'   ❌ CRITICAL: Insufficient price history ({len(trading_engine.price_history)}/2 minimum)')
        elif len(trading_engine.price_history) >= 2 and price_change_percent < grid_threshold:
            print(f'   ⏳ WAITING: Price movement too small ({price_change_percent:.4%} < {grid_threshold:.4%})')
            print(f'   Need: ${abs(current_price * grid_threshold):,.2f} price movement')
            print(f'   Current: ${abs(price_change):,.2f} price movement')
        else:
            print(f'   ✅ CONDITIONS MET: System should be trading')
            
        # Check if trading loop is actually running
        print(f'\n🔄 TRADING LOOP STATUS:')
        print(f'   Engine Running: {trading_engine.is_running}')
        print(f'   Last Update: {getattr(trading_engine, "last_update_time", "Unknown")}')
        
        return True
        
    except Exception as e:
        print(f'❌ ERROR: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_trading_logic()
