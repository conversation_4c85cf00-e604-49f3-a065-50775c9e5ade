"""
Demo: 5% Per Trade Risk Management
Shows how the 5% per trade option works with cross margin
"""

def calculate_trade_scenarios():
    """Calculate different trading scenarios with 5% risk"""
    print("💰 5% PER TRADE RISK MANAGEMENT DEMO")
    print("=" * 50)
    print("Cross Margin Trading with Dynamic Risk Sizing")
    print("=" * 50)
    
    # Starting scenarios
    scenarios = [
        {"balance": 300.0, "name": "Starting Balance"},
        {"balance": 400.0, "name": "After Some Profits"},
        {"balance": 500.0, "name": "Growing Account"},
        {"balance": 750.0, "name": "Strong Performance"},
        {"balance": 1000.0, "name": "Doubled Account"},
        {"balance": 1500.0, "name": "5x Growth"},
    ]
    
    print("\n📊 RISK CALCULATION BY ACCOUNT SIZE")
    print("-" * 70)
    print(f"{'Account Size':<15} {'Risk (5%)':<12} {'Profit Target':<15} {'RR Ratio':<10} {'Status'}")
    print("-" * 70)
    
    for scenario in scenarios:
        balance = scenario["balance"]
        risk_amount = balance * 0.05  # 5% risk
        profit_target = risk_amount * 2.5  # 2.5:1 reward ratio
        
        # Status based on minimum requirement
        if balance >= 300:
            status = "✅ Eligible"
        else:
            status = "❌ Too Small"
        
        print(f"${balance:<14.2f} ${risk_amount:<11.2f} ${profit_target:<14.2f} {'2.5:1':<9} {status}")
    
    return scenarios

def show_growth_projection():
    """Show how account grows with 5% risk management"""
    print("\n🚀 ACCOUNT GROWTH PROJECTION")
    print("=" * 50)
    print("Assuming 60% win rate with 2.5:1 risk-reward ratio")
    print("=" * 50)
    
    balance = 300.0
    trades_per_day = 4.8  # From ML model
    win_rate = 0.60
    days = 30
    
    print(f"\n📈 30-Day Growth Simulation:")
    print(f"   Starting Balance: ${balance:.2f}")
    print(f"   Trades per Day: {trades_per_day}")
    print(f"   Win Rate: {win_rate:.1%}")
    print(f"   Risk per Trade: 5% of balance")
    print(f"   Reward Ratio: 2.5:1")
    
    daily_balances = []
    
    for day in range(1, days + 1):
        daily_trades = int(trades_per_day)
        
        for trade in range(daily_trades):
            risk_amount = balance * 0.05
            
            # Simulate trade outcome
            import random
            random.seed(day * 100 + trade)  # Consistent results
            
            if random.random() < win_rate:
                # Win: +2.5x risk
                balance += risk_amount * 2.5
            else:
                # Loss: -1x risk
                balance -= risk_amount
        
        daily_balances.append(balance)
        
        # Show weekly progress
        if day % 7 == 0 or day == days:
            profit = balance - 300.0
            roi = (profit / 300.0) * 100
            print(f"   Day {day:2d}: ${balance:7.2f} (${profit:+7.2f}, {roi:+5.1f}%)")
    
    final_profit = balance - 300.0
    final_roi = (final_profit / 300.0) * 100
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"   Final Balance: ${balance:.2f}")
    print(f"   Total Profit: ${final_profit:+.2f}")
    print(f"   ROI: {final_roi:+.1f}%")
    print(f"   Average Daily Growth: {(final_roi / days):.2f}%")

def compare_risk_modes():
    """Compare fixed vs percentage risk modes"""
    print("\n⚖️ FIXED vs PERCENTAGE RISK COMPARISON")
    print("=" * 60)
    
    scenarios = [
        {"balance": 300, "trades": 10},
        {"balance": 500, "trades": 10},
        {"balance": 750, "trades": 10},
        {"balance": 1000, "trades": 10},
    ]
    
    print(f"{'Balance':<10} {'Fixed Risk':<12} {'Fixed Profit':<13} {'5% Risk':<10} {'5% Profit':<12} {'Advantage'}")
    print("-" * 75)
    
    for scenario in scenarios:
        balance = scenario["balance"]
        trades = scenario["trades"]
        
        # Fixed mode
        fixed_risk = 10.0
        fixed_profit = 25.0
        fixed_total = trades * (fixed_profit * 0.6 - fixed_risk * 0.4)  # 60% win rate
        
        # Percentage mode
        pct_risk = balance * 0.05
        pct_profit = pct_risk * 2.5
        pct_total = trades * (pct_profit * 0.6 - pct_risk * 0.4)  # 60% win rate
        
        advantage = "5% Mode" if pct_total > fixed_total else "Fixed Mode"
        advantage_amount = abs(pct_total - fixed_total)
        
        print(f"${balance:<9.0f} ${fixed_risk:<11.2f} ${fixed_profit:<12.2f} ${pct_risk:<9.2f} ${pct_profit:<11.2f} {advantage} (+${advantage_amount:.2f})")

def show_cross_margin_benefits():
    """Explain cross margin benefits"""
    print("\n🔗 CROSS MARGIN BENEFITS")
    print("=" * 40)
    print("✅ **Advantages of Cross Margin Trading:**")
    print("   • Uses entire account balance as collateral")
    print("   • Reduces liquidation risk across all positions")
    print("   • More efficient capital utilization")
    print("   • Better for multiple simultaneous grid trades")
    print("   • Allows larger position sizes with same risk")
    
    print("\n📊 **5% Risk with Cross Margin:**")
    print("   • Risk is calculated on total account balance")
    print("   • Positions can be larger due to cross-collateral")
    print("   • Multiple grid levels can be active simultaneously")
    print("   • Account grows faster as balance increases")
    
    print("\n⚠️ **Requirements:**")
    print("   • Minimum $300 account balance")
    print("   • Cross margin must be enabled")
    print("   • Suitable for experienced traders")
    print("   • Higher profit potential, higher complexity")

def main():
    """Main demo function"""
    print("🎯 ENHANCED GRID TRADING SYSTEM")
    print("5% Per Trade Risk Management Demo")
    print()
    
    # Calculate scenarios
    scenarios = calculate_trade_scenarios()
    
    # Show growth projection
    show_growth_projection()
    
    # Compare modes
    compare_risk_modes()
    
    # Cross margin benefits
    show_cross_margin_benefits()
    
    print("\n" + "=" * 60)
    print("🚀 IMPLEMENTATION READY")
    print("=" * 60)
    print("✅ 5% per trade option added to website")
    print("✅ Cross margin configuration enabled")
    print("✅ Dynamic risk calculation implemented")
    print("✅ Account balance requirements checked")
    print("✅ Risk mode selection in sidebar")
    print("✅ Real-time P&L calculation")
    
    print("\n🌐 **How to Use:**")
    print("   1. Start webapp: streamlit run simple_enhanced_webapp.py --server.port 8502")
    print("   2. Go to Test Trading mode")
    print("   3. Select '5% of account per trade' in Risk Mode")
    print("   4. Ensure account balance is $300+")
    print("   5. Start test trading to see dynamic risk sizing")
    
    print("\n💡 **Key Features:**")
    print("   • Automatic risk calculation (5% of current balance)")
    print("   • 2.5:1 reward ratio maintained")
    print("   • Cross margin trading enabled")
    print("   • Real-time balance updates")
    print("   • Eligibility checking ($300+ required)")
    print("   • Trade history shows risk mode")
    
    print("\n🎉 **Ready for aggressive growth trading!**")

if __name__ == "__main__":
    main()
