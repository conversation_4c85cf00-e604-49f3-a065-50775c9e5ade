#!/usr/bin/env python3
"""
🔄 INTEGRATED UNIVERSAL BACKTESTER FOR REINFORCEMENT LEARNING
============================================================
Built-in backtesting and validation system that becomes part of the algorithm itself
- Real-time performance validation
- Continuous model improvement through RL
- Automatic overfitting detection
- Dynamic model adjustment
- Self-correcting performance metrics
"""

import os
import sys
import json
import random
import math
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod

@dataclass
class PerformanceMetrics:
    """Standardized performance metrics for all models"""
    win_rate: float = 0.0
    profit_factor: float = 0.0
    max_drawdown: float = 0.0
    total_return: float = 0.0
    sharpe_ratio: float = 0.0
    trades_per_day: float = 0.0
    avg_trade_duration: float = 0.0
    confidence_score: float = 0.0
    
    def to_dict(self) -> Dict:
        return {
            'win_rate': self.win_rate,
            'profit_factor': self.profit_factor,
            'max_drawdown': self.max_drawdown,
            'total_return': self.total_return,
            'sharpe_ratio': self.sharpe_ratio,
            'trades_per_day': self.trades_per_day,
            'avg_trade_duration': self.avg_trade_duration,
            'confidence_score': self.confidence_score
        }

class IntegratedBacktestEngine:
    """Core backtesting engine that integrates with any trading model"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.historical_performance = []
        self.current_trades = []
        self.closed_trades = []
        self.equity_curve = []
        self.balance = config.get('starting_balance', 1000.0)
        self.peak_equity = self.balance
        
        # Performance tracking
        self.performance_window = config.get('performance_window', 100)  # Last 100 trades
        self.validation_frequency = config.get('validation_frequency', 24)  # Every 24 hours
        self.last_validation = datetime.now()
        
    def validate_trade_signal(self, signal: Dict, current_price: float, market_data: Dict) -> Tuple[bool, float, str]:
        """Validate a trade signal before execution using integrated backtesting"""
        
        # Quick backtest simulation for this signal
        simulated_outcome = self._simulate_trade_outcome(signal, current_price, market_data)
        
        # Calculate confidence based on recent performance
        recent_performance = self._get_recent_performance()
        
        # Adjust confidence based on market conditions
        market_confidence = self._assess_market_conditions(market_data)
        
        # Combined confidence score
        final_confidence = (simulated_outcome['confidence'] * 0.4 + 
                          recent_performance.confidence_score * 0.4 + 
                          market_confidence * 0.2)
        
        # Decision logic
        should_execute = (
            final_confidence > self.config.get('min_confidence', 0.6) and
            simulated_outcome['expected_profit'] > 0 and
            self._risk_management_check(signal, current_price)
        )
        
        reason = self._generate_decision_reason(should_execute, final_confidence, simulated_outcome)
        
        return should_execute, final_confidence, reason
    
    def record_trade_execution(self, trade: Dict):
        """Record actual trade execution for performance tracking"""
        trade['execution_time'] = datetime.now()
        trade['backtest_prediction'] = trade.get('predicted_outcome', {})
        self.current_trades.append(trade)
    
    def record_trade_close(self, trade_id: str, exit_price: float, exit_reason: str):
        """Record trade closure and update performance metrics"""
        
        # Find and close the trade
        for trade in self.current_trades:
            if trade.get('id') == trade_id:
                trade['exit_price'] = exit_price
                trade['exit_time'] = datetime.now()
                trade['exit_reason'] = exit_reason
                trade['actual_profit'] = self._calculate_trade_profit(trade)
                
                # Move to closed trades
                self.closed_trades.append(trade)
                self.current_trades.remove(trade)
                
                # Update performance metrics
                self._update_performance_metrics(trade)
                
                # Check if validation is needed
                self._check_validation_trigger()
                break
    
    def get_current_performance(self) -> PerformanceMetrics:
        """Get current model performance metrics"""
        return self._calculate_performance_metrics(self.closed_trades[-self.performance_window:])
    
    def _simulate_trade_outcome(self, signal: Dict, current_price: float, market_data: Dict) -> Dict:
        """Simulate trade outcome using historical patterns"""
        
        # Get similar historical scenarios
        similar_scenarios = self._find_similar_market_conditions(market_data)
        
        if not similar_scenarios:
            return {'confidence': 0.5, 'expected_profit': 0, 'risk_score': 0.5}
        
        # Analyze outcomes of similar scenarios
        outcomes = []
        for scenario in similar_scenarios:
            outcome = self._analyze_scenario_outcome(scenario, signal)
            outcomes.append(outcome)
        
        # Calculate expected outcome
        avg_profit = np.mean([o['profit'] for o in outcomes])
        win_rate = np.mean([1 if o['profit'] > 0 else 0 for o in outcomes])
        risk_score = np.std([o['profit'] for o in outcomes]) / abs(avg_profit) if avg_profit != 0 else 1.0
        
        confidence = min(win_rate * (1 - risk_score * 0.5), 0.95)
        
        return {
            'confidence': confidence,
            'expected_profit': avg_profit,
            'risk_score': risk_score,
            'sample_size': len(outcomes)
        }
    
    def _get_recent_performance(self) -> PerformanceMetrics:
        """Get recent performance for confidence calculation"""
        recent_trades = self.closed_trades[-50:]  # Last 50 trades
        return self._calculate_performance_metrics(recent_trades)
    
    def _assess_market_conditions(self, market_data: Dict) -> float:
        """Assess current market conditions for confidence adjustment"""
        
        # Volatility assessment
        volatility = market_data.get('volatility', 0.02)
        volatility_score = 1.0 - min(volatility / 0.05, 1.0)  # Lower volatility = higher confidence
        
        # Trend strength assessment
        trend_strength = abs(market_data.get('trend', 0))
        trend_score = min(trend_strength, 1.0)
        
        # Volume assessment
        volume_ratio = market_data.get('volume_ratio', 1.0)
        volume_score = min(volume_ratio, 2.0) / 2.0
        
        # Combined market confidence
        market_confidence = (volatility_score * 0.4 + trend_score * 0.3 + volume_score * 0.3)
        
        return market_confidence
    
    def _risk_management_check(self, signal: Dict, current_price: float) -> bool:
        """Perform risk management checks"""
        
        # Check position sizing
        position_size = signal.get('position_size', 0)
        max_position = self.balance * self.config.get('max_position_pct', 0.1)
        
        if position_size > max_position:
            return False
        
        # Check correlation with existing trades
        correlation_risk = self._check_correlation_risk(signal)
        if correlation_risk > self.config.get('max_correlation', 0.7):
            return False
        
        # Check drawdown limits
        current_drawdown = (self.peak_equity - self.balance) / self.peak_equity
        if current_drawdown > self.config.get('max_drawdown', 0.2):
            return False
        
        return True
    
    def _check_validation_trigger(self):
        """Check if model validation should be triggered"""
        
        time_since_validation = datetime.now() - self.last_validation
        
        # Trigger validation based on time or trade count
        should_validate = (
            time_since_validation.total_seconds() > self.validation_frequency * 3600 or
            len(self.closed_trades) % 50 == 0  # Every 50 trades
        )
        
        if should_validate:
            self._perform_model_validation()
            self.last_validation = datetime.now()
    
    def _perform_model_validation(self):
        """Perform comprehensive model validation"""
        
        print(f"\n🔍 INTEGRATED MODEL VALIDATION - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Calculate current performance
        current_perf = self.get_current_performance()
        
        # Compare with historical performance
        performance_trend = self._analyze_performance_trend()
        
        # Detect overfitting or degradation
        degradation_analysis = self._detect_performance_degradation()
        
        # Generate recommendations
        recommendations = self._generate_model_recommendations(current_perf, degradation_analysis)
        
        # Log validation results
        validation_result = {
            'timestamp': datetime.now().isoformat(),
            'current_performance': current_perf.to_dict(),
            'performance_trend': performance_trend,
            'degradation_analysis': degradation_analysis,
            'recommendations': recommendations
        }
        
        self.historical_performance.append(validation_result)
        
        # Print summary
        print(f"📊 Current Performance:")
        print(f"   Win Rate: {current_perf.win_rate:.1%}")
        print(f"   Profit Factor: {current_perf.profit_factor:.2f}")
        print(f"   Max Drawdown: {current_perf.max_drawdown:.1%}")
        print(f"   Confidence Score: {current_perf.confidence_score:.1%}")
        
        if degradation_analysis['is_degrading']:
            print(f"⚠️ Performance Degradation Detected!")
            print(f"   Severity: {degradation_analysis['severity']}")
            print(f"   Recommendations: {', '.join(recommendations)}")
        else:
            print(f"✅ Model Performance Stable")
        
        print("=" * 60)
        
        return validation_result
    
    def _calculate_performance_metrics(self, trades: List[Dict]) -> PerformanceMetrics:
        """Calculate performance metrics from trade list"""
        
        if not trades:
            return PerformanceMetrics()
        
        # Basic metrics
        total_trades = len(trades)
        winning_trades = sum(1 for t in trades if t.get('actual_profit', 0) > 0)
        win_rate = winning_trades / total_trades
        
        # Profit metrics
        profits = [t.get('actual_profit', 0) for t in trades]
        total_profit = sum(profits)
        
        winning_profits = [p for p in profits if p > 0]
        losing_profits = [p for p in profits if p < 0]
        
        avg_win = np.mean(winning_profits) if winning_profits else 0
        avg_loss = np.mean(losing_profits) if losing_profits else 0
        profit_factor = abs(sum(winning_profits) / sum(losing_profits)) if losing_profits else float('inf')
        
        # Risk metrics
        returns = np.array(profits) / self.balance
        max_drawdown = self._calculate_max_drawdown(trades)
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        
        # Trading frequency
        if len(trades) >= 2:
            time_span = (trades[-1]['exit_time'] - trades[0]['execution_time']).total_seconds() / 86400
            trades_per_day = total_trades / max(time_span, 1)
        else:
            trades_per_day = 0
        
        # Confidence score based on consistency
        confidence_score = self._calculate_confidence_score(trades)
        
        return PerformanceMetrics(
            win_rate=win_rate,
            profit_factor=profit_factor,
            max_drawdown=max_drawdown,
            total_return=total_profit / self.balance,
            sharpe_ratio=sharpe_ratio,
            trades_per_day=trades_per_day,
            confidence_score=confidence_score
        )
    
    def _calculate_confidence_score(self, trades: List[Dict]) -> float:
        """Calculate model confidence score based on consistency"""
        
        if len(trades) < 10:
            return 0.5  # Low confidence with few trades
        
        # Analyze prediction accuracy
        prediction_accuracy = []
        for trade in trades:
            predicted = trade.get('backtest_prediction', {})
            actual_profit = trade.get('actual_profit', 0)
            
            if predicted:
                predicted_profit = predicted.get('expected_profit', 0)
                if predicted_profit != 0:
                    accuracy = 1 - abs(actual_profit - predicted_profit) / abs(predicted_profit)
                    prediction_accuracy.append(max(0, accuracy))
        
        # Consistency metrics
        profits = [t.get('actual_profit', 0) for t in trades]
        profit_consistency = 1 - (np.std(profits) / abs(np.mean(profits))) if np.mean(profits) != 0 else 0
        
        # Win rate stability
        win_rates = []
        window_size = 20
        for i in range(window_size, len(trades), 5):
            window_trades = trades[i-window_size:i]
            window_win_rate = sum(1 for t in window_trades if t.get('actual_profit', 0) > 0) / window_size
            win_rates.append(window_win_rate)
        
        win_rate_stability = 1 - np.std(win_rates) if len(win_rates) > 1 else 0.5
        
        # Combined confidence score
        if prediction_accuracy:
            confidence = (
                np.mean(prediction_accuracy) * 0.4 +
                max(0, profit_consistency) * 0.3 +
                win_rate_stability * 0.3
            )
        else:
            confidence = (
                max(0, profit_consistency) * 0.5 +
                win_rate_stability * 0.5
            )
        
        return min(confidence, 0.95)  # Cap at 95%
    
    def _detect_performance_degradation(self) -> Dict:
        """Detect if model performance is degrading"""
        
        if len(self.closed_trades) < 100:
            return {'is_degrading': False, 'severity': 'NONE', 'indicators': []}
        
        # Compare recent vs historical performance
        recent_trades = self.closed_trades[-50:]
        historical_trades = self.closed_trades[-200:-50] if len(self.closed_trades) >= 200 else []
        
        if not historical_trades:
            return {'is_degrading': False, 'severity': 'NONE', 'indicators': []}
        
        recent_perf = self._calculate_performance_metrics(recent_trades)
        historical_perf = self._calculate_performance_metrics(historical_trades)
        
        # Check for degradation indicators
        indicators = []
        
        # Win rate degradation
        win_rate_drop = historical_perf.win_rate - recent_perf.win_rate
        if win_rate_drop > 0.1:
            indicators.append('WIN_RATE_DEGRADATION')
        
        # Profit factor degradation
        pf_drop = historical_perf.profit_factor - recent_perf.profit_factor
        if pf_drop > 0.5:
            indicators.append('PROFIT_FACTOR_DEGRADATION')
        
        # Increased drawdown
        dd_increase = recent_perf.max_drawdown - historical_perf.max_drawdown
        if dd_increase > 0.05:
            indicators.append('DRAWDOWN_INCREASE')
        
        # Confidence drop
        conf_drop = historical_perf.confidence_score - recent_perf.confidence_score
        if conf_drop > 0.2:
            indicators.append('CONFIDENCE_DEGRADATION')
        
        # Determine severity
        if len(indicators) >= 3:
            severity = 'CRITICAL'
        elif len(indicators) >= 2:
            severity = 'HIGH'
        elif len(indicators) >= 1:
            severity = 'MEDIUM'
        else:
            severity = 'NONE'
        
        return {
            'is_degrading': len(indicators) > 0,
            'severity': severity,
            'indicators': indicators,
            'win_rate_drop': win_rate_drop,
            'profit_factor_drop': pf_drop,
            'drawdown_increase': dd_increase,
            'confidence_drop': conf_drop
        }
    
    def _generate_model_recommendations(self, current_perf: PerformanceMetrics, degradation: Dict) -> List[str]:
        """Generate recommendations for model improvement"""
        
        recommendations = []
        
        if degradation['is_degrading']:
            if 'WIN_RATE_DEGRADATION' in degradation['indicators']:
                recommendations.append('RETRAIN_MODEL')
                recommendations.append('ADJUST_SIGNAL_THRESHOLD')
            
            if 'PROFIT_FACTOR_DEGRADATION' in degradation['indicators']:
                recommendations.append('OPTIMIZE_RISK_REWARD_RATIO')
                recommendations.append('REVIEW_EXIT_STRATEGY')
            
            if 'DRAWDOWN_INCREASE' in degradation['indicators']:
                recommendations.append('REDUCE_POSITION_SIZE')
                recommendations.append('IMPLEMENT_STRICTER_STOPS')
            
            if 'CONFIDENCE_DEGRADATION' in degradation['indicators']:
                recommendations.append('RECALIBRATE_CONFIDENCE_MODEL')
                recommendations.append('UPDATE_MARKET_REGIME_DETECTION')
        
        # Performance-based recommendations
        if current_perf.win_rate < 0.5:
            recommendations.append('FUNDAMENTAL_MODEL_REVIEW')
        
        if current_perf.profit_factor < 1.2:
            recommendations.append('IMPROVE_TRADE_SELECTION')
        
        if current_perf.max_drawdown > 0.2:
            recommendations.append('ENHANCE_RISK_MANAGEMENT')
        
        return list(set(recommendations))  # Remove duplicates
    
    def get_integration_status(self) -> Dict:
        """Get current integration status and health"""
        
        return {
            'total_trades': len(self.closed_trades),
            'current_balance': self.balance,
            'current_performance': self.get_current_performance().to_dict(),
            'last_validation': self.last_validation.isoformat(),
            'validation_count': len(self.historical_performance),
            'integration_health': 'HEALTHY' if self.get_current_performance().confidence_score > 0.6 else 'DEGRADED'
        }

    def _find_similar_market_conditions(self, current_market: Dict) -> List[Dict]:
        """Find similar historical market conditions for simulation"""
        # Simplified implementation - would use more sophisticated matching
        return []

    def _analyze_scenario_outcome(self, scenario: Dict, signal: Dict) -> Dict:
        """Analyze outcome of similar historical scenario"""
        # Simplified implementation
        return {'profit': random.uniform(-20, 50)}

    def _calculate_trade_profit(self, trade: Dict) -> float:
        """Calculate actual trade profit"""
        entry_price = trade.get('entry_price', 0)
        exit_price = trade.get('exit_price', 0)
        position_size = trade.get('position_size', 0)
        direction = trade.get('direction', 'BUY')

        if direction == 'BUY':
            return (exit_price - entry_price) * position_size
        else:
            return (entry_price - exit_price) * position_size

    def _update_performance_metrics(self, trade: Dict):
        """Update running performance metrics"""
        self.balance += trade.get('actual_profit', 0)
        if self.balance > self.peak_equity:
            self.peak_equity = self.balance

    def _check_correlation_risk(self, signal: Dict) -> float:
        """Check correlation risk with existing trades"""
        # Simplified implementation
        return 0.3

    def _analyze_performance_trend(self) -> Dict:
        """Analyze performance trend over time"""
        if len(self.historical_performance) < 2:
            return {'trend': 'STABLE', 'direction': 'NEUTRAL'}

        recent_scores = [p['current_performance']['confidence_score']
                        for p in self.historical_performance[-5:]]

        if len(recent_scores) >= 2:
            trend_direction = 'IMPROVING' if recent_scores[-1] > recent_scores[0] else 'DECLINING'
        else:
            trend_direction = 'NEUTRAL'

        return {'trend': 'STABLE', 'direction': trend_direction}

    def _calculate_max_drawdown(self, trades: List[Dict]) -> float:
        """Calculate maximum drawdown from trade history"""
        if not trades:
            return 0.0

        running_balance = self.config.get('starting_balance', 1000.0)
        peak = running_balance
        max_dd = 0.0

        for trade in trades:
            running_balance += trade.get('actual_profit', 0)
            if running_balance > peak:
                peak = running_balance
            else:
                drawdown = (peak - running_balance) / peak
                max_dd = max(max_dd, drawdown)

        return max_dd

    def _generate_decision_reason(self, should_execute: bool, confidence: float, simulation: Dict) -> str:
        """Generate human-readable reason for trade decision"""
        if should_execute:
            return f"EXECUTE: Confidence {confidence:.1%}, Expected profit ${simulation['expected_profit']:.2f}"
        else:
            return f"SKIP: Low confidence {confidence:.1%} or negative expected outcome"

class ReinforcementLearningIntegration:
    """Reinforcement Learning integration with backtesting feedback"""

    def __init__(self, backtester: IntegratedBacktestEngine):
        self.backtester = backtester
        self.learning_rate = 0.01
        self.exploration_rate = 0.1
        self.model_parameters = {}
        self.reward_history = []

    def update_model_from_feedback(self, trade_result: Dict):
        """Update model parameters based on trade feedback"""

        # Calculate reward based on actual vs predicted performance
        predicted_profit = trade_result.get('backtest_prediction', {}).get('expected_profit', 0)
        actual_profit = trade_result.get('actual_profit', 0)

        # Reward function: accuracy bonus + profit bonus
        accuracy_reward = 1.0 - abs(predicted_profit - actual_profit) / max(abs(predicted_profit), 1.0)
        profit_reward = 1.0 if actual_profit > 0 else -0.5

        total_reward = accuracy_reward * 0.6 + profit_reward * 0.4
        self.reward_history.append(total_reward)

        # Update model parameters using simple gradient ascent
        self._update_parameters(trade_result, total_reward)

        print(f"🧠 RL Update: Reward {total_reward:.3f}, Accuracy {accuracy_reward:.3f}")

    def _update_parameters(self, trade_result: Dict, reward: float):
        """Update model parameters based on reward"""

        # Extract features from trade
        features = self._extract_trade_features(trade_result)

        # Simple parameter update (would be more sophisticated in practice)
        for feature, value in features.items():
            if feature not in self.model_parameters:
                self.model_parameters[feature] = 0.5

            # Update parameter based on reward
            gradient = reward * value * self.learning_rate
            self.model_parameters[feature] += gradient

            # Keep parameters in reasonable bounds
            self.model_parameters[feature] = max(0.1, min(0.9, self.model_parameters[feature]))

    def _extract_trade_features(self, trade_result: Dict) -> Dict:
        """Extract features from trade for learning"""
        return {
            'confidence': trade_result.get('confidence', 0.5),
            'market_volatility': trade_result.get('market_volatility', 0.02),
            'position_size': trade_result.get('position_size', 0) / 1000.0,  # Normalized
            'time_of_day': datetime.now().hour / 24.0  # Normalized
        }

    def get_adjusted_confidence(self, base_confidence: float, features: Dict) -> float:
        """Get RL-adjusted confidence score"""

        if not self.model_parameters:
            return base_confidence

        # Apply learned adjustments
        adjustment = 0.0
        for feature, value in features.items():
            if feature in self.model_parameters:
                adjustment += self.model_parameters[feature] * value

        # Normalize adjustment
        adjustment = adjustment / len(features) - 0.5  # Center around 0

        # Apply adjustment with bounds
        adjusted_confidence = base_confidence + adjustment * 0.2  # Max 20% adjustment
        return max(0.1, min(0.95, adjusted_confidence))

    def should_explore(self) -> bool:
        """Determine if model should explore (try different parameters)"""
        return random.random() < self.exploration_rate

    def get_learning_status(self) -> Dict:
        """Get current learning status"""

        recent_rewards = self.reward_history[-50:] if len(self.reward_history) >= 50 else self.reward_history

        return {
            'total_updates': len(self.reward_history),
            'avg_recent_reward': np.mean(recent_rewards) if recent_rewards else 0,
            'learning_rate': self.learning_rate,
            'exploration_rate': self.exploration_rate,
            'parameter_count': len(self.model_parameters),
            'learning_trend': 'IMPROVING' if len(recent_rewards) >= 10 and
                           np.mean(recent_rewards[-10:]) > np.mean(recent_rewards[-20:-10]) else 'STABLE'
        }

def create_integrated_backtester(model_config: Dict) -> IntegratedBacktestEngine:
    """Factory function to create integrated backtester for any model"""
    
    default_config = {
        'starting_balance': 1000.0,
        'performance_window': 100,
        'validation_frequency': 24,  # hours
        'min_confidence': 0.6,
        'max_position_pct': 0.1,
        'max_correlation': 0.7,
        'max_drawdown': 0.2
    }
    
    # Merge with model-specific config
    config = {**default_config, **model_config}
    
    return IntegratedBacktestEngine(config)

def main():
    """Example usage of integrated backtester"""
    
    print("🔄 INTEGRATED UNIVERSAL BACKTESTER")
    print("=" * 50)
    print("Creating integrated backtesting system...")
    
    # Example model configuration
    model_config = {
        'model_name': 'Conservative Elite',
        'starting_balance': 1000.0,
        'min_confidence': 0.7,
        'validation_frequency': 12  # Every 12 hours
    }
    
    # Create integrated backtester
    backtester = create_integrated_backtester(model_config)
    
    print(f"✅ Integrated backtester created for: {model_config['model_name']}")
    print(f"📊 Integration Status: {backtester.get_integration_status()}")
    
    return backtester

if __name__ == "__main__":
    main()
