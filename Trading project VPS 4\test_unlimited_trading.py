#!/usr/bin/env python3
"""
UNLIMITED TRADING VERIFICATION TEST
===================================

This script tests that all daily trade restrictions have been completely
removed from the trading system and that unlimited trading frequency is enabled.

Author: Bitcoin Freedom Trading System
Date: 2025-06-03
"""

import os
import sys
import json

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_unlimited_trading():
    """Test that daily trade restrictions have been completely removed."""
    print("🚀 TESTING UNLIMITED TRADING CONFIGURATION")
    print("=" * 60)
    
    try:
        # Test 1: Check metadata file
        print("📋 Test 1: Checking metadata file...")
        metadata_path = os.path.join("models", "webapp_focused_model_metadata.json")
        
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            trades_per_day = metadata.get('trades_per_day', 0)
            max_daily_trades = metadata.get('max_daily_trades', 0)
            performance_notes = metadata.get('performance_notes', '')
            
            print(f"   Trades per day: {trades_per_day}")
            print(f"   Max daily trades: {max_daily_trades}")
            print(f"   Performance notes: {performance_notes}")
            
            if (trades_per_day == "UNLIMITED" and 
                max_daily_trades == "NONE" and 
                "UNLIMITED trading frequency" in performance_notes):
                print("   ✅ PASS: Metadata shows unlimited trading")
            else:
                print("   ❌ FAIL: Metadata still shows trade limits")
                return False
        else:
            print("   ❌ FAIL: Metadata file not found")
            return False
        
        # Test 2: Test trading engine configuration
        print("\n🤖 Test 2: Testing trading engine configuration...")
        try:
            # Import the trading engine
            from live_trading_web_app import LiveTradingEngine, BestCompositeModel
            
            # Create model and engine
            model = BestCompositeModel()
            engine = LiveTradingEngine(model)
            
            # Check if max_daily_trades attribute exists
            if hasattr(engine, 'max_daily_trades'):
                print(f"   ❌ FAIL: max_daily_trades still exists: {engine.max_daily_trades}")
                return False
            else:
                print("   ✅ PASS: max_daily_trades attribute removed")
            
            # Test should_enter_trade method multiple times
            print("\n🎯 Test 3: Testing trade entry frequency...")
            trade_attempts = 0
            successful_entries = 0
            
            # Simulate 100 trade checks
            for i in range(100):
                if engine.should_enter_trade():
                    successful_entries += 1
                trade_attempts += 1
            
            entry_rate = successful_entries / trade_attempts
            print(f"   Trade entry rate: {entry_rate:.1%} ({successful_entries}/{trade_attempts})")
            
            # Should have reasonable entry rate (20-50% for active trading)
            if 0.20 <= entry_rate <= 0.50:
                print("   ✅ PASS: Active trading frequency detected")
            else:
                print(f"   ⚠️ WARNING: Entry rate {entry_rate:.1%} may be too low/high")
            
            # Test 4: Check risk management settings
            print("\n🛡️ Test 4: Testing risk management settings...")
            print(f"   Max open positions: {engine.max_open_positions}")
            print(f"   Daily loss limit: ${engine.daily_loss_limit}")
            
            if engine.max_open_positions > 0 and engine.daily_loss_limit > 0:
                print("   ✅ PASS: Essential risk management preserved")
            else:
                print("   ❌ FAIL: Essential risk management missing")
                return False
            
            # Test 5: Verify no daily trade counting
            print("\n📊 Test 5: Verifying no daily trade counting...")
            
            # Check the should_enter_trade method source for restrictions
            import inspect
            source = inspect.getsource(engine.should_enter_trade)
            
            if "daily_trades" in source.lower() and "max_daily" in source.lower():
                print("   ❌ FAIL: Daily trade counting still present in code")
                return False
            else:
                print("   ✅ PASS: No daily trade counting found")
            
            print("\n🎉 ALL TESTS PASSED!")
            print("🚀 Unlimited trading frequency is properly configured")
            print("🔓 No daily trade restrictions detected")
            return True
            
        except Exception as e:
            print(f"   ❌ FAIL: Error testing trading engine: {e}")
            return False
    
    except Exception as e:
        print(f"❌ FAIL: Error in unlimited trading test: {e}")
        return False

def test_api_response():
    """Test that API responses show unlimited trading."""
    print("\n🌐 TESTING API RESPONSE")
    print("=" * 40)
    
    try:
        import requests
        import time
        
        # Wait a moment for any webapp to be running
        time.sleep(2)
        
        try:
            response = requests.get('http://localhost:5000/api/trading_status', timeout=5)
            if response.status_code == 200:
                data = response.json()
                risk_mgmt = data.get('risk_management', {})
                
                max_daily = risk_mgmt.get('max_daily_trades', 'UNKNOWN')
                daily_target = risk_mgmt.get('daily_profit_target', 'UNKNOWN')
                
                print(f"   Max daily trades: {max_daily}")
                print(f"   Daily profit target: {daily_target}")
                
                if max_daily == 'UNLIMITED' and daily_target == 'NONE':
                    print("   ✅ PASS: API shows unlimited trading")
                    return True
                else:
                    print("   ❌ FAIL: API still shows trade limits")
                    return False
            else:
                print(f"   ⚠️ WARNING: API not available (HTTP {response.status_code})")
                return True  # Don't fail if webapp not running
        except Exception as e:
            print(f"   ⚠️ WARNING: Could not test API: {e}")
            return True  # Don't fail if webapp not running
    
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 UNLIMITED TRADING VERIFICATION")
    print("=" * 60)
    print("Testing that ALL daily trade restrictions have been removed")
    print("=" * 60)
    
    # Test core configuration
    config_success = test_unlimited_trading()
    
    # Test API response
    api_success = test_api_response()
    
    print("\n" + "=" * 60)
    if config_success and api_success:
        print("✅ UNLIMITED TRADING TEST: PASSED")
        print("🚀 Daily trade restrictions completely removed")
        print("🔓 System configured for unlimited trading frequency")
        print("📈 Trading based purely on market opportunities")
    else:
        print("❌ UNLIMITED TRADING TEST: FAILED")
        print("⚠️ Some daily trade restrictions may still exist")
    print("=" * 60)
    
    return config_success and api_success

if __name__ == "__main__":
    main()
