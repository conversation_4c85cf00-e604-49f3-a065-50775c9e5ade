#!/usr/bin/env python3
"""
Check the actual risk-to-reward ratio being used in the system
"""

import sys
import os

def check_config_files():
    """Check all configuration files for risk-reward ratios"""
    print("🔍 CHECKING RISK-REWARD RATIO CONFIGURATION")
    print("=" * 80)
    
    configs_found = []
    
    # Check main trading config
    config_file = "config/trading_config.py"
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            content = f.read()
            if 'REWARD_RATIO' in content:
                for line in content.split('\n'):
                    if 'REWARD_RATIO' in line and not line.strip().startswith('#'):
                        configs_found.append(('trading_config.py', line.strip()))
    
    # Check simple trading executor
    executor_file = "simple_trading_executor.py"
    if os.path.exists(executor_file):
        with open(executor_file, 'r') as f:
            content = f.read()
            for line in content.split('\n'):
                if 'profit_target = risk_amount *' in line:
                    configs_found.append(('simple_trading_executor.py', line.strip()))
    
    print("📊 CONFIGURATION FOUND:")
    for source, config in configs_found:
        print(f"   {source}: {config}")
    
    return configs_found

def test_actual_trading_execution():
    """Test what the actual trading system is using"""
    print("\n🧪 TESTING ACTUAL TRADING EXECUTION")
    print("=" * 80)
    
    try:
        sys.path.append('.')
        from simple_trading_executor import SimpleTradingExecutor
        
        # Create executor
        executor = SimpleTradingExecutor()
        
        # Test signal
        test_signal = {
            'action': 'BUY',
            'price': 105000.0,
            'confidence': 0.85,
            'grid_level': 0
        }
        
        print(f"💹 Testing with signal: {test_signal['action']} @ ${test_signal['price']:,.2f}")
        
        # Execute test trade
        trade = executor.execute_trade(test_signal, risk_mode='fixed')
        
        if trade:
            risk_amount = trade.risk_amount
            profit_target = trade.profit_target
            ratio = profit_target / risk_amount
            
            print(f"\n📊 ACTUAL TRADE EXECUTION RESULTS:")
            print(f"   Risk Amount: ${risk_amount:.2f}")
            print(f"   Profit Target: ${profit_target:.2f}")
            print(f"   Risk:Reward Ratio: 1:{ratio:.1f}")
            
            if ratio == 2.5:
                print(f"   ✅ CONFIRMED: System uses 2.5:1 risk-reward ratio")
            elif ratio == 1.0:
                print(f"   ⚠️  FOUND: System uses 1:1 risk-reward ratio")
            else:
                print(f"   🔍 FOUND: System uses {ratio:.1f}:1 risk-reward ratio")
                
            return ratio
        else:
            print("❌ Failed to execute test trade")
            return None
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return None

def check_webapp_model_config():
    """Check what the webapp model is configured for"""
    print("\n🌐 CHECKING WEBAPP MODEL CONFIGURATION")
    print("=" * 80)
    
    try:
        sys.path.append('.')
        from live_trading_web_app import BestCompositeModel
        
        model = BestCompositeModel()
        
        print(f"📊 WEBAPP MODEL CONFIGURATION:")
        print(f"   Model ID: {model.model_id}")
        print(f"   Risk per Trade: ${model.risk_per_trade:.2f}")
        print(f"   Account Size: ${model.account_size:.2f}")
        
        # Check if model has reward ratio
        if hasattr(model, 'reward_ratio'):
            print(f"   Reward Ratio: {model.reward_ratio}:1")
        
        if hasattr(model, 'profit_target'):
            ratio = model.profit_target / model.risk_per_trade
            print(f"   Profit Target: ${model.profit_target:.2f}")
            print(f"   Calculated Ratio: 1:{ratio:.1f}")
            return ratio
        else:
            print(f"   No explicit profit target found")
            return None
            
    except Exception as e:
        print(f"❌ Webapp model check failed: {e}")
        return None

def check_grid_vs_fixed_targets():
    """Check if system uses grid-based or fixed profit targets"""
    print("\n🎯 GRID VS FIXED PROFIT TARGETS")
    print("=" * 80)
    
    current_price = 105000.0
    grid_spacing = 0.0025  # 0.25%
    
    # Grid-based calculation
    grid_profit = current_price * grid_spacing  # $262.50
    
    # Fixed ratio calculation (from test above)
    fixed_risk = 10.0
    fixed_profit_2_5 = fixed_risk * 2.5  # $25.00
    fixed_profit_1_0 = fixed_risk * 1.0  # $10.00
    
    print(f"💹 Current Price: ${current_price:,.2f}")
    print(f"📊 Grid Spacing: {grid_spacing:.4f} ({grid_spacing*100:.2f}%)")
    print(f"")
    print(f"🎯 PROFIT TARGET COMPARISON:")
    print(f"   Grid-based (0.25%): ${grid_profit:.2f}")
    print(f"   Fixed 1:1 ratio: ${fixed_profit_1_0:.2f}")
    print(f"   Fixed 2.5:1 ratio: ${fixed_profit_2_5:.2f}")
    print(f"")
    
    if abs(grid_profit - fixed_profit_2_5) < 1.0:
        print(f"✅ Grid profit ≈ 2.5:1 fixed profit (similar values)")
    elif abs(grid_profit - fixed_profit_1_0) < 1.0:
        print(f"✅ Grid profit ≈ 1:1 fixed profit (similar values)")
    else:
        print(f"🔍 Grid profit is different from both fixed ratios")
        print(f"   Grid gives {grid_profit/fixed_risk:.1f}:1 effective ratio")

def main():
    """Main analysis function"""
    print("🎯 RISK-REWARD RATIO ANALYSIS")
    print("=" * 80)
    print("Checking what ratio the system actually uses...")
    print("=" * 80)
    
    # Check configuration files
    configs = check_config_files()
    
    # Test actual execution
    actual_ratio = test_actual_trading_execution()
    
    # Check webapp model
    webapp_ratio = check_webapp_model_config()
    
    # Compare grid vs fixed
    check_grid_vs_fixed_targets()
    
    # Final summary
    print("\n" + "=" * 80)
    print("🎯 RISK-REWARD RATIO SUMMARY")
    print("=" * 80)
    
    if actual_ratio:
        if actual_ratio == 2.5:
            print("✅ CONFIRMED: System uses 2.5:1 risk-reward ratio")
            print("   • Risk: $10 per trade")
            print("   • Profit Target: $25 per trade")
            print("   • This is BETTER than 1:1 grid spacing")
        elif actual_ratio == 1.0:
            print("⚠️  FOUND: System uses 1:1 risk-reward ratio")
            print("   • Risk: $10 per trade")
            print("   • Profit Target: $10 per trade")
            print("   • This matches grid spacing exactly")
        else:
            print(f"🔍 FOUND: System uses {actual_ratio:.1f}:1 risk-reward ratio")
    else:
        print("❌ Could not determine actual risk-reward ratio")
    
    print(f"\n💡 IMPLICATIONS:")
    if actual_ratio == 2.5:
        print(f"   • Higher profit targets = fewer winning trades needed")
        print(f"   • 93.2% win rate with 2.5:1 ratio = VERY profitable")
        print(f"   • Break-even win rate: 28.6% (much lower than 93.2%)")
    elif actual_ratio == 1.0:
        print(f"   • Equal risk-reward = 50% win rate needed to break even")
        print(f"   • 93.2% win rate with 1:1 ratio = still profitable")
        print(f"   • More conservative approach")

if __name__ == "__main__":
    main()
