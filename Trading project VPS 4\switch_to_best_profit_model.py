#!/usr/bin/env python3
"""
Switch to the highest profit model trained with real data
"""

import requests
import json

def switch_to_best_model():
    """Switch to the Conservative Elite model (highest profit with real data)"""
    
    print("🔄 Switching to Best Profit Model (Real Data Trained)")
    print("=" * 60)
    
    # First, get available models
    try:
        response = requests.get('http://localhost:5000/api/models', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("📊 Available Models:")
            for key, model in data['models'].items():
                active = "🟢 ACTIVE" if model['active'] else "⚪"
                print(f"{active} {key}: {model['name']} - ${model['net_profit']:.2f} profit")
        else:
            print(f"❌ Failed to get models: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error getting models: {e}")
        return False
    
    # Switch to conservative_elite (highest profit with real data)
    try:
        switch_data = {'model_key': 'conservative_elite'}
        response = requests.post(
            'http://localhost:5000/api/switch_model',
            json=switch_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'success':
                print(f"✅ SUCCESS: {result['message']}")
                print(f"🎯 Active Model: {result['active_model']}")
                return True
            else:
                print(f"❌ Switch failed: {result['error']}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error switching model: {e}")
        return False

def get_model_status():
    """Get current model status"""
    try:
        response = requests.get('http://localhost:5000/api/trading_status', timeout=10)
        if response.status_code == 200:
            data = response.json()
            model_info = data['model_info']
            print(f"\n📊 CURRENT MODEL STATUS:")
            print(f"   Model ID: {model_info['model_id']}")
            print(f"   Composite Score: {model_info['composite_score']}%")
            print(f"   Target Trades/Day: {model_info['target_trades_per_day']}")
            return True
        else:
            print(f"❌ Failed to get status: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting status: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Model Switching Tool")
    print("Switching to Conservative Elite (Highest Profit + Real Data)")
    print()
    
    if switch_to_best_model():
        print("\n🎉 Model switch successful!")
        get_model_status()
    else:
        print("\n❌ Model switch failed!")
        print("💡 Make sure the webapp is running on http://localhost:5000")
