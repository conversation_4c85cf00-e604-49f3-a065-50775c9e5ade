#!/usr/bin/env python3
"""
Simple test to verify Bitcoin Freedom webapp is running and responding
"""

import requests
import json
import time

def test_webapp_connection():
    """Test if the webapp is responding"""
    base_url = "http://localhost:5000"
    
    print("🔍 Testing Bitcoin Freedom webapp connection...")
    
    try:
        # Test main page
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print("✅ Main page accessible")
        else:
            print(f"❌ Main page returned status: {response.status_code}")
            
        # Test API endpoints
        endpoints = ['/api/trading_status', '/api/recent_trades', '/api/health_check']
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint} - OK")
                else:
                    print(f"⚠️ {endpoint} - Status: {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint} - Error: {str(e)}")
                
        print("\n🎯 Webapp connection test complete!")
        
    except Exception as e:
        print(f"❌ Failed to connect to webapp: {str(e)}")
        print("Make sure the webapp is running on http://localhost:5000")

if __name__ == "__main__":
    test_webapp_connection()
