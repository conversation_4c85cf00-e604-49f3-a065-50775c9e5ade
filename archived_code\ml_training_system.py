"""
ML Training System for Trading Bot
Implements actual machine learning model training with real data
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import joblib
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

try:
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.svm import SVC
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import TimeSeriesSplit
    import xgboost as xgb
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️ Installing required ML packages...")

try:
    import ccxt
    CCXT_AVAILABLE = True
except ImportError:
    CCXT_AVAILABLE = False
    print("⚠️ Installing CCXT for Binance data...")

class DataCollector:
    """Real market data collection from Binance"""

    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('DataCollector')

        if CCXT_AVAILABLE:
            self.exchange = ccxt.binance({
                'apiKey': config.BINANCE_API_KEY,
                'secret': config.BINANCE_SECRET_KEY,
                'sandbox': config.BINANCE_TESTNET,
                'enableRateLimit': True,
            })
        else:
            self.exchange = None
            self.logger.warning("CCXT not available, using simulated data")

    def collect_historical_data(self, symbol: str, timeframe: str = '1h', days: int = 100) -> pd.DataFrame:
        """Collect historical OHLCV data"""
        try:
            if self.exchange:
                # Real data collection
                since = self.exchange.milliseconds() - days * 24 * 60 * 60 * 1000
                ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, since)

                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)

                self.logger.info(f"Collected {len(df)} real data points for {symbol}")
                return df
            else:
                # Simulated data for testing
                return self._generate_simulated_data(symbol, days)

        except Exception as e:
            self.logger.error(f"Data collection failed: {e}")
            return self._generate_simulated_data(symbol, days)

    def _generate_simulated_data(self, symbol: str, days: int) -> pd.DataFrame:
        """Generate realistic simulated market data"""
        dates = pd.date_range(end=datetime.now(), periods=days*24, freq='H')

        # Generate realistic price movements
        np.random.seed(42)  # For reproducible results
        returns = np.random.normal(0, 0.02, len(dates))  # 2% volatility

        # Starting prices based on symbol
        if 'BTC' in symbol:
            start_price = 45000
        elif 'ETH' in symbol:
            start_price = 3000
        else:
            start_price = 1.0

        prices = [start_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))

        # Generate OHLCV data
        df = pd.DataFrame(index=dates)
        df['close'] = prices
        df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
        df['high'] = df[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.01, len(df)))
        df['low'] = df[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.01, len(df)))
        df['volume'] = np.random.uniform(1000, 10000, len(df))

        self.logger.info(f"Generated {len(df)} simulated data points for {symbol}")
        return df

class FeatureEngineering:
    """Advanced feature engineering for trading signals"""

    def __init__(self):
        self.logger = logging.getLogger('FeatureEngineering')

    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive trading features"""
        features_df = df.copy()

        # Technical indicators
        features_df = self._add_moving_averages(features_df)
        features_df = self._add_momentum_indicators(features_df)
        features_df = self._add_volatility_indicators(features_df)
        features_df = self._add_volume_indicators(features_df)
        features_df = self._add_price_patterns(features_df)

        # Remove NaN values
        features_df = features_df.dropna()

        self.logger.info(f"Created {len(features_df.columns)} features from {len(df)} data points")
        return features_df

    def _add_moving_averages(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add moving average features"""
        for period in [5, 10, 20, 50]:
            df[f'sma_{period}'] = df['close'].rolling(period).mean()
            df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
            df[f'price_sma_{period}_ratio'] = df['close'] / df[f'sma_{period}']

        return df

    def _add_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add momentum-based features"""
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))

        # MACD
        ema12 = df['close'].ewm(span=12).mean()
        ema26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema12 - ema26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']

        # Rate of Change
        for period in [5, 10, 20]:
            df[f'roc_{period}'] = df['close'].pct_change(period)

        return df

    def _add_volatility_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility-based features"""
        # Bollinger Bands
        sma20 = df['close'].rolling(20).mean()
        std20 = df['close'].rolling(20).std()
        df['bb_upper'] = sma20 + (std20 * 2)
        df['bb_lower'] = sma20 - (std20 * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])

        # Average True Range
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        df['atr'] = true_range.rolling(14).mean()

        return df

    def _add_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based features"""
        # Volume moving averages
        df['volume_sma_20'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma_20']

        # On-Balance Volume
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()

        return df

    def _add_price_patterns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add price pattern features"""
        # Price position in recent range
        for period in [10, 20]:
            high_period = df['high'].rolling(period).max()
            low_period = df['low'].rolling(period).min()
            df[f'price_position_{period}'] = (df['close'] - low_period) / (high_period - low_period)

        # Gap detection
        df['gap'] = df['open'] - df['close'].shift(1)
        df['gap_pct'] = df['gap'] / df['close'].shift(1)

        return df

class MLModelTrainer:
    """Machine Learning model training and validation"""

    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('MLModelTrainer')
        self.models = {}
        self.scalers = {}

        if not SKLEARN_AVAILABLE:
            raise ImportError("Scikit-learn and XGBoost are required for ML training")

    def prepare_training_data(self, features_df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features and labels for training"""
        # Create labels: 1 if price goes up in next period, 0 otherwise
        features_df['future_return'] = features_df['close'].shift(-1) / features_df['close'] - 1
        features_df['label'] = (features_df['future_return'] > 0.001).astype(int)  # 0.1% threshold

        # Remove non-feature columns
        feature_columns = [col for col in features_df.columns
                          if col not in ['open', 'high', 'low', 'close', 'volume', 'future_return', 'label']]

        X = features_df[feature_columns].values
        y = features_df['label'].values

        # Remove rows with NaN
        mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
        X = X[mask]
        y = y[mask]

        self.logger.info(f"Prepared training data: {X.shape[0]} samples, {X.shape[1]} features")
        return X, y

    def train_models(self, X: np.ndarray, y: np.ndarray, symbol: str) -> Dict[str, float]:
        """Train multiple ML models and return performance scores"""

        # Split data for time series (last 30% for testing)
        split_idx = int(len(X) * 0.7)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]

        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        self.scalers[symbol] = scaler

        # Define models to train
        model_configs = {
            'random_forest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
            'gradient_boosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
            'logistic_regression': LogisticRegression(random_state=42, max_iter=1000),
            'xgboost': xgb.XGBClassifier(n_estimators=100, random_state=42, eval_metric='logloss')
        }

        results = {}

        for model_name, model in model_configs.items():
            try:
                self.logger.info(f"Training {model_name} for {symbol}...")

                # Train model
                if model_name == 'logistic_regression':
                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                else:
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)

                # Calculate metrics
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, zero_division=0)
                recall = recall_score(y_test, y_pred, zero_division=0)
                f1 = f1_score(y_test, y_pred, zero_division=0)

                # Calculate composite score (weighted average)
                composite_score = (accuracy * 0.4 + precision * 0.3 + recall * 0.2 + f1 * 0.1)

                results[model_name] = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'composite_score': composite_score,
                    'model': model
                }

                self.logger.info(f"{model_name} - Accuracy: {accuracy:.3f}, Composite: {composite_score:.3f}")

            except Exception as e:
                self.logger.error(f"Failed to train {model_name}: {e}")
                continue

        # Select best model based on composite score
        if results:
            best_model_name = max(results.keys(), key=lambda k: results[k]['composite_score'])
            best_score = results[best_model_name]['composite_score']

            self.models[symbol] = {
                'model': results[best_model_name]['model'],
                'model_name': best_model_name,
                'composite_score': best_score,
                'metrics': results[best_model_name]
            }

            self.logger.info(f"Best model for {symbol}: {best_model_name} (Score: {best_score:.3f})")

            return results
        else:
            raise Exception("No models were successfully trained")

    def save_models(self, symbol: str):
        """Save trained models to disk"""
        if symbol not in self.models:
            raise ValueError(f"No trained model found for {symbol}")

        model_dir = self.config.MODELS_DIR
        os.makedirs(model_dir, exist_ok=True)

        # Save model
        model_path = os.path.join(model_dir, f"{symbol}_model.joblib")
        joblib.dump(self.models[symbol], model_path)

        # Save scaler
        scaler_path = os.path.join(model_dir, f"{symbol}_scaler.joblib")
        joblib.dump(self.scalers[symbol], scaler_path)

        self.logger.info(f"Saved model and scaler for {symbol}")

    def load_models(self, symbol: str):
        """Load trained models from disk"""
        model_dir = self.config.MODELS_DIR

        model_path = os.path.join(model_dir, f"{symbol}_model.joblib")
        scaler_path = os.path.join(model_dir, f"{symbol}_scaler.joblib")

        if os.path.exists(model_path) and os.path.exists(scaler_path):
            self.models[symbol] = joblib.load(model_path)
            self.scalers[symbol] = joblib.load(scaler_path)
            self.logger.info(f"Loaded model and scaler for {symbol}")
            return True
        else:
            self.logger.warning(f"No saved model found for {symbol}")
            return False

# ML Model Pipeline
# This module orchestrates TCN, CNN, PPO, and ensemble training/testing
# Uses grid_feature_engineering and grid_composite_metrics modules

from grid_feature_engineering import generate_features
from grid_composite_metrics import GridCompositeMetrics
from grid_trading_core import simulate_grid_trades

class MLTrainingPipeline:
    def __init__(self, train_data, test_data, config):
        self.train_data = train_data
        self.test_data = test_data
        self.config = config
        self.metrics = GridCompositeMetrics()
        # ...initialize models...

    def train_and_evaluate(self):
        # Feature engineering
        X_train = generate_features(self.train_data)
        X_test = generate_features(self.test_data)
        # ...train TCN, CNN, PPO models...
        # ...ensemble logic...
        # Evaluate on test set using composite reward
        # Save best composite and best net profit models
        # ...save model metadata...
        pass

class TradingMLSystem:
    """Complete ML training system for trading"""

    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('TradingMLSystem')

        self.data_collector = DataCollector(config)
        self.feature_engineer = FeatureEngineering()
        self.model_trainer = MLModelTrainer(config)

        # Setup logging
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format=config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(os.path.join(config.LOGS_DIR, 'ml_training.log')),
                logging.StreamHandler()
            ]
        )

    def train_all_models(self) -> Dict[str, Dict]:
        """Train models for all trading pairs"""
        results = {}

        for symbol in self.config.TRADING_PAIRS:
            try:
                self.logger.info(f"🤖 Starting ML training for {symbol}")

                # Collect data (60 days training + 30 days testing + buffer)
                total_days = self.config.TRAINING_DAYS + self.config.TESTING_DAYS + 10
                df = self.data_collector.collect_historical_data(symbol, '1h', total_days)

                if len(df) < 100:  # Minimum data requirement
                    self.logger.warning(f"Insufficient data for {symbol}, skipping...")
                    continue

                # Feature engineering
                features_df = self.feature_engineer.create_features(df)

                # Prepare training data
                X, y = self.model_trainer.prepare_training_data(features_df)

                # Train models
                model_results = self.model_trainer.train_models(X, y, symbol)

                # Save best model
                self.model_trainer.save_models(symbol)

                results[symbol] = model_results

                # Check if composite score meets threshold
                best_score = self.model_trainer.models[symbol]['composite_score']
                if best_score >= self.config.COMPOSITE_THRESHOLD:
                    self.logger.info(f"✅ {symbol} model meets threshold: {best_score:.3f}")
                else:
                    self.logger.warning(f"⚠️ {symbol} model below threshold: {best_score:.3f}")

            except Exception as e:
                self.logger.error(f"Failed to train model for {symbol}: {e}")
                continue

        return results

    def get_prediction(self, symbol: str, current_data: pd.DataFrame) -> Tuple[float, float]:
        """Get prediction from trained model"""
        if symbol not in self.model_trainer.models:
            if not self.model_trainer.load_models(symbol):
                raise ValueError(f"No trained model available for {symbol}")

        # Engineer features for current data
        features_df = self.feature_engineer.create_features(current_data)

        # Get latest features
        feature_columns = [col for col in features_df.columns
                          if col not in ['open', 'high', 'low', 'close', 'volume', 'future_return', 'label']]

        X = features_df[feature_columns].iloc[-1:].values

        # Scale features if needed
        model_info = self.model_trainer.models[symbol]
        if model_info['model_name'] == 'logistic_regression':
            X = self.model_trainer.scalers[symbol].transform(X)

        # Get prediction
        model = model_info['model']
        prediction = model.predict_proba(X)[0][1]  # Probability of positive class
        confidence = model_info['composite_score']

        return prediction, confidence

def main():
    """Main training execution"""
    config = TradingConfig()

    # Create directories
    os.makedirs(config.MODELS_DIR, exist_ok=True)
    os.makedirs(config.LOGS_DIR, exist_ok=True)

    # Initialize ML system
    ml_system = TradingMLSystem(config)

    print("🤖 Starting ML Model Training System...")
    print("=" * 60)

    # Train all models
    results = ml_system.train_all_models()

    # Summary report
    print("\n📊 TRAINING RESULTS SUMMARY")
    print("=" * 60)

    successful_models = 0
    threshold_met = 0

    for symbol, symbol_results in results.items():
        if symbol_results:
            best_model = max(symbol_results.keys(), key=lambda k: symbol_results[k]['composite_score'])
            best_score = symbol_results[best_model]['composite_score']

            status = "✅ PASS" if best_score >= config.COMPOSITE_THRESHOLD else "❌ FAIL"
            print(f"{symbol}: {best_model} - Score: {best_score:.3f} {status}")

            successful_models += 1
            if best_score >= config.COMPOSITE_THRESHOLD:
                threshold_met += 1

    print(f"\n📈 SUMMARY:")
    print(f"Models Trained: {successful_models}/{len(config.TRADING_PAIRS)}")
    print(f"Threshold Met: {threshold_met}/{successful_models}")
    print(f"Success Rate: {(threshold_met/max(successful_models,1))*100:.1f}%")

    if threshold_met > 0:
        print(f"\n🎉 {threshold_met} models ready for live trading!")
    else:
        print(f"\n⚠️ No models met the {config.COMPOSITE_THRESHOLD} threshold requirement")

if __name__ == "__main__":
    main()
