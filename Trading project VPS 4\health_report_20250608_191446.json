{"timestamp": "2025-06-08T19:14:43.971746", "duration_seconds": 2.666478, "overall_status": "CRITICAL", "total_checks": 25, "issues_count": 10, "warnings_count": 5, "results": {"webapp_connectivity": "HEALTHY", "template_correct": "HEALTHY", "api__api_trading_status": "HEALTHY", "api__api_health_check": "ERROR", "api__api_preflight_check": "ERROR", "api__api_recent_trades": "ERROR", "binance_connection": "ERROR", "account_balance": "ERROR", "price_data": "HEALTHY", "database_file": "HEALTHY", "trades_table": "HEALTHY", "recent_trades_count": 0, "recent_trades_api": "ERROR", "trading_engine_running": "HEALTHY", "model_configuration": "HEALTHY", "model_metrics": "HEALTHY", "risk_management": "HEALTHY", "dashboard_ui": "ERROR", "real_time_updates": "WARNING", "health_check_interaction": "ERROR", "preflight_interaction": "ERROR", "response_time__": 15.***************, "response_time__api_trading_status": 49.**************, "error_handling_404": "HEALTHY", "data_accuracy": "HEALTHY"}, "issues": ["API endpoint /api/health_check returned 404", "API endpoint /api/preflight_check returned 404", "API endpoint /api/recent_trades returned 404", "Binance API connection failed", "Account balance not available", "Recent trades API failed with status 404", "Missing UI elements: Status section, Balance section", "Health check interaction failed: 404", "Preflight check interaction failed: 404", "Health check returned HTTP 404"], "warnings": ["Missing risk parameter: max_open_trades", "Missing risk parameter: risk_per_trade", "Missing risk parameter: leverage", "Real-time timestamps not updating", "Missing data field: account_balance"], "recommendations": ["Check Binance API credentials and network connectivity"]}