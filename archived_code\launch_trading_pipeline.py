"""
Trading Pipeline Launcher
Simplified launcher for the complete trading system
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

def check_system_requirements():
    """Check if all system requirements are met"""
    print("🔍 CHECKING SYSTEM REQUIREMENTS...")
    print("=" * 50)
    
    # Check critical files
    critical_files = [
        'complete_trading_system.py',
        'ml_training_system.py',
        'live_binance_integration.py',
        'trade_manager_webapp.py',
        'config/trading_config.py'
    ]
    
    missing_files = []
    for file in critical_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ Missing critical files: {missing_files}")
        return False
    
    # Check directories
    config = TradingConfig()
    directories = ['logs', 'models', 'data']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"📁 Created directory: {directory}")
        else:
            print(f"✅ Directory exists: {directory}")
    
    print("\n✅ All system requirements met!")
    return True

def check_api_configuration():
    """Check API configuration status"""
    print("\n🔑 CHECKING API CONFIGURATION...")
    print("=" * 50)
    
    try:
        config = TradingConfig()
        
        # Check if API keys are configured
        if hasattr(config, 'BINANCE_API_KEY') and config.BINANCE_API_KEY:
            print("✅ Binance API key configured")
            api_configured = True
        else:
            print("⚠️ Binance API key not configured")
            api_configured = False
        
        if hasattr(config, 'BINANCE_SECRET_KEY') and config.BINANCE_SECRET_KEY:
            print("✅ Binance secret key configured")
        else:
            print("⚠️ Binance secret key not configured")
            api_configured = False
        
        if not api_configured:
            print("\n📝 To configure API keys, run:")
            print("   python secure_api_manager.py --setup")
            print("\n⚠️ System will run in SIMULATION mode without API keys")
        
        return api_configured
        
    except Exception as e:
        print(f"❌ Error checking API configuration: {e}")
        return False

def launch_ml_training():
    """Launch ML model training"""
    print("\n🤖 LAUNCHING ML MODEL TRAINING...")
    print("=" * 50)
    
    try:
        from ml_training_system import TradingMLSystem
        
        config = TradingConfig()
        ml_system = TradingMLSystem(config)
        
        print("📊 Training models for all trading pairs...")
        results = ml_system.train_all_models()
        
        # Check results
        successful_models = 0
        for symbol, symbol_results in results.items():
            if symbol_results:
                best_model = max(symbol_results.keys(), key=lambda k: symbol_results[k]['composite_score'])
                best_score = symbol_results[best_model]['composite_score']
                
                if best_score >= config.COMPOSITE_SCORE_THRESHOLD:
                    print(f"✅ {symbol}: {best_model} - Score: {best_score:.3f}")
                    successful_models += 1
                else:
                    print(f"⚠️ {symbol}: {best_model} - Score: {best_score:.3f} (below threshold)")
        
        if successful_models > 0:
            print(f"\n🎉 ML Training Complete: {successful_models} models ready")
            return True
        else:
            print(f"\n⚠️ No models met threshold, but continuing with available models")
            return True
            
    except Exception as e:
        print(f"❌ ML training failed: {e}")
        return False

def launch_web_interface():
    """Launch web interface"""
    print("\n🌐 LAUNCHING WEB INTERFACE...")
    print("=" * 50)
    
    try:
        from trade_manager_webapp import LiveTradeManager, TradeManagerWebApp
        
        config = TradingConfig()
        
        # Initialize trade manager
        trade_manager = LiveTradeManager(config)
        
        # Initialize web app
        web_app = TradeManagerWebApp(trade_manager)
        
        print("🚀 Starting web interface on port 8081...")
        print("📊 Dashboard URL: http://localhost:8081")
        print("🔧 Features available:")
        print("   • Real-time trading dashboard")
        print("   • Manual trade controls")
        print("   • Performance monitoring")
        print("   • Position management")
        
        # Start web app (this will block)
        web_app.run(host='127.0.0.1', port=8081, debug=False)
        
        return True
        
    except Exception as e:
        print(f"❌ Web interface launch failed: {e}")
        return False

async def launch_complete_system():
    """Launch the complete integrated system"""
    print("\n🚀 LAUNCHING COMPLETE TRADING SYSTEM...")
    print("=" * 50)
    
    try:
        from complete_trading_system import CompleteTradingSystem
        
        # Initialize system
        system = CompleteTradingSystem()
        
        print("📋 Step 1: System Initialization")
        if not await system.initialize_system():
            print("❌ System initialization failed")
            return False
        
        print("🤖 Step 2: ML Model Training")
        if not await system.train_models():
            print("❌ Model training failed")
            return False
        
        print("🌐 Step 3: Starting Web Interface")
        if not system.start_web_interface():
            print("❌ Web interface failed to start")
            return False
        
        print("💰 Step 4: Starting Live Trading")
        if not await system.start_live_trading():
            print("❌ Live trading failed to start")
            return False
        
        print("\n🎉 COMPLETE TRADING SYSTEM OPERATIONAL!")
        print("=" * 60)
        print("📊 System Status:")
        
        status = system.get_system_status()
        print(f"   💰 Account Balance: ${status.get('account_balance', {}).get('total_value_usdt', 0):,.2f}")
        print(f"   🎯 Trading Active: {'Yes' if status['trading_active'] else 'No'}")
        print(f"   📈 Total Trades: {status['total_trades']}")
        print(f"   🌐 Web Interface: http://localhost:8081")
        
        print("\n🔴 Press Ctrl+C to stop trading...")
        
        # Keep system running
        while True:
            await asyncio.sleep(60)
            
            # Print periodic status
            status = system.get_system_status()
            print(f"📊 Status: {status['daily_trades']} trades today, "
                  f"Success rate: {status['success_rate']:.1f}%")
    
    except KeyboardInterrupt:
        print("\n🛑 Stopping trading system...")
        if 'system' in locals():
            system.stop_trading()
        print("✅ Trading system stopped safely")
        return True
    
    except Exception as e:
        print(f"\n❌ System error: {e}")
        if 'system' in locals():
            system.stop_trading()
        return False

def main():
    """Main launcher function"""
    print("🚀 TRADING PIPELINE LAUNCHER")
    print("=" * 60)
    print(f"Launch Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Step 1: Check system requirements
    if not check_system_requirements():
        print("\n❌ System requirements not met. Please fix issues and try again.")
        return False
    
    # Step 2: Check API configuration
    api_configured = check_api_configuration()
    
    # Step 3: Show launch options
    print("\n🎯 LAUNCH OPTIONS:")
    print("=" * 30)
    print("1. 🤖 ML Training Only")
    print("2. 🌐 Web Interface Only")
    print("3. 🚀 Complete Trading System")
    print("4. ❌ Exit")
    
    while True:
        try:
            choice = input("\nSelect option (1-4): ").strip()
            
            if choice == '1':
                print("\n🤖 Launching ML Training...")
                success = launch_ml_training()
                if success:
                    print("\n✅ ML Training completed successfully!")
                else:
                    print("\n❌ ML Training failed!")
                break
                
            elif choice == '2':
                print("\n🌐 Launching Web Interface...")
                success = launch_web_interface()
                break
                
            elif choice == '3':
                if not api_configured:
                    print("\n⚠️ WARNING: API keys not configured!")
                    print("System will run in simulation mode.")
                    confirm = input("Continue anyway? (y/N): ").strip().lower()
                    if confirm != 'y':
                        print("❌ Launch cancelled")
                        break
                
                print("\n🚀 Launching Complete Trading System...")
                success = asyncio.run(launch_complete_system())
                break
                
            elif choice == '4':
                print("\n👋 Goodbye!")
                return True
                
            else:
                print("❌ Invalid choice. Please select 1-4.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            return True
        except Exception as e:
            print(f"\n❌ Error: {e}")
            return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
