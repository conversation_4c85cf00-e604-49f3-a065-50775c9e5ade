"""
Test Mode Validator
Ensures test trading results match live trading logic exactly
"""

import os
import sys
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import asdict

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

from enhanced_test_trading_engine import TestTradingEngine, TestTrade, TradingMode


class TestModeValidator:
    """Validates that test mode matches live trading logic exactly"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.logger = logging.getLogger('TestModeValidator')
        self.validation_results = {}
        
    def validate_trade_logic(self, test_trade: TestTrade, expected_live_trade: Dict) -> Dict:
        """Validate that test trade matches expected live trade logic"""
        validation_result = {
            'trade_id': test_trade.trade_id,
            'validation_time': datetime.now().isoformat(),
            'passed': True,
            'errors': [],
            'warnings': [],
            'details': {}
        }
        
        try:
            # Validate entry price logic
            if abs(test_trade.entry_price - expected_live_trade.get('entry_price', 0)) > 0.01:
                validation_result['errors'].append(
                    f"Entry price mismatch: Test={test_trade.entry_price}, Expected={expected_live_trade.get('entry_price')}"
                )
                validation_result['passed'] = False
            
            # Validate exit price calculation
            if abs(test_trade.exit_price - expected_live_trade.get('exit_price', 0)) > 0.01:
                validation_result['errors'].append(
                    f"Exit price mismatch: Test={test_trade.exit_price}, Expected={expected_live_trade.get('exit_price')}"
                )
                validation_result['passed'] = False
            
            # Validate position sizing
            if abs(test_trade.position_size - expected_live_trade.get('position_size', 0)) > 0.001:
                validation_result['errors'].append(
                    f"Position size mismatch: Test={test_trade.position_size}, Expected={expected_live_trade.get('position_size')}"
                )
                validation_result['passed'] = False
            
            # Validate commission calculation
            if abs(test_trade.commission_total - expected_live_trade.get('commission_total', 0)) > 0.001:
                validation_result['errors'].append(
                    f"Commission mismatch: Test={test_trade.commission_total}, Expected={expected_live_trade.get('commission_total')}"
                )
                validation_result['passed'] = False
            
            # Validate risk amount
            if abs(test_trade.risk_amount - self.config.FIXED_RISK_AMOUNT) > 0.001:
                validation_result['errors'].append(
                    f"Risk amount mismatch: Test={test_trade.risk_amount}, Expected={self.config.FIXED_RISK_AMOUNT}"
                )
                validation_result['passed'] = False
            
            # Validate target profit
            if abs(test_trade.target_profit - self.config.TARGET_PROFIT) > 0.001:
                validation_result['errors'].append(
                    f"Target profit mismatch: Test={test_trade.target_profit}, Expected={self.config.TARGET_PROFIT}"
                )
                validation_result['passed'] = False
            
            # Store validation details
            validation_result['details'] = {
                'test_trade': asdict(test_trade),
                'expected_live_trade': expected_live_trade,
                'config_values': {
                    'fixed_risk_amount': self.config.FIXED_RISK_AMOUNT,
                    'target_profit': self.config.TARGET_PROFIT,
                    'grid_spacing': self.config.GRID_SPACING,
                    'commission_rate': 0.001
                }
            }
            
        except Exception as e:
            validation_result['errors'].append(f"Validation error: {str(e)}")
            validation_result['passed'] = False
            self.logger.error(f"Error validating trade {test_trade.trade_id}: {e}")
        
        return validation_result
    
    def validate_session_metrics(self, test_session: Dict, expected_live_session: Dict) -> Dict:
        """Validate that test session metrics match live trading metrics"""
        validation_result = {
            'session_validation_time': datetime.now().isoformat(),
            'passed': True,
            'errors': [],
            'warnings': [],
            'metrics_comparison': {}
        }
        
        try:
            test_metrics = test_session.get('performance_metrics', {})
            expected_metrics = expected_live_session.get('performance_metrics', {})
            
            # Key metrics to validate
            key_metrics = [
                'win_rate', 'total_trades', 'profit_factor', 'max_drawdown',
                'sortino_ratio', 'calmar_ratio', 'composite_score'
            ]
            
            for metric in key_metrics:
                test_value = test_metrics.get(metric, 0)
                expected_value = expected_metrics.get(metric, 0)
                
                # Allow small tolerance for floating point differences
                tolerance = 0.001
                if abs(test_value - expected_value) > tolerance:
                    validation_result['errors'].append(
                        f"Metric {metric} mismatch: Test={test_value}, Expected={expected_value}"
                    )
                    validation_result['passed'] = False
                
                validation_result['metrics_comparison'][metric] = {
                    'test_value': test_value,
                    'expected_value': expected_value,
                    'difference': abs(test_value - expected_value),
                    'within_tolerance': abs(test_value - expected_value) <= tolerance
                }
            
            # Validate final balance calculation
            test_balance = test_session.get('final_balance', 0)
            expected_balance = expected_live_session.get('final_balance', 0)
            
            if abs(test_balance - expected_balance) > 0.01:
                validation_result['errors'].append(
                    f"Final balance mismatch: Test=${test_balance:.2f}, Expected=${expected_balance:.2f}"
                )
                validation_result['passed'] = False
            
        except Exception as e:
            validation_result['errors'].append(f"Session validation error: {str(e)}")
            validation_result['passed'] = False
            self.logger.error(f"Error validating session metrics: {e}")
        
        return validation_result
    
    def validate_grid_logic(self, test_engine: TestTradingEngine, current_price: float) -> Dict:
        """Validate grid level calculation and touch detection"""
        validation_result = {
            'grid_validation_time': datetime.now().isoformat(),
            'passed': True,
            'errors': [],
            'warnings': [],
            'grid_details': {}
        }
        
        try:
            # Validate grid spacing
            grid_levels = list(test_engine.grid_levels.values())
            if len(grid_levels) < 2:
                validation_result['warnings'].append("Insufficient grid levels for spacing validation")
                return validation_result
            
            # Check grid spacing consistency
            for i in range(1, len(grid_levels)):
                prev_price = grid_levels[i-1].price
                curr_price = grid_levels[i].price
                
                # Calculate actual spacing
                actual_spacing = abs(curr_price - prev_price) / prev_price
                expected_spacing = self.config.GRID_SPACING
                
                if abs(actual_spacing - expected_spacing) > 0.0001:  # 0.01% tolerance
                    validation_result['errors'].append(
                        f"Grid spacing inconsistent: Level {i} spacing={actual_spacing:.4f}, Expected={expected_spacing:.4f}"
                    )
                    validation_result['passed'] = False
            
            # Validate grid touch detection
            touched_levels = test_engine._check_grid_touches(current_price)
            
            validation_result['grid_details'] = {
                'total_levels': len(grid_levels),
                'current_price': current_price,
                'touched_levels': len(touched_levels),
                'grid_spacing': self.config.GRID_SPACING,
                'price_range': {
                    'min': min(level.price for level in grid_levels) if grid_levels else 0,
                    'max': max(level.price for level in grid_levels) if grid_levels else 0
                }
            }
            
        except Exception as e:
            validation_result['errors'].append(f"Grid validation error: {str(e)}")
            validation_result['passed'] = False
            self.logger.error(f"Error validating grid logic: {e}")
        
        return validation_result
    
    def run_comprehensive_validation(self, test_engine: TestTradingEngine, 
                                   comparison_data: Optional[Dict] = None) -> Dict:
        """Run comprehensive validation of test trading engine"""
        validation_report = {
            'validation_time': datetime.now().isoformat(),
            'test_mode': 'TEST',
            'overall_passed': True,
            'validation_categories': {},
            'summary': {
                'total_validations': 0,
                'passed_validations': 0,
                'failed_validations': 0,
                'warnings': 0
            }
        }
        
        try:
            # Validate configuration consistency
            config_validation = self._validate_configuration(test_engine)
            validation_report['validation_categories']['configuration'] = config_validation
            
            # Validate grid logic if engine is initialized
            if test_engine.grid_levels:
                current_price = 50000.0  # Default test price
                grid_validation = self.validate_grid_logic(test_engine, current_price)
                validation_report['validation_categories']['grid_logic'] = grid_validation
            
            # Validate test trades if any exist
            if test_engine.closed_trades:
                trade_validations = []
                for trade in test_engine.closed_trades:
                    # Create expected live trade for comparison
                    expected_live_trade = self._create_expected_live_trade(trade)
                    trade_validation = self.validate_trade_logic(trade, expected_live_trade)
                    trade_validations.append(trade_validation)
                
                validation_report['validation_categories']['trade_logic'] = trade_validations
            
            # Calculate summary statistics
            all_validations = []
            for category, validations in validation_report['validation_categories'].items():
                if isinstance(validations, list):
                    all_validations.extend(validations)
                else:
                    all_validations.append(validations)
            
            validation_report['summary']['total_validations'] = len(all_validations)
            validation_report['summary']['passed_validations'] = sum(1 for v in all_validations if v.get('passed', False))
            validation_report['summary']['failed_validations'] = sum(1 for v in all_validations if not v.get('passed', True))
            validation_report['summary']['warnings'] = sum(len(v.get('warnings', [])) for v in all_validations)
            
            # Overall pass/fail
            validation_report['overall_passed'] = validation_report['summary']['failed_validations'] == 0
            
        except Exception as e:
            validation_report['overall_passed'] = False
            validation_report['error'] = str(e)
            self.logger.error(f"Error in comprehensive validation: {e}")
        
        return validation_report
    
    def _validate_configuration(self, test_engine: TestTradingEngine) -> Dict:
        """Validate configuration consistency"""
        return {
            'passed': True,
            'config_mode': test_engine.mode.value,
            'initial_capital': test_engine.config.INITIAL_CAPITAL,
            'grid_spacing': test_engine.config.GRID_SPACING,
            'fixed_risk': test_engine.config.FIXED_RISK_AMOUNT,
            'target_profit': test_engine.config.TARGET_PROFIT,
            'validation_time': datetime.now().isoformat()
        }
    
    def _create_expected_live_trade(self, test_trade: TestTrade) -> Dict:
        """Create expected live trade data for comparison"""
        return {
            'entry_price': test_trade.entry_price,
            'exit_price': test_trade.exit_price,
            'position_size': test_trade.position_size,
            'commission_total': test_trade.commission_total,
            'risk_amount': test_trade.risk_amount,
            'target_profit': test_trade.target_profit
        }
    
    def save_validation_report(self, validation_report: Dict, filename: Optional[str] = None) -> str:
        """Save validation report to file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"test_validation_report_{timestamp}.json"
        
        filepath = os.path.join(self.config.LOGS_DIR, filename)
        
        try:
            os.makedirs(self.config.LOGS_DIR, exist_ok=True)
            with open(filepath, 'w') as f:
                json.dump(validation_report, f, indent=2)
            
            self.logger.info(f"Validation report saved: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error saving validation report: {e}")
            return ""
