#!/usr/bin/env python3
"""
Real Money Trading Webapp Launcher
Launch script for the complete trading dashboard
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = [
        'streamlit',
        'plotly',
        'ccxt',
        'pandas',
        'numpy',
        'cryptography'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def setup_environment():
    """Set up the trading environment"""
    # Add current directory to Python path
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Create necessary directories
    (current_dir / "config").mkdir(exist_ok=True)
    (current_dir / "data").mkdir(exist_ok=True)
    (current_dir / "logs").mkdir(exist_ok=True)
    (current_dir / "models").mkdir(exist_ok=True)
    
    print("✅ Environment setup complete")

def launch_webapp():
    """Launch the Streamlit webapp"""
    webapp_file = Path(__file__).parent / "real_money_trading_webapp.py"
    
    if not webapp_file.exists():
        print(f"❌ Webapp file not found: {webapp_file}")
        return False
    
    print("🚀 Launching Real Money Trading Webapp...")
    print("🌐 Opening browser in 3 seconds...")
    
    try:
        # Launch Streamlit
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run",
            str(webapp_file),
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ])
        
        # Wait a moment then open browser
        time.sleep(3)
        try:
            webbrowser.open("http://localhost:8501")
        except:
            print("📖 Manual browser opening required: http://localhost:8501")
        
        print("\n" + "="*60)
        print("🎯 REAL MONEY TRADING WEBAPP LAUNCHED")
        print("="*60)
        print("📊 Dashboard: http://localhost:8501")
        print("🔑 Configure API keys in the sidebar")
        print("🤖 Use Start/Stop Bot controls")
        print("📈 Monitor live trading metrics")
        print("⚠️  REAL MONEY TRADING - USE WITH CAUTION")
        print("="*60)
        print("\n💡 Press Ctrl+C to stop the webapp")
        
        # Wait for process
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 Webapp stopped by user")
        process.terminate()
    except Exception as e:
        print(f"❌ Error launching webapp: {e}")
        return False
    
    return True

def main():
    """Main launcher function"""
    print("🎯 Real Money Trading Webapp Launcher")
    print("="*50)
    
    # Check dependencies
    print("📋 Checking dependencies...")
    if not check_dependencies():
        return False
    
    print("✅ All dependencies found")
    
    # Setup environment
    print("🔧 Setting up environment...")
    setup_environment()
    
    # Launch webapp
    success = launch_webapp()
    
    if success:
        print("✅ Webapp launched successfully")
    else:
        print("❌ Failed to launch webapp")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
