{"timestamp": "2025-06-05T18:31:22.419827", "overall_status": "CRITICAL", "summary": {"total_checks": 9, "healthy": 2, "warnings": 2, "errors": 0, "critical": 4}, "detailed_results": {"server": {"status": "HEALTHY", "response_time": 0.009224, "status_code": 200}, "api_endpoints": {"/api/trading_status": {"status": "HEALTHY", "response_time": 0.015659, "data_keys": ["current_price", "is_live_mode", "is_running", "last_update", "model_info", "performance", "price_source"]}, "/api/open_positions": {"status": "ERROR", "status_code": 404}, "/api/recent_trades": {"status": "HEALTHY", "response_time": 0.00686, "data_keys": "non-dict"}, "/api/cross_margin_analysis": {"status": "ERROR", "status_code": 404}, "/api/trading_test_status": {"status": "ERROR", "status_code": 404}}, "real_time_data": {"status": "HEALTHY", "current_btc_price": 104444.62, "price_source": "real_api", "last_update": "2025-06-05T18:31:19.235444"}, "model_integration": {"status": "WARNING", "model_id": "conservative_elite", "composite_score": 79.1, "cycle": null, "checks": {"model_id_correct": false, "composite_score_correct": false, "cycle_correct": false, "tcn_weight_present": false, "cnn_weight_present": false, "ppo_weight_present": false}, "component_weights": {"tcn": null, "cnn": null, "ppo": null}}, "cross_margin": {"status": "CRITICAL", "error": "Expecting value: line 1 column 1 (char 0)"}, "trading_engine": {"status": "CRITICAL", "error": "Expecting value: line 1 column 1 (char 0)"}, "test_functions": {"status": "CRITICAL", "error": "Expecting value: line 1 column 1 (char 0)"}, "web_interface": {"status": "WARNING", "response_time": 0.012397, "content_length": 7124, "ui_checks": {"dashboard_loads": false, "start_trading_button": false, "stop_trading_button": false, "test_functions_section": false, "performance_section": true, "btc_price_display": true}}, "data_integrity": {"status": "CRITICAL", "error": "Expecting value: line 1 column 1 (char 0)"}}, "warnings": ["Model integration issues: ['model_id_correct', 'composite_score_correct', 'cycle_correct', 'tcn_weight_present', 'cnn_weight_present', 'ppo_weight_present']", "Missing UI components: ['dashboard_loads', 'start_trading_button', 'stop_trading_button', 'test_functions_section']"], "errors": ["API endpoint /api/open_positions returned 404", "API endpoint /api/cross_margin_analysis returned 404", "API endpoint /api/trading_test_status returned 404", "Cross margin check failed: Expecting value: line 1 column 1 (char 0)", "Trading engine check failed: Expecting value: line 1 column 1 (char 0)", "Test functions check failed: Expecting value: line 1 column 1 (char 0)", "Data integrity check failed: Expecting value: line 1 column 1 (char 0)"], "recommendations": ["🚨 CRITICAL: Address critical issues immediately before trading", "🤖 Verify Cycle 1 model is properly loaded and configured", "⚡ Review trading engine configuration and restart if needed", "📊 Verify cross margin calculations and grid trading parameters", "⚠️ Review and address all warnings before live trading"]}