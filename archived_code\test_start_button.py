#!/usr/bin/env python3
"""
Simple test to verify Start Test button functionality
"""

import streamlit as st
import time
from datetime import datetime

# Setup page config
st.set_page_config(
    page_title="Start Button Test",
    page_icon="🧪",
    layout="wide"
)

def start_test_trading():
    """Start test trading"""
    print("🚀 START_TEST_TRADING FUNCTION CALLED")
    try:
        # Import and initialize the simple trading executor
        from simple_trading_executor import get_trading_executor
        print("✅ Successfully imported get_trading_executor")

        # Initialize trading executor
        st.session_state.trading_executor = get_trading_executor(300.0)
        st.session_state.trading_executor.reset()
        print(f"✅ Trading executor initialized")

        st.session_state.engine_running = True
        st.session_state.test_balance = 300.0
        print(f"✅ Session state updated: engine_running={st.session_state.engine_running}")

        st.success("🧪 Test trading started successfully!")
        st.info("🔄 Real trading executor is now monitoring grid levels and executing trades")

    except Exception as e:
        print(f"❌ Error in start_test_trading: {e}")
        st.error(f"❌ Error starting test trading: {e}")
        # Fallback to simple mode
        st.session_state.engine_running = True
        st.session_state.test_balance = 300.0
        st.warning("⚠️ Started in basic simulation mode")

    time.sleep(1)
    st.rerun()

def stop_test_trading():
    """Stop test trading"""
    print("🛑 STOP_TEST_TRADING FUNCTION CALLED")
    st.session_state.engine_running = False
    st.success("🧪 Test trading stopped!")
    time.sleep(1)
    st.rerun()

def main():
    """Main test interface"""
    st.title("🧪 Start Button Test")
    
    # Initialize session state
    if 'engine_running' not in st.session_state:
        st.session_state.engine_running = False
    if 'test_balance' not in st.session_state:
        st.session_state.test_balance = 300.0
    
    # Display current state
    st.subheader("Current State")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Engine Running", st.session_state.engine_running)
    with col2:
        st.metric("Test Balance", f"${st.session_state.test_balance:.2f}")
    with col3:
        st.metric("Current Time", datetime.now().strftime('%H:%M:%S'))
    
    # Control buttons
    st.subheader("Controls")
    col1, col2 = st.columns(2)
    
    with col1:
        start_disabled = st.session_state.engine_running
        st.caption(f"Start button disabled: {start_disabled}")
        if st.button("▶️ Start Test", disabled=start_disabled):
            print("🔥 START TEST BUTTON CLICKED!")
            start_test_trading()
    
    with col2:
        stop_disabled = not st.session_state.engine_running
        st.caption(f"Stop button disabled: {stop_disabled}")
        if st.button("⏹️ Stop Test", disabled=stop_disabled):
            print("🔥 STOP TEST BUTTON CLICKED!")
            stop_test_trading()
    
    # Debug info
    st.subheader("Debug Information")
    st.json({
        "engine_running": st.session_state.engine_running,
        "test_balance": st.session_state.test_balance,
        "has_trading_executor": 'trading_executor' in st.session_state,
        "session_state_keys": list(st.session_state.keys())
    })
    
    # Test trading executor if available
    if 'trading_executor' in st.session_state and st.session_state.trading_executor:
        st.subheader("Trading Executor Status")
        executor = st.session_state.trading_executor
        st.json({
            "balance": executor.balance,
            "open_trades": len(executor.open_trades),
            "total_trades": executor.total_trades,
            "current_price": executor.get_current_price()
        })

if __name__ == "__main__":
    main()
