<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading System Validation Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; background-color: #f8f9fa; }
        .metric { display: inline-block; margin: 10px; padding: 15px; background: #e8f4fd; border-radius: 5px; min-width: 150px; text-align: center; }
        .success { color: #27ae60; font-weight: bold; }
        .warning { color: #f39c12; font-weight: bold; }
        .error { color: #e74c3c; font-weight: bold; }
        .locked { background-color: #ffe6e6; border: 1px solid #ff9999; padding: 10px; border-radius: 5px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #3498db; color: white; }
        .status-production { background-color: #d4edda; color: #155724; padding: 5px; border-radius: 3px; }
        .status-candidate { background-color: #fff3cd; color: #856404; padding: 5px; border-radius: 3px; }
        .status-backup { background-color: #f8d7da; color: #721c24; padding: 5px; border-radius: 3px; }
        .highlight { background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Trading System Validation Report</h1>
            <h2>TCN-CNN-PPO Ensemble with Integrated Backtester</h2>
            <p><strong>Generated:</strong> 2025-06-08 13:07:55</p>
            <p><strong>Report ID:</strong> 20250608_130755</p>
        </div>

        <div class="highlight">
            <h3>🎯 DEPLOYMENT RECOMMENDATION</h3>
            <div class="error">
                <h4>❌ REQUIRES IMPROVEMENT</h4>
                <p><strong>Composite Score:</strong> 0.657 (Target: ≥0.85)</p>
                <p><strong>Reward Scale:</strong> 0.0 to 1.0 (1 = highest performance)</p>
            </div>
        </div>

        <div class="section">
            <h3>🔒 Locked Parameters Verification</h3>
            <div class="locked">
                <strong>✅ ALL PARAMETERS LOCKED AND VERIFIED</strong>
                <ul>
                    <li><strong>Grid Spacing:</strong> 0.0025 (0.25%) - LOCKED</li>
                    <li><strong>Risk-Reward Ratio:</strong> 2.5:1 - LOCKED</li>
                    <li><strong>Training Days:</strong> 60 - LOCKED</li>
                    <li><strong>Testing Days:</strong> 30 - LOCKED</li>
                    <li><strong>Ensemble Weights:</strong> TCN(40%) + CNN(40%) + PPO(20%) - LOCKED</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h3>📊 Performance Summary</h3>
            <div class="metric">
                <h4>Composite Score</h4>
                <div class="error">0.657</div>
                <small>Scale: 0-1 (1=highest)</small>
            </div>
            <div class="metric">
                <h4>Win Rate</h4>
                <div class="success">59.6%</div>
                <small>Target: ≥55%</small>
            </div>
            <div class="metric">
                <h4>Total Profit</h4>
                <div class="success">$1246.87</div>
                <small>Starting: $300</small>
            </div>
            <div class="metric">
                <h4>Total Trades</h4>
                <div>307</div>
                <small>30-day period</small>
            </div>
            <div class="metric">
                <h4>Final Balance</h4>
                <div class="success">$1546.87</div>
                <small>ROI: +415.6%</small>
            </div>
        </div>

        <div class="section">
            <h3>🎯 Composite Score Breakdown (LOCKED FORMULA)</h3>
            <table>
                <tr><th>Component</th><th>Weight</th><th>Description</th></tr>
                <tr><td>Sortino Ratio (normalized)</td><td>25%</td><td>Risk-adjusted returns focusing on downside</td></tr>
                <tr><td>Ulcer Index (inverted)</td><td>20%</td><td>Drawdown risk measure (lower is better)</td></tr>
                <tr><td>Equity Curve R²</td><td>15%</td><td>Smoothness and consistency of returns</td></tr>
                <tr><td>Profit Stability</td><td>15%</td><td>Consistency of profit generation</td></tr>
                <tr><td>Upward Move Ratio</td><td>15%</td><td>Percentage of winning trades</td></tr>
                <tr><td>Drawdown Duration (inverted)</td><td>10%</td><td>Recovery time from losses</td></tr>
            </table>
            <p><strong>Formula:</strong> 0.25×Sortino + 0.20×UlcerInv + 0.15×R² + 0.15×Stability + 0.15×Upward + 0.10×DDInv</p>
        </div>

        <div class="section">
            <h3>🧠 Model Architecture (LOCKED)</h3>
            <table>
                <tr><th>Component</th><th>Weight</th><th>Purpose</th><th>Status</th></tr>
                <tr><td>TCN (Temporal Convolutional Network)</td><td>40%</td><td>Time series pattern recognition</td><td>✅ LOCKED</td></tr>
                <tr><td>CNN (Convolutional Neural Network)</td><td>40%</td><td>Chart pattern detection</td><td>✅ LOCKED</td></tr>
                <tr><td>PPO (Proximal Policy Optimization)</td><td>20%</td><td>Reinforcement learning decisions</td><td>✅ LOCKED</td></tr>
            </table>
        </div>

        <div class="section">
            <h3>📈 Technical Indicators (LOCKED)</h3>
            <table>
                <tr><th>Indicator</th><th>Parameters</th><th>Purpose</th><th>Status</th></tr>
                <tr><td>VWAP</td><td>24 period</td><td>Volume-weighted average price</td><td>✅ LOCKED</td></tr>
                <tr><td>Bollinger Bands</td><td>20 window, 2 std dev</td><td>Volatility and mean reversion</td><td>✅ LOCKED</td></tr>
                <tr><td>RSI</td><td>14 period</td><td>Momentum oscillator</td><td>✅ LOCKED</td></tr>
                <tr><td>ETH/BTC Ratio</td><td>0.05 threshold</td><td>Market correlation indicator</td><td>✅ LOCKED</td></tr>
            </table>
        </div>

        <div class="section">
            <h3>💾 Saved Models</h3>
            <table>
                <tr><th>Model Type</th><th>Filename</th><th>Status</th></tr>
                <tr><td>Best Composite</td><td>backup_model_20250608_130755.json</td><td class="status-backup">BACKUP</td></tr>
                <tr><td>Best Profit</td><td>best_profit_model_20250608_130755.json</td><td class="status-backup">BACKUP</td></tr>
            </table>
        </div>

        <div class="section">
            <h3>🔍 Backtester Validation Results</h3>
            <ul>
                <li class="success">✅ Integrated backtester validation: PASSED</li>
                <li class="success">✅ Real-time signal validation: ENABLED</li>
                <li class="success">✅ Performance monitoring: ACTIVE</li>
                <li class="success">✅ Risk management: VALIDATED</li>
                <li class="success">✅ Parameter lock verification: PASSED</li>
            </ul>
        </div>

        <div class="section">
            <h3>📋 Training Pipeline Status</h3>
            <table>
                <tr><th>Phase</th><th>Status</th><th>Details</th></tr>
                <tr><td>Parameter Lock Verification</td><td class="success">✅ PASSED</td><td>All locked parameters verified</td></tr>
                <tr><td>Backtester Initialization</td><td class="success">✅ COMPLETED</td><td>Integrated validation enabled</td></tr>
                <tr><td>Data Collection</td><td class="success">✅ COMPLETED</td><td>60 days training + 30 days testing</td></tr>
                <tr><td>Model Training</td><td class="success">✅ COMPLETED</td><td>TCN-CNN-PPO ensemble trained</td></tr>
                <tr><td>Out-of-Sample Testing</td><td class="success">✅ COMPLETED</td><td>30-day validation period</td></tr>
                <tr><td>Model Saving</td><td class="success">✅ COMPLETED</td><td>Best composite + best profit saved</td></tr>
                <tr><td>HTML Report Generation</td><td class="success">✅ COMPLETED</td><td>This report</td></tr>
            </table>
        </div>

        <div class="section">
            <h3>⚠️ Important Notes</h3>
            <ul>
                <li><strong>Reward System:</strong> Operates on 0-1 scale where 1.0 represents highest performance</li>
                <li><strong>Parameter Lock:</strong> All critical parameters are LOCKED and cannot be modified</li>
                <li><strong>Validation:</strong> Integrated backtester validates every trading signal in real-time</li>
                <li><strong>Model Saving:</strong> Dual criteria - saves both highest composite score AND highest net profit</li>
                <li><strong>Compliance:</strong> System enforces strict adherence to locked specifications</li>
                <li><strong>Out-of-Sample:</strong> All performance metrics based on unseen 30-day test data</li>
            </ul>
        </div>

        <div class="section">
            <h3>🎯 Next Steps</h3>
            <ol>
                <li><strong>Review Results:</strong> Analyze performance metrics and validation results</li>
                <li><strong>Deploy Model:</strong> Use saved model files for live trading (if approved)</li>
                <li><strong>Monitor Performance:</strong> Continuous monitoring with integrated backtester</li>
                <li><strong>Maintain Compliance:</strong> Ensure all locked parameters remain unchanged</li>
            </ol>
        </div>

        <footer style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
            <p><strong>Comprehensive Training System with Integrated Backtester</strong></p>
            <p>Generated: 2025-06-08 13:07:55 | Report ID: 20250608_130755</p>
            <p>🔒 All parameters locked and verified | 📊 Reward scale: 0-1 (1=highest)</p>
        </footer>
    </div>
</body>
</html>