#!/usr/bin/env python3
"""
Data Cache Manager for Bitcoin Freedom Trading System
Ensures data persistence during updates, outages, and system restarts.
"""

import os
import json
import sqlite3
import pickle
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataCacheManager:
    """Comprehensive data caching system for trading data persistence."""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = cache_dir
        self.db_path = os.path.join(cache_dir, "trading_cache.db")
        self.json_cache_path = os.path.join(cache_dir, "json_cache")
        self.pickle_cache_path = os.path.join(cache_dir, "pickle_cache")
        
        # Create cache directories
        os.makedirs(cache_dir, exist_ok=True)
        os.makedirs(self.json_cache_path, exist_ok=True)
        os.makedirs(self.pickle_cache_path, exist_ok=True)
        
        # Thread lock for concurrent access
        self.lock = threading.Lock()
        
        # Initialize database
        self._init_database()
        
        logger.info(f"✅ Data Cache Manager initialized: {cache_dir}")
    
    def _init_database(self):
        """Initialize SQLite database for structured data caching."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Trading data cache table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    data TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expiry DATETIME,
                    data_type TEXT DEFAULT 'json'
                )
            ''')
            
            # Price data cache table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    price REAL NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    source TEXT DEFAULT 'binance'
                )
            ''')
            
            # Model performance cache table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_id TEXT UNIQUE NOT NULL,
                    performance_data TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Trade history cache table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trade_history_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE NOT NULL,
                    trade_data TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'active'
                )
            ''')
            
            conn.commit()
            logger.info("✅ Database tables initialized")
    
    def cache_data(self, key: str, data: Any, expiry_hours: int = 24, data_type: str = 'json') -> bool:
        """Cache data with automatic expiry."""
        try:
            with self.lock:
                # Calculate expiry time
                expiry = datetime.now() + timedelta(hours=expiry_hours)
                
                # Serialize data based on type
                if data_type == 'json':
                    serialized_data = json.dumps(data)
                elif data_type == 'pickle':
                    serialized_data = pickle.dumps(data).hex()
                else:
                    serialized_data = str(data)
                
                # Store in database
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT OR REPLACE INTO trading_cache 
                        (key, data, expiry, data_type) 
                        VALUES (?, ?, ?, ?)
                    ''', (key, serialized_data, expiry, data_type))
                    conn.commit()
                
                logger.info(f"✅ Cached data: {key} (expires: {expiry})")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to cache data {key}: {e}")
            return False
    
    def get_cached_data(self, key: str) -> Optional[Any]:
        """Retrieve cached data if not expired."""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT data, expiry, data_type FROM trading_cache 
                        WHERE key = ? AND (expiry IS NULL OR expiry > ?)
                    ''', (key, datetime.now()))
                    
                    result = cursor.fetchone()
                    if not result:
                        return None
                    
                    serialized_data, expiry, data_type = result
                    
                    # Deserialize data based on type
                    if data_type == 'json':
                        return json.loads(serialized_data)
                    elif data_type == 'pickle':
                        return pickle.loads(bytes.fromhex(serialized_data))
                    else:
                        return serialized_data
                        
        except Exception as e:
            logger.error(f"❌ Failed to retrieve cached data {key}: {e}")
            return None
    
    def cache_price_data(self, symbol: str, price: float, source: str = 'binance') -> bool:
        """Cache price data for quick recovery."""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO price_cache (symbol, price, source) 
                        VALUES (?, ?, ?)
                    ''', (symbol, price, source))
                    conn.commit()
                
                # Keep only last 1000 price records per symbol
                cursor.execute('''
                    DELETE FROM price_cache 
                    WHERE symbol = ? AND id NOT IN (
                        SELECT id FROM price_cache 
                        WHERE symbol = ? 
                        ORDER BY timestamp DESC 
                        LIMIT 1000
                    )
                ''', (symbol, symbol))
                conn.commit()
                
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to cache price data: {e}")
            return False
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """Get latest cached price for symbol."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT price FROM price_cache 
                    WHERE symbol = ? 
                    ORDER BY timestamp DESC 
                    LIMIT 1
                ''', (symbol,))
                
                result = cursor.fetchone()
                return result[0] if result else None
                
        except Exception as e:
            logger.error(f"❌ Failed to get latest price: {e}")
            return None
    
    def cache_model_performance(self, model_id: str, performance_data: Dict) -> bool:
        """Cache model performance data."""
        try:
            with self.lock:
                serialized_data = json.dumps(performance_data)
                
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT OR REPLACE INTO model_cache 
                        (model_id, performance_data) 
                        VALUES (?, ?)
                    ''', (model_id, serialized_data))
                    conn.commit()
                
                logger.info(f"✅ Cached model performance: {model_id}")
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to cache model performance: {e}")
            return False
    
    def get_model_performance(self, model_id: str) -> Optional[Dict]:
        """Get cached model performance data."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT performance_data FROM model_cache 
                    WHERE model_id = ?
                ''', (model_id,))
                
                result = cursor.fetchone()
                if result:
                    return json.loads(result[0])
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get model performance: {e}")
            return None
    
    def cache_trade_data(self, trade_id: str, trade_data: Dict) -> bool:
        """Cache individual trade data."""
        try:
            with self.lock:
                serialized_data = json.dumps(trade_data)
                
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT OR REPLACE INTO trade_history_cache 
                        (trade_id, trade_data) 
                        VALUES (?, ?)
                    ''', (trade_id, serialized_data))
                    conn.commit()
                
                return True
                
        except Exception as e:
            logger.error(f"❌ Failed to cache trade data: {e}")
            return False
    
    def get_all_cached_trades(self) -> List[Dict]:
        """Get all cached trade data."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT trade_data FROM trade_history_cache 
                    ORDER BY timestamp DESC
                ''')
                
                results = cursor.fetchall()
                return [json.loads(row[0]) for row in results]
                
        except Exception as e:
            logger.error(f"❌ Failed to get cached trades: {e}")
            return []
    
    def cleanup_expired_cache(self) -> int:
        """Clean up expired cache entries."""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        DELETE FROM trading_cache 
                        WHERE expiry IS NOT NULL AND expiry < ?
                    ''', (datetime.now(),))
                    
                    deleted_count = cursor.rowcount
                    conn.commit()
                
                logger.info(f"✅ Cleaned up {deleted_count} expired cache entries")
                return deleted_count
                
        except Exception as e:
            logger.error(f"❌ Failed to cleanup cache: {e}")
            return 0
    
    def backup_cache(self, backup_path: str) -> bool:
        """Create a backup of the entire cache."""
        try:
            import shutil
            
            # Create backup directory
            os.makedirs(backup_path, exist_ok=True)
            
            # Copy database
            shutil.copy2(self.db_path, os.path.join(backup_path, "trading_cache.db"))
            
            # Copy cache directories
            shutil.copytree(self.json_cache_path, os.path.join(backup_path, "json_cache"), dirs_exist_ok=True)
            shutil.copytree(self.pickle_cache_path, os.path.join(backup_path, "pickle_cache"), dirs_exist_ok=True)
            
            logger.info(f"✅ Cache backup created: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to backup cache: {e}")
            return False

# Global cache manager instance
cache_manager = DataCacheManager()

def get_cache_manager() -> DataCacheManager:
    """Get the global cache manager instance."""
    return cache_manager
