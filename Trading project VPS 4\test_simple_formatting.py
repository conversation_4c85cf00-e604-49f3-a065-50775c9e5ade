#!/usr/bin/env python3
"""
Simple test for date/time formatting functions
"""

from datetime import datetime

def format_full_datetime_for_js(datetime_value):
    """Format datetime value for JavaScript Date() constructor."""
    if not datetime_value:
        return None

    try:
        # Handle different datetime formats
        datetime_str = str(datetime_value)

        # If it's already an ISO datetime string with T, return as-is
        if 'T' in datetime_str:
            return datetime_str

        # Try to parse as datetime object
        if isinstance(datetime_value, datetime):
            return datetime_value.isoformat()

        # Try to parse string as datetime
        try:
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return dt.isoformat()
        except:
            pass

        # Handle space-separated datetime format
        if ' ' in datetime_str and len(datetime_str.split(' ')) == 2:
            try:
                dt = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                return dt.isoformat()
            except:
                pass

        # If it's just a time string, combine with today's date
        if ':' in datetime_str and len(datetime_str.split(':')) >= 2 and ' ' not in datetime_str:
            try:
                today = datetime.now().strftime('%Y-%m-%d')
                full_datetime_str = f"{today} {datetime_str}"
                dt = datetime.strptime(full_datetime_str, '%Y-%m-%d %H:%M:%S')
                return dt.isoformat()
            except:
                pass

        return None
    except Exception as e:
        print(f"⚠️ Error formatting datetime for JS {datetime_value}: {e}")
        return None

def format_time_from_db(time_value):
    """Format time value from database to HH:MM:SS format."""
    if not time_value:
        return 'Unknown'
    
    try:
        # Handle different time formats
        time_str = str(time_value)
        
        # If it's an ISO datetime string with T
        if 'T' in time_str:
            return time_str.split('T')[1][:8]
        
        # Handle space-separated datetime format first
        if ' ' in time_str and len(time_str.split(' ')) == 2:
            try:
                dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                return dt.strftime('%H:%M:%S')
            except:
                pass

        # If it's already in HH:MM:SS format
        if ':' in time_str and len(time_str.split(':')) >= 2 and ' ' not in time_str:
            parts = time_str.split(':')
            if len(parts) >= 3:
                return f"{parts[0]:0>2}:{parts[1]:0>2}:{parts[2][:2]:0>2}"
            else:
                return f"{parts[0]:0>2}:{parts[1]:0>2}:00"
        
        # Try to parse as datetime object
        if isinstance(time_value, datetime):
            return time_value.strftime('%H:%M:%S')
        
        # Try to parse string as datetime
        try:
            dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            return dt.strftime('%H:%M:%S')
        except:
            pass
        

        
        return 'Unknown'
    except Exception as e:
        print(f"⚠️ Error formatting time {time_value}: {e}")
        return 'Unknown'

def format_date_from_db(date_value):
    """Format date value from database to YYYY-MM-DD format."""
    if not date_value:
        return None
    
    try:
        # Handle different date formats
        date_str = str(date_value)
        
        # If it's an ISO datetime string with T
        if 'T' in date_str:
            return date_str.split('T')[0]
        
        # If it's already in YYYY-MM-DD format
        if '-' in date_str and len(date_str.split('-')) == 3:
            parts = date_str.split('-')
            if len(parts[0]) == 4:  # Year first
                return date_str[:10]  # Take first 10 chars (YYYY-MM-DD)
        
        # Try to parse as datetime object
        if isinstance(date_value, datetime):
            return date_value.strftime('%Y-%m-%d')
        
        # Try to parse string as datetime
        try:
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d')
        except:
            pass
        
        # Handle space-separated datetime format
        if ' ' in date_str and len(date_str.split(' ')) == 2:
            try:
                dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                return dt.strftime('%Y-%m-%d')
            except:
                pass
        
        return None
    except Exception as e:
        print(f"⚠️ Error formatting date {date_value}: {e}")
        return None

if __name__ == '__main__':
    print("🧪 Testing Date/Time Formatting Functions")
    print("=" * 50)

    # Test cases for JavaScript datetime formatting
    js_test_cases = [
        ("2024-06-06T14:30:25", "2024-06-06T14:30:25"),
        ("2024-06-06T14:30:25.123456", "2024-06-06T14:30:25.123456"),
        ("2024-06-06 14:30:25", "2024-06-06T14:30:25"),
        ("14:30:25", f"{datetime.now().strftime('%Y-%m-%d')}T14:30:25"),
        ("", None),
        (None, None),
        (datetime(2024, 6, 6, 14, 30, 25), "2024-06-06T14:30:25")
    ]

    all_passed = True

    print("\n📋 Testing JavaScript DateTime Formatting:")
    for i, (input_val, expected_js) in enumerate(js_test_cases, 1):
        print(f"\n   Test {i}: {input_val}")

        result_js = format_full_datetime_for_js(input_val)

        js_ok = result_js == expected_js

        print(f"   JS DateTime: {result_js} {'✅' if js_ok else '❌'} (Expected: {expected_js})")

        if not js_ok:
            all_passed = False

    # Test cases for regular formatting
    test_cases = [
        ("2024-06-06T14:30:25", "14:30:25", "2024-06-06"),
        ("2024-06-06T14:30:25.123456", "14:30:25", "2024-06-06"),
        ("2024-06-06 14:30:25", "14:30:25", "2024-06-06"),
        ("14:30:25", "14:30:25", None),
        ("", "Unknown", None),
        (None, "Unknown", None),
        (datetime(2024, 6, 6, 14, 30, 25), "14:30:25", "2024-06-06")
    ]

    print("\n📋 Testing Regular Date/Time Formatting:")
    for i, (input_val, expected_time, expected_date) in enumerate(test_cases, 1):
        print(f"\n   Test {i}: {input_val}")

        result_time = format_time_from_db(input_val)
        result_date = format_date_from_db(input_val)

        time_ok = result_time == expected_time
        date_ok = result_date == expected_date

        print(f"   Time: {result_time} {'✅' if time_ok else '❌'} (Expected: {expected_time})")
        print(f"   Date: {result_date} {'✅' if date_ok else '❌'} (Expected: {expected_date})")

        if not (time_ok and date_ok):
            all_passed = False

    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Date/time formatting is working correctly.")
        print("💡 The 'Invalid Date' issue should now be resolved in the webapp.")
        print("🔧 JavaScript will now receive proper ISO datetime strings.")
    else:
        print("❌ Some tests failed. Date/time formatting needs more work.")
    print("=" * 50)
