#!/usr/bin/env python3
"""
Check if webapp is working and fix any issues
"""

import subprocess
import time
import sys
import os

def check_port_5000():
    """Check if port 5000 is in use"""
    try:
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
        if ':5000' in result.stdout:
            print("✅ Port 5000 is in use")
            return True
        else:
            print("❌ Port 5000 is not in use")
            return False
    except:
        print("⚠️ Could not check port status")
        return False

def start_webapp():
    """Start the webapp"""
    print("🚀 Starting Bitcoin Freedom Webapp...")
    
    # Change to correct directory
    os.chdir("Trading project VPS 4")
    
    # Start webapp
    try:
        process = subprocess.Popen([
            sys.executable, '-u', 'minimal_working_webapp.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("⏳ Waiting for webapp to start...")
        time.sleep(5)
        
        if check_port_5000():
            print("✅ Webapp started successfully!")
            print("🌐 Access at: http://localhost:5000")
            print("🤖 Conservative Elite Model Active")
            print("📊 93.2% Win Rate | $3,106.50 Profit Potential")
            
            # Keep it running
            print("\n🔍 Webapp is running... (Press Ctrl+C to stop)")
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping webapp...")
                process.terminate()
                
        else:
            print("❌ Webapp failed to start")
            stdout, stderr = process.communicate(timeout=5)
            if stderr:
                print(f"Error: {stderr}")
                
    except Exception as e:
        print(f"❌ Error starting webapp: {e}")

def main():
    """Main function"""
    print("🔍 BITCOIN FREEDOM WEBAPP STATUS CHECK")
    print("=" * 50)
    
    if check_port_5000():
        print("✅ Webapp appears to be running already")
        print("🌐 Try accessing: http://localhost:5000")
    else:
        print("🔄 Webapp not running, starting it...")
        start_webapp()

if __name__ == "__main__":
    main()
