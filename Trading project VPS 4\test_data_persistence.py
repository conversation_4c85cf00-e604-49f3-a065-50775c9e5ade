#!/usr/bin/env python3
"""
Test Data Persistence System
Tests the dashboard data retention capabilities.
"""

import sys
import os
import json
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_persistence():
    """Test the data persistence system."""
    print("🧪 TESTING DATA PERSISTENCE SYSTEM")
    print("=" * 50)
    
    # Test 1: Cache Manager Availability
    print("📋 Test 1: Cache Manager Availability")
    try:
        from data_cache_manager import DataCacheManager, get_cache_manager
        cache_manager = get_cache_manager()
        print("✅ Cache Manager: Available")
        cache_available = True
    except Exception as e:
        print(f"❌ Cache Manager: Not Available - {e}")
        cache_available = False
    
    if not cache_available:
        print("⚠️ Cannot test persistence without cache manager")
        return False
    
    # Test 2: Cache Data Storage
    print("\n📋 Test 2: Cache Data Storage")
    try:
        test_data = {
            'test_timestamp': datetime.now().isoformat(),
            'total_trades': 5,
            'total_profit': 125.50,
            'win_rate': 80.0,
            'current_equity': 425.50
        }
        
        success = cache_manager.cache_data('test_dashboard_data', test_data, expiry_hours=1)
        if success:
            print("✅ Data Storage: Success")
        else:
            print("❌ Data Storage: Failed")
            return False
    except Exception as e:
        print(f"❌ Data Storage: Error - {e}")
        return False
    
    # Test 3: Cache Data Retrieval
    print("\n📋 Test 3: Cache Data Retrieval")
    try:
        retrieved_data = cache_manager.get_cached_data('test_dashboard_data')
        if retrieved_data:
            print("✅ Data Retrieval: Success")
            print(f"   Retrieved: {json.dumps(retrieved_data, indent=2)}")
            
            # Verify data integrity
            if (retrieved_data.get('total_trades') == 5 and 
                retrieved_data.get('total_profit') == 125.50):
                print("✅ Data Integrity: Verified")
            else:
                print("❌ Data Integrity: Failed")
                return False
        else:
            print("❌ Data Retrieval: No data found")
            return False
    except Exception as e:
        print(f"❌ Data Retrieval: Error - {e}")
        return False
    
    # Test 4: Trade Data Caching
    print("\n📋 Test 4: Trade Data Caching")
    try:
        test_trades = [
            {
                'trade_id': 'TEST_001',
                'direction': 'BUY',
                'entry_price': 103000.00,
                'quantity': 0.001,
                'pnl': 25.50,
                'status': 'CLOSED',
                'entry_date': '2024-01-15',
                'entry_time': '14:30:00'
            },
            {
                'trade_id': 'TEST_002',
                'direction': 'SELL',
                'entry_price': 102500.00,
                'quantity': 0.0015,
                'pnl': -10.25,
                'status': 'CLOSED',
                'entry_date': '2024-01-15',
                'entry_time': '15:45:00'
            }
        ]
        
        success = cache_manager.cache_data('test_recent_trades', test_trades, expiry_hours=168)
        if success:
            print("✅ Trade Data Caching: Success")
        else:
            print("❌ Trade Data Caching: Failed")
            return False
    except Exception as e:
        print(f"❌ Trade Data Caching: Error - {e}")
        return False
    
    # Test 5: Trade Data Recovery
    print("\n📋 Test 5: Trade Data Recovery")
    try:
        retrieved_trades = cache_manager.get_cached_data('test_recent_trades')
        if retrieved_trades and len(retrieved_trades) == 2:
            print("✅ Trade Data Recovery: Success")
            print(f"   Retrieved {len(retrieved_trades)} trades")
            
            # Verify first trade
            first_trade = retrieved_trades[0]
            if (first_trade.get('trade_id') == 'TEST_001' and 
                first_trade.get('pnl') == 25.50):
                print("✅ Trade Data Integrity: Verified")
            else:
                print("❌ Trade Data Integrity: Failed")
                return False
        else:
            print("❌ Trade Data Recovery: Failed")
            return False
    except Exception as e:
        print(f"❌ Trade Data Recovery: Error - {e}")
        return False
    
    # Test 6: Cleanup Test Data
    print("\n📋 Test 6: Cleanup Test Data")
    try:
        # Note: Cache manager doesn't have explicit delete, data will expire
        print("✅ Cleanup: Test data will expire automatically")
    except Exception as e:
        print(f"⚠️ Cleanup: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 DATA PERSISTENCE TEST RESULTS:")
    print("✅ All tests passed successfully!")
    print("💾 Dashboard data will be retained across restarts")
    print("🔄 Trade history will persist during disconnections")
    print("📊 Performance metrics will be cached for recovery")
    print("=" * 50)
    
    return True

if __name__ == '__main__':
    success = test_data_persistence()
    if success:
        print("\n🎉 Data persistence system is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Data persistence system has issues!")
        sys.exit(1)
