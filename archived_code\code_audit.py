#!/usr/bin/env python3
"""
Code Audit System - Phase 1 Implementation
Security audit, code quality checks, and vulnerability scanning
"""

import os
import sys
import ast
import re
import json
import subprocess
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class AuditIssue:
    """Audit issue structure"""
    severity: str  # 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO'
    category: str  # 'SECURITY', 'PERFORMANCE', 'MAINTAINABILITY', 'RELIABILITY'
    file_path: str
    line_number: int
    issue_type: str
    description: str
    recommendation: str
    auto_fixable: bool = False

class SecurityAuditor:
    """Security-focused code auditing"""

    def __init__(self):
        self.logger = logging.getLogger('SecurityAuditor')
        self.security_patterns = {
            'hardcoded_secrets': [
                r'api_key\s*=\s*["\'][^"\']+["\']',
                r'secret\s*=\s*["\'][^"\']+["\']',
                r'password\s*=\s*["\'][^"\']+["\']',
                r'token\s*=\s*["\'][^"\']+["\']',
                r'["\'][A-Za-z0-9]{32,}["\']',  # Long strings that might be keys
            ],
            'sql_injection': [
                r'execute\s*\(\s*["\'].*%.*["\']',
                r'query\s*\(\s*["\'].*\+.*["\']',
                r'cursor\.execute\s*\(\s*f["\']',
            ],
            'unsafe_imports': [
                r'import\s+pickle',
                r'from\s+pickle\s+import',
                r'import\s+subprocess',
                r'import\s+os',
                r'eval\s*\(',
                r'exec\s*\(',
            ],
            'weak_crypto': [
                r'md5\s*\(',
                r'sha1\s*\(',
                r'random\.random\s*\(',
                r'random\.randint\s*\(',
            ]
        }

    def audit_file(self, file_path: str) -> List[AuditIssue]:
        """Audit a single Python file for security issues"""
        issues = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')

            # Check for security patterns
            for category, patterns in self.security_patterns.items():
                for pattern in patterns:
                    for line_num, line in enumerate(lines, 1):
                        if re.search(pattern, line, re.IGNORECASE):
                            issues.append(self._create_security_issue(
                                category, file_path, line_num, line, pattern
                            ))

            # Parse AST for deeper analysis
            try:
                tree = ast.parse(content)
                issues.extend(self._analyze_ast(tree, file_path, lines))
            except SyntaxError as e:
                issues.append(AuditIssue(
                    severity='HIGH',
                    category='RELIABILITY',
                    file_path=file_path,
                    line_number=e.lineno or 0,
                    issue_type='SYNTAX_ERROR',
                    description=f'Syntax error: {e.msg}',
                    recommendation='Fix syntax error before deployment'
                ))

        except Exception as e:
            self.logger.error(f"Error auditing file {file_path}: {e}")
            issues.append(AuditIssue(
                severity='MEDIUM',
                category='RELIABILITY',
                file_path=file_path,
                line_number=0,
                issue_type='AUDIT_ERROR',
                description=f'Could not audit file: {e}',
                recommendation='Investigate file accessibility and format'
            ))

        return issues

    def _create_security_issue(self, category: str, file_path: str, line_num: int,
                             line: str, pattern: str) -> AuditIssue:
        """Create security issue based on pattern match"""
        severity_map = {
            'hardcoded_secrets': 'CRITICAL',
            'sql_injection': 'CRITICAL',
            'unsafe_imports': 'HIGH',
            'weak_crypto': 'MEDIUM'
        }

        description_map = {
            'hardcoded_secrets': 'Potential hardcoded secret or API key detected',
            'sql_injection': 'Potential SQL injection vulnerability',
            'unsafe_imports': 'Unsafe import or function usage',
            'weak_crypto': 'Weak cryptographic function usage'
        }

        recommendation_map = {
            'hardcoded_secrets': 'Move secrets to environment variables or secure config',
            'sql_injection': 'Use parameterized queries or ORM',
            'unsafe_imports': 'Review import necessity and use safer alternatives',
            'weak_crypto': 'Use stronger cryptographic functions (SHA-256+)'
        }

        return AuditIssue(
            severity=severity_map.get(category, 'MEDIUM'),
            category='SECURITY',
            file_path=file_path,
            line_number=line_num,
            issue_type=category.upper(),
            description=f"{description_map.get(category, 'Security issue')}: {line.strip()}",
            recommendation=recommendation_map.get(category, 'Review and fix security issue'),
            auto_fixable=category == 'hardcoded_secrets'
        )

    def _analyze_ast(self, tree: ast.AST, file_path: str, lines: List[str]) -> List[AuditIssue]:
        """Analyze AST for security issues"""
        issues = []

        for node in ast.walk(tree):
            # Check for dangerous function calls
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    if node.func.id in ['eval', 'exec', 'compile']:
                        issues.append(AuditIssue(
                            severity='CRITICAL',
                            category='SECURITY',
                            file_path=file_path,
                            line_number=node.lineno,
                            issue_type='DANGEROUS_FUNCTION',
                            description=f'Dangerous function call: {node.func.id}',
                            recommendation='Avoid using eval/exec, use safer alternatives'
                        ))

            # Check for hardcoded strings that look like secrets
            if isinstance(node, ast.Str):
                if len(node.s) > 20 and re.match(r'^[A-Za-z0-9+/=]+$', node.s):
                    issues.append(AuditIssue(
                        severity='HIGH',
                        category='SECURITY',
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type='POTENTIAL_SECRET',
                        description=f'Potential hardcoded secret: {node.s[:20]}...',
                        recommendation='Move to environment variable or secure config',
                        auto_fixable=True
                    ))

        return issues

class CodeQualityAuditor:
    """Code quality and maintainability auditing"""

    def __init__(self):
        self.logger = logging.getLogger('CodeQualityAuditor')

    def audit_file(self, file_path: str) -> List[AuditIssue]:
        """Audit file for code quality issues"""
        issues = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')

            # Check file-level metrics
            issues.extend(self._check_file_metrics(file_path, lines))

            # Parse and analyze AST
            try:
                tree = ast.parse(content)
                issues.extend(self._analyze_complexity(tree, file_path))
                issues.extend(self._check_naming_conventions(tree, file_path))
                issues.extend(self._check_documentation(tree, file_path))
            except SyntaxError:
                pass  # Already handled in security audit

        except Exception as e:
            self.logger.error(f"Error in quality audit for {file_path}: {e}")

        return issues

    def _check_file_metrics(self, file_path: str, lines: List[str]) -> List[AuditIssue]:
        """Check file-level metrics"""
        issues = []

        # File length check
        if len(lines) > 1000:
            issues.append(AuditIssue(
                severity='MEDIUM',
                category='MAINTAINABILITY',
                file_path=file_path,
                line_number=len(lines),
                issue_type='LONG_FILE',
                description=f'File is very long ({len(lines)} lines)',
                recommendation='Consider splitting into smaller modules'
            ))

        # Line length check
        for line_num, line in enumerate(lines, 1):
            if len(line) > 120:
                issues.append(AuditIssue(
                    severity='LOW',
                    category='MAINTAINABILITY',
                    file_path=file_path,
                    line_number=line_num,
                    issue_type='LONG_LINE',
                    description=f'Line too long ({len(line)} characters)',
                    recommendation='Break long lines for better readability',
                    auto_fixable=True
                ))

        return issues

    def _analyze_complexity(self, tree: ast.AST, file_path: str) -> List[AuditIssue]:
        """Analyze code complexity"""
        issues = []

        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                complexity = self._calculate_cyclomatic_complexity(node)
                if complexity > 10:
                    issues.append(AuditIssue(
                        severity='MEDIUM',
                        category='MAINTAINABILITY',
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type='HIGH_COMPLEXITY',
                        description=f'Function {node.name} has high complexity ({complexity})',
                        recommendation='Consider breaking function into smaller parts'
                    ))

        return issues

    def _calculate_cyclomatic_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity of a function"""
        complexity = 1  # Base complexity

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1

        return complexity

    def _check_naming_conventions(self, tree: ast.AST, file_path: str) -> List[AuditIssue]:
        """Check naming conventions"""
        issues = []

        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if not re.match(r'^[a-z_][a-z0-9_]*$', node.name):
                    issues.append(AuditIssue(
                        severity='LOW',
                        category='MAINTAINABILITY',
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type='NAMING_CONVENTION',
                        description=f'Function name {node.name} does not follow snake_case',
                        recommendation='Use snake_case for function names'
                    ))

            elif isinstance(node, ast.ClassDef):
                if not re.match(r'^[A-Z][a-zA-Z0-9]*$', node.name):
                    issues.append(AuditIssue(
                        severity='LOW',
                        category='MAINTAINABILITY',
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type='NAMING_CONVENTION',
                        description=f'Class name {node.name} does not follow PascalCase',
                        recommendation='Use PascalCase for class names'
                    ))

        return issues

    def _check_documentation(self, tree: ast.AST, file_path: str) -> List[AuditIssue]:
        """Check documentation coverage"""
        issues = []

        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                if not ast.get_docstring(node):
                    issues.append(AuditIssue(
                        severity='LOW',
                        category='MAINTAINABILITY',
                        file_path=file_path,
                        line_number=node.lineno,
                        issue_type='MISSING_DOCSTRING',
                        description=f'{type(node).__name__} {node.name} lacks documentation',
                        recommendation='Add docstring to document purpose and usage'
                    ))

        return issues

class ComprehensiveAuditor:
    """Main auditing system that coordinates all auditors"""

    def __init__(self):
        self.logger = logging.getLogger('ComprehensiveAuditor')
        self.security_auditor = SecurityAuditor()
        self.quality_auditor = CodeQualityAuditor()

    def audit_project(self, project_dir: str) -> Dict[str, Any]:
        """Audit entire project"""
        self.logger.info(f"Starting comprehensive audit of {project_dir}")

        all_issues = []
        file_count = 0

        # Find all Python files
        for root, dirs, files in os.walk(project_dir):
            # Skip common non-source directories
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache', 'venv', 'env']]

            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    file_count += 1

                    self.logger.debug(f"Auditing {file_path}")

                    # Run security audit
                    security_issues = self.security_auditor.audit_file(file_path)
                    all_issues.extend(security_issues)

                    # Run quality audit
                    quality_issues = self.quality_auditor.audit_file(file_path)
                    all_issues.extend(quality_issues)

        # Generate summary
        summary = self._generate_summary(all_issues, file_count)

        return {
            'summary': summary,
            'issues': [asdict(issue) for issue in all_issues],
            'files_audited': file_count,
            'audit_timestamp': datetime.now().isoformat()
        }

    def _generate_summary(self, issues: List[AuditIssue], file_count: int) -> Dict[str, Any]:
        """Generate audit summary"""
        severity_counts = {'CRITICAL': 0, 'HIGH': 0, 'MEDIUM': 0, 'LOW': 0, 'INFO': 0}
        category_counts = {'SECURITY': 0, 'PERFORMANCE': 0, 'MAINTAINABILITY': 0, 'RELIABILITY': 0}
        auto_fixable_count = 0

        for issue in issues:
            severity_counts[issue.severity] += 1
            category_counts[issue.category] += 1
            if issue.auto_fixable:
                auto_fixable_count += 1

        total_issues = len(issues)
        critical_and_high = severity_counts['CRITICAL'] + severity_counts['HIGH']

        # Calculate risk score (0-100)
        risk_score = min(100, (critical_and_high * 10) + (severity_counts['MEDIUM'] * 3) + severity_counts['LOW'])

        # Determine overall status
        if severity_counts['CRITICAL'] > 0:
            status = 'CRITICAL'
        elif severity_counts['HIGH'] > 5:
            status = 'HIGH_RISK'
        elif severity_counts['MEDIUM'] > 10:
            status = 'MEDIUM_RISK'
        else:
            status = 'LOW_RISK'

        return {
            'total_issues': total_issues,
            'files_audited': file_count,
            'severity_breakdown': severity_counts,
            'category_breakdown': category_counts,
            'auto_fixable_issues': auto_fixable_count,
            'risk_score': risk_score,
            'overall_status': status,
            'recommendations': self._get_recommendations(severity_counts, category_counts)
        }

    def _get_recommendations(self, severity_counts: Dict[str, int],
                           category_counts: Dict[str, int]) -> List[str]:
        """Generate recommendations based on audit results"""
        recommendations = []

        if severity_counts['CRITICAL'] > 0:
            recommendations.append("🚨 CRITICAL: Address all critical security issues before deployment")

        if severity_counts['HIGH'] > 0:
            recommendations.append("⚠️ HIGH: Review and fix high-severity issues")

        if category_counts['SECURITY'] > 5:
            recommendations.append("🔒 Consider implementing additional security measures")

        if category_counts['MAINTAINABILITY'] > 20:
            recommendations.append("🔧 Focus on code refactoring to improve maintainability")

        if not recommendations:
            recommendations.append("✅ Code quality looks good! Continue following best practices")

        return recommendations

    def generate_report(self, audit_results: Dict[str, Any], output_file: str):
        """Generate detailed audit report"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Code Audit Report\n\n")
            f.write(f"**Generated:** {audit_results['audit_timestamp']}\n\n")

            summary = audit_results['summary']
            f.write("## Summary\n\n")
            f.write(f"- **Files Audited:** {summary['files_audited']}\n")
            f.write(f"- **Total Issues:** {summary['total_issues']}\n")
            f.write(f"- **Risk Score:** {summary['risk_score']}/100\n")
            f.write(f"- **Overall Status:** {summary['overall_status']}\n\n")

            f.write("### Severity Breakdown\n\n")
            for severity, count in summary['severity_breakdown'].items():
                f.write(f"- **{severity}:** {count}\n")

            f.write("\n### Category Breakdown\n\n")
            for category, count in summary['category_breakdown'].items():
                f.write(f"- **{category}:** {count}\n")

            f.write("\n### Recommendations\n\n")
            for rec in summary['recommendations']:
                f.write(f"- {rec}\n")

            f.write("\n## Detailed Issues\n\n")

            # Group issues by severity
            issues_by_severity = {}
            for issue in audit_results['issues']:
                severity = issue['severity']
                if severity not in issues_by_severity:
                    issues_by_severity[severity] = []
                issues_by_severity[severity].append(issue)

            for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO']:
                if severity in issues_by_severity:
                    f.write(f"\n### {severity} Issues\n\n")
                    for issue in issues_by_severity[severity]:
                        f.write(f"**{issue['file_path']}:{issue['line_number']}**\n")
                        f.write(f"- **Type:** {issue['issue_type']}\n")
                        f.write(f"- **Description:** {issue['description']}\n")
                        f.write(f"- **Recommendation:** {issue['recommendation']}\n")
                        if issue['auto_fixable']:
                            f.write("- **Auto-fixable:** Yes\n")
                        f.write("\n")

def main():
    """Main audit execution"""
    import argparse

    parser = argparse.ArgumentParser(description='Comprehensive Code Audit')
    parser.add_argument('project_dir', help='Project directory to audit')
    parser.add_argument('--output', '-o', default='audit_report.md', help='Output report file')
    parser.add_argument('--json', help='Output JSON results file')

    args = parser.parse_args()

    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    print("🔍 Starting Comprehensive Code Audit...")

    auditor = ComprehensiveAuditor()
    results = auditor.audit_project(args.project_dir)

    # Generate reports
    auditor.generate_report(results, args.output)
    print(f"📄 Audit report generated: {args.output}")

    if args.json:
        with open(args.json, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"📊 JSON results saved: {args.json}")

    # Print summary
    summary = results['summary']
    print(f"\n📊 AUDIT SUMMARY")
    print(f"Files Audited: {summary['files_audited']}")
    print(f"Total Issues: {summary['total_issues']}")
    print(f"Risk Score: {summary['risk_score']}/100")
    print(f"Overall Status: {summary['overall_status']}")

    print(f"\n🚨 Critical: {summary['severity_breakdown']['CRITICAL']}")
    print(f"⚠️ High: {summary['severity_breakdown']['HIGH']}")
    print(f"📋 Medium: {summary['severity_breakdown']['MEDIUM']}")
    print(f"ℹ️ Low: {summary['severity_breakdown']['LOW']}")

    if summary['severity_breakdown']['CRITICAL'] > 0:
        print("\n🚨 CRITICAL ISSUES FOUND - Address before deployment!")
        return False
    elif summary['severity_breakdown']['HIGH'] > 0:
        print("\n⚠️ HIGH SEVERITY ISSUES - Review and fix recommended")
        return False
    else:
        print("\n✅ No critical issues found!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
