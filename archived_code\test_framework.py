#!/usr/bin/env python3
"""
Comprehensive Testing Framework - Phase 1 Implementation
Unit tests, integration tests, and system validation
"""

import os
import sys
import unittest
import sqlite3
import tempfile
import shutil
import json
import time
import threading
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Add project paths
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))

from core_infrastructure import HealthMonitor, AutoFixSystem, HealthCheck
from trading_config import TradingConfig

class TestTradingConfig(unittest.TestCase):
    """Test trading configuration"""

    def setUp(self):
        self.config = TradingConfig()

    def test_config_initialization(self):
        """Test configuration loads correctly"""
        self.assertIsNotNone(self.config.BASE_DIR)
        self.assertIsNotNone(self.config.DATA_DIR)
        self.assertIsNotNone(self.config.LOGS_DIR)
        self.assertTrue(hasattr(self.config, 'TRADING_PAIRS'))
        self.assertTrue(hasattr(self.config, 'FIXED_RISK_AMOUNT'))

    def test_directory_creation(self):
        """Test that required directories are created"""
        required_dirs = [
            self.config.DATA_DIR,
            self.config.LOGS_DIR,
            self.config.MODELS_DIR,
            self.config.REPORTS_DIR
        ]

        for directory in required_dirs:
            self.assertTrue(os.path.exists(directory), f"Directory {directory} should exist")

    def test_trading_parameters(self):
        """Test trading parameters are valid"""
        self.assertGreater(self.config.FIXED_RISK_AMOUNT, 0)
        self.assertIsInstance(self.config.TRADING_PAIRS, list)
        self.assertGreater(len(self.config.TRADING_PAIRS), 0)

class TestAutoFixSystem(unittest.TestCase):
    """Test automated fix system"""

    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config = TradingConfig()
        self.config.DATA_DIR = self.temp_dir
        self.auto_fix = AutoFixSystem(self.config)

    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_database_fix_initialization(self):
        """Test auto-fix system initializes correctly"""
        self.assertIsNotNone(self.auto_fix.config)
        self.assertEqual(self.auto_fix.max_fix_attempts, 3)
        self.assertEqual(self.auto_fix.fix_cooldown, 300)

    def test_database_lock_fix(self):
        """Test database lock auto-fix"""
        result = self.auto_fix._fix_database_issues("lock", "database is locked")
        self.assertTrue(result)

    def test_api_timeout_fix(self):
        """Test API timeout auto-fix"""
        result = self.auto_fix._fix_api_connection("timeout", "connection timeout")
        self.assertTrue(result)

    def test_rate_limit_fix(self):
        """Test rate limit auto-fix"""
        start_time = time.time()
        result = self.auto_fix._fix_api_connection("rate_limit", "rate limit exceeded")
        end_time = time.time()

        self.assertTrue(result)
        # Should have waited at least 60 seconds (mocked in real implementation)

    def test_memory_cleanup_fix(self):
        """Test memory cleanup auto-fix"""
        result = self.auto_fix._fix_memory_issues("memory", "out of memory")
        self.assertTrue(result)

    def test_fix_attempt_tracking(self):
        """Test fix attempt tracking and limits"""
        fix_key = "test_component_test_error"

        # First attempt should work
        self.assertFalse(self.auto_fix._exceeded_max_attempts(fix_key))

        # Record multiple attempts
        for _ in range(3):
            self.auto_fix._record_fix_attempt(fix_key)

        # Should now exceed max attempts
        self.assertTrue(self.auto_fix._exceeded_max_attempts(fix_key))

    def test_cooldown_mechanism(self):
        """Test fix attempt cooldown"""
        fix_key = "test_cooldown"

        # Record attempt
        self.auto_fix._record_fix_attempt(fix_key)

        # Should be in cooldown
        self.assertTrue(self.auto_fix._is_in_cooldown(fix_key))

class TestHealthMonitor(unittest.TestCase):
    """Test health monitoring system"""

    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config = TradingConfig()
        self.config.DATA_DIR = self.temp_dir
        self.config.LOGS_DIR = self.temp_dir
        self.health_monitor = HealthMonitor(self.config)

    def tearDown(self):
        self.health_monitor.stop_monitoring()
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_health_monitor_initialization(self):
        """Test health monitor initializes correctly"""
        self.assertIsNotNone(self.health_monitor.config)
        self.assertIsNotNone(self.health_monitor.auto_fix)
        self.assertFalse(self.health_monitor.monitoring)

    def test_database_health_check(self):
        """Test database health check"""
        # Create a test database
        db_path = os.path.join(self.temp_dir, 'trades.db')
        conn = sqlite3.connect(db_path)
        conn.execute("CREATE TABLE test_table (id INTEGER)")
        conn.execute("CREATE TABLE test_table2 (id INTEGER)")
        conn.execute("CREATE TABLE test_table3 (id INTEGER)")
        conn.close()

        check = self.health_monitor._check_database_health()
        self.assertEqual(check.component, "database")
        self.assertIn(check.status, ["HEALTHY", "WARNING"])

    def test_filesystem_health_check(self):
        """Test filesystem health check"""
        check = self.health_monitor._check_filesystem_health()
        self.assertEqual(check.component, "file_system")
        self.assertIn(check.status, ["HEALTHY", "WARNING", "CRITICAL", "FAILED"])

    @patch('requests.get')
    def test_api_connectivity_check(self, mock_get):
        """Test API connectivity check"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response

        check = self.health_monitor._check_api_connectivity()
        self.assertEqual(check.component, "api_connection")
        self.assertEqual(check.status, "HEALTHY")

    @patch('requests.get')
    def test_api_connectivity_failure(self, mock_get):
        """Test API connectivity failure"""
        # Mock API failure
        mock_get.side_effect = Exception("Connection failed")

        check = self.health_monitor._check_api_connectivity()
        self.assertEqual(check.component, "api_connection")
        self.assertEqual(check.status, "FAILED")

    def test_health_check_execution(self):
        """Test running all health checks"""
        checks = self.health_monitor.run_health_checks()

        self.assertIsInstance(checks, list)
        self.assertGreater(len(checks), 0)

        # Check that all expected components are tested
        components = [check.component for check in checks]
        expected_components = ["database", "file_system", "memory", "api_connection", "process"]

        for component in expected_components:
            self.assertIn(component, components)

    def test_health_summary(self):
        """Test health summary generation"""
        # Run health checks first
        health_results = self.health_monitor.run_health_checks()

        # Process the results to populate health history
        self.health_monitor._process_health_results(health_results)

        summary = self.health_monitor.get_health_summary()

        # Check if we got a proper summary or the "no data" response
        if "overall_status" in summary:
            self.assertIn("critical_issues", summary)
            self.assertIn("warning_issues", summary)
            self.assertIn("components", summary)
        else:
            # Handle the case where no health data is available
            self.assertIn("status", summary)
            self.assertEqual(summary["status"], "UNKNOWN")

    def test_monitoring_start_stop(self):
        """Test starting and stopping monitoring"""
        self.assertFalse(self.health_monitor.monitoring)

        self.health_monitor.start_monitoring()
        self.assertTrue(self.health_monitor.monitoring)

        # Wait a moment for thread to start
        time.sleep(0.1)

        self.health_monitor.stop_monitoring()
        self.assertFalse(self.health_monitor.monitoring)

class TestDatabaseOperations(unittest.TestCase):
    """Test database operations"""

    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, 'test_trades.db')

    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_database_creation(self):
        """Test database creation and initialization"""
        from trade_manager_webapp import TradeDatabase

        db = TradeDatabase(self.db_path)
        db.init_database()

        # Check that database file exists
        self.assertTrue(os.path.exists(self.db_path))

        # Check that tables were created
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()

        expected_tables = ['trades', 'positions', 'performance']
        for table in expected_tables:
            self.assertIn(table, tables)

    def test_trade_insertion(self):
        """Test trade data insertion"""
        from trade_manager_webapp import TradeDatabase, Trade

        db = TradeDatabase(self.db_path)
        db.init_database()

        # Create test trade
        trade = Trade(
            id="test_001",
            symbol="BTCUSDT",
            side="BUY",
            position_size=10.0,
            entry_price=45000.0,
            quantity=0.000222,
            status="FILLED",
            entry_time=datetime.now().isoformat(),
            entry_commission=0.01,
            exit_commission=0.01,
            total_commission=0.02,
            balance_before=1000.0,
            balance_after=1000.23
        )

        # Insert trade
        db.insert_trade(trade)

        # Retrieve and verify
        trades = db.get_recent_trades(1)
        self.assertEqual(len(trades), 1)
        self.assertEqual(trades[0]['id'], "test_001")
        self.assertEqual(trades[0]['symbol'], "BTCUSDT")

class TestCommissionCalculations(unittest.TestCase):
    """Test commission calculation system"""

    def setUp(self):
        from trade_manager_webapp import CommissionCalculator
        self.calc = CommissionCalculator()

    def test_commission_rate(self):
        """Test commission rate is correct"""
        self.assertEqual(self.calc.COMMISSION_RATE, 0.001)  # 0.1%

    def test_commission_calculation(self):
        """Test commission calculation for various trade sizes"""
        test_cases = [
            (10.0, 0.01, 0.01, 0.02),    # $10 trade
            (20.0, 0.02, 0.02, 0.04),    # $20 trade
            (100.0, 0.10, 0.10, 0.20),   # $100 trade
        ]

        for position_size, expected_entry, expected_exit, expected_total in test_cases:
            result = self.calc.calculate_trade_commission(position_size)

            self.assertEqual(result['entry_commission'], expected_entry)
            self.assertEqual(result['exit_commission'], expected_exit)
            self.assertEqual(result['total_commission'], expected_total)

    def test_net_pnl_calculation(self):
        """Test net P&L calculation after commissions"""
        gross_pnl = 0.50
        total_commission = 0.02

        net_pnl = self.calc.calculate_net_pnl(gross_pnl, total_commission)
        self.assertEqual(net_pnl, 0.48)

    def test_break_even_threshold(self):
        """Test break-even threshold calculation"""
        position_size = 10.0
        threshold = self.calc.break_even_threshold(position_size)

        # Should be 0.2% of position size
        expected = position_size * 0.002
        self.assertEqual(threshold, expected)

class TestManualTradeControls(unittest.TestCase):
    """Test manual trade size control system"""

    def setUp(self):
        from trade_manager_webapp import ManualTradeController
        self.controller = ManualTradeController(account_balance=1000.0)

    def test_controller_initialization(self):
        """Test manual trade controller initialization"""
        self.assertEqual(self.controller.account_balance, 1000.0)
        self.assertEqual(self.controller.default_trade_size, 10.0)
        self.assertEqual(self.controller.max_trade_size_pct, 0.05)

    def test_trade_size_validation(self):
        """Test trade size validation"""
        # Valid trade size
        result = self.controller.validate_trade_size(25.0)
        self.assertTrue(result['valid'])

        # Too small
        result = self.controller.validate_trade_size(0.5)
        self.assertFalse(result['valid'])

        # Too large (over 5% of account)
        result = self.controller.validate_trade_size(100.0)
        self.assertFalse(result['valid'])

    def test_manual_override_setting(self):
        """Test setting manual trade size override"""
        result = self.controller.set_manual_trade_size("BTCUSDT", 20.0)
        self.assertTrue(result['success'])

        # Get trade size should return override
        trade_info = self.controller.get_trade_size("BTCUSDT")
        self.assertEqual(trade_info['size'], 20.0)
        self.assertTrue(trade_info['is_override'])

    def test_daily_override_limits(self):
        """Test daily override limit enforcement"""
        # Set a large override that would exceed daily limit
        self.controller.daily_override_total = 90.0  # Already used 90

        result = self.controller.set_manual_trade_size("BTCUSDT", 50.0)
        self.assertFalse(result['success'])
        self.assertIn("Daily override limit exceeded", result['error'])

class TestSystemIntegration(unittest.TestCase):
    """Integration tests for the complete system"""

    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config = TradingConfig()
        self.config.DATA_DIR = self.temp_dir
        self.config.LOGS_DIR = self.temp_dir

    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_full_system_startup(self):
        """Test complete system startup sequence"""
        # Initialize health monitor
        health_monitor = HealthMonitor(self.config)

        # Run initial health checks
        health_results = health_monitor.run_health_checks()
        self.assertIsInstance(health_results, list)
        self.assertGreater(len(health_results), 0)

        # Start monitoring
        health_monitor.start_monitoring()
        self.assertTrue(health_monitor.monitoring)

        # Wait for a few monitoring cycles
        time.sleep(2)

        # Check health summary
        summary = health_monitor.get_health_summary()
        self.assertIn("overall_status", summary)

        # Stop monitoring
        health_monitor.stop_monitoring()
        self.assertFalse(health_monitor.monitoring)

    @patch('requests.get')
    def test_api_integration(self, mock_get):
        """Test API integration with health monitoring"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response

        health_monitor = HealthMonitor(self.config)
        check = health_monitor._check_api_connectivity()

        self.assertEqual(check.status, "HEALTHY")
        mock_get.assert_called_once()

def run_all_tests():
    """Run all tests and return results"""
    # Create test suite
    test_suite = unittest.TestSuite()

    # Add all test classes
    test_classes = [
        TestTradingConfig,
        TestAutoFixSystem,
        TestHealthMonitor,
        TestDatabaseOperations,
        TestCommissionCalculations,
        TestManualTradeControls,
        TestSystemIntegration
    ]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    return result

def main():
    """Main test execution"""
    print("🧪 Running Comprehensive Test Suite...")
    print("=" * 60)

    result = run_all_tests()

    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    print(f"Tests Run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")

    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")

    if result.errors:
        print("\n🚨 ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")

    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
    print(f"\n✅ Success Rate: {success_rate:.1f}%")

    if result.wasSuccessful():
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("⚠️ Some tests failed. Please review and fix issues.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
