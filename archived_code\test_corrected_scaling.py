#!/usr/bin/env python3
"""
Test Corrected Risk Scaling in Live System
"""

import requests

def test_corrected_scaling():
    print('🔧 TESTING CORRECTED RISK SCALING IN LIVE SYSTEM')
    print('=' * 70)

    try:
        response = requests.get('http://localhost:5000/api/risk_scaling', timeout=5)
        if response.status_code == 200:
            data = response.json()
            
            print('✅ Risk Scaling API Working!')
            print('📊 CORRECTED RISK SCALING:')
            
            examples = data.get('examples', {})
            
            print(f'   At $1,000: Risk ${examples.get("account_1000", {}).get("risk", 0)}, Target ${examples.get("account_1000", {}).get("profit_target", 0)}')
            print(f'   At $1,500: Risk ${examples.get("account_1500", {}).get("risk", 0)}, Target ${examples.get("account_1500", {}).get("profit_target", 0)}')
            print(f'   At $2,000: Risk ${examples.get("account_2000", {}).get("risk", 0)}, Target ${examples.get("account_2000", {}).get("profit_target", 0)}')
            print(f'   At $2,500: Risk ${examples.get("account_2500", {}).get("risk", 0)}, Target ${examples.get("account_2500", {}).get("profit_target", 0)}')
            
            # Verify the correction
            risk_1000 = examples.get("account_1000", {}).get("risk", 0)
            risk_1500 = examples.get("account_1500", {}).get("risk", 0)
            risk_2000 = examples.get("account_2000", {}).get("risk", 0)
            risk_2500 = examples.get("account_2500", {}).get("risk", 0)
            
            print(f'\n🎯 VERIFICATION:')
            print(f'   $1,000 → $20 risk: {"✅ CORRECT" if risk_1000 == 20 else "❌ WRONG"}')
            print(f'   $1,500 → $30 risk: {"✅ CORRECT" if risk_1500 == 30 else "❌ WRONG"}')
            print(f'   $2,000 → $40 risk: {"✅ CORRECT" if risk_2000 == 40 else "❌ WRONG"}')
            print(f'   $2,500 → $50 risk: {"✅ CORRECT" if risk_2500 == 50 else "❌ WRONG"}')
            
            if all([risk_1000 == 20, risk_1500 == 30, risk_2000 == 40, risk_2500 == 50]):
                print(f'\n✅ RISK SCALING CORRECTION SUCCESSFUL!')
                print(f'   Now starts compounding at $1,000 with $20 risk')
                print(f'   Increases by $10 for every $500 as requested')
                return True
            else:
                print(f'\n❌ RISK SCALING STILL INCORRECT')
                return False
            
        else:
            print(f'❌ API Error: {response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ Error: {e}')
        return False

if __name__ == "__main__":
    test_corrected_scaling()
