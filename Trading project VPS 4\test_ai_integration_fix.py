#!/usr/bin/env python3
"""
Test AI Signal Integration Fix
"""

import time
from datetime import datetime
from ai_signal_monitor import ai_monitor

def test_ai_integration():
    print('🔍 TESTING AI SIGNAL INTEGRATION FIX')
    print('=' * 70)

    # Start AI monitoring if not running
    if not ai_monitor.monitoring:
        ai_monitor.start_monitoring()
        print('🤖 AI Monitor started')
        time.sleep(3)  # Give it time to generate some data

    # Check current AI monitor status
    status = ai_monitor.get_current_status()
    print(f'📊 CURRENT AI STATUS:')
    print(f'   Monitoring Active: {status["monitoring"]}')
    print(f'   Current Confidence: {status["current_confidence"]:.1%}')
    print(f'   Above Threshold: {status.get("above_threshold", False)}')
    print(f'   Total Signals: {status.get("total_signals", 0)}')

    # Check recent signals
    recent_signals = ai_monitor.get_recent_signals(5)
    print(f'\n🎯 RECENT AI SIGNALS ({len(recent_signals)}):')
    for i, signal in enumerate(recent_signals, 1):
        print(f'   {i}. {signal["action"]} @ ${signal["price"]:,.2f}')
        print(f'      Confidence: {signal["confidence"]:.1%}')
        print(f'      Action Prob: {signal["action_probability"]:.1%}')
        print(f'      Time: {signal["timestamp"].strftime("%H:%M:%S")}')
        print(f'      Age: {(time.time() - signal["timestamp"].timestamp()):.1f}s')

    # Test signal criteria
    if recent_signals:
        latest = recent_signals[-1]
        print(f'\n🔍 LATEST SIGNAL ANALYSIS:')
        print(f'   Action: {latest["action"]}')
        print(f'   Confidence: {latest["confidence"]:.1%} (need ≥75%)')
        print(f'   Action Probability: {latest["action_probability"]:.1%} (need >40%)')
        
        signal_age = time.time() - latest["timestamp"].timestamp()
        print(f'   Signal Age: {signal_age:.1f}s (need ≤30s)')
        
        meets_criteria = (
            latest["confidence"] >= 0.75 and
            latest["action_probability"] > 0.4 and
            latest["action"] in ['BUY', 'SELL'] and
            signal_age <= 30
        )
        
        print(f'   Meets All Criteria: {"✅ YES" if meets_criteria else "❌ NO"}')
        
        if meets_criteria:
            print(f'   🚨 SIGNAL SHOULD TRIGGER TRADE!')
        else:
            reasons = []
            if latest["confidence"] < 0.75:
                reasons.append(f'confidence {latest["confidence"]:.1%} < 75%')
            if latest["action_probability"] <= 0.4:
                reasons.append(f'action prob {latest["action_probability"]:.1%} ≤ 40%')
            if latest["action"] not in ['BUY', 'SELL']:
                reasons.append(f'action "{latest["action"]}" not actionable')
            if signal_age > 30:
                reasons.append(f'signal age {signal_age:.1f}s > 30s')
            print(f'   ⏳ Blocked by: {", ".join(reasons)}')
            
    else:
        print('   📭 No signals generated yet')

    print(f'\n✅ AI SIGNAL INTEGRATION ADDED TO TRADING LOOP')
    print(f'   - AI monitor signals now checked in should_enter_trade()')
    print(f'   - 75% confidence threshold enforced')
    print(f'   - 40% action probability threshold enforced')
    print(f'   - 30-second signal freshness requirement')
    print(f'   - Integration with existing grid trading system')

if __name__ == "__main__":
    test_ai_integration()
