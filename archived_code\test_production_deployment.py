#!/usr/bin/env python3
"""
PRODUCTION DEPLOYMENT TEST SCRIPT
=================================
Test all resolved issues and verify the production webapp is working correctly.
"""

import requests
import time
import json
from datetime import datetime

class ProductionTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.test_results = {}
        
    def run_all_tests(self):
        """Run comprehensive tests on the production deployment."""
        print("🧪 PRODUCTION DEPLOYMENT TESTING")
        print("=" * 50)
        print(f"🌐 Testing URL: {self.base_url}")
        print(f"⏰ Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        tests = [
            ("Webapp Connectivity", self.test_connectivity),
            ("Trading Engine Status", self.test_engine_status),
            ("Auto Trading Loop", self.test_auto_trading),
            ("P&L Calculations", self.test_pnl_calculations),
            ("Position Sizing", self.test_position_sizing),
            ("UI Responsiveness", self.test_ui_responsiveness),
            ("API Endpoints", self.test_api_endpoints),
            ("Test Trading Functions", self.test_trading_functions)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 Testing: {test_name}")
            try:
                result = test_func()
                if result:
                    print(f"✅ {test_name}: PASSED")
                    passed += 1
                else:
                    print(f"❌ {test_name}: FAILED")
                self.test_results[test_name] = result
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                self.test_results[test_name] = False
        
        # Generate test report
        self.generate_test_report(passed, total)
        
    def test_connectivity(self):
        """Test basic webapp connectivity."""
        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def test_engine_status(self):
        """Test trading engine status and configuration."""
        try:
            response = requests.get(f"{self.base_url}/api/trading_status")
            data = response.json()
            
            # Check required fields
            required_fields = ['is_running', 'current_price', 'model_info', 'performance']
            for field in required_fields:
                if field not in data:
                    return False
            
            # Check model info
            model_info = data['model_info']
            if model_info['composite_score'] != 91.4:
                return False
            
            return True
        except:
            return False
    
    def test_auto_trading(self):
        """Test auto trading functionality."""
        try:
            # Start trading
            start_response = requests.post(f"{self.base_url}/api/start_trading")
            if start_response.status_code != 200:
                return False
            
            # Wait a moment for auto trading to initialize
            time.sleep(3)
            
            # Check status
            status_response = requests.get(f"{self.base_url}/api/trading_status")
            data = status_response.json()
            
            # Verify trading is running
            if not data.get('is_running', False):
                return False
            
            print("   📊 Auto trading loop is active")
            return True
        except:
            return False
    
    def test_pnl_calculations(self):
        """Test P&L calculation fixes."""
        try:
            # Run test cycle to verify P&L calculations
            response = requests.post(f"{self.base_url}/api/test_trading_cycle")
            if response.status_code != 200:
                return False
            
            data = response.json()
            if data.get('status') != 'success':
                return False
            
            # Check P&L is close to $25 target
            pnl = data.get('pnl', 0)
            if abs(pnl - 25.0) > 5.0:  # Allow 5% tolerance
                print(f"   ❌ P&L ({pnl}) not close to $25 target")
                return False
            
            print(f"   ✅ P&L calculation correct: ${pnl:.2f}")
            return True
        except:
            return False
    
    def test_position_sizing(self):
        """Test position sizing accuracy."""
        try:
            response = requests.get(f"{self.base_url}/api/cross_margin_analysis")
            if response.status_code != 200:
                return False
            
            data = response.json()
            buy_position = data.get('buy_position', {})
            
            # Check expected profit is close to $25
            expected_profit = buy_position.get('expected_profit', 0)
            if abs(expected_profit - 25.0) > 1.0:
                return False
            
            # Check actual risk is close to $10
            actual_risk = buy_position.get('actual_risk', 0)
            if abs(actual_risk - 10.0) > 1.0:
                return False
            
            print(f"   ✅ Position sizing: ${actual_risk:.2f} risk → ${expected_profit:.2f} profit")
            return True
        except:
            return False
    
    def test_ui_responsiveness(self):
        """Test UI responsiveness."""
        try:
            # Test main dashboard
            response = requests.get(self.base_url, timeout=5)
            if response.status_code != 200:
                return False
            
            # Check if HTML contains expected elements
            html = response.text
            required_elements = [
                'TCN-CNN-PPO',
                'Trading Dashboard',
                'Start Trading',
                'Stop Trading'
            ]
            
            for element in required_elements:
                if element not in html:
                    return False
            
            print("   ✅ UI loads correctly with all elements")
            return True
        except:
            return False
    
    def test_api_endpoints(self):
        """Test all API endpoints."""
        try:
            endpoints = [
                '/api/trading_status',
                '/api/open_positions', 
                '/api/recent_trades',
                '/api/cross_margin_analysis'
            ]
            
            for endpoint in endpoints:
                response = requests.get(f"{self.base_url}{endpoint}")
                if response.status_code != 200:
                    print(f"   ❌ Endpoint {endpoint} failed")
                    return False
            
            print("   ✅ All API endpoints responding")
            return True
        except:
            return False
    
    def test_trading_functions(self):
        """Test trading functions."""
        try:
            # Test forced trade
            response = requests.post(f"{self.base_url}/api/force_test_trade",
                                   json={'direction': 'BUY'})
            if response.status_code != 200:
                return False
            
            data = response.json()
            if data.get('status') != 'success':
                return False
            
            # Verify trade was created with correct parameters
            if data.get('target_profit') != 25.0:
                return False
            
            if data.get('risk_amount') != 10.0:
                return False
            
            print("   ✅ Test trading functions working")
            return True
        except:
            return False
    
    def generate_test_report(self, passed, total):
        """Generate comprehensive test report."""
        print("\n" + "=" * 50)
        print("📋 PRODUCTION DEPLOYMENT TEST REPORT")
        print("=" * 50)
        
        success_rate = (passed / total) * 100
        print(f"🏥 Overall Success Rate: {success_rate:.1f}% ({passed}/{total})")
        
        if success_rate >= 90:
            print("✅ DEPLOYMENT STATUS: EXCELLENT")
            print("🚀 Ready for live trading!")
        elif success_rate >= 75:
            print("⚠️ DEPLOYMENT STATUS: GOOD")
            print("🔧 Minor issues detected")
        else:
            print("❌ DEPLOYMENT STATUS: NEEDS ATTENTION")
            print("🛠️ Multiple issues require fixing")
        
        print(f"\n📊 DETAILED RESULTS:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {status} {test_name}")
        
        print(f"\n🎯 RESOLVED ISSUES VERIFIED:")
        if self.test_results.get("Auto Trading Loop", False):
            print("   ✅ Auto trading loop working")
        if self.test_results.get("P&L Calculations", False):
            print("   ✅ P&L calculations fixed ($25 targets)")
        if self.test_results.get("Position Sizing", False):
            print("   ✅ Position sizing accurate")
        if self.test_results.get("UI Responsiveness", False):
            print("   ✅ Clean, modern UI deployed")
        
        print("\n" + "=" * 50)
        print("🔍 TESTING COMPLETE")
        print("=" * 50)

def main():
    """Main testing function."""
    print("🧪 STARTING PRODUCTION DEPLOYMENT TESTS...")
    time.sleep(1)
    
    tester = ProductionTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
