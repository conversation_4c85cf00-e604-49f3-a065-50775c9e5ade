#!/usr/bin/env python3
"""
BINANCE CONNECTION TEST SCRIPT
=============================
Test live connection to Binance API for real money trading readiness.
"""

import os
import sys
import json
from datetime import datetime

def test_ccxt_availability():
    """Test if CCXT library is available."""
    try:
        import ccxt
        print("✅ CCXT Library: Available")
        print(f"   Version: {ccxt.__version__}")
        return True, ccxt
    except ImportError:
        print("❌ CCXT Library: Not Available")
        print("   Install with: pip install ccxt")
        return False, None

def load_api_keys():
    """Load API keys from BinanceAPI_2.txt file."""
    try:
        api_file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "BinanceAPI_2.txt")
        
        if os.path.exists(api_file_path):
            with open(api_file_path, 'r') as f:
                lines = f.read().strip().split('\n')
                if len(lines) >= 2:
                    api_key = lines[0].strip()
                    secret_key = lines[1].strip()
                    print("✅ API Keys: Loaded successfully")
                    print(f"   API Key: {api_key[:8]}...{api_key[-8:]}")
                    print(f"   Secret Key: {secret_key[:8]}...{secret_key[-8:]}")
                    return api_key, secret_key
        
        print("❌ API Keys: Could not load from BinanceAPI_2.txt")
        return None, None
        
    except Exception as e:
        print(f"❌ API Keys: Error loading - {e}")
        return None, None

def test_binance_connection(ccxt_module, api_key, secret_key, testnet=True):
    """Test connection to Binance API."""
    try:
        mode = "TESTNET" if testnet else "LIVE MONEY"
        print(f"\n🔗 Testing Binance {mode} Connection...")
        
        exchange = ccxt_module.binance({
            'apiKey': api_key,
            'secret': secret_key,
            'sandbox': testnet,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'spot',
                'adjustForTimeDifference': True,
            }
        })
        
        # Test connection by fetching balance
        print("   📡 Connecting to Binance...")
        balance = exchange.fetch_balance()
        
        print(f"✅ Binance {mode}: Connected successfully!")
        print(f"   Account Type: {balance.get('info', {}).get('accountType', 'Unknown')}")
        
        # Show some balance info
        if 'USDT' in balance:
            usdt_balance = balance['USDT']
            print(f"   USDT Balance: {usdt_balance.get('free', 0):.2f} (Free: {usdt_balance.get('free', 0):.2f})")
        
        if 'BTC' in balance:
            btc_balance = balance['BTC']
            print(f"   BTC Balance: {btc_balance.get('free', 0):.8f} (Free: {btc_balance.get('free', 0):.8f})")
        
        # Test market data access
        print("   📊 Testing market data access...")
        ticker = exchange.fetch_ticker('BTC/USDT')
        print(f"   BTC/USDT Price: ${ticker['last']:,.2f}")
        
        return True, exchange
        
    except Exception as e:
        print(f"❌ Binance {mode}: Connection failed")
        print(f"   Error: {e}")
        return False, None

def test_order_permissions(exchange, testnet=True):
    """Test if we have trading permissions."""
    try:
        mode = "TESTNET" if testnet else "LIVE"
        print(f"\n🔐 Testing {mode} Trading Permissions...")
        
        # Get account info to check permissions
        account = exchange.fetch_balance()
        account_info = account.get('info', {})
        
        permissions = account_info.get('permissions', [])
        print(f"   Account Permissions: {', '.join(permissions) if permissions else 'Unknown'}")
        
        # Check if we can access trading endpoints
        if 'SPOT' in permissions:
            print("✅ Spot Trading: Enabled")
        else:
            print("⚠️ Spot Trading: Status unknown")
            
        return True
        
    except Exception as e:
        print(f"❌ Trading Permissions: Could not verify")
        print(f"   Error: {e}")
        return False

def main():
    """Main test function."""
    print("🔍 BINANCE LIVE TRADING READINESS TEST")
    print("=" * 60)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: CCXT Library
    print("📦 STEP 1: Testing CCXT Library")
    print("-" * 40)
    ccxt_available, ccxt_module = test_ccxt_availability()
    
    if not ccxt_available:
        print("\n❌ CRITICAL: CCXT library not available")
        print("   Install with: pip install ccxt")
        print("   Live trading will not work without CCXT")
        return False
    
    # Test 2: API Keys
    print("\n🔑 STEP 2: Testing API Keys")
    print("-" * 40)
    api_key, secret_key = load_api_keys()
    
    if not api_key or not secret_key:
        print("\n❌ CRITICAL: API keys not available")
        print("   Check BinanceAPI_2.txt file")
        return False
    
    # Test 3: Testnet Connection
    print("\n🧪 STEP 3: Testing Testnet Connection")
    print("-" * 40)
    testnet_success, testnet_exchange = test_binance_connection(
        ccxt_module, api_key, secret_key, testnet=True
    )
    
    if testnet_success:
        test_order_permissions(testnet_exchange, testnet=True)
    
    # Test 4: Live Connection
    print("\n💰 STEP 4: Testing Live Connection")
    print("-" * 40)
    live_success, live_exchange = test_binance_connection(
        ccxt_module, api_key, secret_key, testnet=False
    )
    
    if live_success:
        test_order_permissions(live_exchange, testnet=False)
    
    # Final Report
    print("\n" + "=" * 60)
    print("📋 LIVE TRADING READINESS REPORT")
    print("=" * 60)
    
    if ccxt_available and api_key and (testnet_success or live_success):
        print("✅ READY FOR LIVE TRADING!")
        print("   ✅ CCXT Library: Available")
        print("   ✅ API Keys: Valid")
        if testnet_success:
            print("   ✅ Testnet: Connected")
        if live_success:
            print("   ✅ Live Account: Connected")
        print("\n🎯 You can now enable live trading in the webapp!")
        print("   1. Go to http://localhost:5000")
        print("   2. Click 'Switch to Live Mode'")
        print("   3. Choose testnet or live money")
        return True
    else:
        print("❌ NOT READY FOR LIVE TRADING")
        if not ccxt_available:
            print("   ❌ Install CCXT library")
        if not api_key:
            print("   ❌ Fix API keys")
        if not testnet_success and not live_success:
            print("   ❌ Fix Binance connection")
        return False

if __name__ == "__main__":
    main()
