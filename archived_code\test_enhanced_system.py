"""
Comprehensive Test Suite for Enhanced Trading System
Tests all components including test trading, validation, and audit features
"""

import os
import sys
import unittest
import asyncio
import tempfile
import shutil
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

# Add config to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
from trading_config import TradingConfig

# Import enhanced components
from enhanced_test_trading_engine import TestTradingEngine, TestTradeExecutor, TradingMode, TestTrade
from test_mode_validator import TestModeValidator
from comprehensive_code_audit import EnhancedCodeAuditor


class TestEnhancedTradingEngine(unittest.TestCase):
    """Test enhanced test trading engine"""
    
    def setUp(self):
        """Set up test environment"""
        self.config = TradingConfig()
        self.config.DATA_DIR = tempfile.mkdtemp()
        self.config.LOGS_DIR = tempfile.mkdtemp()
        self.test_engine = TestTradingEngine(self.config)
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.config.DATA_DIR, ignore_errors=True)
        shutil.rmtree(self.config.LOGS_DIR, ignore_errors=True)
    
    def test_test_engine_initialization(self):
        """Test test engine initialization"""
        self.assertEqual(self.test_engine.mode, TradingMode.TEST)
        self.assertFalse(self.test_engine.is_running)
        self.assertEqual(self.test_engine.current_balance, self.config.INITIAL_CAPITAL)
        self.assertEqual(len(self.test_engine.open_trades), 0)
        self.assertEqual(len(self.test_engine.closed_trades), 0)
    
    async def test_start_test_trading(self):
        """Test starting test trading"""
        with patch.object(self.test_engine.data_collector, 'test_connection', return_value=True):
            with patch.object(self.test_engine.data_collector, 'get_real_time_price', return_value=50000.0):
                success = await self.test_engine.start_test_trading()
                self.assertTrue(success)
                self.assertTrue(self.test_engine.is_running)
                self.assertIsNotNone(self.test_engine.start_time)
    
    async def test_stop_test_trading(self):
        """Test stopping test trading"""
        # Start first
        with patch.object(self.test_engine.data_collector, 'test_connection', return_value=True):
            with patch.object(self.test_engine.data_collector, 'get_real_time_price', return_value=50000.0):
                await self.test_engine.start_test_trading()
        
        # Stop
        summary = await self.test_engine.stop_test_trading()
        self.assertTrue(summary['success'])
        self.assertFalse(self.test_engine.is_running)
        self.assertIn('final_balance', summary)
        self.assertIn('total_trades', summary)
    
    def test_test_status(self):
        """Test getting test status"""
        status = self.test_engine.get_test_status()
        self.assertEqual(status['mode'], 'TEST')
        self.assertIn('is_running', status)
        self.assertIn('current_balance', status)
        self.assertIn('total_pnl', status)
        self.assertIn('open_trades', status)
        self.assertIn('closed_trades', status)
    
    def test_export_test_results(self):
        """Test exporting test results"""
        results = self.test_engine.export_test_results()
        self.assertEqual(results['mode'], 'TEST')
        self.assertIn('export_time', results)
        self.assertIn('session_summary', results)
        self.assertIn('all_trades', results)
        self.assertIn('performance_metrics', results)
        self.assertIn('equity_curve', results)


class TestTradeExecutor(unittest.TestCase):
    """Test trade executor functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.config = TradingConfig()
        self.executor = TestTradeExecutor(self.config)
    
    def test_commission_calculation(self):
        """Test commission calculation"""
        trade_value = 1000.0
        commissions = self.executor.calculate_commission(trade_value)
        
        expected_entry = trade_value * 0.001
        expected_exit = trade_value * 0.001
        expected_total = expected_entry + expected_exit
        
        self.assertEqual(commissions['entry_commission'], expected_entry)
        self.assertEqual(commissions['exit_commission'], expected_exit)
        self.assertEqual(commissions['total_commission'], expected_total)
    
    def test_position_size_calculation(self):
        """Test position size calculation"""
        risk_amount = 10.0
        entry_price = 50000.0
        stop_loss_price = 49875.0  # 0.25% below entry
        
        position_size, quantity = self.executor.calculate_position_size(
            risk_amount, entry_price, stop_loss_price
        )
        
        expected_position_size = risk_amount / (entry_price - stop_loss_price)
        expected_quantity = expected_position_size / entry_price
        
        self.assertAlmostEqual(position_size, expected_position_size, places=6)
        self.assertAlmostEqual(quantity, expected_quantity, places=8)


class TestModeValidatorTests(unittest.TestCase):
    """Test mode validator functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.config = TradingConfig()
        self.config.LOGS_DIR = tempfile.mkdtemp()
        self.validator = TestModeValidator(self.config)
        self.test_engine = TestTradingEngine(self.config)
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.config.LOGS_DIR, ignore_errors=True)
    
    def test_trade_logic_validation(self):
        """Test trade logic validation"""
        # Create test trade
        test_trade = TestTrade(
            trade_id="TEST_001",
            symbol="BTCUSDT",
            side="BUY",
            action="BUY",
            entry_price=50000.0,
            exit_price=50125.0,
            position_size=10.0,
            quantity=0.0002,
            risk_amount=10.0,
            target_profit=20.0,
            entry_time=datetime.now().isoformat()
        )
        
        # Create expected live trade
        expected_live_trade = {
            'entry_price': 50000.0,
            'exit_price': 50125.0,
            'position_size': 10.0,
            'commission_total': 0.02,
            'risk_amount': 10.0,
            'target_profit': 20.0
        }
        
        # Validate
        validation_result = self.validator.validate_trade_logic(test_trade, expected_live_trade)
        
        self.assertIn('trade_id', validation_result)
        self.assertIn('passed', validation_result)
        self.assertIn('errors', validation_result)
        self.assertIn('warnings', validation_result)
    
    def test_comprehensive_validation(self):
        """Test comprehensive validation"""
        validation_report = self.validator.run_comprehensive_validation(self.test_engine)
        
        self.assertIn('validation_time', validation_report)
        self.assertIn('test_mode', validation_report)
        self.assertIn('overall_passed', validation_report)
        self.assertIn('validation_categories', validation_report)
        self.assertIn('summary', validation_report)
        
        # Check summary structure
        summary = validation_report['summary']
        self.assertIn('total_validations', summary)
        self.assertIn('passed_validations', summary)
        self.assertIn('failed_validations', summary)
        self.assertIn('warnings', summary)
    
    def test_save_validation_report(self):
        """Test saving validation report"""
        validation_report = {
            'test': 'data',
            'validation_time': datetime.now().isoformat()
        }
        
        filepath = self.validator.save_validation_report(validation_report)
        self.assertTrue(os.path.exists(filepath))
        
        # Verify content
        with open(filepath, 'r') as f:
            import json
            saved_data = json.load(f)
            self.assertEqual(saved_data['test'], 'data')


class TestEnhancedCodeAuditor(unittest.TestCase):
    """Test enhanced code auditor"""
    
    def setUp(self):
        """Set up test environment"""
        self.config = TradingConfig()
        self.auditor = EnhancedCodeAuditor(self.config)
    
    def test_auditor_initialization(self):
        """Test auditor initialization"""
        self.assertIsInstance(self.auditor.core_files, set)
        self.assertIsInstance(self.auditor.unused_patterns, list)
        self.assertIn('audit_time', self.auditor.audit_results)
        self.assertEqual(self.auditor.audit_results['files_analyzed'], 0)
        self.assertEqual(self.auditor.audit_results['issues_found'], 0)
    
    def test_core_files_definition(self):
        """Test core files are properly defined"""
        expected_core_files = {
            'enhanced_test_trading_engine.py',
            'test_mode_validator.py',
            'enhanced_trading_webapp.py',
            'grid_trading_core.py'
        }
        
        for core_file in expected_core_files:
            self.assertIn(core_file, self.auditor.core_files)
    
    def test_unused_patterns(self):
        """Test unused file patterns"""
        test_filenames = [
            'simple_test.py',
            'demo_trading.py',
            'old_file.backup',
            'temp_file.tmp',
            'btc_trading_old.py'
        ]
        
        for filename in test_filenames:
            is_unused = any(
                re.match(pattern, filename) 
                for pattern in self.auditor.unused_patterns
            )
            self.assertTrue(is_unused, f"File {filename} should match unused patterns")


class TestSystemIntegration(unittest.TestCase):
    """Integration tests for the complete enhanced system"""
    
    def setUp(self):
        """Set up integration test environment"""
        self.config = TradingConfig()
        self.config.DATA_DIR = tempfile.mkdtemp()
        self.config.LOGS_DIR = tempfile.mkdtemp()
        
        self.test_engine = TestTradingEngine(self.config)
        self.validator = TestModeValidator(self.config)
        self.auditor = EnhancedCodeAuditor(self.config)
    
    def tearDown(self):
        """Clean up integration test environment"""
        shutil.rmtree(self.config.DATA_DIR, ignore_errors=True)
        shutil.rmtree(self.config.LOGS_DIR, ignore_errors=True)
    
    async def test_full_test_trading_cycle(self):
        """Test complete test trading cycle"""
        # Mock data collector
        with patch.object(self.test_engine.data_collector, 'test_connection', return_value=True):
            with patch.object(self.test_engine.data_collector, 'get_real_time_price', return_value=50000.0):
                
                # Start test trading
                success = await self.test_engine.start_test_trading()
                self.assertTrue(success)
                
                # Process a few cycles
                for _ in range(3):
                    cycle_result = await self.test_engine.process_test_trading_cycle()
                    self.assertTrue(cycle_result['success'])
                
                # Stop test trading
                summary = await self.test_engine.stop_test_trading()
                self.assertTrue(summary['success'])
                
                # Validate the session
                validation_report = self.validator.run_comprehensive_validation(self.test_engine)
                self.assertIn('overall_passed', validation_report)
    
    def test_audit_and_validation_integration(self):
        """Test integration between audit and validation systems"""
        # Run audit
        audit_results = self.auditor.audit_results
        self.assertIsInstance(audit_results, dict)
        
        # Run validation
        validation_report = self.validator.run_comprehensive_validation(self.test_engine)
        self.assertIsInstance(validation_report, dict)
        
        # Both should have timestamps
        self.assertIn('audit_time', audit_results)
        self.assertIn('validation_time', validation_report)
    
    def test_configuration_consistency(self):
        """Test configuration consistency across components"""
        # All components should use the same config
        self.assertEqual(self.test_engine.config.INITIAL_CAPITAL, self.config.INITIAL_CAPITAL)
        self.assertEqual(self.validator.config.GRID_SPACING, self.config.GRID_SPACING)
        self.assertEqual(self.auditor.config.FIXED_RISK_AMOUNT, self.config.FIXED_RISK_AMOUNT)
    
    def test_error_handling_consistency(self):
        """Test error handling consistency across components"""
        # Test invalid operations
        with self.assertRaises(Exception):
            # Try to stop test trading when not running
            asyncio.run(self.test_engine.stop_test_trading())
        
        # Validator should handle invalid data gracefully
        invalid_trade = TestTrade(
            trade_id="INVALID",
            symbol="",
            side="",
            action="",
            entry_price=0,
            exit_price=0,
            position_size=0,
            quantity=0,
            risk_amount=0,
            target_profit=0,
            entry_time=""
        )
        
        validation_result = self.validator.validate_trade_logic(invalid_trade, {})
        self.assertFalse(validation_result['passed'])
        self.assertGreater(len(validation_result['errors']), 0)


def run_all_enhanced_tests():
    """Run all enhanced system tests"""
    print("🧪 Running Enhanced Trading System Tests...")
    print("="*60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestEnhancedTradingEngine,
        TestTradeExecutor,
        TestModeValidatorTests,
        TestEnhancedCodeAuditor,
        TestSystemIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "="*60)
    print("🎯 ENHANCED SYSTEM TEST SUMMARY")
    print("="*60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  • {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n🚨 ERRORS:")
        for test, traceback in result.errors:
            print(f"  • {test}: {traceback.split('Exception:')[-1].strip()}")
    
    if not result.failures and not result.errors:
        print("\n✅ ALL TESTS PASSED!")
    
    return result


if __name__ == "__main__":
    # Import required modules for async tests
    import re
    
    # Run all tests
    result = run_all_enhanced_tests()
    
    # Exit with appropriate code
    exit_code = 0 if not result.failures and not result.errors else 1
    sys.exit(exit_code)
