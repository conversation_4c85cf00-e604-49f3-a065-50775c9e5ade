#!/usr/bin/env python3
"""
Test script to verify simple_trading_executor import
"""

def test_import():
    """Test importing the trading executor"""
    try:
        print("🔍 Testing simple_trading_executor import...")
        
        # Test import
        from simple_trading_executor import get_trading_executor
        print("✅ Import successful!")
        
        # Test executor creation
        executor = get_trading_executor(300.0)
        print(f"✅ Executor created: {type(executor)}")
        print(f"✅ Initial balance: ${executor.balance:.2f}")
        
        # Test reset
        executor.reset()
        print("✅ Reset successful!")
        
        # Test basic functionality
        current_price = executor.get_current_price()
        print(f"✅ Current price: ${current_price:.2f}")
        
        print("\n🎉 All tests passed! Import is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_import()
